import {
  CA_Mastr_Models_v1_0_Models_EntityPermissionRole,
  ConfigurationService,
  UserSlicerService,
} from "@/utils/openapi";
import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { TGroupMode, TStoredQuery } from "@/types/slicer";
import { getGuidDisplayText, getUniqueKey, getUuid } from "@/utils/helpers";
import { apiActivityManager } from "@/utils/api-activity-manager";
import {
  SlicerModuleContextReducerAction,
  SlicerModuleContextState,
  SlicerUserSettingsType,
} from "./SlicerModuleContextTypes";
import { getDefaultState } from ".";

export const initialSlicerModuleContextAction = async (
  _state: SlicerModuleContextState,
  dispatch: React.Dispatch<SlicerModuleContextReducerAction>
): Promise<void> => {
  const userSettingsInSessionStorage = safeSessionStorage.getItem(SESSION_STORAGE_KEY.SLICER_USER_SETTINGS);
  const userSettingsInLocalStorage = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.SLICER_USER_SETTINGS);
  if (userSettingsInSessionStorage && userSettingsInSessionStorage !== "undefined") {
    try {
      const userSettingsParsed = JSON.parse(userSettingsInSessionStorage);
      dispatch({ type: "UPDATE_SLICER_USER_SETTINGS", userSettings: userSettingsParsed });
    } finally {
      // Skip if JSON failed to parse
    }
  } else if (userSettingsInLocalStorage && userSettingsInLocalStorage !== "undefined") {
    const userSettingsParsed = JSON.parse(userSettingsInLocalStorage);
    dispatch({ type: "UPDATE_SLICER_USER_SETTINGS", userSettings: userSettingsParsed });
  }
  const { loan_level_slicer_config } = await ConfigurationService.getLoanLevelSlicerConfiguration();
  dispatch({ type: "SET_LOAN_LEVEL_SLICER_CONFIG", loan_level_slicer_config });
};

export const getSlicerModuleContextAction = (
  state: SlicerModuleContextState,
  dispatch: React.Dispatch<SlicerModuleContextReducerAction>
) => {
  return {
    run: (opts: { lastRun?: string; isOldRun?: boolean; runId?: string } = {}) => {
      const { lastRun, isOldRun, runId = getUuid() } = opts;

      dispatch({
        type: "RUN",
        lastRun: lastRun || new Date().toISOString(),
        runId,
        isOldRun: !!isOldRun,
        lastRunDate: undefined,
      });
    },
    updateQueryDetails: (query: Partial<TStoredQuery>) => {
      dispatch({ type: "UPDATE_QUERY", query });
    },
    loadOldRun: async ({ queryId, runId }: { queryId: number; runId: string | undefined }) => {
      dispatch({ type: "RESET_RUN" });

      let activity;
      if (runId) {
        activity = await apiActivityManager.getAPIActivities(runId);
      }
      if (queryId) {
        const res = await UserSlicerService.getUserSlicer(queryId);
        const query: TStoredQuery = {
          key: getUniqueKey(),
          name: res?.user_slicer?.user_slicer_name ?? "",
          server_id: queryId,
          isQueryOwner:
            res?.user_slicer?.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER ||
            res?.user_slicer?.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.EDITOR,
        };
        dispatch({ type: "LOAD_QUERY", query });

        // Get lastRun for saved query only if no run_id is available in params
        const lastRun = res?.user_slicer?.user_slicer_result;
        if (lastRun?.run_id && lastRun?.run_date && !runId) {
          dispatch({
            type: "RUN",
            runId: lastRun.run_id,
            lastRunDate: lastRun?.run_date,
            isOldRun: true,
            lastRun: undefined,
          });
          return;
        }
      } else if (runId && activity) {
        dispatch({
          type: "LOAD_QUERY",
          query: { key: getUniqueKey(), name: `#${getGuidDisplayText(runId)}` },
        });
      }

      if (runId && activity) {
        dispatch({
          type: "RUN",
          runId,
          lastRunDate: activity?.[0]?.api_start_time ?? undefined,
          api_end_time: activity?.[activity?.length - 1]?.api_end_time ?? undefined,
          isOldRun: true,
          lastRun: new Date().toISOString(),
        });
      }
    },
    resetSlicer: () => {
      dispatch({ type: "LOAD_QUERY", query: { name: "Untitled Query", key: getUniqueKey() } });
      dispatch({ type: "RESET_RUN" });
    },
    loadQuery: (query: TStoredQuery) => {
      dispatch({ type: "LOAD_QUERY", query });
      dispatch({ type: "RESET_RUN" });
    },
    resetRun: () => {
      dispatch({ type: "RESET_RUN" });
    },
    setIsTimerRunning: (isTimerRunning: boolean) => {
      dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning });
      if (isTimerRunning) {
        dispatch({ type: "TOGGLE_IS_PENDING", isPending: true });
        setTimeout(() => {
          dispatch({ type: "TOGGLE_IS_PENDING", isPending: false });
        }, 3000);
      } else {
        dispatch({ type: "TOGGLE_IS_PENDING", isPending: false });
      }
    },
    toggleSlicerSettingsDrawer: () => {
      if (state.slicerSettingsDrawerOpen) {
        dispatch({ type: "CLOSE_SLICER_SETTINGS_DRAWER" });
      } else {
        dispatch({ type: "OPEN_SLICER_SETTINGS_DRAWER" });
      }
    },
    updateSlicerUserSettings: (userSettings: SlicerUserSettingsType) => {
      dispatch({
        type: "UPDATE_SLICER_USER_SETTINGS",
        userSettings: { ...userSettings },
      });
    },
    resetSlicerUserSettings: () => {
      dispatch({ type: "UPDATE_SLICER_USER_SETTINGS", userSettings: getDefaultState().userSettings });
    },
    setGroupMode: (mode: TGroupMode) => {
      dispatch({ type: "SET_GROUP_MODE", mode });
    },
  };
};
