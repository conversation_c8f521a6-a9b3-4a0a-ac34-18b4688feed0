import * as React from "react";
import { useRouter } from "next/router";
import S from "@/constants/strings";
import { getUniqueKey, queryStringToString } from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_Application, CA_Mastr_Models_v1_0_Models_Page } from "@/utils/openapi";
import { SlicerModuleContextReducer } from "./SlicerModuleContextReducer";
import { getSlicerModuleContextAction, initialSlicerModuleContextAction } from "./SlicerModuleContextAction";
import { SlicerModuleContextState, SlicerModuleContextType } from "./SlicerModuleContextTypes";

export const getDefaultState = (): SlicerModuleContextState => {
  return {
    title: S.MODULES.SLICER.TITLE,
    slicerSettingsDrawerOpen: false,
    isTimerRunning: false,
    isPending: false,
    lastRun: undefined,
    loan_level_slicer_config: undefined,
    userSettings: {
      useCache: false,
      timeoutMinutes: 30,
    },
    app: CA_Mastr_Models_v1_0_Models_Application.SLICER,
    page: CA_Mastr_Models_v1_0_Models_Page.SLICER,
    noCache: false,
    groupMode: undefined,
    query: {
      key: getUniqueKey(),
      name: "Untitled Query",
    },
  };
};

const SlicerModuleContext = React.createContext<SlicerModuleContextType | undefined>(undefined);

const SlicerModuleContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const router = useRouter();
  const [state, dispatch] = React.useReducer(SlicerModuleContextReducer, getDefaultState());

  React.useEffect(() => {
    initialSlicerModuleContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: SlicerModuleContextType = React.useMemo(
    () => ({ state, action: getSlicerModuleContextAction(state, dispatch) }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [state, dispatch]
  );

  React.useEffect(() => {
    const id = queryStringToString(router.query.id);
    const runId = queryStringToString(router.query.run_id);

    if (id || runId) {
      value.action.loadOldRun({ queryId: Number(id), runId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.query.id, router.query.run_id]);

  if (!state.loan_level_slicer_config) {
    return null;
  }

  return <SlicerModuleContext.Provider value={value}>{children}</SlicerModuleContext.Provider>;
};

const useSlicerModule = (): SlicerModuleContextType => {
  const context = React.useContext(SlicerModuleContext);
  if (context === undefined) {
    throw new Error("useSlicerModule must be used within a SlicerModuleContextProvider");
  }
  return context;
};

export { SlicerModuleContextProvider, useSlicerModule };
