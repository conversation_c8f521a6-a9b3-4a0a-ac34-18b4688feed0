import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { SlicerModuleContextReducerAction, SlicerModuleContextState } from "./SlicerModuleContextTypes";

export const SlicerModuleContextReducer = (
  state: SlicerModuleContextState,
  action: SlicerModuleContextReducerAction
): SlicerModuleContextState => {
  switch (action.type) {
    case "SET_IS_TIMER_RUNNING": {
      return {
        ...state,
        isTimerRunning: action.isTimerRunning,
      };
    }
    case "TOGGLE_IS_PENDING": {
      return {
        ...state,
        isPending: action.isPending,
      };
    }
    case "SET_LOAN_LEVEL_SLICER_CONFIG": {
      return {
        ...state,
        loan_level_slicer_config: action.loan_level_slicer_config,
      };
    }
    case "OPEN_SLICER_SETTINGS_DRAWER": {
      return { ...state, slicerSettingsDrawerOpen: true };
    }
    case "CLOSE_SLICER_SETTINGS_DRAWER": {
      return { ...state, slicerSettingsDrawerOpen: false };
    }
    case "UPDATE_SLICER_USER_SETTINGS": {
      const userSettings = { ...state.userSettings, ...action.userSettings };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.SLICER_USER_SETTINGS, JSON.stringify(userSettings));
      safeLocalStorage.setItem(
        LOCAL_STORAGE_KEY.SLICER_USER_SETTINGS,
        JSON.stringify({
          timeoutMinutes: userSettings.timeoutMinutes,
          useCache: userSettings.useCache,
        })
      );
      return { ...state, userSettings };
    }
    case "SET_GROUP_MODE": {
      return { ...state, groupMode: action.mode };
    }
    case "LOAD_QUERY": {
      return { ...state, query: action.query };
    }
    case "UPDATE_QUERY": {
      return { ...state, query: { ...state.query, ...action.query } };
    }
    case "RUN": {
      return { ...state, ...action };
    }
    case "RESET_RUN": {
      return { ...state, isOldRun: undefined, lastRun: undefined, lastRunDate: undefined, runId: undefined };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
