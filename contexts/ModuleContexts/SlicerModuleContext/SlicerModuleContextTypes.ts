import { TGroupMode, TStoredQuery } from "@/types/slicer";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_LoanLevelSlicerConfigDef,
  CA_Mastr_Models_v1_0_Models_Application,
  CA_Mastr_Models_v1_0_Models_Page,
} from "@/utils/openapi";
import { getSlicerModuleContextAction } from "./SlicerModuleContextAction";

export type SlicerModuleContextType = {
  state: SlicerModuleContextState;
  action: SlicerModuleContextAction;
};

export type SlicerUserSettingsType = {
  useCache?: boolean;
  timeoutMinutes?: number;
};

export type SlicerModuleContextState = {
  loan_level_slicer_config?: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_LoanLevelSlicerConfigDef;
  title: string;
  app: CA_Mastr_Models_v1_0_Models_Application;
  page: CA_Mastr_Models_v1_0_Models_Page;
  slicerSettingsDrawerOpen: boolean;
  userSettings: SlicerUserSettingsType;
  isTimerRunning: boolean;
  isPending: boolean;
  lastRun?: string;
  isOldRun?: boolean;
  runId?: string;
  lastRunDate?: string;
  api_end_time?: string;
  noCache: boolean;
  groupMode: TGroupMode | undefined;
  query: TStoredQuery;
};

export type SlicerModuleContextReducerAction =
  | { type: "SET_IS_TIMER_RUNNING"; isTimerRunning: boolean }
  | { type: "TOGGLE_IS_PENDING"; isPending: boolean }
  | {
      type: "SET_LOAN_LEVEL_SLICER_CONFIG";
      loan_level_slicer_config?: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_LoanLevelSlicerConfigDef;
    }
  | { type: "OPEN_SLICER_SETTINGS_DRAWER" }
  | { type: "CLOSE_SLICER_SETTINGS_DRAWER" }
  | { type: "UPDATE_SLICER_USER_SETTINGS"; userSettings: SlicerUserSettingsType }
  | {
      type: "SET_GROUP_MODE";
      mode: TGroupMode;
    }
  | {
      type: "LOAD_QUERY";
      query: TStoredQuery;
    }
  | {
      type: "UPDATE_QUERY";
      query: Partial<TStoredQuery>;
    }
  | {
      type: "RUN";
      runId: string;
      isOldRun: boolean;
      lastRun?: string;
      lastRunDate?: string;
      api_end_time?: string;
    }
  | {
      type: "RESET_RUN";
    };

export type SlicerModuleContextAction = ReturnType<typeof getSlicerModuleContextAction>;
