import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { PricerModuleContextReducerAction, PricerModuleContextState } from "./PricerModuleContextTypes";

export const PricerModuleContextReducer = (
  state: PricerModuleContextState,
  action: PricerModuleContextReducerAction
): PricerModuleContextState => {
  switch (action.type) {
    case "UPDATE_BOND": {
      return {
        ...state,
        bond_name: action.bond_name,
        security_info: action.security_info,
        latestFactorDate: action.latestFactorDate,
        isLoadingBond: false,
      };
    }
    case "OPEN_PRICER_SETTINGS_DRAWER": {
      return { ...state, pricerSettingsDrawerOpen: true };
    }
    case "CLOSE_PRICER_SETTINGS_DRAWER": {
      return { ...state, pricerSettingsDrawerOpen: false, activeSettingsDrawerKey: "" };
    }
    case "UPDATE_PRICER_USER_SETTINGS": {
      const userSettings = { ...state.userSettings, ...action.userSettings };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));
      safeLocalStorage.setItem(
        LOCAL_STORAGE_KEY.PRICER_USER_SETTINGS,
        JSON.stringify({
          timeoutMinutes: userSettings.timeoutMinutes,
          useCache: userSettings.useCache,
        })
      );
      return { ...state, userSettings };
    }
    case "UPDATE_USER_MODEL_DIAL_ID": {
      const userSettings = { ...state.userSettings, user_model_dial_id: action.user_model_dial_id };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));
      return { ...state, userSettings };
    }
    case "UPDATE_AD_HOC_DEAL_DIAL": {
      const userSettings = { ...state.userSettings, ad_hoc_deal_dial: action.dealDial };

      // Persist ad hoc dial in user preference
      safeLocalStorage.setItem(LOCAL_STORAGE_KEY.AD_HOC_DIAL, JSON.stringify(action.dealDial));

      return { ...state, userSettings };
    }
    case "IS_AD_HOC_SELECTED": {
      const userSettings = { ...state.userSettings, ad_hoc_dial_selected: action.isSelected };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));

      return { ...state, userSettings };
    }
    case "IS_AD_HOC_SELECTED_COPY": {
      const userSettingsCopy = { ...state.userSettingsCopy, ad_hoc_dial_selected: action.isSelected };

      return { ...state, userSettingsCopy };
    }
    case "UPDATE_USER_VECTOR_ID": {
      const userSettings = { ...state.userSettings, user_vector_id: action.user_vector_id };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));
      return { ...state, userSettings };
    }
    case "UPDATE_AD_HOC_VECTOR": {
      const userSettings = { ...state.userSettings, ad_hoc_vector: action.vector_data };
      // Persist ad hoc vector in user preference
      safeLocalStorage.setItem(LOCAL_STORAGE_KEY.AD_HOC_VECTOR, JSON.stringify(action.vector_data));
      return { ...state, userSettings };
    }
    case "IS_AD_HOC_VECTOR_SELECTED": {
      const userSettings = { ...state.userSettings, ad_hoc_vector_selected: action.isSelected };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));
      return { ...state, userSettings };
    }
    case "UPDATE_PRICER_USER_SETTINGS_COPY": {
      const userSettingsCopy = { ...state.userSettingsCopy, ...action.userSettingsCopy };
      return { ...state, userSettingsCopy };
    }
    case "SET_ACTIVE_SETTINGS_DRAWER_KEY": {
      return { ...state, activeSettingsDrawerKey: action.activeSettingsDrawerKey };
    }
    case "SET_IS_TIMER_RUNNING": {
      return {
        ...state,
        isTimerRunning: action.isTimerRunning,
        hasTimerRun: state.hasTimerRun || action.isTimerRunning,
      };
    }
    case "TOGGLE_USE_REPLINE_DIALS_OVERRIDE": {
      const userSettings = { ...state.userSettings, use_repline_dials_override: action.use_repline_dials_override };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));
      return { ...state, userSettings };
    }
    case "SET_IS_LOADING_BOND": {
      return { ...state, isLoadingBond: action.isLoadingBond };
    }
    case "SET_IS_BOND_NOT_APPLICABLE": {
      return { ...state, bondNotApplicableMessage: action.message };
    }
    case "SET_CURVE_DATE": {
      const userSettings = { ...state.userSettings, curve_date: action.curve_date };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS, JSON.stringify(userSettings));
      return { ...state, userSettings };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
