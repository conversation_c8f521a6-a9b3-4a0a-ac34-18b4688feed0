import * as React from "react";
import { useRouter } from "next/router";
import S from "@/constants/strings";
import { queryStringToString } from "@/utils/helpers";
import { getDateTypeFromString, getDefaultIntegerValue, getDefaultStringValue } from "@/utils/helpers";
import { metadataHelper } from "@/utils/metadata-helper";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { BOND_NOT_APPLICABLE_EVENT } from "@/utils/swr-hooks/constants";
import {
  CA_Mastr_Api_v1_0_Models_Util_StringOption,
  CA_Mastr_Models_v1_0_Models_MarketDataSource,
} from "@/utils/openapi";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { PricerModuleContextReducer } from "./PricerModuleContextReducer";
import { getPricerModuleContextAction, initialPricerModuleContextAction } from "./PricerModuleContextAction";
import { PricerModuleContextState, PricerModuleContextType } from "./PricerModuleContextTypes";

export const getDefaultState = (
  marketDataSources: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null | undefined
): PricerModuleContextState => {
  const metadata = metadataHelper.getMetadata();

  return {
    title: S.MODULES.PRICER.TITLE,
    pricerSettingsDrawerOpen: false,
    activeSettingsDrawerKey: "",
    isTimerRunning: false,
    hasTimerRun: false,
    bondNotApplicableMessage: undefined,
    userSettings: {
      curve_date: getDateTypeFromString(metadata?.pricer_settings?.curve_date?.value),
      pricing_date: getDateTypeFromString(metadata?.pricer_settings?.curve_date?.value),
      cdu_date_conventional: getDateTypeFromString(
        metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_conventional ?? undefined
      ),
      cdu_date_gnm: getDateTypeFromString(metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_gnm ?? undefined),
      cdu_date_gpl: getDateTypeFromString(metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_gpl ?? undefined),
      repline_level: getDefaultStringValue(metadata?.pricer_settings?.repline_level),
      ignore_cdu_paydate_after: getDateTypeFromString(metadata?.pricer_settings?.ignore_paydate_after?.value),
      key_rate_points: getDefaultIntegerValue(metadata?.pricer_settings?.key_rate_points),
      key_curve_shift: metadata?.pricer_settings?.key_rate_bp_shift?.value,
      vcpuPerOas: getDefaultIntegerValue(metadata?.pricer_settings?.vcpu_per_oas),
      model_version: getDefaultStringValue(metadata?.pricer_settings?.version),
      current_coupon_model: getDefaultStringValue(metadata?.pricer_settings?.current_coupon_model),
      primary_secondary_spread: getDefaultStringValue(metadata?.pricer_settings?.primary_secondary_spread),
      interestRatePaths: metadata?.pricer_settings?.interest_rate_paths?.value,
      type: getDefaultStringValue(metadata?.pricer_settings?.type),
      calibration: getDefaultStringValue(metadata?.pricer_settings?.calibration),
      yield_curve_model: getDefaultStringValue(metadata?.pricer_settings?.yield_curve_model),
      user_model_dial_id: undefined,
      user_vector_id: undefined,
      use_repline_dials_override: undefined,
      useCache: false,
      timeoutMinutes: 30,
      ad_hoc_dial_selected: false,
      ad_hoc_vector_selected: false,
      market_data_source: getDefaultStringValue(marketDataSources) as CA_Mastr_Models_v1_0_Models_MarketDataSource,
      input_data_override: {
        current_coupon_calibration_date_override: null,
        curve_date_override: null,
        interest_rate_calibration_date_override: null,
        survey_rate_date_override: null,
      },
    },
    userSettingsCopy: {
      curve_date: undefined,
      cdu_date_conventional: undefined,
      cdu_date_gnm: undefined,
      cdu_date_gpl: undefined,
      repline_level: undefined,
      model_version: undefined,
      current_coupon_model: undefined,
      calibration: undefined,
      user_model_dial_id: undefined,
      user_vector_id: undefined,
      ad_hoc_dial_selected: undefined,
      ad_hoc_vector_selected: undefined,
      market_data_source: undefined,
    },
  };
};

const PricerModuleContext = React.createContext<PricerModuleContextType | undefined>(undefined);

const PricerModuleContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { query } = useRouter();
  const { state: authState } = useAuthentication();

  const [state, dispatch] = React.useReducer(
    PricerModuleContextReducer,
    getDefaultState(authState?.userData?.user?.market_data_sources)
  );
  const {
    state: { metadata },
  } = useGlobalContext();

  React.useEffect(() => {
    initialPricerModuleContextAction(state, dispatch, metadata);

    const onBondNotApplicable = (event: Event) => {
      const message = (event as CustomEvent).detail.message;
      window.hasUnsavedChanges = false;
      dispatch({ type: "SET_IS_BOND_NOT_APPLICABLE", message });
    };
    document.addEventListener(BOND_NOT_APPLICABLE_EVENT, onBondNotApplicable);

    return () => document.removeEventListener(BOND_NOT_APPLICABLE_EVENT, onBondNotApplicable);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: PricerModuleContextType = React.useMemo(
    () => ({ state, action: getPricerModuleContextAction(state, dispatch) }),
    [state, dispatch]
  );
  React.useEffect(() => {
    // UI-4012: Since we aren't loading a bond in the empirical tracking page, we don't want to call the loadBond action which resets all session settings
    const bond_name = queryStringToString(query.bond_name);
    value.action.loadBond(bond_name);
  }, [query.bond_name]); // eslint-disable-line react-hooks/exhaustive-deps

  return <PricerModuleContext.Provider value={value}>{children}</PricerModuleContext.Provider>;
};

const usePricerModule = (): PricerModuleContextType => {
  const context = React.useContext(PricerModuleContext);
  if (context === undefined) {
    throw new Error("usePricerModule must be used within a PricerModuleContextProvider");
  }
  return context;
};

export { PricerModuleContextProvider, usePricerModule };
