import Router from "next/router";
import {
  CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  CA_Mastr_Models_v1_0_Models_AdHocVector,
  CA_Mastr_Models_v1_0_Models_MarketDataSource,
  CA_Mastr_Models_v1_0_Models_ModelDialData,
  CA_Mastr_Models_v1_0_Models_Stage,
  UtilService,
} from "@/utils/openapi";
import { inMemoryStorage, safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { IN_MEMORY_STORAGE_KEY, LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import {
  getAPIErrorMessage,
  getDateTypeFromString,
  getDefaultStringValue,
  getLastDayInMonth,
  parseDateToYYYYMMDD,
  removeUndefinedElementsFromObject,
} from "@/utils/helpers";
import { showWarningToast } from "@/design-system/theme/toast";
import { runToastManager } from "@/utils/run-toast-manager";
import { getUserClaim } from "../MarketDataModuleContext/MarketDataModuleContextAction";
import {
  PricerModuleContextReducerAction,
  PricerModuleContextState,
  PricerUserSettingsCopyType,
  PricerUserSettingsType,
} from "./PricerModuleContextTypes";
import { getDefaultState } from ".";

async function getCelloCduDatesFromCurveDate(curveDate: Date) {
  try {
    const data = await UtilService.getCelloCduDates(parseDateToYYYYMMDD(curveDate));

    return data.cello_cdu_dates ?? undefined;
  } catch (e) {
    return undefined;
  }
}

export function getIgnoreCduPayDateAfterFromCurveDate(curveDate: Date, latestCurveDate?: Date) {
  if (!latestCurveDate || !curveDate) return undefined;
  const lastDayOfMonthOfCurveDate = getLastDayInMonth(curveDate);
  const ignore_cdu_paydate_after =
    lastDayOfMonthOfCurveDate > latestCurveDate ? latestCurveDate : lastDayOfMonthOfCurveDate;
  return ignore_cdu_paydate_after;
}

function clearInputsFromSessionStorage() {
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.DASHBOARD_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.MATRIX_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.CASHFLOW_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.SCENARIO_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.REPLINE_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.PROJECTION_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.PROJECTION_DETAIL_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.TRACKING_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.POOL_DISTRIBUTION_INPUTS);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.POOL_LIST_INPUTS);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.LOAN_DISTRIBUTION_INPUTS);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.LOAN_LIST_INPUTS);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.PREPAY_TRACKING_INPUTS);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.BOND_COMPARE_LAST_RUN_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID);
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID);
}

export const initialPricerModuleContextAction = async (
  _state: PricerModuleContextState,
  dispatch: React.Dispatch<PricerModuleContextReducerAction>,
  metadata?: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse
): Promise<void> => {
  const userSettingsInSessionStorage = safeSessionStorage.getItem(SESSION_STORAGE_KEY.PRICER_USER_SETTINGS);
  const userSettingsInLocalStorage = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.PRICER_USER_SETTINGS);
  const userSettingsParsed = userSettingsInSessionStorage && JSON.parse(userSettingsInSessionStorage);

  // LOAD AD HOC FROM LOCAL STORAGE
  const adHocJSON = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.AD_HOC_DIAL);
  if (adHocJSON && adHocJSON !== "undefined") {
    dispatch({ type: "UPDATE_AD_HOC_DEAL_DIAL", dealDial: JSON.parse(adHocJSON) });
  }

  // LOAD AD HOC VECTOR FROM LOCAL STORAGE
  const adHocVectorJSON = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.AD_HOC_VECTOR);
  if (adHocVectorJSON && adHocVectorJSON !== "undefined") {
    dispatch({ type: "UPDATE_AD_HOC_VECTOR", vector_data: JSON.parse(adHocVectorJSON) });
  }

  const user = await getUserClaim();

  if (userSettingsInSessionStorage && userSettingsInSessionStorage !== "undefined") {
    try {
      userSettingsParsed["cdu_date_conventional"] = getDateTypeFromString(userSettingsParsed["cdu_date_conventional"]);
      userSettingsParsed["cdu_date_gnm"] = getDateTypeFromString(userSettingsParsed["cdu_date_gnm"]);
      userSettingsParsed["cdu_date_gpl"] = getDateTypeFromString(userSettingsParsed["cdu_date_gpl"]);
      userSettingsParsed["curve_date"] = getDateTypeFromString(userSettingsParsed["curve_date"]);
      userSettingsParsed["pricing_date"] = getDateTypeFromString(userSettingsParsed["pricing_date"]);
      userSettingsParsed["cello_cdu_date"] = getDateTypeFromString(userSettingsParsed["cello_cdu_date"]);
      userSettingsParsed["ignore_cdu_paydate_after"] = userSettingsParsed["ignore_cdu_paydate_after"]
        ? getDateTypeFromString(userSettingsParsed["ignore_cdu_paydate_after"])
        : getIgnoreCduPayDateAfterFromCurveDate(
            userSettingsParsed["curve_date"],
            metadata?.pricer_settings?.curve_date
              ? getDateTypeFromString(metadata?.pricer_settings?.curve_date?.value)
              : undefined
          );
      userSettingsParsed["market_data_source"] = userSettingsParsed["market_data_source"]
        ? userSettingsParsed["market_data_source"]
        : (getDefaultStringValue(user?.market_data_sources) as CA_Mastr_Models_v1_0_Models_MarketDataSource);

      if (userSettingsParsed["input_data_override"]?.current_coupon_calibration_date_override) {
        userSettingsParsed["input_data_override"].current_coupon_calibration_date_override = getDateTypeFromString(
          userSettingsParsed["input_data_override"].current_coupon_calibration_date_override
        );
      }
      if (userSettingsParsed["input_data_override"]?.curve_date_override) {
        userSettingsParsed["input_data_override"].curve_date_override = getDateTypeFromString(
          userSettingsParsed["input_data_override"].curve_date_override
        );
      }
      if (userSettingsParsed["input_data_override"]?.interest_rate_calibration_date_override) {
        userSettingsParsed["input_data_override"].interest_rate_calibration_date_override = getDateTypeFromString(
          userSettingsParsed["input_data_override"].interest_rate_calibration_date_override
        );
      }
      if (userSettingsParsed["input_data_override"]?.survey_rate_date_override) {
        userSettingsParsed["input_data_override"].survey_rate_date_override = getDateTypeFromString(
          userSettingsParsed["input_data_override"].survey_rate_date_override
        );
      }

      delete userSettingsParsed?.["ad_hoc_deal_dial"];
      delete userSettingsParsed?.["ad_hoc_dial_selected"];
      delete userSettingsParsed?.["ad_hoc_vector_selected"];
      delete userSettingsParsed?.["ad_hoc_vector_selected"];

      dispatch({ type: "UPDATE_PRICER_USER_SETTINGS", userSettings: userSettingsParsed });
      dispatch({
        type: "UPDATE_PRICER_USER_SETTINGS_COPY",
        userSettingsCopy: userSettingsParsed,
      });
    } finally {
      // Skip if JSON failed to parse
    }
  } else if (userSettingsInLocalStorage && userSettingsInLocalStorage !== "undefined") {
    try {
      const userSettingsParsed = JSON.parse(userSettingsInLocalStorage);
      dispatch({ type: "UPDATE_PRICER_USER_SETTINGS", userSettings: userSettingsParsed });
    } finally {
      // Skip if JSON failed to parse
    }
  }
};

export const getPricerModuleContextAction = (
  state: PricerModuleContextState,
  dispatch: React.Dispatch<PricerModuleContextReducerAction>
) => {
  return {
    redirectToBond: async (bond_name: string | undefined) => {
      if (!bond_name)
        return Router.push({
          pathname: Router.pathname,
        });

      if (window?.hasUnsavedChanges) {
        const ok = window.confirm(`The page has some unsaved changes. Do you want continue without saving?`);
        if (!ok) return;
      }
      window.hasUnsavedChanges = false;

      state.bond_name && inMemoryStorage.removeItem(IN_MEMORY_STORAGE_KEY.API_WARNING_MESSAGE);

      if (state.bond_name === bond_name) {
        return;
      }

      Router.push({
        pathname: Router.pathname,
        query: {
          bond_name,
        },
      });

      if (state.bondNotApplicableMessage) {
        dispatch({ type: "SET_IS_BOND_NOT_APPLICABLE", message: undefined });
      }
      clearInputsFromSessionStorage();
    },
    loadBond: async (bond_name: string | undefined) => {
      runToastManager.removeToasts();

      if (bond_name) {
        try {
          const lastLoadedBond = safeSessionStorage.getItem(SESSION_STORAGE_KEY.LAST_LOADED_BOND);
          if (lastLoadedBond) {
            if (lastLoadedBond !== bond_name) {
              safeSessionStorage.setItem(SESSION_STORAGE_KEY.LAST_LOADED_BOND, bond_name);
              // UI-1445 - Clear deal dial on bond change
              dispatch({ type: "UPDATE_USER_MODEL_DIAL_ID", user_model_dial_id: undefined });
              dispatch({
                type: "UPDATE_PRICER_USER_SETTINGS_COPY",
                userSettingsCopy: { user_model_dial_id: undefined },
              });
              clearInputsFromSessionStorage();
            }
          } else {
            safeSessionStorage.setItem(SESSION_STORAGE_KEY.LAST_LOADED_BOND, bond_name);
          }
          dispatch({ type: "SET_IS_LOADING_BOND", isLoadingBond: true });

          const security_info = await UtilService.getSecurityInfo(bond_name);

          if (
            security_info?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.COMPLETED &&
            security_info?.security_info
          ) {
            const isLoanPage = !!Router.pathname.match(/\/loan-[^/]+$/); // loan-list, loan-distribution
            const data = isLoanPage
              ? await UtilService.getLatestFactorDateLoanLevel(bond_name)
              : await UtilService.getLatestFactorDate(bond_name);

            const latestFactorDate = (data?.latest_factor_date && new Date(data.latest_factor_date)) || undefined;
            dispatch({ type: "SET_IS_BOND_NOT_APPLICABLE", message: undefined });
            dispatch({
              type: "UPDATE_BOND",
              bond_name,
              security_info: security_info?.security_info,
              latestFactorDate,
            });
          } else {
            showWarningToast("Error", `Failed to load security - ${bond_name}`);
            dispatch({ type: "SET_IS_LOADING_BOND", isLoadingBond: false });
          }
        } catch (e) {
          showWarningToast("Error", getAPIErrorMessage(e));

          Router.push({
            pathname: Router.pathname,
          });
        }
      } else {
        safeSessionStorage.removeItem(SESSION_STORAGE_KEY.LAST_LOADED_BOND);
        clearInputsFromSessionStorage();
        if (state.bondNotApplicableMessage) {
          dispatch({ type: "SET_IS_BOND_NOT_APPLICABLE", message: undefined });
        }
        dispatch({
          type: "UPDATE_BOND",
          bond_name: undefined,
          security_info: undefined,
        });
      }
    },
    togglePricerSettingsDrawer: () => {
      if (state.pricerSettingsDrawerOpen) {
        dispatch({ type: "CLOSE_PRICER_SETTINGS_DRAWER" });
      } else {
        dispatch({ type: "OPEN_PRICER_SETTINGS_DRAWER" });
      }
    },
    updatePricerUserSettings: (userSettings: PricerUserSettingsType, updateOriginalAndCopy?: boolean) => {
      // Remove undefined items from user settings
      const _userSettings = {
        ...state.userSettings,
        ...removeUndefinedElementsFromObject(userSettings),
      };

      if (updateOriginalAndCopy) {
        dispatch({ type: "UPDATE_PRICER_USER_SETTINGS", userSettings: _userSettings });
        dispatch({ type: "UPDATE_PRICER_USER_SETTINGS_COPY", userSettingsCopy: _userSettings });
        return;
      }
      dispatch({ type: "UPDATE_PRICER_USER_SETTINGS", userSettings: _userSettings });
    },
    updatePricerUserSettingsCopy: (
      userSettingsCopy?: PricerUserSettingsCopyType,
      updateAdHocDial?: boolean,
      updateAdHocVector?: boolean
    ) => {
      dispatch({ type: "UPDATE_PRICER_USER_SETTINGS_COPY", userSettingsCopy });

      if (updateAdHocDial) {
        dispatch({
          type: "IS_AD_HOC_SELECTED",
          isSelected: userSettingsCopy?.ad_hoc_dial_selected ?? false,
        });
        dispatch({
          type: "UPDATE_USER_MODEL_DIAL_ID",
          user_model_dial_id: userSettingsCopy?.user_model_dial_id,
        });
      }

      if (updateAdHocVector) {
        dispatch({
          type: "IS_AD_HOC_VECTOR_SELECTED",
          isSelected: userSettingsCopy?.ad_hoc_vector_selected ?? false,
        });
        dispatch({
          type: "UPDATE_USER_VECTOR_ID",
          user_vector_id: userSettingsCopy?.user_vector_id,
        });
      }
    },
    toggleAdHocSelected: (isSelected: boolean) => {
      dispatch({ type: "IS_AD_HOC_SELECTED", isSelected });
    },
    resetPricerUserSettings: async () => {
      const user = await getUserClaim();
      const userSettings = getDefaultState(user?.market_data_sources).userSettings;
      // These fields are not in settings drawer, therefore should not be reset when reset is clicked
      delete userSettings.user_model_dial_id;
      delete userSettings.user_vector_id;
      delete userSettings.use_repline_dials_override;

      dispatch({ type: "UPDATE_PRICER_USER_SETTINGS", userSettings });
    },
    setActiveSettingsDrawerKey: (activeSettingsDrawerKey: string) => {
      dispatch({ type: "SET_ACTIVE_SETTINGS_DRAWER_KEY", activeSettingsDrawerKey });
    },
    setIsTimerRunning: (isTimerRunning: boolean) => {
      if (state.isTimerRunning !== isTimerRunning) {
        dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning });
      }
    },
    stopTimer: () => {
      dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning: false });
    },
    updateUserModelDialId: (user_model_dial_id?: number) => {
      dispatch({ type: "UPDATE_USER_MODEL_DIAL_ID", user_model_dial_id });
    },
    updateAdHocDealDial: (dealDial?: Array<CA_Mastr_Models_v1_0_Models_ModelDialData>) => {
      dispatch({ type: "UPDATE_AD_HOC_DEAL_DIAL", dealDial });
    },
    updateUserVectorId: (user_vector_id?: number) => {
      dispatch({ type: "UPDATE_USER_VECTOR_ID", user_vector_id });
    },
    updateAdHocVector: (vector_data?: CA_Mastr_Models_v1_0_Models_AdHocVector) => {
      dispatch({ type: "UPDATE_AD_HOC_VECTOR", vector_data });
    },
    toggleAdHocVectorSelected: (isSelected: boolean) => {
      dispatch({ type: "IS_AD_HOC_VECTOR_SELECTED", isSelected });
    },
    toggleUseReplineDials: (use_repline_dials_override?: boolean) => {
      dispatch({ type: "TOGGLE_USE_REPLINE_DIALS_OVERRIDE", use_repline_dials_override });
    },
    setCurveDate: (curve_date: Date) => {
      dispatch({ type: "SET_CURVE_DATE", curve_date });
    },
    getCelloCduDatesFromCurveDate,
    getIgnoreCduPayDateAfterFromCurveDate,
    clearInputsFromSessionStorage,
  };
};
