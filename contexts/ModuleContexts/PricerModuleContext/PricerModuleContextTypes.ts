import {
  CA_Mastr_Api_v1_0_Models_SecurityInfo,
  CA_Mastr_Models_v1_0_Models_AdHocVector,
  CA_Mastr_Models_v1_0_Models_MarketDataSource,
  CA_Mastr_Models_v1_0_Models_ModelDialData,
} from "@/utils/openapi";
import { getPricerModuleContextAction } from "./PricerModuleContextAction";

export type PricerModuleContextType = {
  state: PricerModuleContextState;
  action: PricerModuleContextAction;
};

export type PricerModuleContextDispatch = (action: PricerModuleContextReducerAction) => void;

export type PricerUserSettingsType = {
  curve_date?: Date;
  pricing_date?: Date;
  cdu_date_conventional?: Date;
  cdu_date_gnm?: Date;
  cdu_date_gpl?: Date;
  repline_level?: string | null | undefined;
  ignore_cdu_paydate_after?: Date;
  key_rate_points?: number;
  key_curve_shift?: number;
  vcpuPerOas?: number;
  model_version?: string;
  current_coupon_model?: string;
  primary_secondary_spread?: string;
  interestRatePaths?: number;
  type?: string;
  calibration?: string;
  yield_curve_model?: string;
  user_model_dial_id?: number;
  ad_hoc_dial_selected?: boolean;
  ad_hoc_deal_dial?: Array<CA_Mastr_Models_v1_0_Models_ModelDialData>;
  ad_hoc_vector_selected?: boolean;
  ad_hoc_vector?: CA_Mastr_Models_v1_0_Models_AdHocVector;
  user_vector_id?: number;
  use_repline_dials_override?: boolean;
  useCache?: boolean;
  timeoutMinutes?: number;
  market_data_source?: CA_Mastr_Models_v1_0_Models_MarketDataSource;
  input_data_override?: {
    curve_date_override?: Date | null;
    interest_rate_calibration_date_override?: Date | null;
    current_coupon_calibration_date_override?: Date | null;
    survey_rate_date_override?: Date | null;
  };
};

export type PricerUserSettingsCopyType = Partial<PricerUserSettingsType>;

export type PricerModuleContextState = {
  title: string;
  bond_name?: string;
  bondNotApplicableMessage?: string;
  security_info?: CA_Mastr_Api_v1_0_Models_SecurityInfo;
  latestFactorDate?: Date;
  pricerSettingsDrawerOpen: boolean;
  userSettings: PricerUserSettingsType;
  userSettingsCopy: PricerUserSettingsCopyType;
  activeSettingsDrawerKey: string;
  isTimerRunning: boolean;
  hasTimerRun: boolean;
  isLoadingBond?: boolean;
};

export type PricerModuleContextReducerAction =
  | {
      type: "UPDATE_BOND";
      bond_name?: string;
      security_info?: CA_Mastr_Api_v1_0_Models_SecurityInfo;
      latestFactorDate?: Date;
    }
  | { type: "OPEN_PRICER_SETTINGS_DRAWER" }
  | { type: "CLOSE_PRICER_SETTINGS_DRAWER" }
  | { type: "UPDATE_PRICER_USER_SETTINGS"; userSettings: PricerUserSettingsType }
  | { type: "UPDATE_PRICER_USER_SETTINGS_COPY"; userSettingsCopy: PricerUserSettingsCopyType | undefined }
  | { type: "SET_ACTIVE_SETTINGS_DRAWER_KEY"; activeSettingsDrawerKey: string }
  | { type: "SET_IS_TIMER_RUNNING"; isTimerRunning: boolean }
  | { type: "UPDATE_USER_MODEL_DIAL_ID"; user_model_dial_id?: number }
  | { type: "UPDATE_AD_HOC_DEAL_DIAL"; dealDial?: Array<CA_Mastr_Models_v1_0_Models_ModelDialData> }
  | { type: "UPDATE_USER_VECTOR_ID"; user_vector_id?: number }
  | { type: "UPDATE_AD_HOC_VECTOR"; vector_data?: CA_Mastr_Models_v1_0_Models_AdHocVector }
  | { type: "TOGGLE_USE_REPLINE_DIALS_OVERRIDE"; use_repline_dials_override?: boolean }
  | { type: "SET_IS_LOADING_BOND"; isLoadingBond?: boolean }
  | { type: "IS_AD_HOC_SELECTED"; isSelected: boolean }
  | { type: "IS_AD_HOC_SELECTED_COPY"; isSelected: boolean }
  | { type: "IS_AD_HOC_VECTOR_SELECTED"; isSelected: boolean }
  | { type: "SET_IS_BOND_NOT_APPLICABLE"; message?: string }
  | { type: "SET_CURVE_DATE"; curve_date?: Date };

export type PricerModuleContextAction = ReturnType<typeof getPricerModuleContextAction>;

export type updatePricerUserSettingsAction = (userSettings: PricerUserSettingsType, updateCopy: boolean) => void;
