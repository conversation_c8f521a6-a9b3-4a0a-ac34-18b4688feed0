import * as React from "react";
import S from "@/constants/strings";
import {
  EmpiricalTrackingModuleContextState,
  EmpiricalTrackingModuleContextType,
} from "./EmpiricalTrackingModuleContextTypes";
import { EmpiricalTrackingModuleContextReducer } from "./EmpiricalTrackingModuleContextReducer";
import {
  getEmpiricalTrackingModuleContextAction,
  initialEmpiricalTrackingModuleContextAction,
} from "./EmpiricalTrackingModuleContextAction";

export const getDefaultState = (): EmpiricalTrackingModuleContextState => {
  return {
    title: S.MODULES.PRICER.TITLE,
    empiricalTrackingSettingsDrawerOpen: false,
    activeSettingsDrawerKey: "",
    isTimerRunning: false,
    hasTimerRun: false,
    userSettings: {
      useCache: false,
      timeoutMinutes: 30,
    },
    userSettingsCopy: {
      useCache: false,
      timeoutMinutes: 30,
    },
  };
};

const EmpiricalTrackingModuleContext = React.createContext<EmpiricalTrackingModuleContextType | undefined>(undefined);

const EmpiricalTrackingModuleContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(EmpiricalTrackingModuleContextReducer, getDefaultState());

  React.useEffect(() => {
    initialEmpiricalTrackingModuleContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: EmpiricalTrackingModuleContextType = React.useMemo(
    () => ({ state, action: getEmpiricalTrackingModuleContextAction(state, dispatch) }),
    [state, dispatch]
  );

  return <EmpiricalTrackingModuleContext.Provider value={value}>{children}</EmpiricalTrackingModuleContext.Provider>;
};

const useEmpiricalTrackingModule = (): EmpiricalTrackingModuleContextType => {
  const context = React.useContext(EmpiricalTrackingModuleContext);
  if (context === undefined) {
    throw new Error("useEmpiricalTrackingModule must be used within a EmpiricalTrackingModuleContextProvider");
  }
  return context;
};

export { EmpiricalTrackingModuleContextProvider, useEmpiricalTrackingModule };
