import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { removeUndefinedElementsFromObject } from "@/utils/helpers";
import {
  EmpiricalTrackingModuleContextReducerAction,
  EmpiricalTrackingModuleContextState,
  EmpiricalTrackingUserSettingsCopyType,
  EmpiricalTrackingUserSettingsType,
} from "./EmpiricalTrackingModuleContextTypes";

function clearInputsFromSessionStorage() {
  safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID);
  //Since we aren't loading a bond in the empirical tracking page, we don't want to wipe the empirical tracking last run id
  // safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID);
}

export const initialEmpiricalTrackingModuleContextAction = async (
  _state: EmpiricalTrackingModuleContextState,
  dispatch: React.Dispatch<EmpiricalTrackingModuleContextReducerAction>
): Promise<void> => {
  const userSettingsInSessionStorage = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_USER_SETTINGS);
  const userSettingsInLocalStorage = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.EMPIRICAL_TRACKING_USER_SETTINGS);
  const userSettingsParsed = userSettingsInSessionStorage && JSON.parse(userSettingsInSessionStorage);

  if (userSettingsInSessionStorage && userSettingsInSessionStorage !== "undefined") {
    try {
      dispatch({ type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS", userSettings: userSettingsParsed });
      dispatch({
        type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS_COPY",
        userSettingsCopy: userSettingsParsed,
      });
    } finally {
      // Skip if JSON failed to parse
    }
  } else if (userSettingsInLocalStorage && userSettingsInLocalStorage !== "undefined") {
    try {
      const userSettingsParsed = JSON.parse(userSettingsInLocalStorage);
      dispatch({ type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS", userSettings: userSettingsParsed });
    } finally {
      // Skip if JSON failed to parse
    }
  }
};

export const getEmpiricalTrackingModuleContextAction = (
  state: EmpiricalTrackingModuleContextState,
  dispatch: React.Dispatch<EmpiricalTrackingModuleContextReducerAction>
) => {
  return {
    toggleEmpiricalTrackingSettingsDrawer: () => {
      if (state.empiricalTrackingSettingsDrawerOpen) {
        dispatch({ type: "CLOSE_EMPIRICAL_TRACKING_SETTINGS_DRAWER" });
      } else {
        dispatch({ type: "OPEN_EMPIRICAL_TRACKING_SETTINGS_DRAWER" });
      }
    },
    updateEmpiricalTrackingUserSettings: (
      userSettings: EmpiricalTrackingUserSettingsType,
      updateOriginalAndCopy?: boolean
    ) => {
      // Remove undefined items from user settings
      const _userSettings = {
        ...state.userSettings,
        ...removeUndefinedElementsFromObject(userSettings),
      };

      if (updateOriginalAndCopy) {
        dispatch({ type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS", userSettings: _userSettings });
        dispatch({ type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS_COPY", userSettingsCopy: _userSettings });
        return;
      }
      dispatch({ type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS", userSettings: _userSettings });
    },
    updateEmpiricalTrackingUserSettingsCopy: (userSettingsCopy?: EmpiricalTrackingUserSettingsCopyType) => {
      dispatch({ type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS_COPY", userSettingsCopy });
    },
    setActiveSettingsDrawerKey: (activeSettingsDrawerKey: string) => {
      dispatch({ type: "SET_ACTIVE_SETTINGS_DRAWER_KEY", activeSettingsDrawerKey });
    },
    setIsTimerRunning: (isTimerRunning: boolean) => {
      if (state.isTimerRunning !== isTimerRunning) {
        dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning });
      }
    },
    stopTimer: () => {
      dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning: false });
    },
    clearInputsFromSessionStorage,
  };
};
