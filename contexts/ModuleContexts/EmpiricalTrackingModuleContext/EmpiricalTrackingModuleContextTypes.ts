import { getEmpiricalTrackingModuleContextAction } from "./EmpiricalTrackingModuleContextAction";

export type EmpiricalTrackingModuleContextType = {
  state: EmpiricalTrackingModuleContextState;
  action: EmpiricalTrackingModuleContextAction;
};

export type EmpiricalTrackingModuleContextDispatch = (action: EmpiricalTrackingModuleContextReducerAction) => void;

export type EmpiricalTrackingUserSettingsType = {
  useCache?: boolean;
  timeoutMinutes?: number;
};

export type EmpiricalTrackingUserSettingsCopyType = Partial<EmpiricalTrackingUserSettingsType>;

export type EmpiricalTrackingModuleContextState = {
  title: string;
  empiricalTrackingSettingsDrawerOpen: boolean;
  userSettings: EmpiricalTrackingUserSettingsType;
  userSettingsCopy: EmpiricalTrackingUserSettingsCopyType;
  activeSettingsDrawerKey: string;
  isTimerRunning: boolean;
  hasTimerRun: boolean;
};

export type EmpiricalTrackingModuleContextReducerAction =
  | { type: "OPEN_EMPIRICAL_TRACKING_SETTINGS_DRAWER" }
  | { type: "CLOSE_EMPIRICAL_TRACKING_SETTINGS_DRAWER" }
  | { type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS"; userSettings: EmpiricalTrackingUserSettingsType }
  | {
      type: "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS_COPY";
      userSettingsCopy: EmpiricalTrackingUserSettingsCopyType | undefined;
    }
  | { type: "SET_ACTIVE_SETTINGS_DRAWER_KEY"; activeSettingsDrawerKey: string }
  | { type: "SET_IS_TIMER_RUNNING"; isTimerRunning: boolean };

export type EmpiricalTrackingModuleContextAction = ReturnType<typeof getEmpiricalTrackingModuleContextAction>;

export type updateEmpiricalTrackingUserSettingsAction = (
  userSettings: EmpiricalTrackingUserSettingsType,
  updateCopy: boolean
) => void;
