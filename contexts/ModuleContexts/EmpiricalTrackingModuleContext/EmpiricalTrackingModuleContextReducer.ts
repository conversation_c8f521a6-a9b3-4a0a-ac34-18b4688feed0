import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import {
  EmpiricalTrackingModuleContextReducerAction,
  EmpiricalTrackingModuleContextState,
} from "./EmpiricalTrackingModuleContextTypes";

export const EmpiricalTrackingModuleContextReducer = (
  state: EmpiricalTrackingModuleContextState,
  action: EmpiricalTrackingModuleContextReducerAction
): EmpiricalTrackingModuleContextState => {
  switch (action.type) {
    case "OPEN_EMPIRICAL_TRACKING_SETTINGS_DRAWER": {
      return { ...state, empiricalTrackingSettingsDrawerOpen: true };
    }
    case "CLOSE_EMPIRICAL_TRACKING_SETTINGS_DRAWER": {
      return { ...state, empiricalTrackingSettingsDrawerOpen: false, activeSettingsDrawerKey: "" };
    }
    case "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS": {
      const userSettings = { ...state.userSettings, ...action.userSettings };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_USER_SETTINGS, JSON.stringify(userSettings));
      safeLocalStorage.setItem(
        LOCAL_STORAGE_KEY.EMPIRICAL_TRACKING_USER_SETTINGS,
        JSON.stringify({
          timeoutMinutes: userSettings.timeoutMinutes,
          useCache: userSettings.useCache,
        })
      );
      return { ...state, userSettings };
    }
    case "UPDATE_EMPIRICAL_TRACKING_USER_SETTINGS_COPY": {
      const userSettingsCopy = { ...state.userSettingsCopy, ...action.userSettingsCopy };
      return { ...state, userSettingsCopy };
    }
    case "SET_ACTIVE_SETTINGS_DRAWER_KEY": {
      return { ...state, activeSettingsDrawerKey: action.activeSettingsDrawerKey };
    }
    case "SET_IS_TIMER_RUNNING": {
      return {
        ...state,
        isTimerRunning: action.isTimerRunning,
        hasTimerRun: state.hasTimerRun || action.isTimerRunning,
      };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
