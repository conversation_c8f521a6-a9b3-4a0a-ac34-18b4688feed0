import { CA_Mastr_Api_v1_0_Models_SecurityInfo, CA_Mastr_Models_v1_0_Models_MarketDataSource } from "@/utils/openapi";
import { getMarketDataModuleContextAction } from "./MarketDataModuleContextAction";

export type MarketDataModuleContextType = {
  state: MarketDataModuleContextState;
  action: MarketDataModuleContextAction;
};

export type MarketDataModuleContextDispatch = (action: MarketDataModuleContextReducerAction) => void;

export type MarketDataUserSettingsType = {
  curve_date?: Date;
  cdu_date_conventional?: Date;
  cdu_date_gnm?: Date;
  cdu_date_gpl?: Date;
  model_version?: string;
  current_coupon_model?: string;
  type?: string;
  yield_curve_model?: string;
  useCache?: boolean;
  timeoutMinutes?: number;
  market_data_source?: CA_Mastr_Models_v1_0_Models_MarketDataSource;
};

export type MarketDataUserSettingsCopyType = Partial<MarketDataUserSettingsType>;

export type MarketDataModuleContextState = {
  title: string;
  security_info?: CA_Mastr_Api_v1_0_Models_SecurityInfo;
  marketDataSettingsDrawerOpen: boolean;
  userSettings: MarketDataUserSettingsType;
  userSettingsCopy: MarketDataUserSettingsCopyType;
  activeSettingsDrawerKey: string;
  isTimerRunning: boolean;
  hasTimerRun: boolean;
};

export type MarketDataModuleContextReducerAction =
  | {
      type: "UPDATE_BOND";
      bond_name?: string;
      security_info?: CA_Mastr_Api_v1_0_Models_SecurityInfo;
      latestFactorDate?: Date;
    }
  | { type: "OPEN_MARKET_DATA_SETTINGS_DRAWER" }
  | { type: "CLOSE_MARKET_DATA_SETTINGS_DRAWER" }
  | { type: "UPDATE_MARKET_DATA_USER_SETTINGS"; userSettings: MarketDataUserSettingsType }
  | { type: "UPDATE_MARKET_DATA_USER_SETTINGS_COPY"; userSettingsCopy: MarketDataUserSettingsCopyType | undefined }
  | { type: "SET_ACTIVE_SETTINGS_DRAWER_KEY"; activeSettingsDrawerKey: string }
  | { type: "SET_IS_TIMER_RUNNING"; isTimerRunning: boolean };

export type MarketDataModuleContextAction = ReturnType<typeof getMarketDataModuleContextAction>;

export type updateMarketDataUserSettingsAction = (
  userSettings: MarketDataUserSettingsType,
  updateCopy: boolean
) => void;
