import * as React from "react";
import S from "@/constants/strings";
import { getDateTypeFromString, getDefaultStringValue } from "@/utils/helpers";
import { metadataHelper } from "@/utils/metadata-helper";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import {
  CA_Mastr_Api_v1_0_Models_Util_StringOption,
  CA_Mastr_Models_v1_0_Models_MarketDataSource,
} from "@/utils/openapi";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { runToastManager } from "@/utils/run-toast-manager";
import { MarketDataModuleContextReducer } from "./MarketDataModuleContextReducer";
import {
  getMarketDataModuleContextAction,
  initialMarketDataModuleContextAction,
} from "./MarketDataModuleContextAction";
import { MarketDataModuleContextState, MarketDataModuleContextType } from "./MarketDataModuleContextTypes";

export const getDefaultState = (
  marketDataSources: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null | undefined
): MarketDataModuleContextState => {
  const metadata = metadataHelper.getMetadata();

  return {
    title: S.MODULES.MARKET_DATA.TITLE,
    marketDataSettingsDrawerOpen: false,
    activeSettingsDrawerKey: "",
    isTimerRunning: false,
    hasTimerRun: false,
    userSettings: {
      curve_date: getDateTypeFromString(metadata?.market_data_settings?.curve_date?.value),
      cdu_date_conventional: getDateTypeFromString(
        metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_conventional ?? undefined
      ),
      cdu_date_gnm: getDateTypeFromString(metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_gnm ?? undefined),
      cdu_date_gpl: getDateTypeFromString(metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_gpl ?? undefined),
      model_version: getDefaultStringValue(metadata?.pricer_settings?.version),
      current_coupon_model: getDefaultStringValue(metadata?.pricer_settings?.current_coupon_model),
      type: getDefaultStringValue(metadata?.pricer_settings?.type),
      yield_curve_model: getDefaultStringValue(metadata?.pricer_settings?.yield_curve_model),
      useCache: false,
      timeoutMinutes: 30,
      market_data_source: getDefaultStringValue(marketDataSources) as CA_Mastr_Models_v1_0_Models_MarketDataSource,
    },
    userSettingsCopy: {
      curve_date: getDateTypeFromString(metadata?.market_data_settings?.curve_date?.value),
      cdu_date_conventional: getDateTypeFromString(
        metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_conventional ?? undefined
      ),
      cdu_date_gnm: getDateTypeFromString(metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_gnm ?? undefined),
      cdu_date_gpl: getDateTypeFromString(metadata?.pricer_settings?.cello_cdu_dates?.cdu_date_gpl ?? undefined),
      model_version: getDefaultStringValue(metadata?.pricer_settings?.version),
      current_coupon_model: getDefaultStringValue(metadata?.pricer_settings?.current_coupon_model),
      market_data_source: getDefaultStringValue(marketDataSources) as CA_Mastr_Models_v1_0_Models_MarketDataSource,
    },
  };
};

const MarketDataModuleContext = React.createContext<MarketDataModuleContextType | undefined>(undefined);

const MarketDataModuleContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const { state: authState } = useAuthentication();
  const [state, dispatch] = React.useReducer(
    MarketDataModuleContextReducer,
    getDefaultState(authState?.userData?.user?.market_data_sources)
  );
  const {
    state: { metadata },
  } = useGlobalContext();

  React.useEffect(() => {
    /**
     * Since toastManager is a singleton and is tied to run ids of pages,
     * we need to remove all toasts when switching pages to avoid request errors popping up on the wrong page.
     */
    runToastManager.removeToasts();
    initialMarketDataModuleContextAction(state, dispatch, metadata);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: MarketDataModuleContextType = React.useMemo(
    () => ({ state, action: getMarketDataModuleContextAction(state, dispatch) }),
    [state, dispatch]
  );

  return <MarketDataModuleContext.Provider value={value}>{children}</MarketDataModuleContext.Provider>;
};

const useMarketDataModule = (): MarketDataModuleContextType => {
  const context = React.useContext(MarketDataModuleContext);
  if (context === undefined) {
    throw new Error("useMarketDataModule must be used within a MarketDataModuleContextProvider");
  }
  return context;
};

export { MarketDataModuleContextProvider, useMarketDataModule };
