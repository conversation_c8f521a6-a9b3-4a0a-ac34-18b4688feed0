import {
  AuthenticationService,
  CA_Mastr_Api_v1_0_Models_Auth_User,
  CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  CA_Mastr_Models_v1_0_Models_MarketDataSource,
  UtilService,
} from "@/utils/openapi";
import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import {
  getDateTypeFromString,
  getDefaultStringValue,
  getLastDayInMonth,
  parseDateToYYYYMMDD,
  removeUndefinedElementsFromObject,
} from "@/utils/helpers";

import {
  MarketDataModuleContextReducerAction,
  MarketDataModuleContextState,
  MarketDataUserSettingsCopyType,
  MarketDataUserSettingsType,
} from "./MarketDataModuleContextTypes";
import { getDefaultState } from ".";

async function getCelloCduDatesFromCurveDate(curveDate: Date) {
  try {
    const data = await UtilService.getCelloCduDates(parseDateToYYYYMMDD(curveDate));

    return data.cello_cdu_dates ?? undefined;
  } catch (e) {
    return undefined;
  }
}

//store userSettings so we don't have to fetch it every time
let userSettings: CA_Mastr_Api_v1_0_Models_Auth_User | null | undefined = null;

export const fetchUserClaim = async () => {
  if (!userSettings) {
    try {
      const response = await AuthenticationService.getClaim();
      userSettings = response.user;
    } catch (error) {
      console.error("Error fetching metadata", error);
    }
  }
  return userSettings;
};

export const getUserClaim = async () => {
  if (!userSettings) {
    return await fetchUserClaim();
  }
  return userSettings;
};

export function getIgnoreCduPayDateAfterFromCurveDate(curveDate: Date, latestCurveDate?: Date) {
  if (!latestCurveDate || !curveDate) return undefined;
  const lastDayOfMonthOfCurveDate = getLastDayInMonth(curveDate);
  const ignore_cdu_paydate_after =
    lastDayOfMonthOfCurveDate > latestCurveDate ? latestCurveDate : lastDayOfMonthOfCurveDate;
  return ignore_cdu_paydate_after;
}

export const initialMarketDataModuleContextAction = async (
  _state: MarketDataModuleContextState,
  dispatch: React.Dispatch<MarketDataModuleContextReducerAction>,
  metadata?: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse
): Promise<void> => {
  const userSettingsInSessionStorage = safeSessionStorage.getItem(SESSION_STORAGE_KEY.MARKET_DATA_USER_SETTINGS);
  const userSettingsInLocalStorage = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.MARKET_DATA_USER_SETTINGS);
  const userSettingsParsed = userSettingsInSessionStorage && JSON.parse(userSettingsInSessionStorage);

  const user = await getUserClaim();

  if (userSettingsInSessionStorage && userSettingsInSessionStorage !== "undefined") {
    try {
      userSettingsParsed["cdu_date_conventional"] = getDateTypeFromString(userSettingsParsed["cdu_date_conventional"]);
      userSettingsParsed["cdu_date_gnm"] = getDateTypeFromString(userSettingsParsed["cdu_date_gnm"]);
      userSettingsParsed["cdu_date_gpl"] = getDateTypeFromString(userSettingsParsed["cdu_date_gpl"]);
      userSettingsParsed["curve_date"] = getDateTypeFromString(userSettingsParsed["curve_date"]);
      userSettingsParsed["cello_cdu_date"] = getDateTypeFromString(userSettingsParsed["cello_cdu_date"]);
      userSettingsParsed["ignore_cdu_paydate_after"] = userSettingsParsed["ignore_cdu_paydate_after"]
        ? getDateTypeFromString(userSettingsParsed["ignore_cdu_paydate_after"])
        : getIgnoreCduPayDateAfterFromCurveDate(
            userSettingsParsed["curve_date"],
            metadata?.market_data_settings?.curve_date
              ? getDateTypeFromString(metadata?.market_data_settings?.curve_date?.value)
              : undefined
          );
      userSettingsParsed["market_data_source"] = userSettingsParsed["market_data_source"]
        ? userSettingsParsed["market_data_source"]
        : (getDefaultStringValue(user?.market_data_sources) as CA_Mastr_Models_v1_0_Models_MarketDataSource);
      userSettingsParsed["model_version"] = userSettingsParsed["model_version"]
        ? userSettingsParsed["model_version"]
        : getDefaultStringValue(metadata?.pricer_settings?.version);

      dispatch({ type: "UPDATE_MARKET_DATA_USER_SETTINGS", userSettings: userSettingsParsed });
      dispatch({
        type: "UPDATE_MARKET_DATA_USER_SETTINGS_COPY",
        userSettingsCopy: userSettingsParsed,
      });
    } finally {
      // Skip if JSON failed to parse
    }
  } else if (userSettingsInLocalStorage && userSettingsInLocalStorage !== "undefined") {
    try {
      const userSettings = getDefaultState(user?.market_data_sources).userSettings;

      const userSettingsParsed = JSON.parse(userSettingsInLocalStorage);

      dispatch({
        type: "UPDATE_MARKET_DATA_USER_SETTINGS",
        userSettings: {
          ...userSettings,
          ...userSettingsParsed,
        },
      });
      dispatch({
        type: "UPDATE_MARKET_DATA_USER_SETTINGS_COPY",
        userSettingsCopy: {
          ...userSettings,
          ...userSettingsParsed,
        },
      });
    } finally {
      // Skip if JSON failed to parse
    }
  } else {
    const userSettings = getDefaultState(user?.market_data_sources).userSettings;
    dispatch({ type: "UPDATE_MARKET_DATA_USER_SETTINGS", userSettings });
    dispatch({
      type: "UPDATE_MARKET_DATA_USER_SETTINGS_COPY",
      userSettingsCopy: userSettings,
    });
  }
  dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning: false });
};

export const getMarketDataModuleContextAction = (
  state: MarketDataModuleContextState,
  dispatch: React.Dispatch<MarketDataModuleContextReducerAction>
) => {
  return {
    toggleMarketDataSettingsDrawer: () => {
      if (state.marketDataSettingsDrawerOpen) {
        dispatch({ type: "CLOSE_MARKET_DATA_SETTINGS_DRAWER" });
      } else {
        dispatch({ type: "OPEN_MARKET_DATA_SETTINGS_DRAWER" });
      }
    },
    updateMarketDataUserSettings: (userSettings: MarketDataUserSettingsType, updateCopy?: boolean) => {
      ({ userSettings });
      // Remove undefined items from user settings
      const _userSettings = {
        ...state.userSettings,
        ...removeUndefinedElementsFromObject(userSettings),
      };

      dispatch({ type: "UPDATE_MARKET_DATA_USER_SETTINGS", userSettings: _userSettings });

      if (updateCopy) {
        dispatch({ type: "UPDATE_MARKET_DATA_USER_SETTINGS_COPY", userSettingsCopy: _userSettings });
      }
    },
    updateMarketDataUserSettingsCopy: (userSettingsCopy?: MarketDataUserSettingsCopyType) => {
      dispatch({ type: "UPDATE_MARKET_DATA_USER_SETTINGS_COPY", userSettingsCopy });
    },

    resetMarketDataUserSettings: async () => {
      const user = await getUserClaim();

      const userSettings = getDefaultState(user?.market_data_sources).userSettings;

      dispatch({ type: "UPDATE_MARKET_DATA_USER_SETTINGS", userSettings });
    },
    setActiveSettingsDrawerKey: (activeSettingsDrawerKey: string) => {
      dispatch({ type: "SET_ACTIVE_SETTINGS_DRAWER_KEY", activeSettingsDrawerKey });
    },
    setIsTimerRunning: (isTimerRunning: boolean) => {
      if (state.isTimerRunning !== isTimerRunning) {
        dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning });
      }
    },
    stopTimer: () => {
      dispatch({ type: "SET_IS_TIMER_RUNNING", isTimerRunning: false });
    },
    getCelloCduDatesFromCurveDate,
    getIgnoreCduPayDateAfterFromCurveDate,
  };
};
