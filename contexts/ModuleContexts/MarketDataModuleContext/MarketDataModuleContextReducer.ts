import { LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { MarketDataModuleContextReducerAction, MarketDataModuleContextState } from "./MarketDataModuleContextTypes";

export const MarketDataModuleContextReducer = (
  state: MarketDataModuleContextState,
  action: MarketDataModuleContextReducerAction
): MarketDataModuleContextState => {
  switch (action.type) {
    case "UPDATE_BOND": {
      return {
        ...state,
        security_info: action.security_info,
      };
    }
    case "OPEN_MARKET_DATA_SETTINGS_DRAWER": {
      return { ...state, marketDataSettingsDrawerOpen: true };
    }
    case "CLOSE_MARKET_DATA_SETTINGS_DRAWER": {
      return { ...state, marketDataSettingsDrawerOpen: false, activeSettingsDrawerKey: "" };
    }
    case "UPDATE_MARKET_DATA_USER_SETTINGS": {
      const userSettings = { ...state.userSettings, ...action.userSettings };
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.MARKET_DATA_USER_SETTINGS, JSON.stringify(userSettings));
      safeLocalStorage.setItem(
        LOCAL_STORAGE_KEY.MARKET_DATA_USER_SETTINGS,
        JSON.stringify({
          timeoutMinutes: userSettings.timeoutMinutes,
          useCache: userSettings.useCache,
        })
      );
      return { ...state, userSettings };
    }

    case "UPDATE_MARKET_DATA_USER_SETTINGS_COPY": {
      const userSettingsCopy = { ...state.userSettingsCopy, ...action.userSettingsCopy };
      return { ...state, userSettingsCopy };
    }
    case "SET_ACTIVE_SETTINGS_DRAWER_KEY": {
      return { ...state, activeSettingsDrawerKey: action.activeSettingsDrawerKey };
    }
    case "SET_IS_TIMER_RUNNING": {
      return {
        ...state,
        isTimerRunning: action.isTimerRunning,
        hasTimerRun: state.hasTimerRun || action.isTimerRunning,
      };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
