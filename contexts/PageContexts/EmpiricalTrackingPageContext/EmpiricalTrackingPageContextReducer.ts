import {
  EmpiricalTrackingPageContextReducerAction,
  EmpiricalTrackingPageContextState,
} from "./EmpiricalTrackingPageContextTypes";

export const TrackingPageContextReducer = (
  state: EmpiricalTrackingPageContextState,
  action: EmpiricalTrackingPageContextReducerAction
): EmpiricalTrackingPageContextState => {
  switch (action.type) {
    case "RUN": {
      return {
        ...state,
        lastRun: action.date,
        lastRunId: action.lastRunId,
        noCache: action.no_cache,
        oldRunId: null,
        api_start_time: action.api_start_time,
        isRequestStopped: false,
      };
    }
    case "SET_OLD_RUN": {
      return {
        ...state,
        oldRunId: action.oldRunId,
      };
    }
    case "SET_GRID_HAS_CHANGES": {
      return {
        ...state,
        grid: {
          hasChanges: action.hasChanges,
        },
      };
    }
    case "RESET_RUN": {
      return {
        ...state,
        lastRun: undefined,
        lastRunId: undefined,
        oldRunId: null,
        isRequestStopped: true,
      };
    }
  }
};
