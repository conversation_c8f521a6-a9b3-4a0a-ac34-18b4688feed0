import { RefObject } from "react";
import { AgGridReact } from "ag-grid-react";
import { getEmpiricalTrackingPageContextAction } from "./EmpiricalTrackingPageContextAction";

export type EmpiricalTrackingPageContextType = {
  refs: {
    detailGridRef: RefObject<AgGridReact>;
  };

  state: EmpiricalTrackingPageContextState;
  action: EmpiricalTrackingPageContextAction;
};

export type EmpiricalTrackingPageContextDispatch = (action: EmpiricalTrackingPageContextReducerAction) => void;

export type EmpiricalTrackingPageContextState = {
  lastRun: string | undefined;
  lastRunId: string | undefined;
  noCache: boolean;
  oldRunId: string | null;
  api_start_time?: string;
  grid: {
    hasChanges: boolean;
  };
  isRequestStopped: boolean;
};

export type EmpiricalTrackingPageContextReducerAction =
  | {
      type: "RUN";
      date: string;
      lastRunId: string;
      no_cache: boolean;
      autoRun?: boolean;
      isOldRun?: boolean;
      api_start_time?: string;
    }
  | {
      type: "SET_OLD_RUN";
      oldRunId: string | null;
    }
  | {
      type: "SET_GRID_HAS_CHANGES";
      hasChanges: boolean;
    }
  | {
      type: "RESET_RUN";
    };

export type EmpiricalTrackingPageContextAction = ReturnType<typeof getEmpiricalTrackingPageContextAction>;
