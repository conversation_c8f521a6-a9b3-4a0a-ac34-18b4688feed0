import Router from "next/router";
import { getUuid } from "@/utils/helpers";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeSessionStorage } from "@/utils/local-storage";
import {
  EmpiricalTrackingPageContextReducerAction,
  EmpiricalTrackingPageContextState,
} from "./EmpiricalTrackingPageContextTypes";

export const initialEmpiricalTrackingPageContextAction = async (
  _state: EmpiricalTrackingPageContextState, // eslint-disable-line @typescript-eslint/no-unused-vars
  _dispatch: React.Dispatch<EmpiricalTrackingPageContextReducerAction> // eslint-disable-line @typescript-eslint/no-unused-vars
): Promise<void> => {
  // [INFO] - if we have run_id in the address bar do not fetch the lastRunId from session
  const routerQuery = Router.query;
  if (routerQuery?.run_id) {
    //if we have run id in the address bar we store that in the session storage
    const runId = routerQuery.run_id.toString();
    safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID, runId);
    return;
  }
  if (routerQuery?.id) {
    return;
  }

  const lastRunId = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID);
  const lastQueryId = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID);
  if (lastRunId && lastQueryId) {
    Router.push(
      {
        pathname: Router.pathname,
        query: {
          ...routerQuery,
          id: lastQueryId,
          run_id: lastRunId,
        },
      },
      undefined,
      { shallow: true }
    );

    _dispatch({
      type: "SET_OLD_RUN",
      oldRunId: lastRunId,
    });
    return;
  }

  if (lastRunId) {
    Router.push(
      {
        pathname: Router.pathname,
        query: {
          ...routerQuery,
          run_id: lastRunId,
        },
      },
      undefined,
      { shallow: true }
    );

    _dispatch({
      type: "SET_OLD_RUN",
      oldRunId: lastRunId,
    });
  }
};

export const getEmpiricalTrackingPageContextAction = (
  state: EmpiricalTrackingPageContextState,
  dispatch: React.Dispatch<EmpiricalTrackingPageContextReducerAction>
) => {
  return {
    run: (opts: { date?: string; no_cache?: boolean } = {}) => {
      const { date, no_cache } = opts;
      const runId = getUuid();
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID, runId);
      dispatch({
        type: "RUN",
        date: date || new Date().toISOString(),
        no_cache: !!no_cache,
        lastRunId: runId,
        isOldRun: false,
      });
      return runId;
    },
    setOldRun: (oldRunId: string | null) => {
      dispatch({
        type: "SET_OLD_RUN",
        oldRunId,
      });
    },

    setHasChanges: (hasChanges: boolean) => {
      dispatch({
        type: "SET_GRID_HAS_CHANGES",
        hasChanges,
      });
    },
    resetRun: () => {
      dispatch({ type: "RESET_RUN" });
    },
  };
};
