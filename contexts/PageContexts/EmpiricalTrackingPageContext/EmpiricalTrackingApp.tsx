import * as React from "react";
import { AgGridReact } from "ag-grid-react";
import { getParam } from "@/utils/helpers";
import { TrackingPageContextReducer } from "./EmpiricalTrackingPageContextReducer";
import {
  getEmpiricalTrackingPageContextAction,
  initialEmpiricalTrackingPageContextAction,
} from "./EmpiricalTrackingPageContextAction";
import {
  EmpiricalTrackingPageContextState,
  EmpiricalTrackingPageContextType,
} from "./EmpiricalTrackingPageContextTypes";

const getDefaultState = (): EmpiricalTrackingPageContextState => {
  const runId = getParam("run_id");
  return {
    lastRun: undefined,
    lastRunId: undefined,
    noCache: false,
    oldRunId: runId,
    grid: {
      hasChanges: false,
    },
    isRequestStopped: false,
  };
};

const EmpiricalTrackingPageContext = React.createContext<EmpiricalTrackingPageContextType | undefined>(undefined);

const EmpiricalTrackingPageContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const detailGridRef = React.useRef<AgGridReact>(null);
  const [state, dispatch] = React.useReducer(TrackingPageContextReducer, getDefaultState());

  React.useEffect(() => {
    initialEmpiricalTrackingPageContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: EmpiricalTrackingPageContextType = React.useMemo(
    () => ({
      refs: { detailGridRef },
      state,
      action: getEmpiricalTrackingPageContextAction(state, dispatch),
    }),
    [state, dispatch]
  );

  return <EmpiricalTrackingPageContext.Provider value={value}>{children}</EmpiricalTrackingPageContext.Provider>;
};

const useEmpiricalTrackingPage = (): EmpiricalTrackingPageContextType => {
  const context = React.useContext(EmpiricalTrackingPageContext);
  if (context === undefined) {
    throw new Error("useEmpiricalTrackingPage must be used within a EmpiricalTrackingPageContextProvider");
  }
  return context;
};

export { EmpiricalTrackingPageContextProvider, useEmpiricalTrackingPage };
