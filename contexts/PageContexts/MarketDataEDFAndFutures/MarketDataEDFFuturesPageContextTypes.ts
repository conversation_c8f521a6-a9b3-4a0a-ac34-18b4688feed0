import { CA_Mastr_Models_v1_0_Models_EDFRiskDataSource } from "@/utils/openapi";
import { getMarketDataEDFFuturesPageContextAction } from "./MarketDataEDFFuturesPageContextAction";

export type MarketDataEDFFuturesPageContextType = {
  state: MarketDataEDFFuturesPageContextState;
  action: MarketDataEDFFuturesPageContextAction;
};

export type EDFFuturesPageSettingsType = {
  dataSource: CA_Mastr_Models_v1_0_Models_EDFRiskDataSource;
};

export type MarketDataEDFFuturesPageContextState = {
  edfFuturesPageSettings: EDFFuturesPageSettingsType;
  edfFuturesPageSettingsCopy: EDFFuturesPageSettingsType;
  edfDescription: string;
  noCache: boolean;
};

export type MarketDataEDFFuturesPageContextReducerAction =
  | {
      type: "UPDATE_SETTINGS";
      edfFuturesPageSettings: Partial<EDFFuturesPageSettingsType>;
    }
  | {
      type: "UPDATE_DESCRIPTION";
      edfDescription: string;
    };

export type MarketDataEDFFuturesPageContextAction = ReturnType<typeof getMarketDataEDFFuturesPageContextAction>;
