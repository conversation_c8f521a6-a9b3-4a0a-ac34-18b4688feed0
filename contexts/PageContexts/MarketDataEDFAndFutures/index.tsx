import * as React from "react";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { CA_Mastr_Models_v1_0_Models_EDFRiskDataSource } from "@/utils/openapi";
import { MarketDataEDFFuturesPageContextReducer } from "./MarketDataEDFFuturesPageContextReducer";
import {
  getMarketDataEDFFuturesPageContextAction,
  initialMarketDataEDFFuturesPageContextAction,
} from "./MarketDataEDFFuturesPageContextAction";
import {
  MarketDataEDFFuturesPageContextState,
  MarketDataEDFFuturesPageContextType,
} from "./MarketDataEDFFuturesPageContextTypes";

const getDefaultState = (): MarketDataEDFFuturesPageContextState => {
  return {
    edfFuturesPageSettings: {
      dataSource: CA_Mastr_Models_v1_0_Models_EDFRiskDataSource.CELLO,
    },
    edfFuturesPageSettingsCopy: {
      dataSource: CA_Mastr_Models_v1_0_Models_EDFRiskDataSource.CELLO,
    },
    edfDescription: "ticker",
    noCache: false,
  };
};

const MarketDataEDFFuturesPageContext = React.createContext<MarketDataEDFFuturesPageContextType | undefined>(undefined);

const MarketDataEDFFuturesPageContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(MarketDataEDFFuturesPageContextReducer, getDefaultState());
  const {
    action: { setIsTimerRunning },
  } = useMarketDataModule();

  React.useEffect(() => {
    setIsTimerRunning(true);
    initialMarketDataEDFFuturesPageContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: MarketDataEDFFuturesPageContextType = React.useMemo(
    () => ({ state, action: getMarketDataEDFFuturesPageContextAction(dispatch) }),
    [state, dispatch]
  );

  return <MarketDataEDFFuturesPageContext.Provider value={value}>{children}</MarketDataEDFFuturesPageContext.Provider>;
};

const useMarketDataEDFFuturesPage = (): MarketDataEDFFuturesPageContextType => {
  const context = React.useContext(MarketDataEDFFuturesPageContext);
  if (context === undefined) {
    throw new Error("useMarketDataEDFFuturesPage must be used within a MarketDataEDFFuturesPageContextProvider");
  }
  return context;
};

export { MarketDataEDFFuturesPageContextProvider, useMarketDataEDFFuturesPage };
