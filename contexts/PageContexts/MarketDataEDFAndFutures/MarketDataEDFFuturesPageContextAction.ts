import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { getDateTypeFromString } from "@/utils/helpers";
import { safeSessionStorage } from "@/utils/local-storage";
import { CA_Mastr_Models_v1_0_Models_EDFRiskDataSource } from "@/utils/openapi";
import {
  EDFFuturesPageSettingsType,
  MarketDataEDFFuturesPageContextReducerAction,
  MarketDataEDFFuturesPageContextState,
} from "./MarketDataEDFFuturesPageContextTypes";

export const initialMarketDataEDFFuturesPageContextAction = async (
  _state: MarketDataEDFFuturesPageContextState,
  dispatch: React.Dispatch<MarketDataEDFFuturesPageContextReducerAction>
): Promise<void> => {
  const lastSavedEDFFuturesInputsJSON = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EDFANDFUTURES_INPUTS);
  const lastSavedEDFDescriptionJSON = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EDFANDFUTURES_DESCRIPTION);
  if (lastSavedEDFFuturesInputsJSON && lastSavedEDFFuturesInputsJSON !== "undefined") {
    const lastSavedEDFFuturesInputs = JSON.parse(lastSavedEDFFuturesInputsJSON);
    const { edfFuturesPageSettings } = lastSavedEDFFuturesInputs;

    dispatch({
      type: "UPDATE_SETTINGS",
      edfFuturesPageSettings: {
        ...edfFuturesPageSettings,
        asOfDate: getDateTypeFromString(edfFuturesPageSettings.asOfDate),
      },
    });
  } else {
    dispatch({
      type: "UPDATE_SETTINGS",
      edfFuturesPageSettings: {
        dataSource: CA_Mastr_Models_v1_0_Models_EDFRiskDataSource.CELLO,
      },
    });
  }
  if (lastSavedEDFDescriptionJSON && lastSavedEDFDescriptionJSON !== "undefined") {
    const lastSavedEDFFuturesInputs = JSON.parse(lastSavedEDFDescriptionJSON);
    const { edfDescription } = lastSavedEDFFuturesInputs;

    dispatch({
      type: "UPDATE_DESCRIPTION",
      edfDescription,
    });
  }
};

export const getMarketDataEDFFuturesPageContextAction = (
  dispatch: React.Dispatch<MarketDataEDFFuturesPageContextReducerAction>
) => {
  return {
    updateEdfFuturesSettings: (edfFuturesPageSettings: Partial<EDFFuturesPageSettingsType>) => {
      dispatch({ type: "UPDATE_SETTINGS", edfFuturesPageSettings });
    },
    updateEdfDescription: (edfDescription?: string) => {
      if (!edfDescription) return;
      safeSessionStorage.setItem(
        SESSION_STORAGE_KEY.EDFANDFUTURES_DESCRIPTION,
        JSON.stringify({
          edfDescription: edfDescription,
        })
      );
      dispatch({ type: "UPDATE_DESCRIPTION", edfDescription });
    },
  };
};
