import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import {
  MarketDataEDFFuturesPageContextReducerAction,
  MarketDataEDFFuturesPageContextState,
} from "./MarketDataEDFFuturesPageContextTypes";

export const MarketDataEDFFuturesPageContextReducer = (
  state: MarketDataEDFFuturesPageContextState,
  action: MarketDataEDFFuturesPageContextReducerAction
): MarketDataEDFFuturesPageContextState => {
  switch (action.type) {
    case "UPDATE_SETTINGS": {
      const edfFuturesPageSettings = {
        ...state.edfFuturesPageSettings,
        ...action.edfFuturesPageSettings,
      };
      safeSessionStorage.setItem(
        SESSION_STORAGE_KEY.EDFANDFUTURES_INPUTS,
        JSON.stringify({
          edfFuturesPageSettings: edfFuturesPageSettings,
        })
      );
      return { ...state, edfFuturesPageSettings };
    }
    case "UPDATE_DESCRIPTION": {
      return { ...state, edfDescription: action.edfDescription };
    }

    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
