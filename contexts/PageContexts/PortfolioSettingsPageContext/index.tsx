import * as React from "react";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { PortfolioSettingsPageContextReducer } from "./PortfolioSettingsPageContextReducer";
import { getPortfolioSettingsPageContextAction } from "./PortfolioSettingsPageContextAction";
import {
  PortfolioSettingsPageContextState,
  PortfolioSettingsPageContextType,
} from "./PortfolioSettingsPageContextTypes";

const getDefaultState = (): PortfolioSettingsPageContextState => {
  const settingsString = safeSessionStorage.getItem(SESSION_STORAGE_KEY.PORTFOLIO_SETTINGS);

  if (settingsString && settingsString !== "undefined") {
    try {
      const settingsJson = JSON.parse(settingsString);
      const { settings } = settingsJson;
      return {
        settings: settings || {
          useCache: false,
        },
        isDrawerOpen: false,
      };
    } finally {
      // Skip if <PERSON><PERSON><PERSON> failed to parse
    }
  }

  return {
    settings: {
      useCache: false,
    },
    isDrawerOpen: false,
  };
};

const PortfolioSettingsPageContext = React.createContext<PortfolioSettingsPageContextType | undefined>(undefined);

const PortfolioSettingsPageContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(PortfolioSettingsPageContextReducer, getDefaultState());

  const value: PortfolioSettingsPageContextType = React.useMemo(
    () => ({ state, action: getPortfolioSettingsPageContextAction(dispatch) }),
    [state, dispatch]
  );

  return <PortfolioSettingsPageContext.Provider value={value}>{children}</PortfolioSettingsPageContext.Provider>;
};

const usePortfolioSettings = (): PortfolioSettingsPageContextType => {
  const context = React.useContext(PortfolioSettingsPageContext);
  if (context === undefined) {
    throw new Error("usePortfolioSettings must be used within a PortfolioSettignsPageContextProvider");
  }
  return context;
};

export { PortfolioSettingsPageContextProvider, usePortfolioSettings };
