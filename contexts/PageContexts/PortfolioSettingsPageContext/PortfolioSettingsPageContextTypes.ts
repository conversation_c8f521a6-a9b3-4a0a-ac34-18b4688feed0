import { getPortfolioSettingsPageContextAction } from "./PortfolioSettingsPageContextAction";

export type PortfolioSettingsPageContextType = {
  state: PortfolioSettingsPageContextState;
  action: PortfolioSettingsPageContextAction;
};

export type PortfolioSettingsPageSettingsType = {
  useCache?: boolean;
  timeoutMinutes?: number;
};

export type PortfolioSettingsPageContextState = {
  settings: PortfolioSettingsPageSettingsType;
  isDrawerOpen: boolean;
};

export type PortfolioSettingsPageContextReducerAction =
  | {
      type: "UPDATE_SETTINGS";
      settings: Partial<PortfolioSettingsPageSettingsType>;
    }
  | {
      type: "SET_DRAWER_OPEN";
      isOpen: boolean;
    };

export type PortfolioSettingsPageContextAction = ReturnType<typeof getPortfolioSettingsPageContextAction>;
