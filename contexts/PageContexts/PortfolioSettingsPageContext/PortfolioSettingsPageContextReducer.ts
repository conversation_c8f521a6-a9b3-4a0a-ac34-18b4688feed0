import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeSessionStorage } from "@/utils/local-storage";
import {
  PortfolioSettingsPageContextReducerAction,
  PortfolioSettingsPageContextState,
} from "./PortfolioSettingsPageContextTypes";

export const PortfolioSettingsPageContextReducer = (
  state: PortfolioSettingsPageContextState,
  action: PortfolioSettingsPageContextReducerAction
): PortfolioSettingsPageContextState => {
  switch (action.type) {
    case "UPDATE_SETTINGS": {
      const settings = {
        ...state.settings,
        ...action.settings,
      };
      safeSessionStorage.setItem(
        SESSION_STORAGE_KEY.PORTFOLIO_SETTINGS,
        JSON.stringify({
          settings: settings,
        })
      );
      return { ...state, settings };
    }
    case "SET_DRAWER_OPEN": {
      return { ...state, isDrawerOpen: action.isOpen };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
