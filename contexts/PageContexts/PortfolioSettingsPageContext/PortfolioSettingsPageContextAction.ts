import {
  PortfolioSettingsPageContextReducerAction,
  PortfolioSettingsPageSettingsType,
} from "./PortfolioSettingsPageContextTypes";

export const getPortfolioSettingsPageContextAction = (
  dispatch: React.Dispatch<PortfolioSettingsPageContextReducerAction>
) => {
  return {
    updateSettings: (settings: Partial<PortfolioSettingsPageSettingsType>) => {
      dispatch({ type: "UPDATE_SETTINGS", settings });
    },
    openDrawer: () => {
      dispatch({ type: "SET_DRAWER_OPEN", isOpen: true });
    },
    closeDrawer: () => {
      dispatch({ type: "SET_DRAWER_OPEN", isOpen: false });
    },
  };
};
