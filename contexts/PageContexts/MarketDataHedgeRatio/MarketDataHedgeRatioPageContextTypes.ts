import { AgencyValue } from "@/utils/helpers/market-data/helper";
import { CA_Mastr_Models_v1_0_Models_TBAMonthType } from "@/utils/openapi";
import { getMarketDataHedgeRatioPageContextAction } from "./MarketDataHedgeRatioPageContextAction";

export type MarketDataHedgeRatioPageContextType = {
  state: MarketDataHedgeRatioPageContextState;
  action: MarketDataHedgeRatioPageContextAction;
};

export type HedgeRatioPageSettingsType = {
  agency: AgencyValue;
  monthType: CA_Mastr_Models_v1_0_Models_TBAMonthType;
  riskModelVersion?: string;
};

export type HedgeRatioPageSettingsCopyType = Partial<HedgeRatioPageSettingsType>;

export type MarketDataHedgeRatioPageContextState = {
  hedgeRatioPageSettings: HedgeRatioPageSettingsType;
  hedgeRatioPageSettingsCopy: HedgeRatioPageSettingsCopyType;
  noCache: boolean;
};

export type MarketDataHedgeRatioPageContextReducerAction = {
  type: "UPDATE_SETTINGS";
  hedgeRatioPageSettings: Partial<HedgeRatioPageSettingsType>;
};

export type MarketDataHedgeRatioPageContextAction = ReturnType<typeof getMarketDataHedgeRatioPageContextAction>;
