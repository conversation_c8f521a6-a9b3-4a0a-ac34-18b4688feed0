import * as React from "react";
import { CA_Mastr_Models_v1_0_Models_TBAMonthType, CA_Mastr_Models_v1_0_Models_TBATicker } from "@/utils/openapi";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import {
  getMarketDataHedgeRatioPageContextAction,
  initialMarketDataHedgeRatioPageContextAction,
} from "./MarketDataHedgeRatioPageContextAction";
import {
  MarketDataHedgeRatioPageContextState,
  MarketDataHedgeRatioPageContextType,
} from "./MarketDataHedgeRatioPageContextTypes";
import { MarketDataHedgeRatioPageContextReducer } from "./MarketDataHedgeRatioPageContextReducer";

export const defaultAgency = {
  value: CA_Mastr_Models_v1_0_Models_TBATicker.FNCL,
  displayValue: "FNCL",
  label: "FNCL",
  secondaryLabel: "Fannie Mae 30 Year Fixed Rate Mortgages",
  id: 0,
};

const getDefaultState = (): MarketDataHedgeRatioPageContextState => {
  return {
    hedgeRatioPageSettings: {
      agency: defaultAgency,
      monthType: CA_Mastr_Models_v1_0_Models_TBAMonthType.FRONT,
    },
    hedgeRatioPageSettingsCopy: {
      agency: undefined,
      monthType: undefined,
    },
    noCache: false,
  };
};

const MarketDataHedgeRatioPageContext = React.createContext<MarketDataHedgeRatioPageContextType | undefined>(undefined);

const MarketDataHedgeRatioPageContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(MarketDataHedgeRatioPageContextReducer, getDefaultState());
  const {
    action: { setIsTimerRunning },
  } = useMarketDataModule();

  React.useEffect(() => {
    setIsTimerRunning(true);
    initialMarketDataHedgeRatioPageContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: MarketDataHedgeRatioPageContextType = React.useMemo(
    () => ({ state, action: getMarketDataHedgeRatioPageContextAction(dispatch) }),
    [state, dispatch]
  );

  return <MarketDataHedgeRatioPageContext.Provider value={value}>{children}</MarketDataHedgeRatioPageContext.Provider>;
};

const useMarketDataHedgeRatioPage = (): MarketDataHedgeRatioPageContextType => {
  const context = React.useContext(MarketDataHedgeRatioPageContext);
  if (context === undefined) {
    throw new Error("useMarketDataHedgeRatioPage must be used within a MarketDataHedgeRatioPageContextProvider");
  }
  return context;
};

export { MarketDataHedgeRatioPageContextProvider, useMarketDataHedgeRatioPage };
