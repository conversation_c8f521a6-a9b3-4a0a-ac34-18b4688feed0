import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeSessionStorage } from "@/utils/local-storage";
import {
  MarketDataHedgeRatioPageContextReducerAction,
  MarketDataHedgeRatioPageContextState,
} from "../MarketDataHedgeRatio/MarketDataHedgeRatioPageContextTypes";

export const MarketDataHedgeRatioPageContextReducer = (
  state: MarketDataHedgeRatioPageContextState,
  action: MarketDataHedgeRatioPageContextReducerAction
): MarketDataHedgeRatioPageContextState => {
  switch (action.type) {
    case "UPDATE_SETTINGS": {
      const hedgeRatioPageSettings = {
        ...state.hedgeRatioPageSettings,
        ...action.hedgeRatioPageSettings,
      };
      safeSessionStorage.setItem(
        SESSION_STORAGE_KEY.HEDGE_RATIO_INPUTS,
        JSON.stringify({
          hedgeRatioPageSettings: hedgeRatioPageSettings,
        })
      );
      return { ...state, hedgeRatioPageSettings };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
