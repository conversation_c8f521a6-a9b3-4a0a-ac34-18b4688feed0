import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { getAPIErrorMessage, getDateTypeFromString } from "@/utils/helpers";
import { safeSessionStorage } from "@/utils/local-storage";
import { CA_Mastr_Models_v1_0_Models_TBAMonthType } from "@/utils/openapi";
import { showErrorToast } from "@/design-system/theme/toast";

import {
  HedgeRatioPageSettingsType,
  MarketDataHedgeRatioPageContextReducerAction,
  MarketDataHedgeRatioPageContextState,
} from "./MarketDataHedgeRatioPageContextTypes";
import { defaultAgency } from ".";

export const initialMarketDataHedgeRatioPageContextAction = async (
  _state: MarketDataHedgeRatioPageContextState,
  dispatch: React.Dispatch<MarketDataHedgeRatioPageContextReducerAction>
): Promise<void> => {
  const lastSavedHedgeRatioInputsJSON = safeSessionStorage.getItem(SESSION_STORAGE_KEY.HEDGE_RATIO_INPUTS);
  if (lastSavedHedgeRatioInputsJSON && lastSavedHedgeRatioInputsJSON !== "undefined") {
    try {
      const lastSavedHedgeRatioInputs = JSON.parse(lastSavedHedgeRatioInputsJSON);
      const { hedgeRatioPageSettings } = lastSavedHedgeRatioInputs;

      dispatch({
        type: "UPDATE_SETTINGS",
        hedgeRatioPageSettings: {
          ...hedgeRatioPageSettings,
          asOfDate: getDateTypeFromString(hedgeRatioPageSettings.asOfDate),
        },
      });
    } finally {
      // Skip if JSON failed to parse
    }
  } else {
    try {
      dispatch({
        type: "UPDATE_SETTINGS",
        hedgeRatioPageSettings: {
          agency: defaultAgency,
          monthType: CA_Mastr_Models_v1_0_Models_TBAMonthType.FRONT,
        },
      });
    } catch (err) {
      showErrorToast("Error", getAPIErrorMessage(err));
    }
  }
};

export const getMarketDataHedgeRatioPageContextAction = (
  dispatch: React.Dispatch<MarketDataHedgeRatioPageContextReducerAction>
) => {
  return {
    updateHedgeRatioPageSettings: (hedgeRatioPageSettings: Partial<HedgeRatioPageSettingsType>) => {
      dispatch({ type: "UPDATE_SETTINGS", hedgeRatioPageSettings });
    },
  };
};
