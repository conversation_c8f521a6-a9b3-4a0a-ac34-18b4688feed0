import * as React from "react";
import { CA_Mastr_Models_v1_0_Models_TBAMonthType, CA_Mastr_Models_v1_0_Models_TBATicker } from "@/utils/openapi";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { MarketDataPerformancePageContextReducer } from "./MarketDataPerformancePageContextReducer";
import {
  getMarketDataPerformancePageContextAction,
  initialMarketDataPerformancePageContextAction,
} from "./MarketDataPerformancePageContextAction";
import {
  MarketDataPerformancePageContextState,
  MarketDataPerformancePageContextType,
} from "./MarketDataPerformancePageContextTypes";

export const defaultAgency = {
  value: CA_Mastr_Models_v1_0_Models_TBATicker.FNCL,
  displayValue: "FNCL",
  label: "FNCL",
  secondaryLabel: "Fannie Mae 30 Year Fixed Rate Mortgages",
  id: 0,
};

const getDefaultState = (): MarketDataPerformancePageContextState => {
  return {
    performancePageSettings: {
      agency: defaultAgency,
      monthType: CA_Mastr_Models_v1_0_Models_TBAMonthType.FRONT,
    },
    performancePageSettingsCopy: {
      agency: undefined,
      monthType: undefined,
    },
    noCache: false,
  };
};

const MarketDataPerformancePageContext = React.createContext<MarketDataPerformancePageContextType | undefined>(
  undefined
);

const MarketDataPerformancePageContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(MarketDataPerformancePageContextReducer, getDefaultState());
  const {
    action: { setIsTimerRunning },
  } = useMarketDataModule();

  React.useEffect(() => {
    setIsTimerRunning(true);
    initialMarketDataPerformancePageContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: MarketDataPerformancePageContextType = React.useMemo(
    () => ({ state, action: getMarketDataPerformancePageContextAction(dispatch) }),
    [state, dispatch]
  );

  return (
    <MarketDataPerformancePageContext.Provider value={value}>{children}</MarketDataPerformancePageContext.Provider>
  );
};

const useMarketDataPerformancePage = (): MarketDataPerformancePageContextType => {
  const context = React.useContext(MarketDataPerformancePageContext);
  if (context === undefined) {
    throw new Error("useMarketDataPerformancePage must be used within a MarketDataPerfomancePageContextProvider");
  }
  return context;
};

export { MarketDataPerformancePageContextProvider, useMarketDataPerformancePage };
