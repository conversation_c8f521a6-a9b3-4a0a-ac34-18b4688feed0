import { AgencyValue } from "@/utils/helpers/market-data/helper";
import { CA_Mastr_Models_v1_0_Models_TBAMonthType } from "@/utils/openapi";
import { getMarketDataPerformancePageContextAction } from "./MarketDataPerformancePageContextAction";

export type MarketDataPerformancePageContextType = {
  state: MarketDataPerformancePageContextState;
  action: MarketDataPerformancePageContextAction;
};

export type PerformancePageSettingsType = {
  agency: AgencyValue;
  monthType?: CA_Mastr_Models_v1_0_Models_TBAMonthType;
  riskModelVersion?: string;
};

export type PerformancePageSettingsCopyType = Partial<PerformancePageSettingsType>;

export type MarketDataPerformancePageContextState = {
  performancePageSettings: PerformancePageSettingsType;
  performancePageSettingsCopy: PerformancePageSettingsCopyType;
  noCache: boolean;
};

export type MarketDataPerformancePageContextReducerAction = {
  type: "UPDATE_SETTINGS";
  performancePageSettings: Partial<PerformancePageSettingsType>;
};

export type MarketDataPerformancePageContextAction = ReturnType<typeof getMarketDataPerformancePageContextAction>;
