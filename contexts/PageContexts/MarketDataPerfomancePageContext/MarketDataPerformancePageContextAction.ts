import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeSessionStorage } from "@/utils/local-storage";
import {
  MarketDataPerformancePageContextReducerAction,
  MarketDataPerformancePageContextState,
  PerformancePageSettingsType,
} from "./MarketDataPerformancePageContextTypes";

export const initialMarketDataPerformancePageContextAction = async (
  _state: MarketDataPerformancePageContextState,
  dispatch: React.Dispatch<MarketDataPerformancePageContextReducerAction>
): Promise<void> => {
  const lastSavedPerformanceInputsJSON = safeSessionStorage.getItem(SESSION_STORAGE_KEY.PERFORMANCE_INPUTS);
  if (lastSavedPerformanceInputsJSON && lastSavedPerformanceInputsJSON !== "undefined") {
    try {
      const lastSavedPerformanceInputs = JSON.parse(lastSavedPerformanceInputsJSON);
      const { performancePageSettings } = lastSavedPerformanceInputs;

      dispatch({
        type: "UPDATE_SETTINGS",
        performancePageSettings: {
          ...performancePageSettings,
        },
      });
    } finally {
      // Skip if JSON failed to parse
    }
  }
};

export const getMarketDataPerformancePageContextAction = (
  dispatch: React.Dispatch<MarketDataPerformancePageContextReducerAction>
) => {
  return {
    updatePerformancePageSettings: (performancePageSettings: Partial<PerformancePageSettingsType>) => {
      dispatch({ type: "UPDATE_SETTINGS", performancePageSettings });
    },
  };
};
