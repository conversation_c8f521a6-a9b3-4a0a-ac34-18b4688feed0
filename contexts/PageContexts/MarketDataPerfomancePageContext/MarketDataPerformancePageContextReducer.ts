import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeSessionStorage } from "@/utils/local-storage";
import {
  MarketDataPerformancePageContextReducerAction,
  MarketDataPerformancePageContextState,
} from "./MarketDataPerformancePageContextTypes";

export const MarketDataPerformancePageContextReducer = (
  state: MarketDataPerformancePageContextState,
  action: MarketDataPerformancePageContextReducerAction
): MarketDataPerformancePageContextState => {
  switch (action.type) {
    case "UPDATE_SETTINGS": {
      const performancePageSettings = {
        ...state.performancePageSettings,
        ...action.performancePageSettings,
      };
      safeSessionStorage.setItem(
        SESSION_STORAGE_KEY.PERFORMANCE_INPUTS,
        JSON.stringify({
          performancePageSettings: performancePageSettings,
        })
      );
      return { ...state, performancePageSettings };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
