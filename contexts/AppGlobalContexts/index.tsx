import React from "react";
import combineProviders from "@/utils/combine-providers";
import { ConfirmationContextProvider } from "../ConfirmationContext/ConfirmationContextProvider";
import { GlobalContextProvider } from "./GlobalContext";
import { AuthenticationContextProvider } from "./AuthenticationContext";

const AppGlobalContexts: React.FC<React.PropsWithChildren> = ({ children }) => {
  return <AppContextProviders>{children}</AppContextProviders>;
};

const GlobalContextProviderWrapper: React.FC<React.PropsWithChildren> = ({ children }) => {
  // UI- 1615: Removed key. Causing multiple rendering and memory leaks.
  return <GlobalContextProvider>{children}</GlobalContextProvider>;
};

export default AppGlobalContexts;

const AppContextProviders = combineProviders(
  AuthenticationContextProvider,
  GlobalContextProviderWrapper,
  ConfirmationContextProvider
);
