import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { safeLocalStorage } from "@/utils/local-storage";
import { GlobalContextReducerAction, GlobalContextState } from "./GlobalContextTypes";

export const globalStateReducer = (
  state: GlobalContextState,
  action: GlobalContextReducerAction
): GlobalContextState => {
  switch (action.type) {
    case "SET_METADATA": {
      return { ...state, metadata: action.metadata };
    }
    case "OPEN_SETTINGS_DRAWER": {
      return { ...state, settingsDrawerOpen: true };
    }
    case "CLOSE_SETTINGS_DRAWER": {
      return { ...state, settingsDrawerOpen: false };
    }
    case "OPEN_NOTIFICATIONS_DRAWER": {
      return { ...state, notificationsDrawerOpen: true };
    }
    case "CLOSE_NOTIFICATIONS_DRAWER": {
      return { ...state, notificationsDrawerOpen: false };
    }
    case "EXPAND_NAVIGATION_MENU": {
      safeLocalStorage.removeItem(LOCAL_STORAGE_KEY.NAVIGATION_MENU_COLLAPSED);
      return { ...state, isNavigationMenuCollapsed: false };
    }
    case "COLLAPSE_NAVIGATION_MENU": {
      safeLocalStorage.setItem(LOCAL_STORAGE_KEY.NAVIGATION_MENU_COLLAPSED, "true");
      return { ...state, isNavigationMenuCollapsed: true };
    }
    case "UPDATE_NAV_ACCORDION_EXPANDED_INDEX": {
      return { ...state, navAccordionExpandedIndex: action.expandedIndex };
    }
    case "SET_AGENCY_OPTIONS": {
      return { ...state, agencyOptions: action.agencyOptions };
    }
    case "SET_IS_SCROLLING_UP": {
      return { ...state, isScrollingUp: action.isScrollingUp };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
