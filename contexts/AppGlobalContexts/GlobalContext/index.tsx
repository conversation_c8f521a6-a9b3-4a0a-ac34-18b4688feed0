import * as React from "react";
import { safeLocalStorage } from "@/utils/local-storage";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { pricingAccordionListItems } from "@/constants/ListItems/accordionListItems";
import { useAuthentication } from "../AuthenticationContext";
import { globalStateReducer } from "./GlobalContextReducer";
import { getGlobalStateAction, initialGlobalStateAction } from "./GlobalContextAction";
import { GlobalContextState, GlobalContextType } from "./GlobalContextTypes";

const getDefaultState = (): GlobalContextState => {
  return {
    isScrollingUp: true,
    isNavigationMenuCollapsed: !!safeLocalStorage.getItem(LOCAL_STORAGE_KEY.NAVIGATION_MENU_COLLAPSED),
    settingsDrawerOpen: false,
    notificationsDrawerOpen: false,
    navAccordionExpandedIndex: Array.from({ length: pricingAccordionListItems.length }, (_, i) => i),
    agencyOptions: [],
  };
};

const GlobalContext = React.createContext<GlobalContextType | undefined>(undefined);

const GlobalContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(globalStateReducer, getDefaultState());
  const {
    state: { accessToken },
  } = useAuthentication();

  React.useEffect(() => {
    initialGlobalStateAction(state, dispatch);
  }, [accessToken]); // eslint-disable-line react-hooks/exhaustive-deps

  const value: GlobalContextType = React.useMemo(
    () => ({ state, action: getGlobalStateAction(state, dispatch) }),
    [state, dispatch]
  );

  if (!state.metadata) {
    return null;
  }

  return <GlobalContext.Provider value={value}>{children}</GlobalContext.Provider>;
};

const useGlobalContext = (): GlobalContextType => {
  const context = React.useContext(GlobalContext);
  if (context === undefined) {
    throw new Error("useGlobalContext must be used within a GlobalContextProvider");
  }
  return context;
};

export { GlobalContextProvider, useGlobalContext };
