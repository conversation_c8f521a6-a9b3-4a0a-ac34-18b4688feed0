import {
  CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  CA_Mastr_Api_v1_0_Models_Util_StringOptionExt,
} from "@/utils/openapi";
import { getGlobalStateAction } from "./GlobalContextAction";

export type GlobalContextType = {
  state: GlobalContextState;
  action: GlobalContextAction;
};

export type GlobalContextDispatch = (action: GlobalContextReducerAction) => void;

export type GlobalContextState = {
  isScrollingUp: boolean;
  isNavigationMenuCollapsed: boolean;
  settingsDrawerOpen: boolean;
  notificationsDrawerOpen: boolean;
  metadata?: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse;
  navAccordionExpandedIndex: number | number[];
  agencyOptions: CA_Mastr_Api_v1_0_Models_Util_StringOptionExt[];
};

export type GlobalContextReducerAction =
  | { type: "SET_METADATA"; metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse }
  | { type: "SET_AGENCY_OPTIONS"; agencyOptions: CA_Mastr_Api_v1_0_Models_Util_StringOptionExt[] }
  | { type: "OPEN_SETTINGS_DRAWER" }
  | { type: "CLOSE_SETTINGS_DRAWER" }
  | { type: "EXPAND_NAVIGATION_MENU" }
  | { type: "COLLAPSE_NAVIGATION_MENU" }
  | { type: "UPDATE_NAV_ACCORDION_EXPANDED_INDEX"; expandedIndex: number | number[] }
  | { type: "OPEN_NOTIFICATIONS_DRAWER" }
  | { type: "CLOSE_NOTIFICATIONS_DRAWER" }
  | { type: "SET_IS_SCROLLING_UP"; isScrollingUp: boolean };

export type GlobalContextAction = ReturnType<typeof getGlobalStateAction>;
