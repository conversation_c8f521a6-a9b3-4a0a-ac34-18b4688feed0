import Router from "next/router";
import { metadata<PERSON>elper } from "@/utils/metadata-helper";
import { AGENCY_OPTIONS_TO_DISPLAY } from "@/constants/enums";
import { GlobalContextReducerAction, GlobalContextState } from "./GlobalContextTypes";

export const initialGlobalStateAction = async (
  _state: GlobalContextState, // eslint-disable-line @typescript-eslint/no-unused-vars
  dispatch: React.Dispatch<GlobalContextReducerAction>
): Promise<void> => {
  // TODO Try catch
  const metadata = await metadataHelper.loadMetadata();
  dispatch({ type: "SET_METADATA", metadata });
  const tbaAgency = metadata?.basic_search_settings?.tba_agency;
  if (tbaAgency?.length) {
    dispatch({
      type: "SET_AGENCY_OPTIONS",
      agencyOptions: tbaAgency?.filter((agency) => AGENCY_OPTIONS_TO_DISPLAY.includes(agency.value)),
    });
  }
};

export const getGlobalStateAction = (
  state: GlobalContextState,
  dispatch: React.Dispatch<GlobalContextReducerAction>
) => {
  return {
    toggleSettingsDrawer: () => {
      if (state.settingsDrawerOpen) {
        dispatch({ type: "CLOSE_SETTINGS_DRAWER" });
      } else {
        dispatch({ type: "OPEN_SETTINGS_DRAWER" });
      }
    },
    toggleNotificationsDrawer: () => {
      if (state.notificationsDrawerOpen) {
        dispatch({ type: "CLOSE_NOTIFICATIONS_DRAWER" });
      } else {
        dispatch({ type: "OPEN_NOTIFICATIONS_DRAWER" });
      }
    },
    openPage: (url: string, opts: { isExternal: boolean } = { isExternal: false }) => {
      if (opts.isExternal) {
        window.open(url, "_blank");
      } else {
        Router.push(url);
      }
    },
    toggleNavigationMenu: () => {
      if (state.isNavigationMenuCollapsed) {
        dispatch({ type: "EXPAND_NAVIGATION_MENU" });
      } else {
        dispatch({ type: "COLLAPSE_NAVIGATION_MENU" });
      }
    },
    updateNavAccordionExpandedIndex: (expandedIndex: number | number[]) => {
      dispatch({ type: "UPDATE_NAV_ACCORDION_EXPANDED_INDEX", expandedIndex });
    },
    setIsScrollingUp: (isScrollingUp: boolean) => {
      dispatch({ type: "SET_IS_SCROLLING_UP", isScrollingUp });
    },
  };
};
