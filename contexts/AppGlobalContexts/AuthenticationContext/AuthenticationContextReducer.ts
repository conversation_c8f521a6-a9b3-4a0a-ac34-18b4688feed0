import * as Sentry from "@sentry/browser";
import { AuthenticationContextReducerAction, AuthenticationContextState } from "./AuthenticationContextTypes";

export const authenticationContextReducer = (
  state: AuthenticationContextState,
  action: AuthenticationContextReducerAction
): AuthenticationContextState => {
  switch (action.type) {
    case "SIGN_IN": {
      Sentry.setUser(action.data.userData.username ? { email: action.data.userData.username } : null);
      return { ...state, ...action.data };
    }
    case "SIGN_OUT": {
      Sentry.setUser(null);
      return { ...state, accessToken: undefined, userData: undefined };
    }
    case "CLEAR_ACCESS_TOKEN": {
      Sentry.setUser(null);
      return { ...state, accessToken: "" };
    }
    // default: {
    //   throw new Error(`Unhandled action type: ${action.type}`);
    // }
  }
};
