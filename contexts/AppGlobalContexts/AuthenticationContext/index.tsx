import * as React from "react";
import { authenticationContextReducer } from "./AuthenticationContextReducer";
import { getAuthenticationContextAction, initialAuthenticationContextAction } from "./AuthenticationContextAction";
import { AuthenticationContextState, AuthenticationContextType } from "./AuthenticationContextTypes";

const getDefaultState = (): AuthenticationContextState => {
  return {
    accessToken: "empty_token",
  };
};

const AuthenticationContext = React.createContext<AuthenticationContextType | undefined>(undefined);

const AuthenticationContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, dispatch] = React.useReducer(authenticationContextReducer, getDefaultState());

  React.useEffect(() => {
    initialAuthenticationContextAction(state, dispatch);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const value: AuthenticationContextType = React.useMemo(
    () => ({ state, action: getAuthenticationContextAction(state, dispatch) }),
    [state, dispatch]
  );

  return <AuthenticationContext.Provider value={value}>{children}</AuthenticationContext.Provider>;
};

const useAuthentication = (): AuthenticationContextType => {
  const context = React.useContext(AuthenticationContext);
  if (context === undefined) {
    throw new Error("useAuthentication must be used within a AuthenticationContextProvider");
  }
  return context;
};

export { AuthenticationContextProvider, useAuthentication };
