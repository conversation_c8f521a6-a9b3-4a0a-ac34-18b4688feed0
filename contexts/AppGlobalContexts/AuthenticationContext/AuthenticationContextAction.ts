import Router from "next/router";
import { localStorageManager } from "@chakra-ui/react";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import {
  _safeLocalStorage,
  inMemoryObjectStorage,
  inMemoryStorage,
  safeLocalStorage,
  safeSessionStorage,
} from "@/utils/local-storage";
import {
  AuthenticationService,
  CA_Mastr_Api_v1_0_Models_Auth_ConfirmForgotPasswordRequest,
  CA_Mastr_Api_v1_0_Models_Users_ChangePasswordRequestIn,
  OpenAPI,
  UserPreferenceService,
  UserService,
} from "@/utils/openapi";
import { getAPIErrorMessage } from "@/utils/helpers";
import { API_ROOT } from "@/utils/config";
import { showErrorToast, showSuccessToast, toast } from "@/design-system/theme/toast";
import { AuthenticationContextReducerAction, AuthenticationContextState } from "./AuthenticationContextTypes";

export const initialAuthenticationContextAction = async (
  _state: AuthenticationContextState,
  dispatch: React.Dispatch<AuthenticationContextReducerAction>
): Promise<void> => {
  OpenAPI.BASE = API_ROOT;

  const accessToken = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);

  if (accessToken) {
    OpenAPI.TOKEN = accessToken;

    try {
      const claimResponse = await AuthenticationService.getClaim();

      dispatch({
        type: "SIGN_IN",
        data: {
          accessToken: accessToken,
          userData: claimResponse,
        },
      });
    } catch {
      console.debug("Access token is invalid");
      dispatch({
        type: "CLEAR_ACCESS_TOKEN",
      });
      safeLocalStorage.removeItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
    }
  } else {
    dispatch({
      type: "CLEAR_ACCESS_TOKEN",
    });
  }
};

export const getAuthenticationContextAction = (
  _state: AuthenticationContextState,
  dispatch: React.Dispatch<AuthenticationContextReducerAction>
) => {
  return {
    signIn: async (token: string | undefined | null) => {
      if (!token) {
        throw Error("Token is invalid");
      }

      OpenAPI.TOKEN = token;
      try {
        const [claimResponseData, { user_preferences }] = await Promise.all([
          AuthenticationService.getClaim(),
          UserPreferenceService.getUserPreferences(),
        ]);

        user_preferences?.forEach((preference) =>
          _safeLocalStorage.setItem(preference.user_key as string, preference.user_value as string)
        );

        dispatch({
          type: "SIGN_IN",
          data: {
            accessToken: token,
            userData: claimResponseData,
          },
        });
        safeLocalStorage.setItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN, token);
        Router.push((Router.query.next as string) || "/");
      } catch (err) {
        showErrorToast("Error", getAPIErrorMessage(err));
      }
    },
    resetPassword: async (
      data: CA_Mastr_Api_v1_0_Models_Auth_ConfirmForgotPasswordRequest,
      cb: (hasFailed: boolean) => void
    ) => {
      try {
        const result = await AuthenticationService.postConfirmForgotPassword(data);
        if (!result) {
          throw Error("result is invalid");
        }
        cb(false);
      } catch (error) {
        cb(true);
        showErrorToast("Error", getAPIErrorMessage(error));
      }
    },
    changePassword: async (
      data: CA_Mastr_Api_v1_0_Models_Users_ChangePasswordRequestIn,
      cb: (hasFailed: boolean) => void
    ) => {
      try {
        const result = await UserService.postChangePassword(data);
        if (!result) {
          throw Error("result is invalid");
        }
        cb(false);
      } catch (error) {
        cb(true);
        showErrorToast("Error", getAPIErrorMessage(error));
      }
    },
    forgotPassword: async (userName: string) => {
      try {
        const result = await AuthenticationService.postForgotPassword(userName);
        if (!result) {
          throw Error("result is invalid");
        }
        showSuccessToast("Success", result.message ?? undefined);
        Router.push({ pathname: "/reset-password", query: { email: userName } });
      } catch (error) {
        showErrorToast("Error", getAPIErrorMessage(error));
      }
    },
    signOut: () => {
      const colorMode = localStorageManager.get();
      OpenAPI.TOKEN = "";
      safeLocalStorage.clear();
      safeSessionStorage.clear();
      inMemoryStorage.clear();
      inMemoryObjectStorage.clear();
      toast.closeAll();
      dispatch({ type: "SIGN_OUT" });
      colorMode && localStorageManager.set(colorMode);
    },
  };
};
