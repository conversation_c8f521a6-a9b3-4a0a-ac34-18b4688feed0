import { CA_Mastr_Api_v1_0_Models_Auth_ClaimResponse } from "@/utils/openapi";
import { getAuthenticationContextAction } from "./AuthenticationContextAction";

export type AuthenticationContextType = {
  state: AuthenticationContextState;
  action: AuthenticationContextAction;
};

export type AuthenticationContextDispatch = (action: AuthenticationContextReducerAction) => void;

export type AuthenticationContextState = {
  accessToken?: string;
  userData?: CA_Mastr_Api_v1_0_Models_Auth_ClaimResponse;
};

export type AuthenticationContextReducerAction =
  | {
      type: "SIGN_IN";
      data: {
        accessToken: string;
        userData: CA_Mastr_Api_v1_0_Models_Auth_ClaimResponse;
      };
    }
  | { type: "SIGN_OUT" }
  | { type: "CLEAR_ACCESS_TOKEN" };

export type AuthenticationContextAction = ReturnType<typeof getAuthenticationContextAction>;
