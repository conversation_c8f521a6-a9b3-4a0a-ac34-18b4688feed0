import React, { useState } from "react";
import ConfirmationModalPopup, { ConfirmationModalPopupProps } from "@/design-system/organisms/ConfirmationModalPopup";
import { ConfirmParams, ConfirmationContextType, voidFunction } from "./ConfirmationContextTypes";

const DEFAULT_TEXT = "Are you sure?";

export const ConfirmationContext = React.createContext<ConfirmationContextType | undefined>(undefined);

const ConfirmationContextProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [state, setState] = useState<ConfirmationModalPopupProps>({
    headerText: DEFAULT_TEXT,
    isOpen: false,
    onConfirm: voidFunction,
    onCancel: voidFunction,
  });

  function confirm(params: ConfirmParams) {
    const { headerText: txt, ...restParams } = params;
    const headerText = txt ?? state.headerText;

    return new Promise((resolve, reject) => {
      setState({
        ...restParams,
        headerText,
        isOpen: !state.isOpen,
        onConfirm: () => {
          setState({ ...state, isOpen: false, headerText });
          resolve(true);
        },
        onCancel: (reason?: string) => {
          setState({ ...state, isOpen: false, headerText });
          reject(reason);
        },
      });
    });
  }

  const context: ConfirmationContextType = {
    confirm: confirm,
  };

  return (
    <ConfirmationContext.Provider value={context}>
      <ConfirmationModalPopup showCloseIcon={false} {...state} />
      {children}
    </ConfirmationContext.Provider>
  );
};

const useConfirmation = (): ConfirmationContextType => {
  const context = React.useContext(ConfirmationContext);
  if (context === undefined) {
    throw new Error("usePricerModule must be used within a PricerModuleContextProvider");
  }
  return context;
};

export { ConfirmationContextProvider, useConfirmation };
