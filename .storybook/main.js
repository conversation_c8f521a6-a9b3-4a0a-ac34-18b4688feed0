const path = require("path");
module.exports = {
  stories: ["../design-system/**/*.stories.tsx"],
  webpackFinal: async (config) => {
    config.resolve.alias = {
      "@": path.resolve(__dirname, "../"),
    };
    return config;
  },
  addons: ["@storybook/addon-essentials", "@chakra-ui/storybook-addon"],
  features: {
    emotionAlias: false,
  },
  docs: {
    autodocs: true,
  },
  framework: {
    name: "@storybook/nextjs",
    options: {},
  },
};
