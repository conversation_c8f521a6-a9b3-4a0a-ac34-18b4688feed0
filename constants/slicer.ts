import { OperatorsType, SlicerConfigFormat, SlicerDataSetOptions } from "@/types/slicer";
import { CA_Mastr_Models_v1_0_Models_ConfigurationType, CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";

export const SlicerDataSetDropdownOptions = [
  {
    id: 0,
    value: SlicerDataSetOptions.RESIDENTIAL_LOAN_LEVEL,
    displayValue: "Agency Residential Loan-Level",
  },
  {
    id: 1,
    value: SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL,
    displayValue: "Agency Multi-Family Loan-Level",
  },
];

export const DimensionsWithSecondaryOptionLabel = ["TICKER", "POOLTYPE"];

export const CREATABLE_WITH_BOND_VALIDATION = ["BONDNAME", "POOLNUMBER"];

export const COLUMNS_DEPENDANT_ON_FACTOR_DATE = ["3m", "6m", "12m", "24m"];

export const formatsWithDatePicker: string[] = [SlicerConfigFormat.YEAR, SlicerConfigFormat.YEAR_MONTH];

export const operatorSymbols = {
  [CA_Mastr_Models_v1_0_Models_Operator.VECTOR]: "V",
  [CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO]: "=",
  [CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO]: "<=",
  [CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN]: "<",
  [CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO]: ">=",
  [CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN]: ">",
  [CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE]: "[ , ]",
  [CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE]: "[ , )",
  [CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE]: "( , ]",
  [CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF]: "{ }",
  [CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE]: "{~}",
  [CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF]: "{–}",
};

export const WITHIN_RANGE_INPUTS = [
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE,
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE,
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE,
];

export const operatorMetaData = {
  [CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF],
    displayName: "Element Of",
    value: CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO],
    displayName: "Equal To",
    value: CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE],
    displayName: "Element Like",
    value: CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF],
    displayName: "Not An Element Of",
    value: CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE],
    displayName: "Within Range",
    value: CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE],
    displayName: "Within Range Left Inclusive",
    value: CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE],
    displayName: "Within Range Right Inclusive",
    value: CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN],
    displayName: "Less Than",
    value: CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN],
    displayName: "Greater Than",
    value: CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO],
    displayName: "Less Than Or Equal To",
    value: CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO],
    displayName: "Greater Than Or Equal To",
    value: CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.VECTOR]: {
    symbol: operatorSymbols[CA_Mastr_Models_v1_0_Models_Operator.VECTOR],
    displayName: "Vector",
    value: CA_Mastr_Models_v1_0_Models_Operator.VECTOR,
  },
  [CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK]: {
    symbol: "LB",
    displayName: "Look Back",
    value: CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK,
  },
};

export const getOperators = (
  configType: string | null | undefined,
  opt: {
    isVector?: boolean;
    excludeInclusiveOperators?: boolean;
    excludeNotElementOf?: boolean;
    includeLookBack?: boolean;
  } = {}
): OperatorsType[] => {
  const { isVector, excludeInclusiveOperators, excludeNotElementOf, includeLookBack } = opt;

  let output: (OperatorsType | false)[] = [];

  switch (configType) {
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.MULTIPLE:
      output = [
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO],
        !excludeNotElementOf && operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF],
      ];
      break;
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST:
      output = [
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF],
        !excludeNotElementOf && operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF],
      ];
      break;
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE:
      output = [
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE],
        !excludeNotElementOf && operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF],
      ];
      break;
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_SINGLE:
      output = [operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO]];
      break;
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.SINGLE:
      output = [operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO]];
      break;
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY:
      output = [
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO],
      ];
      break;
    case CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE:
      output = [
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE],
        !excludeInclusiveOperators &&
          operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE],
        !excludeInclusiveOperators &&
          operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO],
        !!isVector && operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.VECTOR],
        !!includeLookBack && operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK],
      ];
      break;
    default:
      output = [
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO],
        operatorMetaData[CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO],
      ];
      break;
  }

  return output.filter(Boolean) as OperatorsType[];
};

export const operatorPrecedence = [
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE,
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE,
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE,
  CA_Mastr_Models_v1_0_Models_Operator.VECTOR,
  CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
  CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE,
  CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF,
  CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO,
  CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN,
  CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN,
  CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO,
  CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO,
];

// set "null" to remove max restriction
export const MAX_SLICER_TABS_ALLOWED = 5;

export const ConfigLevelPrecedence = ["Pool Level", "Loan Level"];

export const LATEST_DATE = "@LATEST";

export const DimensionsSupportingLatest = ["FACTORDATE", "ORIGMONTH", "ISSUEMONTH", "ORIGYEAR", "ISSUEYEAR"];

export const SLICER_ROW_HEIGHT = 24;

export const DEFAULT_PREPAY_COLUMNS = [
  { value: "Scham Balance", displayValue: "Scham Balance", id: "scham-balance" },
  { value: "Current Balance", displayValue: "Current Balance", id: "current-balance" },
  { value: "Loan Count", displayValue: "Loan Count", id: "loan-count" },
];

export const DEFAULT_ISSUANCE_COLUMNS = [
  { value: "Issuance Balance", displayValue: "Issuance Balance", id: "issuance-balance" },
  { value: "Loan Count", displayValue: "Loan Count", id: "loan-count" },
];

export const CPR_DEFAULT_COLUMN = [{ dimension: "CPR", values: ["1m"], enabled: true }];

export const MINIMUM_BALANCE_MULTIPLIER = 1000000;
export const DEFAULT_PREPAY_MINIMUM_BALANCE = 1000;
export const DEFAULT_ISSUANCE_MINIMUM_BALANCE = 1;
