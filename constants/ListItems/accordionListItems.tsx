import { IoConstruct, IoDocumentTextOutline, IoLogoUsd } from "react-icons/io5";
import { HiOutlineDocumentReport } from "react-icons/hi";
import { BiLineChart } from "react-icons/bi";
import { AccordionListItems } from "@/types";
import CollateralIcon from "@/design-system/icons/collateral";

export const pricingAccordionListItems: AccordionListItems[] = [
  {
    id: 1,
    title: "PRICING",
    key: "pricer/pricing",
    icon: IoLogoUsd,
    items: [
      {
        key: "dashboard",
        href: "/pricer/pricing/dashboard",
        text: "Dashboard",
      },
      {
        key: "matrix",
        href: "/pricer/pricing/matrix",
        text: "Matrix",
      },
      {
        key: "scenario",
        href: "/pricer/pricing/scenario",
        text: "Scenario",
      },
      {
        key: "total",
        href: "",
        text: "Total Return",
      },
      {
        key: "cash-flows",
        href: "/pricer/pricing/cash-flows",
        text: "Cash Flows",
      },
      {
        key: "comparison",
        href: "/pricer/pricing/comparison",
        text: "Comparison",
      },
      {
        key: "repline",
        href: "/pricer/pricing/repline",
        text: "Repline",
      },
    ],
  },
  {
    id: 2,
    title: "MODEL",
    key: "pricer/model",
    icon: BiLineChart,
    items: [
      {
        key: "tracking",
        href: "/pricer/model/tracking",
        text: "Tracking",
      },
      {
        key: "projection-summary",
        href: "/pricer/model/projection",
        text: "Projection Summary",
      },
      {
        key: "projection-detail",
        href: "/pricer/model/projection-detail",
        text: "Projection Detail",
      },
    ],
  },
  {
    id: 3,
    title: "COLLATERAL",
    key: "pricer/collateral",
    icon: CollateralIcon,
    items: [
      {
        key: "pool-distribution",
        href: "/pricer/collateral/pool-distribution",
        text: "Pool Distribution",
      },
      {
        key: "loan-distribution",
        href: "/pricer/collateral/loan-distribution",
        text: "Loan Distribution",
      },
      {
        key: "pool-list",
        href: "/pricer/collateral/pool-list",
        text: "Pool List",
      },
      {
        key: "loan-list",
        href: "/pricer/collateral/loan-list",
        text: "Loan List",
      },
      {
        key: "empirical-tracking",
        href: "/pricer/collateral/empirical-tracking",
        text: "Empirical Tracking",
      },
      {
        key: "empirical-projection",
        href: "",
        text: "Empirical Projection",
      },
    ],
  },
  {
    id: 4,
    title: "UTILITIES",
    key: "pricer/utilities",
    icon: IoConstruct,
    items: [
      {
        key: "model_dials",
        href: "/pricer/utilities/model-dials",
        text: "Model Dials",
      },
      {
        key: "prepay_vector",
        href: "/pricer/utilities/prepay-vector",
        text: "Prepay Vector",
      },
      {
        key: "tools",
        href: "/pricer/utilities/tools",
        text: "Tools",
      },
    ],
  },
  {
    id: 6,
    title: "REPORTS",
    key: "pricer/reports",
    icon: IoDocumentTextOutline,
    items: [
      {
        key: "prepayment_tracking",
        href: "/pricer/reports/prepayment-tracking",
        text: "Prepayment Tracking",
      },
    ],
  },
];

export const marketDataAccordionListItems: AccordionListItems[] = [
  {
    id: 1,
    title: "MARKET",
    key: "pricer/market",
    icon: HiOutlineDocumentReport,
    items: [
      {
        key: "interest_rates",
        href: "/market/interest-rates",
        text: "Interest Rates",
      },
      {
        key: "rate-projections",
        href: "/market/rate-projections",
        text: "Rate Projections",
      },
      {
        key: "tba",
        href: "/market/tba",
        text: "TBA",
      },
      {
        key: "hedge-ratio",
        href: "/market/hedge-ratio",
        text: "Hedge Ratio",
      },
      {
        key: "futures",
        href: "/market/futures",
        text: "Futures",
      },
      {
        key: "performance",
        href: "/market/performance",
        text: "Performance",
      },
      { key: "volatility", href: "/market/volatility", text: "Volatility" },
      // { key: "volatility-cube", href: "/market/volatility-cube", text: "Volatility Cube" },
    ],
  },
];
