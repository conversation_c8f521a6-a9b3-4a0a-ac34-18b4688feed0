import { MdInvertColors } from "react-icons/md";
import { FiActivity } from "react-icons/fi";
import { HiOutlineDocumentReport } from "react-icons/hi";
import { GoIssueTracks } from "react-icons/go";

import { PortfolioIcon, PricerIcon, SlicerIcon } from "@/design-system/icons";

export type AppNames =
  | "pricer"
  | "slicer"
  | "portfolio"
  | "profile"
  | "activity-stream"
  | "color"
  | "audit-history"
  | "market"
  | "empirical-tracking";

export type moduleItemType = {
  value: AppNames;
  text: string;
  href: string;
  // The type AsComponent is any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  icon: any;
};

type moduleDropDownOptionTypes = {
  key: string;
  items: Array<moduleItemType>;
};

export const moduleDropdownOptions: moduleDropDownOptionTypes = {
  key: "module",
  items: [
    {
      value: "pricer",
      text: "PRICER",
      href: "/#",
      icon: PricerIcon,
    },
    {
      value: "slicer",
      text: "SLICER",
      href: "/slicer",
      icon: SlicerIcon,
    },
    {
      value: "portfolio",
      text: "PORTFOLIO",
      href: "/portfolio",
      icon: PortfolioIcon,
    },
    {
      value: "color",
      text: "COLOR",
      href: "/color",
      icon: MdInvertColors,
    },
    {
      value: "activity-stream",
      text: "ACTIVITY",
      href: "/activity-stream/run",
      icon: FiActivity,
    },
    {
      value: "market",
      text: "MARKET",
      href: "/market/interest-rates",
      icon: HiOutlineDocumentReport,
    },
    {
      value: "empirical-tracking",
      text: "EMPIRICAL TRACKING",
      href: "/empirical-tracking",
      icon: GoIssueTracks,
    },
  ],
};
