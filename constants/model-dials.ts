import { SelectOptionType } from "@/design-system/molecules/CASelectDropdown";
import {
  CA_Mastr_Models_v1_0_Models_Operation,
  CA_Mastr_Models_v1_0_Models_SubmodelDialName as SubModelDials,
} from "@/utils/openapi";

export const defaultOperationValues = {
  [SubModelDials.FINAL_PREPAY_RATE]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.ALL_MORTGAGE_RATE]: CA_Mastr_Models_v1_0_Models_Operation.ADD,
  [SubModelDials.ALL_MIP]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.ALL_GEO_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.REFINANCING_PREPAY_RATE]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.REFINANCING_INCENTIVE]: CA_Mastr_Models_v1_0_Models_Operation.ADD,
  [SubModelDials.REFINANCING_BURNOUT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.REFINANCING_MEDIA_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.REFINANCING_SERVICER_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.REFINANCING_GEO_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.CASHOUT_PREPAY_RATE]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.CASHOUT_SERVICER_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.CASHOUT_GEO_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.TURNOVER_PREPAY_RATE]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.TURNOVER_LOCKIN]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.TURNOVER_SERVICER_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.TURNOVER_GEO_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.INVOLUNTARY_DEFAULT_RATE]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.INVOLUNTARY_GEO_EFFECT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.CURTAILMENT_PREPAY_RATE]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.GPL_TAX_CREDIT]: CA_Mastr_Models_v1_0_Models_Operation.ADD,
  [SubModelDials.GPL_REFINANCING_PENALTY]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.GPL_TURNOVER_PENALTY]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.GPL_TURNOVER_FORCE_PREPAYMENT]: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY,
  [SubModelDials.INDEX_RATE]: CA_Mastr_Models_v1_0_Models_Operation.ADD,
};

const MULTIPLY = { id: 0, value: CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY, displayValue: "Multiply" };
const ADD = { id: 1, value: CA_Mastr_Models_v1_0_Models_Operation.ADD, displayValue: "Add" };
const SUBTRACT = { id: 2, value: CA_Mastr_Models_v1_0_Models_Operation.SUBTRACT, displayValue: "Subtract" };
const CAP = { id: 3, value: CA_Mastr_Models_v1_0_Models_Operation.CAP, displayValue: "Cap" };
const FLOOR = { id: 4, value: CA_Mastr_Models_v1_0_Models_Operation.FLOOR, displayValue: "Floor" };
const EQUAL = { id: 5, value: CA_Mastr_Models_v1_0_Models_Operation.EQUAL, displayValue: "Equal" };

export const allOperations = [MULTIPLY, ADD, SUBTRACT, CAP, FLOOR, EQUAL];

export const operation: { [key: string]: SelectOptionType[] } = {
  [SubModelDials.ALL_GEO_EFFECT]: [MULTIPLY],
  [SubModelDials.REFINANCING_GEO_EFFECT]: [MULTIPLY],
  [SubModelDials.CASHOUT_GEO_EFFECT]: [MULTIPLY],
  [SubModelDials.TURNOVER_GEO_EFFECT]: [MULTIPLY],
  [SubModelDials.INVOLUNTARY_GEO_EFFECT]: [MULTIPLY],
  [SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT]: [MULTIPLY],
  [SubModelDials.REFINANCING_SERVICER_EFFECT]: [MULTIPLY],
  [SubModelDials.CASHOUT_SERVICER_EFFECT]: [MULTIPLY],
  [SubModelDials.TURNOVER_SERVICER_EFFECT]: [MULTIPLY],
};
