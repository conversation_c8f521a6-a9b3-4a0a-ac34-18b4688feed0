import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import {
  CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity,
  CA_Mastr_Models_v1_0_Models_VolCalibrationStrike,
  CA_Mastr_Models_v1_0_Models_VolCalibrationTerm,
} from "@/utils/openapi";

export const MATURITY_OPTIONS = [
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.THREE_MONTH, label: "3M" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.SIX_MONTH, label: "6M" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.ONE_YEAR, label: "1Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.TWO_YEAR, label: "2Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.THREE_YEAR, label: "3Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.FOUR_YEAR, label: "4Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.FIVE_YEAR, label: "5Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.SEVEN_YEAR, label: "7Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.TEN_YEAR, label: "10Y", isDefault: true },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.FIFTEEN_YEAR, label: "15Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity.TWENTY_YEAR, label: "20Y" },
  { value: "ALL", label: "ALL" },
];

export const SWAP_TERM_OPTIONS = [
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.ONE_YEAR, label: "1Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.TWO_YEAR, label: "2Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.THREE_YEAR, label: "3Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.FOUR_YEAR, label: "4Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.FIVE_YEAR, label: "5Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.SEVEN_YEAR, label: "7Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.TEN_YEAR, label: "10Y", isDefault: true },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.FIFTEEN_YEAR, label: "15Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.TWENTY_YEAR, label: "20Y" },
  { value: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm.THIRTY_YEAR, label: "30Y" },
  { value: "ALL", label: "ALL" },
];

export const STRIKE_SHIFTS_OPTIONS = [
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.M100,
    label: "-100",
  },
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.M50,
    label: "-50",
  },
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.M25,
    label: "-25",
  },
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.BASE,
    label: "0",
    isDefault: true,
  },
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.P25,
    label: "+25",
  },
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.P50,
    label: "+50",
  },
  {
    value: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike.P100,
    label: "+100",
  },
  { value: "ALL", label: "ALL" },
];

export const X_AXIS_OPTIONS = [
  {
    displayValue: "Option Maturity",
    value: "maturity",
  },
  {
    displayValue: "Swap Term",
    value: "term",
  },
  {
    displayValue: "Strike Shift",
    value: "strike",
  },
] as const;

export const useModelTypeVolatilityOptions = ({ withMarket = true }) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const modelTypeOptions = [
    ...(metadata?.market_data_settings?.interest_rate_model || []),
    ...(withMarket ? metadata?.market_data_settings?.market_type || [] : []),
  ];

  return [
    ...modelTypeOptions.map((el) => ({
      ...el,
      displayValue: el.display_value,
      label: el.display_value,
      id: el.value,
    })),
  ] as { displayValue: string; value: string; id: string; label: string }[];
};

export const COLUMN_OPTIONS = [
  {
    id: "maturity",
    displayValue: "Option Maturity",
    value: "maturity",
  },
  {
    id: "term",
    displayValue: "Swap Term",
    value: "term",
  },
  {
    id: "strike",
    displayValue: "Strike Shift",
    value: "strike",
  },
] as const;
