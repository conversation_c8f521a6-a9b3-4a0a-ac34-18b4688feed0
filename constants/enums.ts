import { APP_DISPLAY_NAME } from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_Application, CA_Mastr_Models_v1_0_Models_Page } from "@/utils/openapi";
import { AppNames } from "./ListItems/dropdownOptions";

export const BOND_TYPE = {
  ALL: "All",
  COHORTS: "Cohorts",
  TBA: "TBA",
};

export const AGENCY_GPL = "GPL";

export const appsWithoutLeftNavigation: AppNames[] = [
  "slicer",
  "profile",
  "activity-stream",
  "color",
  "audit-history",
  "empirical-tracking",
  "portfolio",
];

export const pagesWithoutSecurity: CA_Mastr_Models_v1_0_Models_Page[] = [
  CA_Mastr_Models_v1_0_Models_Page.SLICER,
  CA_Mastr_Models_v1_0_Models_Page.RATE_PROJECTIONS,
];

export enum VALID_APPLICATIONS {
  PRICER = CA_Mastr_Models_v1_0_Models_Application.PRICER,
  SLICER = CA_Mastr_Models_v1_0_Models_Application.SLICER,
  MARKET_DATA = CA_Mastr_Models_v1_0_Models_Application.MARKET_DATA,
  EMPIRICAL_TRACKING = CA_Mastr_Models_v1_0_Models_Application.EMPIRICAL_TRACKING,
}

type AppRoutesType = {
  [key in VALID_APPLICATIONS]: {
    [key in CA_Mastr_Models_v1_0_Models_Page]?: string;
  };
};

export const APP_ROUTES_V2: AppRoutesType = {
  [VALID_APPLICATIONS.PRICER]: {
    [CA_Mastr_Models_v1_0_Models_Page.DASHBOARD]: "/pricer/pricing/dashboard",
    [CA_Mastr_Models_v1_0_Models_Page.MATRIX]: "/pricer/pricing/matrix",
    [CA_Mastr_Models_v1_0_Models_Page.CASHFLOW]: "/pricer/pricing/cash-flows",
    [CA_Mastr_Models_v1_0_Models_Page.COMPARISON]: "/pricer/pricing/comparison",
    [CA_Mastr_Models_v1_0_Models_Page.TRACKING]: "/pricer/model/tracking",
    [CA_Mastr_Models_v1_0_Models_Page.PROJECTION_SUMMARY]: "/pricer/model/projection",
    [CA_Mastr_Models_v1_0_Models_Page.PROJECTION_DETAIL]: "/pricer/model/projection-detail",
    [CA_Mastr_Models_v1_0_Models_Page.SCENARIO]: "/pricer/pricing/scenario",
    [CA_Mastr_Models_v1_0_Models_Page.REPLINE]: "/pricer/pricing/repline",
    [CA_Mastr_Models_v1_0_Models_Page.LOAN_DISTRIBUTION]: "/pricer/collateral/loan-distribution",
    [CA_Mastr_Models_v1_0_Models_Page.POOL_DISTRIBUTION]: "/pricer/collateral/pool-distribution",
    [CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING]: "/pricer/collateral/empirical-tracking",
  },
  [VALID_APPLICATIONS.SLICER]: {
    [CA_Mastr_Models_v1_0_Models_Page.SLICER]: "/slicer",
  },
  [VALID_APPLICATIONS.MARKET_DATA]: {
    [CA_Mastr_Models_v1_0_Models_Page.RATE_PROJECTIONS]: "/market/rate-projections",
  },
  [VALID_APPLICATIONS.EMPIRICAL_TRACKING]: {
    [CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING]: "/empirical-tracking",
  },
};

export const textFieldWithNumValidation = [
  0,
  1,
  2,
  3,
  4,
  5,
  6,
  7,
  8,
  9,
  ".",
  "Backspace",
  "Tab",
  "ArrowLeft",
  "ArrowRight",
].map(String);

export const defaultAppRoutes: { [key in APP_DISPLAY_NAME]?: string } = {
  Pricer: "/pricer/pricing/dashboard",
  Portfolio: "/portfolio",
};

export const AGENCY_OPTIONS_TO_DISPLAY = ["FNMA", "GNMAII", "FNCI"];

export const CURVE_SHIFT_OPTIONS = [-75, -50, -25, 0, 25, 50, 75];
