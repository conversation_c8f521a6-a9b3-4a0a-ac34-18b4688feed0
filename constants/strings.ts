const currYear = new Date().getFullYear();

const S = {
  APPLICATION: {
    TITLE: "MASTR - Cello Analytics",
    NAME: "MASTR",
  },
  HEADER: {},
  FOOTER: {
    COPYRIGHT: `© ${currYear} Cello Analytics. All rights reserved.`,
    LINK: "https://www.celloanalytics.com",
  },
  PAGES: {
    LOGIN: {
      EMAIL: "Email *",
      PASSWORD: "Password *",
    },
    RESETPASSWORD: {
      EMAIL: "Email *",
      PASSWORD: "New Password *",
      CONFIRM_PASSWORD: "Confirm Password *",
      CONFIRMATION_CODE: "Enter Code *",
    },
    FORGOTPASSWORD: {
      EMAIL: "Email *",
    },
    DASHBOARD: {
      TITLE: "Dashboard",
    },
    USERPROFILE: {
      TITLE: "User Profile",
      MY_PROFILE: {
        TITLE: "MY PROFILE",
        EMAIL: "Email",
        FIRST_NAME: "First Name",
        LAST_NAME: "Last Name",
      },
      CHANGE_PASSWORD: {
        TITLE: "CHANGE PASSWORD",
        LOADING: "SAVING...",
        PASSWORDS_NOT_MATCH: "Passwords do not match!",
        PASSWORD_CHANGED_SUCCESSFULLY: "Password changed successfully!",
        CURRENT_PASSWORD: "Current Password",
        PASSWORD: "New Password",
        CONFIRM_PASSWORD: "Confirm Password",
      },
    },
    PRICER: {},
    COLOR: {
      RUN_CONFIRMATION: {
        HEADER: "Color",
        DESCRIPTION: "Do you want to associate color with your new run?",
      },
    },
  },
  MODULES: {
    PRICER: {
      TITLE: "Pricer",
      ENTER_BOND: "Enter Bond (Ctrl + B)",
      RUN: "Run (Ctrl + Enter)",
      LOAD_BOND: "Load Bond",
      INFO_NOT_AVAILABLE: "The information you requested is not available.",
      STOP: "Stop",
    },
    SLICER: {
      TITLE: "Slicer",
      ENTER_QUERY: "Search Query (Ctrl + B)",
      RUN: "Run (Ctrl + Enter)",
      NO_QUERIES: "No Queries selected",
      NO_QUERIES_DESC: "Select queries and click ▷ to run results.",
      INFO_NOT_AVAILABLE: "The information you requested is not available.",
    },
    MARKET_DATA: {
      TITLE: "Market Data",
      RUN: "Run",
      NO_QUERIES: "No Queries selected",
      NO_QUERIES_DESC: "Select queries and click ▷ to run results.",
      INFO_NOT_AVAILABLE: "The information you requested is not available.",
    },
    EMPIRICAL_TRACKING: {
      TITLE: "Empirical Tracking",
      RUN: "Run",
      NO_QUERIES: "No Queries selected",
      NO_QUERIES_DESC: "Select queries and click ▷ to run results.",
      INFO_NOT_AVAILABLE: "The information you requested is not available.",
    },
  },
  COMMON: {
    NEW: "New",
    SAVE: "Save",
    SAVE_AS: "Save As",
    DUPLICATE: "Duplicate",
    DELETE: "Delete",
    SHARE: "Share",
    RENAME: "Rename",
    RESET: "Reset",
    RESET_TO_DEFAULT: "Reset to Default",
    RESET_TO_LAST_SAVED: "Reset to Last Saved",
    SAVE_AND_CLOSE: "Save & Close",
    DELETE_CONFIRMATION: {
      HEADER: "Delete Data?",
      DESCRIPTION: "By doing this you will lose the data and will not be able to retrieve it.",
    },
    QUIT_EDITING_CONFIRMATION: {
      HEADER: "Quit Editing?",
      DESCRIPTION: "Changes you made so far will not be saved.",
    },
  },
};

export default S;
