// local storage
enum LOCAL_STORAGE_KEY {
  ACCESS_TOKEN = "ca.login.ACCESS_TOKEN",
  NAVIGATION_MENU_COLLAPSED = "ca.global.NAVIGATION_MENU_COLLAPSED",
  DASHBOARD_PERSISTED_INPUTS = "ca.PRICER.DASHBOARD_PERSISTED_INPUTS",
  SCENARIO_PERSISTED_INPUTS = "ca.PRICER.SCENARIO_PERSISTED_INPUTS",
  PREPAY_TRACKING_PERSISTED_INPUTS = "ca.PRICER.PREPAY_TRACKING_PERSISTED_INPUTS",
  LOAN_POOL_DISTRIBUTION_CARD_ORDERING = "ca.LOAN_POOL_DISTRIBUTION_CARD_ORDERING",
  LOAN_POOL_DISTRIBUTION_VIEW = "ca.LOAN_POOL_DISTRIBUTION_VIEW",
  LOAN_POOL_DISTRIBUTION_GPL_VIEW = "ca.LOAN_POOL_DISTRIBUTION_GPL_VIEW",
  COLLAPSED_CARDS = "ca.COLLAPSED_CARDS",
  PRICER_REPLINE_MODEL_DIALS = "ca.PRICER_REPLINE_MODEL_DIALS",
  PRICER_REPLINE_EDITED_CELL = "ca.PRICER_REPLINE_EDITED_CELL",
  GRID_COLUMN_STATE = "ca.GRID_COLUMN_STATE",
  MARKET_DATA_USER_SETTINGS = "ca.MARKET_DATA_USER_SETTINGS",
  PRICER_USER_SETTINGS = "ca.PRICER.PRICER_USER_SETTINGS",
  SLICER_USER_SETTINGS = "ca.SLICER.SLICER_USER_SETTINGS",
  SLICER_VIEW_MODE = "ca.SLICER.SLICER_VIEW_MODE",
  LAST_LOADED_VIEW = "ca.GRID_LAST_LOADED_VIEW",
  GRID_CHART_VIEW = "ca.GRID_CHART_VIEW",
  AD_HOC_DIAL = "ca.AD_HOC_DIAL",
  AD_HOC_VECTOR = "ca.AD_HOC_VECTOR",
  MODEL_DIAL_VIEW_MODE = "ca.MODEL_DIAL_VIEW_MODE",
  EMPIRICAL_TRACKING_USER_SETTINGS = "ca.EMPIRICAL_TRACKING_USER_SETTINGS",
}
export type LOCAL_STORAGE_KEY_TYPE =
  | `${LOCAL_STORAGE_KEY}`
  | `ca.LOAN_POOL_DISTRIBUTION_CARD_ORDERING_${string}_${string}`
  | `ca.GRID_COLUMN_STATE_${string}`
  | `ca.PRICER_REPLINE_MODEL_DIALS_${string}_${string}_${string}`
  | `ca.PRICER.BOND_COMPARE_COLLATERAL_ATTRIBUTE_USER_CATEGORY_v1_${string}`
  | `ca.PRICER_REPLINE_EDITED_CELL_${string}_${string}_${string}`
  | `ca.GRID_LAST_LOADED_VIEW_${string}`
  | `ca.GRID_CHART_VIEW_${string}`
  | `ca.CHART_EXPANDED_STATE_${string}`;

// session storage
enum SESSION_STORAGE_KEY {
  PRICER_USER_SETTINGS = "ca.PRICER.PRICER_USER_SETTINGS",
  EMPIRICAL_TRACKING_USER_SETTINGS = "ca.EMPIRICAL_TRACKING_USER_SETTINGS",
  DASHBOARD_LAST_RUN_ID = "ca.DASHBOARD_LAST_RUN_ID",
  MATRIX_LAST_RUN_ID = "ca.MATRIX_LAST_RUN_ID",
  SCENARIO_LAST_RUN_ID = "ca.SCENARIO_LAST_RUN_ID",
  CASHFLOW_LAST_RUN_ID = "ca.CASHFLOW_LAST_RUN_ID",
  REPLINE_LAST_RUN_ID = "ca.REPLINE_LAST_RUN_ID",
  PROJECTION_DETAIL_LAST_RUN_ID = "ca.PROJECTION_DETAIL_LAST_RUN_ID",
  POOL_DISTRIBUTION_INPUTS = "ca.POOL_DISTRIBUTION_INPUTS",
  POOL_LIST_INPUTS = "ca.POOL_LIST_INPUTS",
  LOAN_DISTRIBUTION_INPUTS = "ca.LOAN_DISTRIBUTION_INPUTS",
  LOAN_LIST_INPUTS = "ca.LOAN_LIST_INPUTS",
  PROJECTION_LAST_RUN_ID = "ca.PROJECTION_LAST_RUN_ID",
  TRACKING_LAST_RUN_ID = "ca.TRACKING_LAST_RUN_ID",
  PERFORMANCE_INPUTS = "ca.PERFORMANCE_INPUTS",
  VOLATILITY_INPUTS = "ca.VOLATILITY_INPUTS",
  VOLATILITY_CUBE_INPUTS = "ca.VOLATILITY_CUBE_INPUTS",
  RATE_PROJECTION_INPUTS = "ca.RATE_PROJECTION_INPUTS",
  INTEREST_RATES_INPUTS = "ca.INTEREST_RATES_INPUTS",
  PERFORMANCE_TSYF_CONTRACT = "ca.PERFORMANCE_TSYF_CONTRACT",
  TBA_INPUTS = "ca.TBA_INPUTS",
  EDFANDFUTURES_INPUTS = "ca.EDFANDFUTURES_INPUTS",
  EDFANDFUTURES_DESCRIPTION = "ca.EDFANDFUTURES_DESCRIPTION",
  HEDGE_RATIO_INPUTS = "ca.HEDGE_RATIO_INPUTS",
  PREPAY_TRACKING_INPUTS = "ca.PREPAY_TRACKING_INPUTS",
  LAST_LOADED_BOND = "ca.LAST_LOADED_BOND",
  SLICER_USER_SETTINGS = "ca.SLICER.SLICER_USER_SETTINGS",
  LOAN_POOL_DISTRIBUTION_CARD_ORDERING = "ca.LOAN_POOL_DISTRIBUTION_CARD_ORDERING",
  BOND_COMPARE_LAST_RUN_ID = "ca.PRICER.BOND_COMPARE_LAST_RUN_ID",
  RATE_PROJECTIONS_LAST_RUN_ID = "ca.PRICER.RATE_PROJECTIONS_LAST_RUN_ID",
  BOND_COMPARE_SECONDARY_SECURITY = "ca.PRICER.BOND_COMPARE_SECONDARY_SECURITY",
  TAG_FILTER_OPERATOR = "ca.TAG_FILTER_OPERATOR",
  EMPIRICAL_TRACKING_LAST_RUN_ID = "ca.EMPIRICAL_TRACKING_LAST_RUN_ID",
  EMPIRICAL_TRACKING_LAST_QUERY_ID = "ca.EMPIRICAL_TRACKING_LAST_QUERY_ID",
  MARKET_DATA_USER_SETTINGS = "ca.MARKET_DATA_USER_SETTINGS",
  PORTFOLIO_SETTINGS = "ca.PORTFOLIO_SETTINGS",
}
export type SESSION_STORAGE_KEY_TYPE =
  | `${SESSION_STORAGE_KEY}`
  | `ca.SLICER.SLICER_CHART_CONFIG_${number | string}`
  | `${SESSION_STORAGE_KEY.TAG_FILTER_OPERATOR}_${string}`;

// in memory storage
enum IN_MEMORY_STORAGE_KEY {
  API_WARNING_MESSAGE = "ca.API_WARNINGS",
}
export type IN_MEMORY_STORAGE_KEY_TYPE = `${IN_MEMORY_STORAGE_KEY}`;

// in memory object storage key
enum IN_MEMORY_OBJECT_STORAGE_KEY {
  CHART_STATE = `ca.CHART_OBJECT_STATE`,
  GRID_VIEW_STATE = "ca.GRID_VIEW_STATE",
  CHART_VIEW_STATE = "ca.CHART_VIEW_STATE",
}

export type IN_MEMORY_OBJECT_STORAGE_KEY_TYPE =
  | `ca.CHART_OBJECT_STATE_${string}`
  | `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${string}`
  | `${IN_MEMORY_OBJECT_STORAGE_KEY.CHART_VIEW_STATE}_${string}`;

export { IN_MEMORY_STORAGE_KEY, SESSION_STORAGE_KEY, LOCAL_STORAGE_KEY, IN_MEMORY_OBJECT_STORAGE_KEY };
