import React from "react";
import AuthenticationWrapper from "@/components/helpers/AuthenticationWrapper";
import { PortfolioSettingsPageContextProvider } from "@/contexts/PageContexts/PortfolioSettingsPageContext";
import { PortfolioView } from "@/components/views/Portfolio/Portfolio/PortfolioView";

type Props = {
  id: string;
};

const Portfolio = ({ id }: Props) => {
  return (
    <AuthenticationWrapper permission="any">
      <PortfolioSettingsPageContextProvider>
        <PortfolioView userPortfolioId={id} />
      </PortfolioSettingsPageContextProvider>
    </AuthenticationWrapper>
  );
};

// ✅ Now this works because we're no longer using static export
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function getServerSideProps(context: any) {
  const { id } = context.params;
  return {
    props: { id },
  };
}

export default Portfolio;
