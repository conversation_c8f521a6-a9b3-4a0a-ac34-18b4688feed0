import AuthenticationWrapper from "@/components/helpers/AuthenticationWrapper";
import { Portfolios } from "@/components/views/Portfolio/index";
import { PortfolioSettingsPageContextProvider } from "@/contexts/PageContexts/PortfolioSettingsPageContext";

const PortfolioPage = () => {
  return (
    <AuthenticationWrapper permission="any">
      <PortfolioSettingsPageContextProvider>
        <Portfolios />
      </PortfolioSettingsPageContextProvider>
    </AuthenticationWrapper>
  );
};

export default PortfolioPage;
