import {
  Box,
  IconButton,
  <PERSON>,
  <PERSON><PERSON><PERSON>r,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tooltip,
  Tr,
  chakra,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import React from "react";
import { FiDatabase } from "react-icons/fi";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import CAIcon from "@/design-system/atoms/CAIcon";
import CAModal from "@/design-system/molecules/CAModal";
import { isSuperAdmin } from "@/utils/helpers";
import ClickToCopy from "@/design-system/molecules/CAAlertCard/ClickToCopy";

interface SQLQueriesData {
  token?: string | null;
  query?: string | null;
  name?: string | null;
}

interface SQLQueriesModalProps {
  data: SQLQueriesData[];
}

export const SQLQueriesModal = ({ data }: SQLQueriesModalProps) => {
  const {
    state: { userData },
  } = useAuthentication();
  const { isOpen, onClose, onOpen } = useDisclosure();
  const [copiedQueryToken, setCopiedQueryToken] = React.useState("");

  return (
    <>
      {isSuperAdmin(userData) && (
        <IconButton
          h="26px"
          w="26px"
          minW="unset"
          borderRadius="50%"
          aria-label="Retrieve SQL Queries"
          icon={<CAIcon as={FiDatabase} mr="0.2" variant="secondary" />}
          onClick={onOpen}
        />
      )}
      <CAModal
        size="4xl"
        modalHeader="SQL Queries"
        isOpen={isOpen}
        onClose={onClose}
        showCloseIcon
        headerStyle={{
          m: 0,
        }}
        modalBodyProps={{
          px: 0,
          pb: 5,
          pt: 0,
        }}
      >
        <Box
          backgroundColor={useColorModeValue("white", "celloBlue.1100")}
          width="auto"
          overflowY="auto"
          overflowX="hidden"
        >
          <Box>
            <TableContainer>
              <Table variant="striped" colorScheme="celloBlue">
                <Thead>
                  <Tr>
                    <Th>
                      <Text variant="tableHead">Filters</Text>
                    </Th>
                    <Th>
                      <Text variant="tableHead">Query</Text>
                    </Th>
                    <Th>
                      <Text variant="tableHead">Copy </Text>
                    </Th>
                  </Tr>
                </Thead>
                <Tbody mb="10">
                  {data?.map((query) => (
                    <Tr key={query.token} minW="full">
                      <Td>{query.name}</Td>
                      <Td>
                        <Tooltip maxW="40rem" label={<chakra.pre overflow="hidden">{query.query}</chakra.pre>}>
                          <Text maxW="39.5rem" overflow="hidden" textOverflow="ellipsis">
                            {query.query ?? "SQL Query is not available."}
                          </Text>
                        </Tooltip>
                      </Td>
                      {Boolean(query.query) && (
                        <Td>
                          <ClickToCopy
                            tooltipLabel={copiedQueryToken === query.token ? "Copied" : "Copy"}
                            setToolTipLabel={() => setCopiedQueryToken(query.token ?? "")}
                            text={query.query ?? ""}
                          />
                        </Td>
                      )}
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </TableContainer>
          </Box>
        </Box>
      </CAModal>
    </>
  );
};
