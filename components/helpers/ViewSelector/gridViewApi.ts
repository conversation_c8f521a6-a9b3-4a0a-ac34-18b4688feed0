import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import {
  CA_Mastr_Api_v1_0_Models_UserGridView_InsertUserGridView,
  CA_Mastr_Api_v1_0_Models_UserGridView_UpdateUserGridView,
  UserGridViewService,
} from "@/utils/openapi";

export const updateGridView = async (gridView: CA_Mastr_Api_v1_0_Models_UserGridView_UpdateUserGridView) => {
  try {
    const res = await UserGridViewService.putUserGridView(gridView);
    return res;
  } catch (err) {
    showErrorToast("Error", `Failed to update grid view "${gridView.grid_view_name}".`);
  }
};

export const insertGridView = async (
  gridView: CA_Mastr_Api_v1_0_Models_UserGridView_InsertUserGridView,
  showToast = true
) => {
  try {
    const res = await UserGridViewService.postUserGridView(gridView);
    showToast && showSuccessToast("Saved", `Successfully added "${gridView.grid_view_name}"`);
    return res;
  } catch (err) {
    showErrorToast("Error", `Failed to add grid view "${gridView.grid_view_name}".`);
  }
};

export const deleteGridView = async (gridViewId: number, gridViewName: string) => {
  try {
    const res = await UserGridViewService.deleteUserGridView(gridViewId);
    showSuccessToast("Deleted", `Successfully deleted "${gridViewName}"`);
    return res;
  } catch (err) {
    showErrorToast("Error", `Failed to delete grid view "${gridViewName}".`);
  }
};
