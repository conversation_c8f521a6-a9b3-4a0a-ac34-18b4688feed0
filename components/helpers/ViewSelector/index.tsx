import React from "react";
import {
  Box,
  Button,
  Center,
  Flex,
  HStack,
  IconButton,
  Input,
  InputGroup,
  InputRightAddon,
  Menu,
  MenuButton,
  MenuDivider,
  MenuGroup,
  MenuItem,
  MenuList,
  Text,
  Tooltip,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoIosArrowDown } from "react-icons/io";
import { IoAdd, IoEllipsisVerticalOutline, IoRefresh, IoSaveOutline, IoTrashOutline } from "react-icons/io5";
import { VscSaveAs } from "react-icons/vsc";
import { GridApi } from "ag-grid-community";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { BiRename } from "react-icons/bi";
import CAIcon, { CAIconVariant } from "@/design-system/atoms/CAIcon";
import PopoverMenu from "@/design-system/molecules/PopoverMenu";
import CATable, { CATableProps } from "@/design-system/molecules/CATable";
import { canDelete, canEdit, getFormattedLocaleDate, getFormattedTime } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView } from "@/utils/openapi";
import CASearch from "@/design-system/molecules/CASearch";
import { showSuccessToast } from "@/design-system/theme/toast";
import { useGetUserGridViews } from "@/utils/swr-hooks";
import ConfirmationModalPopup from "@/design-system/organisms/ConfirmationModalPopup";
import CAInput from "@/design-system/molecules/CAInput";
import S from "@/constants/strings";
import colors from "@/design-system/theme/colors";
import { CAGridProps, GridViewMenuItems } from "@/design-system/molecules/CAGrid/types";
import { DEFAULT_VIEW_NAME, PROPERTIES_TO_STRIP_FOR_SAVING_STATE } from "@/design-system/molecules/CAGrid/constants";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import { useSortTable } from "@/hooks/useSortTable";
import { ColumnHeader } from "@/design-system/molecules/CATable/CATableHeader";
import { removeProperties } from "@/design-system/molecules/CAGrid/helpers";
import { useMemoryStorageState } from "@/hooks/ca-grid/useMemoryStorageState";
import { CAGridState } from "@/design-system/molecules/CAGrid/GridContext";
import { deleteGridView, insertGridView, updateGridView } from "./gridViewApi";

enum ModalStatesType {
  SAVE_AS = "save_as",
  DELETE = "delete",
  RENAME = "rename",
}

export type TOnViewChangeFunc = (
  updatedGridView?: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView,
  updateOnlyState?: boolean,
  reset?: boolean
) => void;

type ViewSelectorProps<TData> = Pick<
  CAGridProps<TData>,
  | "onSaveColumnState"
  | "customState"
  | "onDeleteGridView"
  | "shouldDisableSave"
  | "canCreateOrModifyViews"
  | "allowViewSave"
  | "onCreateNewView"
> & {
  gridKey: string | undefined;
  gridApi?: GridApi<TData> | undefined;
  selectedView?: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView;
  hasUnsavedChanges: boolean;
  menuItems?: GridViewMenuItems[];
  setSelectedGridView: (view: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView, disableReloading?: boolean) => void;
  onSelectView: (view: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView) => void;
  onResetView?: () => void;
  onResetToDefault?: () => void;
};

const headers: ColumnHeader[] = [
  { header: "View Name", accessor: "grid_view_name" },
  { header: "Created by", accessor: "user.firstName" },
  { header: "Created", accessor: "inserted" },
  { header: "Updated", accessor: "updated" },
  { header: "" },
].filter((el) => !!el.header);

const wrapTableCellInsideBox = (cellContent: string) => (
  <Box whiteSpace="nowrap" pr={2} pl={1}>
    {cellContent}
  </Box>
);

const ViewSelector = <TData,>({
  selectedView,
  gridKey,
  gridApi,
  onSaveColumnState,
  hasUnsavedChanges,
  customState,
  onDeleteGridView,
  shouldDisableSave,
  menuItems,
  canCreateOrModifyViews = true,
  allowViewSave = true,
  onSelectView,
  onResetView,
  setSelectedGridView,
  onCreateNewView,
}: // onResetToDefault,
ViewSelectorProps<TData>) => {
  const { data, mutate, isValidating, isLoading } = useGetUserGridViews(gridKey);
  const { getChartStateOnMemory } = useMemoryStorageState();

  const userGridViews = React.useMemo(() => data?.user_grid_views, [data?.user_grid_views]);

  const { filteredList, getInputProps, resetSearch } = useKeywordsSearch({
    getSearchableText: (item) => `${item.grid_view_name} ${item.user?.firstName} ${item.user?.lastName}`,
    list: userGridViews,
  });
  const { getTableProps, sortedData } = useSortTable({ data: filteredList });

  const [isPopoverOpen, togglePopover] = React.useState(false);
  const [confirmationModalState, setConfirmationModalState] = React.useState<ModalStatesType | null>(null);

  const duplicateGridViewNameInput = React.useRef<HTMLInputElement>(null);
  const renameGridViewNameInput = React.useRef<HTMLInputElement>(null);

  const tableRowActiveColor = useColorModeValue(colors.celloBlue[75], colors.celloBlue[500]);
  const menuItemColor = useColorModeValue("charcoal.400", "white");
  const menuItemProps = {
    color: menuItemColor,
    boxSize: 4,
    variant: "default" as CAIconVariant,
  };
  const modalConfig: { [key in ModalStatesType]: { headerText: string; description: string | React.JSX.Element } } = {
    save_as: {
      headerText: S.COMMON.SAVE_AND_CLOSE,
      description: (
        <CAInput
          name="view_name"
          placeholder="View name"
          ref={duplicateGridViewNameInput}
          autoFocus
          defaultValue={`Copy of ${selectedView?.grid_view_name}`}
        />
      ),
    },
    rename: {
      headerText: "Rename",
      description: (
        <CAInput
          name="rename_view_name"
          placeholder="View name"
          ref={renameGridViewNameInput}
          defaultValue={selectedView?.grid_view_name ?? ""}
          autoFocus
        />
      ),
    },
    delete: {
      headerText: S.COMMON.DELETE_CONFIRMATION.HEADER,
      description: S.COMMON.DELETE_CONFIRMATION.DESCRIPTION,
    },
  };

  const tableData = React.useMemo(() => {
    return sortedData?.map((item) => {
      const rowData: CATableProps["data"][0] = {
        name: item.grid_view_name ?? "",
        values: [
          wrapTableCellInsideBox(`${item.user?.firstName ?? ""} ${item.user?.lastName ?? ""}`),
          wrapTableCellInsideBox(`${getFormattedLocaleDate(item.inserted)} ${getFormattedTime(item.inserted)}`),
          wrapTableCellInsideBox(`${getFormattedLocaleDate(item.updated)} ${getFormattedTime(item.updated)}`),
          canDelete(item.role) ? (
            <Center
              p={0.5}
              onClick={(e) => {
                e.stopPropagation();
                deleteGridViewHandler(item?.user_grid_view_id, item?.grid_view_name ?? "");
              }}
              key={`delete-${item.user_grid_view_id}`}
            >
              <CAIcon as={RiDeleteBin6Fill} variant="secondary" />
            </Center>
          ) : (
            <Box />
          ),
        ].filter(Boolean),
      };

      rowData.name = (
        <Box whiteSpace="nowrap">
          <Text variant="tableLeft">{rowData.name}</Text>
        </Box>
      );

      if (selectedView?.user_grid_view_id && item.user_grid_view_id === selectedView?.user_grid_view_id) {
        rowData.rowBackgroundColor = `${tableRowActiveColor} !important`;
      }

      return rowData;
    });
    /**
     * prevent recalculation when **deleteGridViewHandler** and **toggleSharedStatusHandler** changes
     * these two functions are not wrapped in useCallback for performance reasons
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedView?.user_grid_view_id, tableRowActiveColor, userGridViews, sortedData]);

  //  helper functions
  const onPopoverClose = () => {
    resetSearch();
    togglePopover(false);
  };

  const getCurrentGridView = React.useCallback(() => {
    const gridState = gridApi?.getState() as CAGridState;
    const chartState = getChartStateOnMemory();
    const strippedGridState = gridState
      ? removeProperties(
          gridState,
          PROPERTIES_TO_STRIP_FOR_SAVING_STATE.filter((el) => el !== "rangeSelection")
        )
      : undefined;
    return {
      ...strippedGridState,
      customState,
      chartState,
    };
  }, [customState, getChartStateOnMemory, gridApi]);

  const tableItemClick = (
    _selectedItem: { name: string | React.JSX.Element; values: (string | number | React.JSX.Element)[] },
    index: number
  ) => {
    if (sortedData?.length) {
      const newSelectedGridView = sortedData[index];
      if (newSelectedGridView.user_grid_view_id === selectedView?.user_grid_view_id) return;
      onSelectView(newSelectedGridView);
    }

    onPopoverClose();
  };

  const saveGridView = () => {
    updateGridView({
      grid_key: gridKey,
      user_grid_view_id: selectedView?.user_grid_view_id,
      grid_view_name: selectedView?.grid_view_name,
      grid_view_state: JSON.stringify(getCurrentGridView()),
    }).then((res) => {
      if (res) {
        mutate();
        showSuccessToast("Saved", `Successfully saved "${res.user_grid_view?.grid_view_name}"`);
        onSaveColumnState?.(res);
      }
    });
  };

  const renameGridViewHandler = (newGridViewName: string) => {
    updateGridView({
      grid_key: gridKey,
      user_grid_view_id: selectedView?.user_grid_view_id,
      grid_view_name: newGridViewName,
      grid_view_state: selectedView?.grid_view_state,
    }).then((res) => {
      if (res) {
        mutate();
        showSuccessToast("Saved", `Successfully renamed "${selectedView?.grid_view_name}" to "${newGridViewName}"`);
        setSelectedGridView(
          {
            ...selectedView,
            grid_view_state: JSON.stringify(getCurrentGridView()),
            grid_view_name: res?.user_grid_view?.grid_view_name,
          },
          true
        );
      }
    });
  };

  const saveAs = (gridViewName: string) => {
    insertGridView({
      grid_key: gridKey,
      grid_view_name: gridViewName,
      grid_view_state: JSON.stringify(getCurrentGridView()),
    }).then((res) => {
      if (res?.user_grid_view) {
        mutate();
        onSelectView(res.user_grid_view);
      }
    });
  };

  const deleteGridViewHandler = (id: number | undefined, viewName: string) => {
    if (!id) return;
    deleteGridView(id, viewName ?? "").then((res) => {
      if (res) {
        if (id === selectedView?.user_grid_view_id) {
          onDeleteGridView?.();
          const updatedViews = userGridViews?.filter((view) => view.user_grid_view_id !== id);
          const newSelectedView = updatedViews?.find((view) => view.is_system_view) ?? updatedViews?.[0];
          if (newSelectedView) {
            onSelectView(newSelectedView);
          }
        }
        mutate();
      }
    });
  };

  const createNewDefaultView = () => {
    let largestPosition = -1;

    userGridViews?.forEach((el) => {
      const pos = el.grid_view_name?.match(/My View( \((\d+)\))?/);
      if (pos) {
        if (+pos[2] > largestPosition) {
          largestPosition = +pos[2];
        } else {
          largestPosition = Math.max(0, largestPosition);
        }
      }
    });

    const duplicatedExtension = largestPosition > -1 ? ` (${largestPosition + 1})` : "";

    insertGridView(
      {
        grid_key: gridKey,
        grid_view_name: `${DEFAULT_VIEW_NAME}${duplicatedExtension}`,
        grid_view_state: "",
      },
      false
    ).then((res) => {
      if (res?.user_grid_view) {
        mutate();
        onCreateNewView?.(res.user_grid_view);
      }
    });
  };

  const bgColorTable = useColorModeValue("white", "celloBlue.1100");

  const handleSaveOrSaveAs = () => {
    if (allowViewSave && canEdit(selectedView?.role)) {
      saveGridView();
    } else if (canCreateOrModifyViews) {
      setConfirmationModalState(ModalStatesType.SAVE_AS);
    }
  };

  return (
    <>
      <Flex onClick={(e) => e.stopPropagation()}>
        <Box position="relative" flexGrow={1}>
          <Flex alignItems="center">
            {allowViewSave && hasUnsavedChanges && !isLoading && (
              <Tooltip
                label={
                  allowViewSave && canEdit(selectedView?.role)
                    ? "Save Changes"
                    : canCreateOrModifyViews
                    ? "Duplicate As A New View"
                    : null
                }
              >
                <Button
                  onClick={handleSaveOrSaveAs}
                  paddingStart={0}
                  paddingEnd={0}
                  minWidth={4}
                  height={4}
                  mr={2}
                  backgroundColor={"transparent"}
                  textColor={"safetyOrange.500"}
                >
                  <CAIcon as={IoSaveOutline} boxSize={4} variant="default" cursor={"pointer"} />
                </Button>
              </Tooltip>
            )}
            <PopoverMenu
              triggerElement={
                <InputGroup
                  variant="unstyled"
                  onClick={() => {
                    togglePopover(true);
                    mutate();
                  }}
                  _hover={{
                    opacity: 0.7,
                  }}
                  aria-disabled={isValidating}
                  pointerEvents={isValidating ? "none" : "auto"}
                >
                  <>
                    {hasUnsavedChanges && (
                      <Tooltip label={"Unsaved changes"} aria-label={"Unsaved changes"} placement="bottom">
                        <Box
                          position="absolute"
                          zIndex={1}
                          top="50%"
                          transform="translateY(-50%)"
                          left="2"
                          bg={"balticGray.400"}
                          borderRadius={4}
                          height={1.5}
                          width={1.5}
                        />
                      </Tooltip>
                    )}
                    <Tooltip
                      label={selectedView?.grid_view_name ?? ""}
                      aria-label={selectedView?.grid_view_name ?? ""}
                      placement="bottom"
                      isDisabled={!selectedView?.grid_view_name}
                    >
                      <Input
                        pl={hasUnsavedChanges ? "5" : 2}
                        name="view_name"
                        readOnly
                        value={selectedView?.grid_view_name ?? DEFAULT_VIEW_NAME}
                        variant="gridViewSelector"
                        cursor="pointer"
                        placeholder="Load View"
                        disabled={isValidating}
                      />
                    </Tooltip>
                    <InputRightAddon>
                      <IconButton
                        variant="unstyled"
                        aria-label="open-popup"
                        display="inline-flex"
                        icon={<CAIcon as={IoIosArrowDown} variant="default" />}
                        bg={useColorModeValue("misty.300", "celloBlue.800")}
                        minWidth={8}
                        px={1.5}
                        height="2rem"
                        borderTopLeftRadius="0"
                        borderBottomLeftRadius="0"
                        disabled={isValidating}
                        aria-disabled={isValidating}
                      />
                    </InputRightAddon>
                  </>
                </InputGroup>
              }
              onClose={onPopoverClose}
              isPopoverOpen={isPopoverOpen}
              placement="bottom"
              popoverContent={{
                showArrow: false,
                renderInPortal: true,
                maxWidth: "100%",
                body: (
                  <Box
                    backgroundColor={useColorModeValue("white", "celloBlue.1100")}
                    width="auto"
                    maxH="30rem"
                    overflowY="auto"
                    overflowX="hidden"
                  >
                    <Box
                      px="3"
                      py="2"
                      position="absolute"
                      width="full"
                      background={useColorModeValue("white", "celloBlue.1100")}
                    >
                      <HStack>
                        <CASearch
                          name="search"
                          maxW="15rem"
                          placeholder="Search View"
                          isDisabled={!userGridViews || userGridViews.length === 0}
                          {...getInputProps()}
                        />
                      </HStack>
                    </Box>
                    <Box
                      pt="3.25rem"
                      pb="2"
                      overflowX="visible"
                      // pointerEvents={isValidating ? "none" : "auto"}
                      // opacity={isValidating ? 0.5 : "unset"}
                    >
                      <CATable
                        headers={headers}
                        data={tableData ?? []}
                        variant="caFullCentered"
                        onItemClick={tableItemClick}
                        headerStyles={{
                          backgroundColor: bgColorTable,
                          stickyPosition: "3.25rem",
                          textAlign: "left",
                        }}
                        columnsToLeftAlign={[0, 1, 2, 3]}
                        {...getTableProps()}
                      />
                    </Box>
                  </Box>
                ),
              }}
              trigger="click"
            />
          </Flex>
        </Box>

        {/* selected query actions menu */}
        <Menu placement="bottom-start" isLazy closeOnSelect>
          <MenuButton
            as={IconButton}
            aria-label="query-options-menu"
            size="xs"
            ml={2}
            h="inherit"
            icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
            variant="ghost"
            onClick={(e) => {
              e.stopPropagation();
              if (shouldDisableSave?.()) {
                e.preventDefault();
              }
            }}
          />
          <MenuList zIndex="popover" minW="10rem" color={menuItemColor} fontSize="sm">
            {menuItems && menuItems.length > 0 && (
              <>
                {menuItems.map((item, itemIndex) => (
                  <MenuItem key={itemIndex} icon={<CAIcon as={item.icon} {...menuItemProps} />} onClick={item.onClick}>
                    {item.label}
                  </MenuItem>
                ))}
                <MenuDivider />
              </>
            )}
            {canEdit(selectedView?.role) && (
              <MenuItem
                icon={<CAIcon as={BiRename} {...menuItemProps} />}
                onClick={() => setConfirmationModalState(ModalStatesType.RENAME)}
              >
                {S.COMMON.RENAME}
              </MenuItem>
            )}
            {/* <MenuItem
              icon={<CAIcon as={IoRefresh} {...menuItemProps} transform="rotateY(180deg)" />}
              onClick={onResetToDefault}
            >
              {S.COMMON.RESET_TO_DEFAULT}
            </MenuItem> */}
            <MenuItem
              icon={<CAIcon as={IoRefresh} {...menuItemProps} transform="rotateY(180deg)" />}
              onClick={onResetView}
              isDisabled={!hasUnsavedChanges}
            >
              {S.COMMON.RESET_TO_LAST_SAVED}
            </MenuItem>
            <MenuDivider />
            <MenuGroup>
              {canCreateOrModifyViews && (
                <MenuItem icon={<CAIcon as={IoAdd} {...menuItemProps} />} onClick={createNewDefaultView}>
                  {S.COMMON.NEW}
                </MenuItem>
              )}

              {allowViewSave && canEdit(selectedView?.role) && (
                <MenuItem icon={<CAIcon as={IoSaveOutline} {...menuItemProps} />} onClick={saveGridView}>
                  {S.COMMON.SAVE}
                </MenuItem>
              )}

              {canCreateOrModifyViews && (
                <MenuItem
                  icon={<CAIcon as={VscSaveAs} {...menuItemProps} />}
                  onClick={() => setConfirmationModalState(ModalStatesType.SAVE_AS)}
                >
                  {S.COMMON.SAVE_AS}
                </MenuItem>
              )}

              {canDelete(selectedView?.role) && (
                <MenuItem
                  icon={<CAIcon as={IoTrashOutline} {...menuItemProps} />}
                  onClick={() => setConfirmationModalState(ModalStatesType.DELETE)}
                >
                  {S.COMMON.DELETE}
                </MenuItem>
              )}
            </MenuGroup>
          </MenuList>
        </Menu>
      </Flex>

      {/* Duplicate, delete, rename popup */}
      <ConfirmationModalPopup
        isOpen={!!confirmationModalState}
        onModalClose={() => setConfirmationModalState(null)}
        onCancel={() => setConfirmationModalState(null)}
        onConfirm={() => {
          switch (confirmationModalState) {
            case ModalStatesType.SAVE_AS: {
              const gridViewName = duplicateGridViewNameInput?.current?.value;
              if (!gridViewName) {
                duplicateGridViewNameInput?.current?.focus();
                return;
              }

              saveAs(gridViewName);
              break;
            }
            case ModalStatesType.DELETE:
              deleteGridViewHandler(selectedView?.user_grid_view_id, selectedView?.grid_view_name ?? "");
              break;
            case ModalStatesType.RENAME: {
              const newGridViewName = renameGridViewNameInput?.current?.value;
              if (!newGridViewName) {
                duplicateGridViewNameInput?.current?.focus();
                return;
              } else if (newGridViewName === selectedView?.grid_view_name) {
                return;
              }

              renameGridViewHandler(newGridViewName);
              break;
            }
          }

          setConfirmationModalState(null);
        }}
        headerText={confirmationModalState ? modalConfig[confirmationModalState]?.headerText : ""}
        description={confirmationModalState ? modalConfig[confirmationModalState]?.description : ""}
        showCloseIcon
      />
    </>
  );
};

export default ViewSelector;
