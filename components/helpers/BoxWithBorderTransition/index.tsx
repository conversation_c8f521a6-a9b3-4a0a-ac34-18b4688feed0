import * as React from "react";
import { Box, LayoutProps, PositionProps, SpaceProps, useColorModeValue } from "@chakra-ui/react";
import colors from "@/design-system/theme/colors";

interface BoxWithBorderTransitionProps extends LayoutProps, SpaceProps, PositionProps {
  borderColor?: string;
  title?: string;
}

const BoxWithBorderTransition: React.FC<React.PropsWithChildren<BoxWithBorderTransitionProps>> = ({
  children,
  borderColor,
  ...props
}) => {
  const defaultBorderColor = useColorModeValue(colors.gemBlue[500], colors.gemBlue[500]);
  const backgroundColor = borderColor ?? defaultBorderColor;

  return (
    <Box
      position="relative"
      _after={{
        content: `""`,
        height: "2px",
        width: "0",
        bottom: "0px",
        position: "absolute",
        background: backgroundColor,
        right: "50%",
        transition: "width 0.5s ease",
      }}
      _before={{
        content: `""`,
        height: "2px",
        width: "0",
        bottom: "0px",
        position: "absolute",
        background: backgroundColor,
        left: "50%",
        zIndex: 1,
        transition: "width 0.5s ease",
      }}
      _focusWithin={{
        _before: {
          width: "50%",
        },
        _after: {
          width: "50%",
        },
      }}
      _focusVisible={{
        outline: "none",
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

export default BoxWithBorderTransition;
