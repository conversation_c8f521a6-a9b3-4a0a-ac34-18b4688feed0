import * as React from "react";
import { useRouter } from "next/router";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { FeedbackPopover } from "@/components/overlay/FeedbackPopover/FeedbackPopover";

interface AuthenticationWrapperProps {
  permission: "any";
}

const AuthenticationWrapper: React.FC<React.PropsWithChildren<AuthenticationWrapperProps>> = ({ children }) => {
  const {
    state: { accessToken, userData },
  } = useAuthentication();

  const router = useRouter();

  //TODO Verify user has permissions
  if (accessToken !== "empty_token" && (!accessToken || !userData)) {
    const query = router.asPath ? { next: router.asPath } : undefined;
    router.push({ pathname: "/login", query });
    return null;
  }

  return (
    <>
      <>{children}</>
      <FeedbackPopover />
    </>
  );
};

export default AuthenticationWrapper;
