import { Flex, Spacer, Text } from "@chakra-ui/layout";
import { Stack } from "@chakra-ui/react";

const ValueDisplay: React.FC<{ name: string | React.JSX.Element; value: string | number | React.JSX.Element }> = ({
  name,
  value,
}: {
  name: string | React.JSX.Element;
  value: string | number | React.JSX.Element;
}) => {
  return (
    <Flex py={1} alignItems="center">
      {typeof name === "string" ? (
        <Text variant="tableLeft" mr={1}>
          {name}
        </Text>
      ) : (
        name
      )}
      <Spacer />
      {typeof value === "string" || typeof value === "number" ? (
        <Text variant="default" textAlign="right">
          {value}
        </Text>
      ) : (
        value
      )}
    </Flex>
  );
};

const InlineValueDisplay: React.FC<{
  name: string | React.JSX.Element;
  value: string | number | React.JSX.Element;
}> = ({ name, value }: { name: string | React.JSX.Element; value: string | number | React.JSX.Element }) => {
  return (
    <Stack direction="row">
      <Text variant="primary" lineHeight={1} textAlign="left">
        {name}
      </Text>
      <Text lineHeight={1} textAlign="right">
        {value}
      </Text>
    </Stack>
  );
};

export default ValueDisplay;
export { ValueDisplay, InlineValueDisplay };
