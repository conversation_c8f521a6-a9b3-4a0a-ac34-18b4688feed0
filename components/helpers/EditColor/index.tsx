import React from "react";
import { IconButton } from "@chakra-ui/react";
import CAIcon from "@/design-system/atoms/CAIcon";
import { EditColorIcon } from "@/design-system/icons";
import { CustomRequestOptions, useGetBondIndicativesSWR, useGetBondPricingSWR } from "@/utils/swr-hooks";
import { BondIndicativesRequest, BondPricingRequest } from "@/types/swr";
import EditColorModal from "./EditColorModal";

export interface EditColorHandler {
  associateWithRun: (runId: string) => Promise<void>;
  resetColorForm: () => void;
}

// Types
interface EditColorProps {
  colorId?: number | null;
  runId?: string;
  bondName?: string;
  indicativesRequest: BondIndicativesRequest;
  bondPricingRequest: BondPricingRequest;
  requestOpts: CustomRequestOptions;
  updateColorId: (colorId: number | null | undefined) => void;
  isOldRun?: boolean;
}

const EditColor: React.ForwardRefRenderFunction<EditColorHandler, EditColorProps> = (
  {
    runId,
    colorId,
    bondName,
    indicativesRequest,
    bondPricingRequest,
    requestOpts,
    updateColorId,
    isOldRun,
  }: EditColorProps,
  ref
) => {
  const [showEditColorModal, toggleEditColorModal] = React.useState(false);
  const { data: indicativesData } = useGetBondIndicativesSWR(indicativesRequest, { lastRun: "Default", isOldRun });
  const { data: bondPricingData } = useGetBondPricingSWR(bondPricingRequest, requestOpts);

  if (!bondName) {
    return null;
  }

  let fullPrice: number | undefined = undefined;
  if (
    typeof bondPricingData?.results?.[0]?.price === "number" &&
    typeof bondPricingData?.results?.[0]?.accrued_interest === "number"
  ) {
    fullPrice = bondPricingData.results[0].price + bondPricingData.results[0].accrued_interest;
  }

  return (
    <>
      <IconButton
        aria-label="Edit Color"
        onClick={() => toggleEditColorModal(true)}
        variant="primary"
        icon={<CAIcon as={EditColorIcon} variant="default" />}
      />

      <EditColorModal
        ref={ref}
        bondName={bondName}
        runId={runId}
        colorId={colorId ?? undefined}
        onModalClose={() => toggleEditColorModal(false)}
        showEditColorModal={showEditColorModal}
        factor={indicativesData?.bond_structure?.tranche_factor}
        fullPrice={fullPrice}
        defaultOriginalFace={indicativesData?.bond_structure?.tranche_orig_balance ?? undefined}
        updateColorId={updateColorId}
        isOldRun={isOldRun}
      />
    </>
  );
};

export default React.forwardRef(EditColor);
