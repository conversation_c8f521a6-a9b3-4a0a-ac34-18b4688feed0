import React from "react";
import { Controller, useForm } from "react-hook-form";
import { Box, Button, Flex, HStack, Skeleton, Text, chakra } from "@chakra-ui/react";
import { useSWRConfig } from "swr";
import CAModal from "@/design-system/molecules/CAModal";
import { CA_Mastr_Api_v1_0_Models_Color_UpsertColor } from "@/utils/openapi";
import {
  canDelete,
  canEdit,
  formatCurrency,
  getAPIErrorMessage,
  getDateTypeFromString,
  removeEmpty,
} from "@/utils/helpers";
import CATextarea from "@/design-system/molecules/CATextarea";
import { TagDropdown } from "@/design-system/organisms/TagDropdown/TagDropdown";
import {
  swrKeyColorService,
  useDeleteColorSWR,
  useGetRunColorSWR,
  usePostRunColorSWR,
} from "@/utils/swr-hooks/RunColor";
import CADateInput from "@/design-system/molecules/CADateInput";
import S from "@/constants/strings";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { CA_Mastr_Models_v1_0_Models_SelectOptionType } from "@/utils/openapi/models/CA_Mastr_Models_v1_0_Models_SelectOptionType";
import { useConfirmation } from "@/contexts/ConfirmationContext/ConfirmationContextProvider";
import { RemoveNullFields } from "@/utils/type-utils";
import SelectOption from "@/components/helpers/SelectOption";
import CACurrencyInput from "@/design-system/molecules/CACurrencyInput";
import CAInput from "@/design-system/molecules/CAInput";
import ValueDisplay from "../ValueDisplay";
import { ReadOnlyFields } from "./ReadOnlyFields";
import { EditColorHandler } from ".";

export interface EditColorFormType
  extends Omit<RemoveNullFields<CA_Mastr_Api_v1_0_Models_Color_UpsertColor>, "trade_date"> {
  trade_date?: Date;
}

interface Props {
  showEditColorModal: boolean;
  bondName?: string;
  runId?: string;
  colorId?: number;
  onModalClose?: (isDirty?: boolean) => void;
  factor?: number | null | undefined;
  fullPrice?: number | null | undefined;
  defaultOriginalFace?: number;
  updateColorId?: (colorId: number | null | undefined) => void;
  isOldRun?: boolean;
}

const EditColorModal: React.ForwardRefRenderFunction<EditColorHandler, Props> = (
  {
    showEditColorModal,
    bondName,
    runId,
    onModalClose,
    factor,
    fullPrice,
    colorId,
    updateColorId,
    defaultOriginalFace,
    isOldRun,
  },
  ref
) => {
  // FORM
  const {
    register,
    control,
    handleSubmit,
    reset,
    getValues,
    setValue,
    watch,
    formState: { isDirty, isSubmitting },
  } = useForm<EditColorFormType>();
  const lastRunId = React.useRef(runId);

  const [watchCurrentFace, watchMarketValue] = watch(["current_face", "market_value"]);

  const confirmation = useConfirmation();
  const { mutate: globalMutate } = useSWRConfig();
  // REQUEST FOR RUN COLOR
  const { data, isLoading, mutate } = useGetRunColorSWR({
    runId: colorId ? undefined : runId,
    colorId: colorId ?? undefined,
  });
  const { trigger: addEditColor } = usePostRunColorSWR();
  const { trigger: deleteColor, isMutating: isDeleting } = useDeleteColorSWR();

  // CONSTANTS
  const CAN_EDIT = data?.color?.role === undefined || canEdit(data?.color?.role);
  const CAN_DELETE = canDelete(data?.color?.role);

  // EFFECTS
  React.useEffect(() => {
    if (defaultOriginalFace && !colorId && showEditColorModal) {
      setValue("original_face", defaultOriginalFace);
      populateCurrentFaceAndMarketValue(defaultOriginalFace);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showEditColorModal, defaultOriginalFace, isOldRun, colorId]);

  React.useEffect(() => {
    if (runId) {
      if (!lastRunId.current) {
        lastRunId.current = runId;
      } else if (lastRunId.current !== runId) {
        // Reset color on a new run
        // We cannot use key={runId} when using this component because of the requirement to associate runId with color
        lastRunId.current = runId;
        setValue("color_id", undefined);
      }
    }
  }, [runId, setValue]);

  const resetFieldsWithDefaultValues = React.useCallback(() => {
    const colorData = data?.color && removeEmpty(data?.color);
    reset({
      ...colorData,
      market_value: colorData?.market_value ?? getValues("market_value"),
      trade_date: colorData?.trade_date ? getDateTypeFromString(colorData?.trade_date) : undefined,
      color_id: colorData?.color_id ?? colorId,
    });
  }, [colorId, data?.color, reset, getValues]);

  React.useEffect(() => {
    if (CAN_EDIT && showEditColorModal && data?.color) {
      resetFieldsWithDefaultValues();
    }
  }, [data?.color, resetFieldsWithDefaultValues, showEditColorModal, CAN_EDIT]);

  React.useImperativeHandle(ref, () => ({
    associateWithRun,
    resetColorForm,
  }));

  // HANDLERS
  const associateWithRun = async (runId: string) => {
    try {
      await confirmation.confirm({
        headerText: S.PAGES.COLOR.RUN_CONFIRMATION.HEADER,
        description: S.PAGES.COLOR.RUN_CONFIRMATION.DESCRIPTION,
        labels: {
          confirm: "YES",
          cancel: "NO",
        },
      });
      await addEditColor(
        { ...getValues(), color_id: colorId, run_id: runId, security: bondName },
        {
          onSuccess: () => mutate(),
          onError: (e) => showErrorToast("Error", getAPIErrorMessage(e)),
        }
      );
    } catch (error) {
      updateColorId?.(undefined);
      reset({
        trade_date: new Date(),
      });
    }
  };

  const resetColorForm = () => {
    reset({
      trade_date: new Date(),
    });
    updateColorId?.(undefined);
  };

  const onSubmitHandler = handleSubmit(async (data) => {
    await addEditColor(
      { ...data, color_id: colorId, run_id: runId, security: bondName },
      {
        onSuccess: (res) => {
          mutate();
          onModalClose?.(true);
          updateColorId?.(res?.color?.color_id);
          showSuccessToast("Success", "Color info has been updated.");
          // Revalidate search on color after 500ms to wait for changes in DB, in order to show the icon on Pricer Header immediately after the update
          setTimeout(() => {
            globalMutate(swrKeyColorService.postColorByBond(bondName));
          }, 500);
        },
        onError: () => {
          showErrorToast("Error", "Failed to save color info.");
        },
      }
    );
  });

  const handleModalClose = async () => {
    if (!isDirty) return onModalClose?.(false);

    //Show confirmation Modal if user has unsaved changes
    await confirmation.confirm({
      headerText: S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER,
      description: S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION,
    });
    onModalClose?.(true);
    resetFieldsWithDefaultValues();
  };

  const handleDeleteClick = async () => {
    await deleteColor(
      {
        color_id: colorId,
      },
      {
        onSuccess: () => {
          onModalClose?.(true);
          reset({});
          showSuccessToast("Success", "Color deleted successfully!");
          mutate();
          updateColorId?.(undefined);
          // Revalidate search on color after 500ms to wait for changes in DB, in order to show the icon on Pricer Header immediately after the update
          setTimeout(() => {
            globalMutate(swrKeyColorService.postColorByBond(bondName));
          }, 500);
        },
        onError: (error) => {
          showErrorToast("Error", getAPIErrorMessage(error));
        },
      }
    );
  };

  const populateCurrentFaceAndMarketValue = React.useCallback(
    (originalFace: number) => {
      if (isNaN(originalFace)) {
        setValue("current_face", NaN);
        setValue("market_value", NaN);
        return;
      }

      if (originalFace?.toString() && factor?.toString()) {
        const currentFace = originalFace * factor;
        setValue("current_face", Number(currentFace.toFixed(0)));

        if (fullPrice) {
          const marketValue = (currentFace * fullPrice) / 100;
          setValue("market_value", Number(marketValue.toFixed(0)));
        }
      }
    },
    [factor, fullPrice, setValue]
  );

  if (!bondName || !showEditColorModal) return null;
  return (
    <CAModal
      autoFocus={false}
      size="xs"
      modalHeader="Color"
      isOpen={showEditColorModal}
      onClose={handleModalClose}
      showCloseIcon
    >
      {isLoading ? (
        <Skeleton height="25px" mb={4} />
      ) : !CAN_EDIT ? (
        <ReadOnlyFields bondName={bondName} data={data ?? undefined} />
      ) : (
        <chakra.form>
          <Flex mb={2} justifyContent="space-between">
            <Text variant="primary">Security</Text>
            <Text>{bondName}</Text>
          </Flex>
          <Controller
            control={control}
            name="counter_party_1"
            render={({ field }) => (
              <SelectOption
                closeMenuOnSelect
                label="Counter Party 1"
                type={CA_Mastr_Models_v1_0_Models_SelectOptionType.COLOR_COUNTER_PARTY}
                tabSelectsValue={false}
                {...field}
              />
            )}
          />
          <Controller
            control={control}
            name="action"
            render={({ field }) => (
              <SelectOption
                closeMenuOnSelect
                label="Action"
                type={CA_Mastr_Models_v1_0_Models_SelectOptionType.COLOR_ACTION}
                tabSelectsValue={false}
                {...field}
              />
            )}
          />
          <Controller
            control={control}
            name="counter_party_2"
            render={({ field }) => (
              <SelectOption
                closeMenuOnSelect
                label="Counter Party 2"
                type={CA_Mastr_Models_v1_0_Models_SelectOptionType.COLOR_COUNTER_PARTY}
                tabSelectsValue={false}
                {...field}
              />
            )}
          />
          <Controller
            name="trade_date"
            defaultValue={!data ? new Date() : undefined}
            control={control}
            render={({ field: { name, value, onChange, ref } }) => (
              <CADateInput ref={ref} selectedDate={value} onChange={onChange} name={name} label="Trade Date" />
            )}
          />
          <Controller
            name="original_face"
            control={control}
            render={({ field }) => (
              <CACurrencyInput
                {...field}
                label="Original Face ($)"
                onChange={(value: number) => {
                  field.onChange(value);
                  populateCurrentFaceAndMarketValue(value);
                }}
              />
            )}
          />
          <Box py={1}>
            <ValueDisplay name="Current Face ($)" value={formatCurrency(watchCurrentFace, true)} />
          </Box>
          <Box py={1}>
            <ValueDisplay name="Market Value ($)" value={formatCurrency(watchMarketValue, true)} />
          </Box>
          <CAInput label="Official Name" type="text" {...register("official_name")} />
          <Box mt={3}>
            <Text variant="primary">Tags</Text>
            <Controller control={control} name="tags" render={({ field }) => <TagDropdown {...field} />} />
          </Box>
          <Box mt={3}>
            <Text variant="primary">Notes</Text>
            <CATextarea hideLabel {...register("notes")} />
          </Box>
          {(CAN_EDIT || CAN_DELETE) && (
            <HStack alignItems="center" justifyContent="flex-end" mt={6} mb={4}>
              {CAN_DELETE && colorId && (
                <Button variant="secondary" size="sm" onClick={handleDeleteClick} isLoading={isDeleting}>
                  Delete
                </Button>
              )}
              <Button variant="primary" size="sm" isLoading={isSubmitting} onClick={onSubmitHandler}>
                Save
              </Button>
            </HStack>
          )}
        </chakra.form>
      )}
    </CAModal>
  );
};

export default React.forwardRef(EditColorModal);
