import { Box, Flex, Text } from "@chakra-ui/react";
import { formatCurrency, getFormattedLocaleDate } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_Color_ColorResponse } from "@/utils/openapi";

interface ReadOnlyFieldsProps {
  bondName: string;
  data: CA_Mastr_Api_v1_0_Models_Color_ColorResponse | undefined;
}
export const ReadOnlyFields = ({ bondName, data }: ReadOnlyFieldsProps) => {
  return (
    <>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Security</Text>
        <Text>{bondName}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Counter Party 1</Text>
        <Text>{data?.color?.counter_party_1 ?? "-"}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Action</Text>
        <Text>{data?.color?.action ?? "-"}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Counter Party 2</Text>
        <Text>{data?.color?.counter_party_2 ?? "-"}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Trade Date</Text>
        <Text>{getFormattedLocaleDate(data?.color?.trade_date) ?? "-"}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Original Face</Text>
        <Text>${formatCurrency(data?.color?.original_face, true)}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Current Face</Text>
        <Text>${formatCurrency(data?.color?.current_face, true)}</Text>
      </Flex>
      <Flex mb={2} justifyContent="space-between">
        <Text variant="primary">Market Value</Text>
        <Text>${formatCurrency(data?.color?.market_value, true)}</Text>
      </Flex>
      <Box mb={2} justifyContent="space-between">
        <Text variant="primary">Tags</Text>
        <Text>{data?.color?.tags?.join(", ")}</Text>
      </Box>
      <Box mb={2} justifyContent="space-between">
        <Text variant="primary">Notes</Text>
        <Text whiteSpace={"pre-line"}>{data?.color?.notes}</Text>
      </Box>
    </>
  );
};
