import { ButtonProps, IconButton } from "@chakra-ui/react";
import { IoWarningOutline } from "react-icons/io5";
import { MdErrorOutline } from "react-icons/md";
import CAIcon from "@/design-system/atoms/CAIcon";
import useRunToastManager from "@/hooks/useRunToastManager";

interface Props extends ButtonProps {
  runId?: string;
}

const RunAlertButton = ({ runId }: Props) => {
  const { runToastManager } = useRunToastManager();

  if (!runToastManager.getToasts(runId).length) return null;

  const hasErrorToast = runToastManager.hasErrorToasts(runId);
  return (
    <IconButton
      aria-label={hasErrorToast ? "Show Errors" : "Show Warnings"}
      onClick={() => runToastManager.showToasts(runId)}
      variant="primary"
      icon={
        <CAIcon
          as={hasErrorToast ? MdErrorOutline : IoWarningOutline}
          variant="default"
          color={hasErrorToast ? "magenta.500" : "safetyOrange.500"}
        />
      }
    />
  );
};

export default RunAlertButton;
