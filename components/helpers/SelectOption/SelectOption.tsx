import React, { useState } from "react";
import { useDisclosure } from "@chakra-ui/react";
import CAMultiSelectDropdown, { CAMultiSelectDropdownProps } from "@/design-system/molecules/CAMultiSelectDropdown";
import { showErrorToast } from "@/design-system/theme/toast";
import { getAPIErrorMessage, isSuperAdmin } from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_SelectOptionType } from "@/utils/openapi";
import {
  useDeleteOptionSWR,
  usePostOptionSWR,
  useSelectOptionsSWR,
  useUpdateOptionSWR,
} from "@/utils/swr-hooks/SelectOption";
import { OptionWithMenu, SelectContainer } from "@/design-system/molecules/CAMultiSelectDropdown/CustomComponents";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";

interface SelectOptionDropdownProps extends Omit<CAMultiSelectDropdownProps, "options" | "onChange" | "value"> {
  onChange: (value: string | string[]) => void;
  value: string | string[] | undefined;
  type: CA_Mastr_Models_v1_0_Models_SelectOptionType;
}

export const SelectOption = ({ name, value, onChange, type, ...rest }: SelectOptionDropdownProps) => {
  const {
    state: { userData },
  } = useAuthentication();
  const { data, mutate, isLoading } = useSelectOptionsSWR(type);
  const { trigger: addOption, isMutating: isAdding } = usePostOptionSWR();
  const { trigger: deleteOption, isMutating: isDeleting } = useDeleteOptionSWR();
  const { trigger: updateOption, isMutating: isUpdating } = useUpdateOptionSWR();
  const [selectSearchValue, setSelectSearchValue] = useState("");

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isMenuOpen, onOpen: onMenuOpen, onClose: onMenuClose } = useDisclosure();

  const addNewOption = async (value: string) => {
    await addOption(
      {
        type,
        value,
      },
      {
        onSuccess: (addedOption) => {
          if (!addedOption?.select_option?.value) return;
          //Update options to have the new added tag
          if (data && data.select_options?.length) {
            mutate({ ...data, select_options: [...data.select_options, addedOption.select_option] });
          }
          //Update the state so it shows the new option as selected
          onChange?.(addedOption.select_option.value);
          setSelectSearchValue("");
        },
        onError: (error) => showErrorToast("Error", getAPIErrorMessage(error)),
      }
    );
  };

  const options = React.useMemo(
    () =>
      data?.select_options
        ?.map(({ value, ...rest }) => ({
          ...rest,
          label: value ?? "",
          value: value ?? "",
        }))
        .sort((a, b) => a.label.localeCompare(b.label, "en")) ?? [],
    [data]
  );

  const deleteOptionHandler = async (valueToDelete: string | undefined) => {
    const optionId = options?.find((option) => option.value === valueToDelete)?.select_option_id;

    if (!optionId) return;

    await deleteOption(
      { selectOptionId: optionId },
      {
        onSuccess: () => {
          mutate();
          if (value === valueToDelete) {
            onChange("");
          }
          setSelectSearchValue("");
        },
      }
    );
  };

  const updateOptionHandler = async (oldName: string | undefined, newName: string | undefined) => {
    const optionId = options?.find((option) => option.value === oldName)?.select_option_id;
    const isSelected = value === oldName;

    if (!optionId || !newName) return;

    await updateOption(
      { select_option_id: optionId, value: newName },
      {
        onSuccess: () => {
          mutate();
          if (isSelected) {
            onChange(newName);
          }
          setSelectSearchValue("");
        },
      }
    );
  };

  const handleChange = (value: string[]) => {
    onChange(value);
    setSelectSearchValue("");
  };

  return (
    <CAMultiSelectDropdown
      name={name}
      minHeight={26}
      value={value}
      isLoading={isLoading}
      isCreatable={isSuperAdmin(userData)}
      options={[{ label: "　", value: "" }, ...options]}
      onChange={handleChange as Extract<CAMultiSelectDropdownProps, "onChange">}
      menuPosition="absolute"
      onCreateNewOption={addNewOption}
      isDisabled={isAdding}
      isMultiSelect={false}
      autoFocus={false}
      disableScrollOnMenu={isMenuOpen}
      formatCreateLabel={(userInput) => `Add ${userInput}`}
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      onToggle={(state: "open" | "close") => (state === "open" ? onOpen() : onClose())}
      menuIsOpen={isOpen}
      onBlurHandler={(e: React.FocusEvent<HTMLDivElement>) => {
        if (!e.relatedTarget?.id) {
          onClose();
          setSelectSearchValue("");
        }
      }}
      inputValue={selectSearchValue}
      onInputChange={(value, action) => {
        // only set the input when the action that caused the
        // change equals to "input-change" and ignore the other
        // ones like: "set-value", "input-blur", and "menu-close"
        if (action.action === "input-change") {
          setSelectSearchValue(value);
        }
      }}
      components={{
        SelectContainer,
      }}
      action={
        <OptionWithMenu
          isUpdating={isUpdating}
          isDeleting={isDeleting}
          onApplyClick={updateOptionHandler}
          onDeleteClick={deleteOptionHandler}
          onMenuOpen={onMenuOpen}
          onMenuClose={onMenuClose}
        />
      }
      openMenuOnFocus={false}
      {...rest}
    />
  );
};
