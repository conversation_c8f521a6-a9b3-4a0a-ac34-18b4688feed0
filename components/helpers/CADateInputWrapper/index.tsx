import * as React from "react";
import CADateInput from "@/design-system/molecules/CADateInput";
import { monthCellComparator } from "@/utils/helpers";

/* eslint-disable @typescript-eslint/no-explicit-any */
const CADateInputWrapper = React.forwardRef<any, any>(({ date, onDateChange, filterParams }, ref) => {
  const [selectedDate, setSelectedDate] = React.useState<Date | null>(date);
  const inputRef = React.useRef<HTMLInputElement>(null);

  const isMonthInput = React.useMemo(() => filterParams.comparator === monthCellComparator, [filterParams.comparator]);

  React.useEffect(() => {
    setSelectedDate(date);
  }, [date]);

  React.useImperativeHandle(ref, () => ({
    getDate: () => selectedDate,
    setDate: (newDate: Date) => {
      setSelectedDate(newDate);
    },
  }));

  const handleChange = (newDate: Date) => {
    setSelectedDate(newDate);
    onDateChange(newDate);
  };

  return (
    <CADateInput
      ref={inputRef}
      portalId="root"
      popperClassName="ag-custom-component-popup"
      selectedDate={selectedDate}
      onChange={handleChange}
      showMonthYearPicker={isMonthInput}
      placeholderText=""
      showTimeSelect={filterParams.hasTime}
      dateFormat={filterParams.hasTime ? "MM/dd/yy h:mm a" : "MM/dd/yy"}
      includeNonBusinessDays={filterParams.includeNonBusinessDays}
    />
  );
});

CADateInputWrapper.displayName = "CADateInputWrapper";

export default CADateInputWrapper;
