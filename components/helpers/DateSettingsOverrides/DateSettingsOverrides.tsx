import { Text } from "@chakra-ui/react";
import React from "react";
import PricerValueDisplayWrapper from "@/components/views/Pricer/shared/PricerValueDisplayWrapper";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { getFormattedLocaleDate } from "@/utils/helpers";

export const DateSettingsOverrides = () => {
  const {
    state: { userSettings },
  } = usePricerModule();

  return (
    <div>
      {/* Display Input Data Overrides */}
      {Object.values(userSettings.input_data_override ?? {}).some(Boolean) && (
        <Text variant="securitySubHeading" mt={1.5}>
          Overrides
        </Text>
      )}
      {userSettings.input_data_override?.current_coupon_calibration_date_override && (
        <PricerValueDisplayWrapper
          name="Current Coupon Calibration Date"
          link
          value={getFormattedLocaleDate(userSettings.input_data_override?.current_coupon_calibration_date_override)}
          _key="input_data_override.current_coupon_calibration_date_override"
          dateFormatter={getFormattedLocaleDate}
          drawerKey="Current Coupon Calibration Date Override"
        />
      )}
      {userSettings.input_data_override?.curve_date_override && (
        <PricerValueDisplayWrapper
          name="Curve Date"
          link
          value={getFormattedLocaleDate(userSettings.input_data_override?.curve_date_override)}
          _key="input_data_override.curve_date_override"
          dateFormatter={getFormattedLocaleDate}
          drawerKey="Curve Date Override"
        />
      )}
      {userSettings.input_data_override?.interest_rate_calibration_date_override && (
        <PricerValueDisplayWrapper
          name="Interest Rate Calibration Date"
          link
          value={getFormattedLocaleDate(userSettings.input_data_override?.interest_rate_calibration_date_override)}
          _key="input_data_override.interest_rate_calibration_date_override"
          dateFormatter={getFormattedLocaleDate}
          drawerKey="Interest Rate Calibration Date Override"
        />
      )}
      {userSettings.input_data_override?.survey_rate_date_override && (
        <PricerValueDisplayWrapper
          name="Survey Rate Date"
          link
          value={getFormattedLocaleDate(userSettings.input_data_override?.survey_rate_date_override)}
          _key="input_data_override.survey_rate_date_override"
          dateFormatter={getFormattedLocaleDate}
          drawerKey="Survey Rate Date Override"
        />
      )}
    </div>
  );
};
