import { ButtonGroup, IconButton, SpaceProps } from "@chakra-ui/react";
import { IoBarChartOutline, IoGridOutline, IoListOutline } from "react-icons/io5";

export type GridViewKeysType = keyof typeof GridView;

export type GridViewValuesType = (typeof GridView)[GridViewKeysType];

export const GridView = {
  CHART: "chart",
  GRID: "grid",
  TABLE: "table",
} as const;

interface GridChartToggleProps {
  hasTableView?: boolean;
  selectedView: GridViewValuesType;
  setSelectedView?: (view: GridViewValuesType) => void;
  styleProps?: SpaceProps;
}
const GridChartToggle: React.FC<GridChartToggleProps> = ({
  hasTableView = false,
  selectedView,
  setSelectedView,
  styleProps,
}: GridChartToggleProps) => {
  return (
    <ButtonGroup size="xs" isAttached variant="outline" {...styleProps}>
      <IconButton
        icon={<IoListOutline />}
        onClick={() => setSelectedView?.(GridView.GRID)}
        size="sm"
        fontSize="xl"
        mr="-1px"
        variant={selectedView === "grid" ? "primary" : "secondary"}
        title="Grid"
        aria-label="Grid"
      />
      {hasTableView && (
        <IconButton
          icon={<IoGridOutline />}
          onClick={() => setSelectedView?.(GridView.TABLE)}
          size="sm"
          fontSize="xl"
          mr="-1px"
          variant={selectedView === "table" ? "primary" : "secondary"}
          title="Table"
          aria-label="Table"
        />
      )}
      <IconButton
        icon={<IoBarChartOutline />}
        onClick={() => setSelectedView?.(GridView.CHART)}
        size="sm"
        fontSize="xl"
        variant={selectedView === "chart" ? "primary" : "secondary"}
        title="Chart"
        aria-label="Chart"
      />
    </ButtonGroup>
  );
};

export default GridChartToggle;
