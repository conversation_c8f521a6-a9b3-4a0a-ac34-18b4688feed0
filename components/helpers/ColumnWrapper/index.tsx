import { Box, FlexProps, VStack } from "@chakra-ui/layout";
import { FlexboxProps, LayoutProps, SpaceProps } from "@chakra-ui/styled-system";
import { useBreakpointValue } from "@chakra-ui/react";
import CAHeading from "@/design-system/atoms/CAHeading";

export interface ColumnWrapperProps extends LayoutProps, SpaceProps, FlexProps {
  size?:
    | "sm"
    | "md"
    | "lg"
    | "2xl"
    | "inputs"
    | "charts"
    | "distribution-table-card-3"
    | "distribution-table-card-2"
    | "half"
    | "form"
    | "grid"
    | "dashboard-sm"
    | "dashboard-col-container"
    | "dashboard-col-level-one"
    | "dashboard-col-level-two"
    | "dashboard-col-level-three"
    | "bond-compare"
    | "dashboard-col-level-four"
    | "interest-rate-market"
    | "interest-rate-model"
    | "interest-rate-market-column"
    | "performance-col"
    | "edf-futures-col"
    | "slicer-collapsed-query";
  columnHeader?: string | React.JSX.Element;
  hasColumnHeader?: boolean;
}

const ColumnWrapper: React.FC<ColumnWrapperProps> = ({
  children,
  size = "md",
  columnHeader,
  hasColumnHeader = false,
  ...props
}: ColumnWrapperProps) => {
  const isMobile = useBreakpointValue({ base: false, sm: true, md: false });
  let w: LayoutProps["w"] = undefined;
  let maxW: LayoutProps["maxW"] = undefined;
  let alignItems: FlexboxProps["alignItems"] = undefined;
  switch (size) {
    case "sm":
      w = { base: "100%", sm: "100%", md: "33.33%", lg: "33.33%", "2xl": "13%", "3xl": "15%" };
      break;
    case "md":
      w = { base: "100%", sm: "100%", md: "33.33%", lg: "33.33%", "2xl": "13%", "3xl": "15%" };
      break;
    case "lg":
      w = { base: "100%", sm: "100%", md: "45%", lg: "45%", "2xl": "17%", "3xl": "22%" };
      break;
    case "2xl":
      w = { base: "100%", sm: "100%", md: "60%", lg: "60%", "2xl": "32%", "3xl": "33%" };
      break;
    case "inputs":
      w = { base: "100%", sm: "100%", md: "33.33%", lg: "33.33%", "2xl": "18%", "3xl": "17%" };
      break;
    case "distribution-table-card-3":
      w = { base: "100%", sm: "50%", md: "256px" };
      break;
    case "distribution-table-card-2":
      w = { base: "100%", sm: "50%", md: "180px" };
      break;
    case "charts":
      w = { base: "100%", sm: "100%", md: "40%", lg: "40%", "2xl": "24%", "3xl": "20%" };
      break;
    case "half":
      w = { base: "100%", sm: "100%", md: "50%" };
      break;
    case "form":
      w = { base: "100%", sm: "100%", md: "100%", lg: "100%", xl: "38%", "2xl": "32%", "3xl": "33%" };
      break;
    case "grid":
      w = { base: "100%", sm: "100%", md: "100%", lg: "100%", xl: "62%", "2xl": "68%", "3xl": "67%" };
      break;
    case "dashboard-col-container":
      w = { base: "100%", sm: "100%", md: "50%", lg: "50%", "2xl": "50%", "3xl": "50%" };
      maxW = { lg: "5xl" };
      break;
    case "dashboard-col-level-one":
      w = { base: "100%", sm: "100%", md: "100%", lg: "100%", "2xl": "100%", "3xl": "100%" };
      break;
    case "dashboard-col-level-two":
      w = { base: "100%", sm: "100%", md: "100%", lg: "50%", "2xl": "33.33%", "3xl": "33.33%" };
      alignItems = {
        base: "center",
        md: "flex-start",
      };
      break;
    case "dashboard-col-level-three":
      w = { base: "100%", sm: "100%", md: "100%", lg: "50%", "2xl": "66.66%", "3xl": "66.66%" };
      break;
    case "dashboard-col-level-four":
      w = { base: "100%", sm: "100%", md: "100%", lg: "100%", "2xl": "50%", "3xl": "50%" };
      alignItems = {
        base: "center",
        md: "flex-start",
      };
      break;
    case "bond-compare":
      w = { base: "100%", sm: "100%", md: "100%", lg: "100%", "2xl": "50%", "3xl": "50%" };
      break;
    case "interest-rate-market":
      w = { base: "100%", sm: "100%", md: "100%", lg: "75%", xl: "62%", "2xl": "60%", "3xl": "60%" };
      break;
    case "interest-rate-model":
      w = { base: "100%", sm: "100%", md: "80%", lg: "25%", xl: "37%", "2xl": "40%", "3xl": "40%" };
      break;
    case "interest-rate-market-column":
      w = { base: "100%", sm: "100%", md: "33%", lg: "33%", xl: "33%", "2xl": "33%", "3xl": "33%" };
      break;
    case "performance-col":
      w = { sm: "auto", md: "max-content" };
      break;
    case "slicer-collapsed-query":
      w = { base: "100%", xl: "auto" };
      break;
  }

  return (
    <Box w={w} maxW={maxW} p={{ base: 1, sm: 2 }} {...props}>
      {hasColumnHeader && (
        <Box {...(!(isMobile && !columnHeader) && { minH: { sm: "2em" }, mb: 2 })}>
          <CAHeading as="h2" pl={2} textTransform="uppercase" fontWeight="bold">
            {columnHeader}
          </CAHeading>
        </Box>
      )}
      <VStack spacing={4} alignItems={alignItems ?? "stretch"} h="100%">
        {children}
      </VStack>
    </Box>
  );
};

export default ColumnWrapper;
