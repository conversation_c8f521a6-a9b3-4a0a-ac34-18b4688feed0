import { Flex, IconButton } from "@chakra-ui/react";
import { FiActivity } from "react-icons/fi";
import { MdInvertColors } from "react-icons/md";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useActivitiesByBond } from "@/utils/swr-hooks/ActivityStream";
import { useColorsByBond } from "@/utils/swr-hooks/RunColor";
import { CA_Mastr_Api_v1_0_Models_SecurityInfo } from "@/utils/openapi";

const HeaderActionsMenu = ({
  bond_name,
  security_info,
  hasColorSupport,
}: {
  bond_name: string | undefined;
  security_info: CA_Mastr_Api_v1_0_Models_SecurityInfo | undefined;
  hasColorSupport: boolean;
}) => {
  const securityBondName =
    (security_info?.bond_type === "POOL" ? security_info?.poollist_name : security_info?.yieldbook_name) ?? bond_name;
  const { data: activityStreamData } = useActivitiesByBond(securityBondName);
  const { data: colorData } = useColorsByBond(securityBondName, hasColorSupport);

  const hasActivityStreams = activityStreamData?.total_rows && activityStreamData?.total_rows > 0;
  const hasColors = colorData?.total_rows && colorData?.total_rows > 0;
  if (!bond_name) return null;
  return (
    <Flex>
      {hasColors ? (
        <IconButton
          minW="fit-content"
          variant="unstyled"
          aria-label="color"
          mr="0.5rem"
          icon={<CAIcon display="flex" alignItems="center" as={MdInvertColors} size={3} variant="default" />}
          onClick={() => window.open("/color?security=" + securityBondName)}
        />
      ) : null}
      {hasActivityStreams ? (
        <IconButton
          minW="fit-content"
          variant="unstyled"
          aria-label="activity-stream"
          icon={<CAIcon display="flex" alignItems="center" as={FiActivity} size={3} variant="default" />}
          onClick={() => window.open("/activity-stream/run?security=" + securityBondName)}
          isDisabled={!hasActivityStreams}
        />
      ) : null}
    </Flex>
  );
};

export default HeaderActionsMenu;
