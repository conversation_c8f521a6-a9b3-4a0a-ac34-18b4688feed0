import React from "react";
import { <PERSON>, Button, HStack, Skeleton, Text, VStack, useColorModeValue } from "@chakra-ui/react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useSWRConfig } from "swr";
import AvatarProfile from "@/design-system/molecules/AvatarProfile";
import CAInputAutoSuggest from "@/design-system/molecules/CAInputAutoSuggest";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { showErrorToast } from "@/design-system/theme/toast";
import {
  CA_Mastr_Api_v1_0_Models_EntityShare_EntityShareResponse,
  CA_Mastr_Models_v1_0_Models_Entity,
  CA_Mastr_Models_v1_0_Models_EntityPermissionRole,
} from "@/utils/openapi";
import { usePostEntityPermission } from "@/utils/swr-hooks/EntityPermission";
import colors from "@/design-system/theme/colors";
import { useGetUsers } from "@/utils/swr-hooks/User";
import { userSlicerServiceKey } from "@/utils/swr-hooks/Slicer";
import { userEmpiricalTrackingsKey } from "@/utils/swr-hooks/UserEmpiricalTracking";
import AccessMenu from "./AccessMenu";
import { ShareEntityForm } from "./types";

interface Props {
  recordId: number;
  entity: CA_Mastr_Models_v1_0_Models_Entity;
  closeModal: () => void;
  entityPermissions: CA_Mastr_Api_v1_0_Models_EntityShare_EntityShareResponse | null | undefined;
  isLoadingEntityPermissions: boolean;
}

const getDefaultValues = ({ entityPermissions }: Pick<Props, "entityPermissions">) => {
  const isGeneralAccess = entityPermissions?.entity_share?.entity_permissions?.some(
    (permission) =>
      !permission.user && permission.roles?.includes(CA_Mastr_Models_v1_0_Models_EntityPermissionRole.VIEWER)
  );

  const persons = entityPermissions?.entity_share?.entity_permissions
    ?.filter((person) => person.user)
    .map((person) => ({
      user: person.user,
      role: person.roles?.[0],
    }));

  return {
    generalAccess: isGeneralAccess,
    persons,
  };
};
const ShareDetailView: React.FC<Props> = ({
  recordId,
  entity,
  closeModal,
  entityPermissions,
  isLoadingEntityPermissions,
}) => {
  const [userSelectSearch, setUserSelectSearch] = React.useState("");

  const bgHover = useColorModeValue(colors.misty[300], colors.celloBlue[1100]);

  const { register, control, getValues } = useForm<ShareEntityForm>({
    defaultValues: getDefaultValues({ entityPermissions }),
  });
  const { fields: persons, append: addPerson, remove: removePerson } = useFieldArray({ control, name: "persons" });

  const { data: users, isLoading: isLoadingUsers } = useGetUsers();
  const { trigger: saveEntityPermission, isMutating } = usePostEntityPermission();
  const { mutate } = useSWRConfig();

  const autoSuggestOptions = React.useMemo(() => {
    const selectedPersons = persons?.map((person) => person.user?.email) ?? [];

    return (
      users?.users
        ?.filter((user) => !selectedPersons.includes(user?.email))
        ?.map((user) => `${user?.first_name} ${user.last_name}|${user.email}`) ?? []
    );
  }, [users, persons]);

  const savePermissions = () => {
    const permissions = getValues();

    // check that at-least one owner is present
    const ownerPresent = permissions.persons?.some(
      (person) => person.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER
    );

    if (!ownerPresent) {
      showErrorToast("Error", "Please add at-least one owner.");
      return;
    }

    const payload = permissions?.persons?.map((person) => ({
      username: person.user?.email,
      role: person.role,
    }));

    if (permissions.generalAccess) {
      payload.push({
        username: "",
        role: CA_Mastr_Models_v1_0_Models_EntityPermissionRole.VIEWER,
      });
    }

    saveEntityPermission(
      {
        entity,
        entity_record_id: recordId,
        entity_permissions: payload,
      },
      {
        onSuccess: (data) => {
          closeModal();
          mutate(`EntityPermissionService.getEntityPermission-${entity}-${recordId}`);

          const newEntityShare = data?.entity_share;

          if (newEntityShare) {
            switch (newEntityShare.entity) {
              case CA_Mastr_Models_v1_0_Models_Entity.USER_SLICER:
                mutate(userSlicerServiceKey.getUserSlicer);
                if (newEntityShare.entity_record_id) {
                  mutate(userSlicerServiceKey.getUserSlicerById(newEntityShare.entity_record_id));
                }
                break;
              case CA_Mastr_Models_v1_0_Models_Entity.USER_EMPIRICAL_TRACKING:
                mutate(userEmpiricalTrackingsKey.empiricalTrackings);
                if (newEntityShare.entity_record_id) {
                  mutate(userEmpiricalTrackingsKey.empiricalTracking(newEntityShare.entity_record_id));
                }
                break;
            }
          }
        },
      }
    );
  };
  return (
    <>
      {isLoadingEntityPermissions || isLoadingUsers ? (
        <Skeleton h="25px" />
      ) : (
        <VStack spacing={6} w="full" alignItems="flex-start">
          <Box w="full">
            <CAInputAutoSuggest
              placeholder="Add people"
              variant="header"
              name="users-list"
              autoSuggestValues={autoSuggestOptions}
              inputValue={userSelectSearch}
              onChange={(_, { newValue }) => setUserSelectSearch(newValue.split("|")[0])}
              onSelect={(selectedValue) => {
                const [, email] = selectedValue.split("|");
                const selectedUser = users?.users?.find((user) => user.email === email);
                if (selectedUser) {
                  addPerson({ user: selectedUser, role: CA_Mastr_Models_v1_0_Models_EntityPermissionRole.VIEWER });
                  setUserSelectSearch("");
                }
              }}
              onClear={() => setUserSelectSearch("")}
              renderSuggestion={(suggestion) => {
                const [name, email] = suggestion.split("|");

                return (
                  <HStack spacing={4}>
                    <AvatarProfile name={name} />
                    <Box>
                      <Text fontWeight="semibold">{name}</Text>
                      <Text opacity="0.7">{email}</Text>
                    </Box>
                  </HStack>
                );
              }}
            />
          </Box>

          <VStack spacing={4} alignItems="flex-start" w="full">
            <Text variant="secondary" fontSize="xl">
              People with access
            </Text>
            <VStack alignItems="flex-start" w="full">
              {persons.map((person, index) => (
                <HStack
                  key={person.id}
                  cursor="default"
                  w="full"
                  justifyContent="space-between"
                  overflow="visible"
                  _hover={{
                    bg: bgHover,
                    boxShadow: `24px 0 0 0 ${bgHover},  -24px 0 0 0 ${bgHover}`,
                  }}
                  py={2}
                >
                  <HStack spacing={4}>
                    <AvatarProfile name={`${person?.user?.first_name ?? ""} ${person?.user?.last_name ?? ""}`.trim()} />
                    <Box>
                      <Text fontWeight="semibold">{person.user?.first_name + " " + person.user?.last_name}</Text>
                      <Text opacity="0.7">{person.user?.email}</Text>
                    </Box>
                  </HStack>
                  <Controller
                    control={control}
                    name={`persons.${index}.role`}
                    render={({ field: { value, onChange } }) => (
                      <AccessMenu
                        value={value}
                        onChange={(selectedOption) => {
                          if (selectedOption === "REMOVE_ACCESS") {
                            removePerson(index);
                          } else {
                            onChange(selectedOption);
                          }
                        }}
                      />
                    )}
                  />
                </HStack>
              ))}
            </VStack>
          </VStack>

          <Box w="full">
            <Text variant="secondary" fontSize="xl">
              General access
            </Text>
            <Box mt={2} w="inherit">
              <HStack justifyContent="space-between" alignItems="center">
                <Box>
                  <Text variant="primary" fontSize="md">
                    Share with your coworkers
                  </Text>
                  <Text>Give read-only access to your coworkers not listed above.</Text>
                </Box>
                <Box w="2rem">
                  <CASwitchInput {...register("generalAccess")} />
                </Box>
              </HStack>
            </Box>
          </Box>

          <HStack justifyContent="flex-end" w="full">
            <Button
              variant="primary"
              size="sm"
              onClick={savePermissions}
              isDisabled={isMutating}
              isLoading={isMutating}
            >
              Apply
            </Button>
          </HStack>
        </VStack>
      )}
    </>
  );
};

export default ShareDetailView;
