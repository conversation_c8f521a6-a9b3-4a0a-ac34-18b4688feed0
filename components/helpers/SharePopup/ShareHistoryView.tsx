import React from "react";
import { Box, Skeleton, Text } from "@chakra-ui/react";
import { useGetAuditHistoryByRecordId } from "@/utils/swr-hooks/AuditHistory";
import { CA_Mastr_Models_v1_0_Models_Entity } from "@/utils/openapi";
import { getFormattedLongDateWithTime } from "@/utils/helpers";

interface Props {
  recordId: number;
  entity: CA_Mastr_Models_v1_0_Models_Entity;
}

const ShareHistoryView: React.FC<Props> = ({ recordId, entity }) => {
  const { data, isValidating } = useGetAuditHistoryByRecordId({
    recordId: recordId.toString(),
    entity,
    cutoffDate: new Date().toISOString(),
  });

  return (
    <>
      {isValidating ? (
        <Skeleton h="25px" />
      ) : (
        <Box maxH="40rem" overflow="auto">
          {data?.audit_histories?.map((history, index) => (
            <Box key={index} pt="4">
              <Text fontWeight="bold" fontSize="14px">
                {history.user?.first_name} {history.user?.last_name} {history.action?.toLowerCase()} this{" "}
                {history.entity}.
              </Text>
              <Text opacity="60%">{getFormattedLongDateWithTime(history.timestamp)}</Text>
            </Box>
          ))}
        </Box>
      )}
    </>
  );
};

export default ShareHistoryView;
