import React from "react";
import { Box, HStack, useDisclosure } from "@chakra-ui/react";
import { IoMdShare } from "react-icons/io";
import CAModal from "@/design-system/molecules/CAModal";
import CAIcon from "@/design-system/atoms/CAIcon";
import { CA_Mastr_Models_v1_0_Models_Entity } from "@/utils/openapi";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { useGetEntityPermission } from "@/utils/swr-hooks/EntityPermission";
import ShareDetailView from "./ShareDetailView";
import ShareHistoryView from "./ShareHistoryView";

type PopupView = "details" | "history";
interface Props {
  triggerElement?: (open: () => void) => React.JSX.Element;
  recordName: string;
  recordId: number;
  entity: CA_Mastr_Models_v1_0_Models_Entity;
  open?: boolean;
  toggleModal?: (state: boolean) => void;
}

const SharePopup: React.FC<Props> = ({ triggerElement, recordName, recordId, entity, open, toggleModal }) => {
  const { isOpen, onClose, onOpen } = useDisclosure();
  const isModalOpen = isOpen || !!open;
  const [view, setView] = React.useState<PopupView>("details");
  const { data: entityPermissions, isLoading: isLoadingEntityPermissions } = useGetEntityPermission(
    entity,
    recordId,
    isModalOpen
  );

  const openModal = () => {
    onOpen();
    toggleModal?.(true);
  };

  const closeModal = () => {
    onClose();
    toggleModal?.(false);
  };

  return (
    <Box>
      {triggerElement ? (
        <Box onClick={openModal}>{triggerElement(openModal)}</Box>
      ) : (
        <Box onClick={openModal} cursor="pointer">
          <CAIcon as={IoMdShare} variant="secondary" />
        </Box>
      )}
      <CAModal
        autoFocus={false}
        isOpen={isModalOpen}
        showCloseIcon
        onClose={closeModal}
        modalHeader={`Share '${recordName}'`}
        modalBodyProps={{ pb: 4 }}
      >
        <HStack mb={4} justifyContent="flex-end">
          <ToggleButtonGroup
            buttons={[
              { label: "Details", value: "details" },
              { label: "History", value: "history" },
            ]}
            onChange={setView}
            selectedButton={view}
          />
        </HStack>

        {isModalOpen && (
          <>
            <Box display={view === "details" ? "block" : "none"}>
              {entityPermissions && (
                <ShareDetailView
                  closeModal={closeModal}
                  entity={entity}
                  recordId={recordId}
                  entityPermissions={entityPermissions}
                  isLoadingEntityPermissions={isLoadingEntityPermissions}
                />
              )}
            </Box>
            <Box display={view === "history" ? "block" : "none"}>
              <ShareHistoryView recordId={recordId} entity={entity} />
            </Box>
          </>
        )}
      </CAModal>
    </Box>
  );
};

export default SharePopup;
