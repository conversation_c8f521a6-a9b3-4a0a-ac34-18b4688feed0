import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuDivider, MenuGroup, MenuItem, MenuList } from "@chakra-ui/react";
import React from "react";
import { IoChevronDown } from "react-icons/io5";
import { CA_Mastr_Models_v1_0_Models_EntityPermissionRole } from "@/utils/openapi";

interface Props {
  value: CA_Mastr_Models_v1_0_Models_EntityPermissionRole;
  onChange: (entity: CA_Mastr_Models_v1_0_Models_EntityPermissionRole | "REMOVE_ACCESS") => void;
}

const AccessMenu: React.FC<Props> = ({ value, onChange }) => {
  return (
    <Menu size="xs">
      <MenuButton size="sm" rightIcon={<IoChevronDown />} as={Button}>
        {value}
      </MenuButton>
      <MenuList maxW="6rem">
        <MenuGroup>
          <MenuItem onClick={() => onChange(CA_Mastr_Models_v1_0_Models_EntityPermissionRole.VIEWER)}>Viewer</MenuItem>
          <MenuItem onClick={() => onChange(CA_Mastr_Models_v1_0_Models_EntityPermissionRole.EDITOR)}>Editor</MenuItem>
          <MenuItem onClick={() => onChange(CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER)}>Owner</MenuItem>
        </MenuGroup>
        <MenuDivider />
        <MenuGroup>
          <MenuItem onClick={() => onChange("REMOVE_ACCESS")}>Remove access</MenuItem>
        </MenuGroup>
      </MenuList>
    </Menu>
  );
};

export default AccessMenu;
