import { Box, Stack, Text, useBreakpointValue, useColorModeValue } from "@chakra-ui/react";
import { HiOutlineChevronLeft, HiOutlineChevronRight } from "react-icons/hi";

const VerticalCollapsedCardPill = ({
  isCollapsed,
  expandCard,
  expandRight = true,
  title,
  rotate = "left",
}: {
  isCollapsed: boolean;
  expandCard: () => void;
  expandRight?: boolean;
  title: string;
  rotate?: "left" | "right";
}) => {
  const cardBg = useColorModeValue("celloBlue.100", "celloBlue.900");
  const writingMode = useBreakpointValue({ base: "unset", lg: "vertical-rl" }) ?? "unset";
  const iconBg = useColorModeValue("celloBlue.50", "celloBlue.800");

  if (!isCollapsed) return null;
  return (
    <Box
      bg={cardBg}
      borderRadius="2xl"
      px={{ base: 4, lg: 2 }}
      py={{ base: 2, lg: 4 }}
      cursor="pointer"
      onClick={expandCard}
    >
      <Stack direction={{ base: "row", lg: "column" }} gap={4}>
        <Text
          variant="cardHeaderDefault"
          sx={{ writingMode }}
          transform={{ lg: rotate === "right" ? "rotate(-360deg)" : "rotate(180deg)" }}
        >
          {title}
        </Text>
        <Box w="auto" cursor="pointer" borderRadius="full" bg={iconBg} p={1}>
          {expandRight ? <HiOutlineChevronRight /> : <HiOutlineChevronLeft />}
        </Box>
      </Stack>
    </Box>
  );
};

export default VerticalCollapsedCardPill;
