import {
  Flex,
  IconButton,
  Input,
  InputGroup,
  InputLeftAddon,
  Popover,
  PopoverAnchor,
  PopoverBody,
  PopoverContent,
  chakra,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import { useOutsideClick } from "@chakra-ui/react";
import React from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { AiOutlinePlusCircle, AiOutlineSearch } from "react-icons/ai";
import { useRouter } from "next/router";
import CAIcon from "@/design-system/atoms/CAIcon";
import S from "@/constants/strings";
import { SlicerQuerySelector } from "@/components/views/Slicer/shared/SlicerQuerySelector";
import { CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer } from "@/utils/openapi";
import { useGetUserSlicersSWR } from "@/utils/swr-hooks";

export const QuerySearch = () => {
  const ref = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const router = useRouter();
  const { isOpen, onToggle, onOpen, onClose } = useDisclosure();
  const [searchText, setSearchText] = React.useState<string>();
  const color = useColorModeValue("charcoal.200", "whiteAlpha.600");
  const bg = useColorModeValue("celloBlue.50", "celloBlue.1200");

  const { data, mutate } = useGetUserSlicersSWR();

  useOutsideClick({
    ref,
    handler: () => {
      onClose();
    },
  });

  useHotkeys(
    "Ctrl+B, Command+B",
    () => {
      inputRef.current?.focus();
      onToggle();
    },
    []
  );

  const handleQuerySelection = (selectedSlicerQuery: CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer) => {
    router.push("/slicer?id=" + selectedSlicerQuery.user_slicer_id);
    setSearchText("");
    onClose();
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setSearchText(value);
  };

  const handleInputClick = () => {
    onOpen();
    mutate();
  };
  return (
    <Flex justifyContent="center">
      <Popover placement="bottom" isOpen={isOpen} autoFocus={false}>
        <chakra.div ref={ref}>
          <PopoverAnchor>
            <InputGroup
              bg={bg}
              w={{ base: "full", md: "220px" }}
              cursor="pointer"
              onClick={handleInputClick}
              border="none"
              rounded="3xl"
            >
              <InputLeftAddon h="36px" bg={bg} border={0} rounded="3xl" pl={3} pr={2}>
                <CAIcon color={color} size={5} as={AiOutlineSearch} />
              </InputLeftAddon>
              <Input
                ref={inputRef}
                data-testid="query-search"
                h="36px"
                w="200px"
                type="text"
                cursor="pointer"
                placeholder={S.MODULES.SLICER.ENTER_QUERY}
                border="none"
                rounded="3xl"
                value={searchText}
                onChange={handleSearchChange}
                pl="0"
                _focus={{
                  boxShadow: "none",
                }}
                _placeholder={{
                  color,
                }}
              />
            </InputGroup>
          </PopoverAnchor>
          <PopoverContent
            w={{
              base: "90vw",
              xl: "auto",
            }}
          >
            <PopoverBody>
              <SlicerQuerySelector
                data={data}
                onSlicerQueryChange={handleQuerySelection}
                additionalTextSearch={searchText}
              />
            </PopoverBody>
          </PopoverContent>
        </chakra.div>
      </Popover>
      <IconButton
        ml="2"
        aria-label="New Query"
        title="New Query"
        h="36px"
        minW="36px"
        variant="ghost"
        onClick={(e) => {
          e.stopPropagation();
          window.open("/slicer");
        }}
        rounded="full"
        icon={<CAIcon size={5} as={AiOutlinePlusCircle} variant="secondary" />}
      />
    </Flex>
  );
};
