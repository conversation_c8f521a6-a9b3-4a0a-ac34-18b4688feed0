import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  IconButton,
  Text,
  Tooltip,
  chakra,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";
import { useForm } from "react-hook-form";
import { BsRecord2 } from "react-icons/bs";
import { RiScreenshot2Fill } from "react-icons/ri";
import CAFileUpload from "@/design-system/molecules/CAFileUpload/CAFileUpload";
import CAInput from "@/design-system/molecules/CAInput";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import CATextarea from "@/design-system/molecules/CATextarea";
import { FeedBackPayload, usePostFeedBackSWR } from "@/utils/swr-hooks/Feedback";
import { getAPIErrorMessage } from "@/utils/helpers";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";

type ClickUpFeedbackFormFields = Pick<FeedBackPayload, "subject" | "description" | "attachments" | "type">;

const feedbackTypeOptions = [
  {
    id: "Bug Report",
    value: "Bug Report",
  },
  {
    id: "Feature Request",
    value: "Feature Request",
  },
];

export const Feedback = ({
  onSuccessSubmit,
  onStartRecording,
  onScreenshot,
  onClear,
  mediaFiles,
}: {
  onSuccessSubmit: () => void;
  onStartRecording: () => void;
  onScreenshot: () => void;
  onClear: () => void;
  mediaFiles: File[] | null;
}) => {
  const {
    state: { userData },
  } = useAuthentication();
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { isSubmitting },
  } = useForm<ClickUpFeedbackFormFields>();
  const { trigger: postFeedback } = usePostFeedBackSWR();

  const files = watch("attachments");

  const wrapperColor = useColorModeValue("celloBlue.100", "celloBlue.1100");
  const wrapperFormColor = useColorModeValue("white", "celloBlue.800");
  const formColor = useColorModeValue("white", "celloBlue.900");
  const labelColor = useColorModeValue("celloBlue.500", "gemBlue.500");
  const urlColor = useColorModeValue("black", "white");

  const mediaFilesList = React.useMemo(() => {
    const fileList = new DataTransfer();

    mediaFiles?.forEach((file) => {
      fileList.items.add(file);
    });

    return fileList.files;
  }, [mediaFiles]);

  const onSubmit = handleSubmit(async (data) => {
    const params = new URLSearchParams(window.location.href);
    const runId = params.get("run_id") ?? undefined;

    await postFeedback(
      {
        ...data,
        run_id: runId,
        url: window.location.href,
        description: `[${data.type}] ${data.description}`,
        attachments: files?.length ? files : mediaFilesList,
      },
      {
        onSuccess: () => {
          showSuccessToast("Thank You", "Your submission has been received.");
          onSuccessSubmit();
          onClear();
          reset();
        },
        onError: (e) => showErrorToast("Error", getAPIErrorMessage(e)),
      }
    );
  });

  const handleDrop: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer.files.length) {
      setValue("attachments", e.dataTransfer.files);
    }
  };

  const handleClear = () => {
    onClear();
    setValue("attachments", undefined);
  };

  const params = new URLSearchParams(window.location.href);
  const run_id = params.get("run_id") ?? undefined;

  return (
    <>
      <Box p="1.563rem" pt="0.5rem" pb="3.5rem" bg={wrapperColor}>
        <Text fontSize="5xl" variant="formHead">
          Hello {userData?.user?.first_name} 👋
        </Text>
      </Box>
      <Box position="relative" minH={run_id ? "40.063rem" : "37.063rem"} p="1.563rem" bg={wrapperFormColor}>
        <chakra.form
          position="absolute"
          top="-6"
          px="1.25rem"
          width="87%"
          pt="1.5rem"
          pb="0.5rem"
          borderRadius="2xl"
          boxShadow="xl"
          bg={formColor}
          onSubmit={onSubmit}
        >
          <FormControl>
            <FormLabel color={labelColor}>Type*</FormLabel>
            <CASelectDropdown label={""} {...register("type")} options={feedbackTypeOptions} />
          </FormControl>
          <FormControl mt="1rem">
            <FormLabel color={labelColor}>Subject*</FormLabel>
            <CAInput {...register("subject", { required: true })} />
          </FormControl>
          <FormControl mt="1rem">
            <FormLabel color={labelColor}>Description*</FormLabel>
            <CATextarea resize="none" minH="4rem" {...register("description", { required: true })} />
          </FormControl>
          <FormControl mt="1rem">
            <FormLabel color={labelColor}>Attachments</FormLabel>
            <CAFileUpload
              files={files?.length ? files : mediaFilesList}
              onDrop={handleDrop}
              {...register("attachments")}
              onClear={handleClear}
            />
            {!files?.length && !mediaFilesList.length ? (
              <Flex gap={2} my={2}>
                <IconButton
                  w="full"
                  aria-label="screenshot"
                  title="Take Screenshot"
                  icon={<RiScreenshot2Fill size="25" />}
                  onClick={onScreenshot}
                />
                <IconButton
                  w="full"
                  aria-label="record"
                  title="Record Screen"
                  icon={<BsRecord2 size="30" />}
                  onClick={onStartRecording}
                />
              </Flex>
            ) : null}
          </FormControl>
          {run_id && (
            <Box mt="1rem">
              <Text variant="primary" fontSize="14px">
                Run ID
              </Text>
              <Text>{run_id}</Text>
            </Box>
          )}
          <Box mt="1rem">
            <Text variant="primary" fontSize="14px">
              URL
            </Text>
            <Tooltip label={window.location.href} placement="top">
              <CAInput
                variant="primary"
                fontSize="14px"
                placeholder={window.location.href}
                _placeholder={{ color: urlColor }}
                readOnly
              />
            </Tooltip>
          </Box>
          <Button
            isLoading={isSubmitting}
            disabled={isSubmitting}
            type="submit"
            mt="8"
            w="full"
            mb="0.5rem"
            variant="primary"
          >
            Submit
          </Button>
        </chakra.form>
      </Box>
    </>
  );
};
