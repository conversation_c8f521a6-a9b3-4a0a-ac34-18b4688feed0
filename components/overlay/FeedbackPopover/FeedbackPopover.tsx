import {
  Icon<PERSON>utton,
  <PERSON>over,
  <PERSON>overBody,
  <PERSON>over<PERSON>lose<PERSON>utton,
  <PERSON>over<PERSON>ontent,
  <PERSON>overHeader,
  PopoverTrigger,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import React from "react";
import { HiChevronDown } from "react-icons/hi";
import { RiMessage2Fill } from "react-icons/ri";
import { useDisclosure } from "@chakra-ui/react";
import { BsStopCircle } from "react-icons/bs";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import CAIcon from "@/design-system/atoms/CAIcon";
import S from "@/constants/strings";
import { useStandaloneMode } from "@/hooks/useStandaloneMode";
import { useMediaCapture } from "@/hooks/useMediaCapture";
import { useScrolledToBottom } from "@/hooks/useScrolledToBottom";
import { Feedback } from "./Feedback";

export const FeedbackPopover = () => {
  const isStandalone = useStandaloneMode();
  const { isOpen, onClose, onOpen } = useDisclosure();
  const isOnBottom = useScrolledToBottom();
  const { isRecording, startRecording, stopRecording, mediaFile, takeScreenshot, clearMediaFiles } = useMediaCapture({
    onStopRecord: onOpen,
  });

  const {
    state: { accessToken, userData },
  } = useAuthentication();
  const wrapperColor = useColorModeValue("celloBlue.100", "celloBlue.1100");

  const handleScreenshot = () => {
    takeScreenshot();
  };

  const handleStartRecording = () => {
    startRecording();
    onClose();
  };

  const handleStopRecording = () => {
    if (isRecording) {
      stopRecording();
    }
  };

  if (accessToken !== "empty_token" && (!accessToken || !userData)) {
    return null;
  }

  return (
    <Popover
      placement="top"
      onOpen={onOpen}
      isOpen={isOpen}
      onClose={onClose}
      closeOnBlur={false}
      data-html2canvas-ignore
    >
      <PopoverTrigger>
        <IconButton
          position="fixed"
          right="1.25rem"
          bottom={{ base: isStandalone ? "5.5rem" : "3.438rem", md: !isOnBottom ? "0.5rem" : "4.063rem" }}
          bg="magenta.500"
          borderRadius="2xl"
          variant="stop"
          aria-label="Feedback form"
          display={{ base: "flex" }}
          zIndex="sticky"
          onClick={handleStopRecording}
          icon={
            isRecording ? (
              <BsStopCircle size="23" />
            ) : (
              <CAIcon as={isOpen ? HiChevronDown : RiMessage2Fill} variant="light" />
            )
          }
        />
      </PopoverTrigger>

      <PopoverContent
        data-html2canvas-ignore
        right="1.25rem"
        bg={wrapperColor}
        width="sm"
        borderRadius="2xl"
        minW={"400px"}
        overflow="hidden"
      >
        <PopoverHeader fontSize="5xl" bg={wrapperColor} border="none" py="0.5" px="1.563rem" borderRadius="2xl">
          <Text as="span" fontSize="5xl" variant="formTitle">
            {S.APPLICATION.NAME}
          </Text>
        </PopoverHeader>
        <PopoverCloseButton top="5" mr="0.625rem" />
        <PopoverBody>
          <Feedback
            mediaFiles={mediaFile}
            onSuccessSubmit={onClose}
            onScreenshot={handleScreenshot}
            onStartRecording={handleStartRecording}
            onClear={clearMediaFiles}
          />
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
};
