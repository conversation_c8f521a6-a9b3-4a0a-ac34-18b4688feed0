import * as React from "react";
import { Flex, List, ListItem, useColorModeValue, useMediaQuery } from "@chakra-ui/react";
import { IoIosArrowDown } from "react-icons/io";
import { useRouter } from "next/router";
import { marketDataAccordionListItems, pricingAccordionListItems } from "@/constants/ListItems/accordionListItems";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { AccordionListItems, AccordionOption } from "@/types";
import Link from "@/design-system/atoms/Link";
import AccordionList from "@/design-system/molecules/AccordionList";
import PopoverMenu from "@/design-system/molecules/PopoverMenu";
import CAIcon from "@/design-system/atoms/CAIcon";
import { getAppNameFromRoute } from "@/utils/helpers";
import { qs } from "@/hooks/useQueryParameters";

const LeftNavigationContent: React.FC = () => {
  const {
    state: { isNavigationMenuCollapsed: _isNavigationMenuCollapsed, navAccordionExpandedIndex },
    action: { updateNavAccordionExpandedIndex },
  } = useGlobalContext();
  const router = useRouter();
  const currentRouteName = getAppNameFromRoute(router.pathname);
  let accordionListItems: AccordionListItems[] = [];
  const leftNavigationBackgroundColor = useColorModeValue("white", "celloBlue.1000");

  switch (currentRouteName) {
    case "pricer":
      accordionListItems = [...pricingAccordionListItems];
      break;
    case "market":
      accordionListItems = [...marketDataAccordionListItems];
      break;
  }

  const [isLargerThan1040] = useMediaQuery("(min-width: 75rem)"); //   75rem = 1040
  const isNavigationMenuCollapsed = _isNavigationMenuCollapsed && isLargerThan1040;
  const activeBackgroundColor = useColorModeValue("celloBlue.75", "celloBlue.1200");
  const titleColor = useColorModeValue("celloBlue.500", "gemBlue.500");
  const disabledTextColor = useColorModeValue("charcoal.200", "classicGray.600");

  const moduleListItems: AccordionListItems[] = React.useMemo(() => {
    return accordionListItems.map((i) => {
      const item = { ...i };
      item.items = item.items.map((o) => {
        const option = { ...o };

        // Append bond_name query string parameter
        if (router.query.bond_name) {
          option.href = `${option.href}?${qs.stringify({
            bond_name: `${router.query.bond_name}`,
          })}`;
        }
        return option;
      });
      return item;
    });
  }, [router.query.bond_name]); // eslint-disable-line react-hooks/exhaustive-deps

  const renderPopoverBody = (
    accordionObj: AccordionListItems,
    hideTitle: boolean,
    onClick?: (item: AccordionOption) => void
  ) => (
    <>
      {!hideTitle && (
        <ListItem key={accordionObj.id} px={8} mb={2} color={titleColor}>
          {accordionObj.title}
        </ListItem>
      )}
      {accordionObj.items.map((item: AccordionOption) => {
        const isActiveRoute = router.pathname && router.pathname !== "/" && item.href.split("?")[0] === router.pathname;
        const doesRouteExist = item.href !== "" && !item.href?.startsWith("?bond_name");
        const backgroundColor = isActiveRoute ? activeBackgroundColor : "inherit";
        return (
          <ListItem
            key={item.key}
            fontWeight={isActiveRoute ? "bold" : "normal"}
            px={8}
            py={2}
            onClick={onClick ? () => onClick(item) : undefined}
            background={backgroundColor}
          >
            <Link
              key={item.key}
              href={item.href}
              color={doesRouteExist ? "inherit" : disabledTextColor}
              _hover={{
                cursor: doesRouteExist ? "pointer" : "not-allowed",
              }}
            >
              {item.text}
            </Link>
          </ListItem>
        );
      })}
    </>
  );

  const renderCollapsedMenu = (
    accordianMenuList: AccordionListItems[],
    showDropDownIcon: boolean,
    hideTitle: boolean,
    onClick?: (item: AccordionOption) => void
  ) =>
    accordianMenuList.map((accordionObj) => {
      const isActiveMenu = router.pathname && router.pathname !== "/" && router.pathname.includes(accordionObj.key);
      const backgroundColor = isActiveMenu ? activeBackgroundColor : "inherit";
      return (
        <PopoverMenu
          key={accordionObj.id}
          trigger="hover"
          placement="right-start"
          triggerElement={
            <ListItem
              w="full"
              py={2}
              textAlign="center"
              fontSize="md"
              background={backgroundColor}
              {...(accordionObj.key === "module" && { height: "2.75rem" })}
            >
              <CAIcon as={accordionObj.icon} variant="primary" boxSize={5} asListIcon mr={0} />
              {showDropDownIcon && <CAIcon as={IoIosArrowDown} ml={1} boxSize={3} />}
            </ListItem>
          }
          popoverContent={{
            body: <List bg={leftNavigationBackgroundColor}>{renderPopoverBody(accordionObj, hideTitle, onClick)}</List>,
            renderInPortal: true,
          }}
          ml="-8px"
          py={3}
        />
      );
    });

  const onAccordionToggle = (expandedIndex: number | number[]) => {
    updateNavAccordionExpandedIndex(expandedIndex);
  };

  return (
    <Flex
      direction="column"
      bg={leftNavigationBackgroundColor}
      w={{ base: "unset", lg: isNavigationMenuCollapsed ? "60px" : "200px" }}
      style={{ transition: "opacity linear .25s, width linear 0.25s" }}
      h="full"
    >
      <Link href="/#"></Link>

      {!isNavigationMenuCollapsed ? (
        <AccordionList
          accordionList={moduleListItems}
          allowMultiple
          hideAccordionIcon
          defaultIndex={navAccordionExpandedIndex}
          onAccordionToggle={onAccordionToggle}
          currentRoute={router.pathname}
        />
      ) : (
        <List px={0} w="full" minW="60px" h="full">
          {renderCollapsedMenu(moduleListItems, false, false)}
        </List>
      )}
    </Flex>
  );
};

export default LeftNavigationContent;
