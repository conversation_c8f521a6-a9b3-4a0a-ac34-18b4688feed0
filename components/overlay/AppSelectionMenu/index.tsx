import * as React from "react";
import {
  Button,
  Center,
  Grid,
  GridItem,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoAppsSharp } from "react-icons/io5";
import { useRouter } from "next/router";
import Link from "@/design-system/atoms/Link";
import CAIcon from "@/design-system/atoms/CAIcon";
import { moduleDropdownOptions, moduleItemType } from "@/constants/ListItems/dropdownOptions";
import { getAppNameFromRoute } from "@/utils/helpers";

const AppSelectionMenu: React.FC = () => {
  const router = useRouter();
  const currentSelectedApp = getAppNameFromRoute(router.pathname);

  const selectedAppBackgroundColor = useColorModeValue("celloBlue.500", "celloBlue.500");
  const hoverBackgroundColor = useColorModeValue("celloBlue.100", "celloBlue.800");
  const appTextColor = useColorModeValue("celloBlue.400", "white");
  const selectedAppTextColor = useColorModeValue("misty.500", "misty.500");

  const inactiveAppTextColor = useColorModeValue("charcoal.200", "classicGray.600");

  const buttonStyleProps = {
    flex: 1,
    h: "auto",
    variant: "",
    _focus: { boxShadow: "none" },
  };

  const isSelected = (appName: string) => currentSelectedApp === appName;

  return (
    <Menu closeOnBlur isLazy>
      <MenuButton
        as={IconButton}
        cursor="pointer"
        aria-label="App Selection Menu"
        variant="ghost"
        icon={<CAIcon as={IoAppsSharp} variant="secondary" />}
      ></MenuButton>
      <MenuList mr={{ base: 3, md: 0 }} rootProps={{ style: { right: 10, zIndex: 2 } }} p={0} borderRadius="md">
        <Grid templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(3, 1fr)" }} rowGap={4} columnGap={4} m={8}>
          {moduleDropdownOptions.items?.map((item: moduleItemType) => (
            <GridItem w="120px" key={item.value}>
              <Center>
                <Button
                  {...buttonStyleProps}
                  px={4}
                  py={2}
                  backgroundColor={isSelected(item.value) ? selectedAppBackgroundColor : ""}
                  _hover={{
                    cursor: item.href !== "" ? "pointer" : "not-allowed",
                    bg: isSelected(item.value) || !item.href ? selectedAppBackgroundColor : hoverBackgroundColor,
                  }}
                >
                  <Link
                    href={item.href}
                    _hover={{
                      cursor: "inherit",
                    }}
                    pointerEvents={item.href ? "inherit" : "none"}
                    target="_blank"
                  >
                    <Center minH={"45px"}>
                      <CAIcon
                        as={item.icon}
                        boxSize={8}
                        variant={item.href === "" ? "disabled" : isSelected(item.value) ? "light" : "secondary"}
                      />
                    </Center>
                    <Text
                      whiteSpace="break-spaces"
                      pt={2}
                      letterSpacing={1.1}
                      variant="appNameLabel"
                      color={
                        item.href === ""
                          ? inactiveAppTextColor
                          : isSelected(item.value)
                          ? selectedAppTextColor
                          : appTextColor
                      }
                    >
                      {item.text}
                    </Text>
                  </Link>
                </Button>
              </Center>
            </GridItem>
          ))}
        </Grid>
      </MenuList>
    </Menu>
  );
};

export default AppSelectionMenu;
