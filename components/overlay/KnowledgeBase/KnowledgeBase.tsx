import { IconButton } from "@chakra-ui/react";
import * as React from "react";
import { AiOutlineQuestionCircle } from "react-icons/ai";
import CAIcon from "@/design-system/atoms/CAIcon";

export const KnowledgeBase = () => {
  const handleClick = () => {
    window.open("https://doc.clickup.com/55934/d/h/1pky-7020/2205e7e2c2a9bf5");
  };
  return (
    <>
      <IconButton
        aria-label="notifications"
        variant="ghost"
        icon={<CAIcon as={AiOutlineQuestionCircle} variant="secondary" boxSize={5} />}
        onClick={handleClick}
      />
    </>
  );
};
