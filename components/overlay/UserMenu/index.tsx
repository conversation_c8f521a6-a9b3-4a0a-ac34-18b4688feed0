import * as React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, useColorMode } from "@chakra-ui/react";
import { useSWRConfig } from "swr";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import AvatarProfile from "@/design-system/molecules/AvatarProfile";

const UserMenu: React.FC = () => {
  const { colorMode, toggleColorMode } = useColorMode();
  const {
    action: { openPage },
  } = useGlobalContext();
  const {
    state: { userData },
    action: { signOut },
  } = useAuthentication();
  const { mutate } = useSWRConfig();

  const logout = () => {
    signOut();
    // https://swr.vercel.app/docs/advanced/cache.en-US#modify-the-cache-data
    mutate(
      () => true,
      undefined, // update cache data to `undefined`
      { revalidate: false } // do not revalidate
    );
  };

  return (
    <Menu closeOnBlur closeOnSelect isLazy>
      <MenuButton cursor="pointer" h="100%">
        <Box ml={2}>
          <AvatarProfile name={`${userData?.user?.first_name ?? ""} ${userData?.user?.last_name ?? ""}`.trim()} />
        </Box>
      </MenuButton>
      <MenuList rootProps={{ style: { right: 0 } }} borderRadius="md">
        <MenuItem onClick={() => openPage("/profile", { isExternal: true })}>User Profile</MenuItem>
        <MenuItem onClick={toggleColorMode}>{colorMode === "dark" ? "Light" : "Dark"} Theme</MenuItem>
        <MenuDivider />
        <MenuItem onClick={logout}>Sign Out</MenuItem>
      </MenuList>
    </Menu>
  );
};

export default UserMenu;
