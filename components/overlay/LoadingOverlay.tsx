import React from "react";
import { Box, Spinner } from "@chakra-ui/react";

export const LoadingOverlay = ({ isLoading }: { isLoading: boolean }) => {
  return (
    <Box
      position="fixed"
      top={0}
      left={0}
      width="100%"
      height="100%"
      backgroundColor={`rgba(0, 0, 0, ${0.3})`} // customizable opacity
      zIndex={9999}
      display={isLoading ? "flex" : "none"} // Show overlay only when isLoading is true
      justifyContent="center"
      alignItems="center"
    >
      <Spinner color="white" size="xl" />
    </Box>
  );
};
