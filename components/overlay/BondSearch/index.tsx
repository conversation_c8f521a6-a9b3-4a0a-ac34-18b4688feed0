import * as React from "react";
import {
  Box,
  Button,
  Center,
  Flex,
  IconButton,
  InputGroup,
  InputLeftAddon,
  InputRightAddon,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  chakra,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoChevronDown, IoClose, IoSearch } from "react-icons/io5";
import CAIcon from "@/design-system/atoms/CAIcon";
import CAInputAutoSuggest, { CAInputAutoSuggestHandler } from "@/design-system/molecules/CAInputAutoSuggest";
import S from "@/constants/strings";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { getObjectFromValue, parseBondName, validateBondName } from "@/utils/helpers/bond-search";
import { showErrorToast, showWarningToast } from "@/design-system/theme/toast";
import { AGENCY_GPL, BOND_TYPE } from "@/constants/enums";
import { useGetLastAccessedBonds } from "@/utils/swr-hooks";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { FormattedStringOptionsType, getAPIErrorMessage, getFormattedStringOptions } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_Util_StringOption,
  CA_Mastr_Api_v1_0_Models_Util_StringOptionExt,
} from "@/utils/openapi";
import { useValidateBondSWR } from "@/utils/swr-hooks/Util";

type menuOption = (CA_Mastr_Api_v1_0_Models_Util_StringOptionExt | CA_Mastr_Api_v1_0_Models_Util_StringOption) & {
  label?: string;
};

const RenderOptionsDropdown = ({
  defaultLabel,
  options = [],
  selectedOption,
  onSelect,
  name,
  hasDescription,
}: {
  name: string;
  defaultLabel?: string;
  options: menuOption[] | null;
  selectedOption: FormattedStringOptionsType | undefined;
  hasDescription?: boolean;
  onSelect: (option: FormattedStringOptionsType) => void;
}) => {
  return (
    <Box w={{ base: "4.8rem", md: "5.5rem" }} marginInline="0.5rem">
      <CAMultiSelectDropdown
        key={`${selectedOption}`}
        placeholder={defaultLabel}
        tooltipText={selectedOption?.displayValue ?? defaultLabel}
        name={name}
        value={selectedOption}
        shouldOnlyReturnValue={false}
        isMultiSelect={false}
        hasOptionDescription={hasDescription}
        options={getFormattedStringOptions(options ?? [])}
        onChange={(selectedOption) => {
          onSelect(selectedOption as FormattedStringOptionsType);
        }}
        menuPosition="absolute"
      />
    </Box>
  );
};

interface BondSearchProps {
  selectedBond: string;
  isOpen?: boolean;
  onBondSelect: (bondName: string) => void;
}

const BondSearch = ({ selectedBond, onBondSelect, isOpen }: BondSearchProps) => {
  const {
    state: { metadata, agencyOptions },
  } = useGlobalContext();

  // constants
  const isMobile = useBreakpointValue({ base: true, sm: true, md: false, lg: false });
  const inputGutterBackgroundColor = useColorModeValue("misty.300", "celloBlue.900");

  const { data } = useGetLastAccessedBonds();
  const { trigger: validateBond, isMutating: isLoadingBond } = useValidateBondSWR();

  const autoSuggestBondList = data?.last_accessed_bonds ? data?.last_accessed_bonds : [];
  const invalidBondError = "Please make sure bond name is valid.";

  // state
  const [bondType, setBondType] = React.useState(BOND_TYPE.ALL);
  const [bond_name, setBondName] = React.useState(selectedBond);
  const [agency, setAgency] = React.useState<FormattedStringOptionsType | undefined>();
  const [coupon, setCoupon] = React.useState<FormattedStringOptionsType | undefined>();
  const [origYear, setOrigYear] = React.useState<FormattedStringOptionsType | undefined>();
  const [month, setMonth] = React.useState<FormattedStringOptionsType | undefined>();
  const [story, setStory] = React.useState<FormattedStringOptionsType | undefined>();

  const bondTypeMenuRef = React.useRef<HTMLButtonElement>(null);
  const autoSuggestRef = React.useRef<CAInputAutoSuggestHandler>(null);

  const setDefaultBondValues = React.useCallback(
    (bond: { [key: string]: string } | undefined, shouldChangeBondType?: boolean, byPassValidation = false) => {
      if (bond?.type === BOND_TYPE.TBA) {
        shouldChangeBondType && setBondType(bond.type);
        const agency = getObjectFromValue(bond.agency, "value", metadata?.basic_search_settings?.tba_agency ?? []);
        const coupon = getObjectFromValue(bond.coupon, "value", metadata?.basic_search_settings?.tba_coupon ?? []);
        const month = getObjectFromValue(bond.month, "value", metadata?.basic_search_settings?.tba_month ?? []);
        //If agency or coupon or month is not found, set to bond type ALL
        if (!byPassValidation && (!agency || !coupon || !month)) {
          return setBondType(BOND_TYPE.ALL);
        }
        setAgency(agency);
        setCoupon(coupon);
        setMonth(month);
      } else if (bond?.type === BOND_TYPE.COHORTS) {
        shouldChangeBondType && setBondType(bond.type);
        setAgency(getObjectFromValue(bond.agency, "value", metadata?.basic_search_settings?.spec_pool_agency ?? []));
        setOrigYear(getObjectFromValue(bond.origYear, "value", metadata?.basic_search_settings?.spec_pool_year ?? []));
        if (bond?.coupon) {
          setCoupon(getObjectFromValue(bond.coupon, "value", metadata?.basic_search_settings?.spec_pool_coupon ?? []));
          setStory(getObjectFromValue(bond.story, "value", metadata?.basic_search_settings?.spec_pool_story ?? []));
        } else {
          setStory(getObjectFromValue(bond.story, "value", metadata?.basic_search_settings?.spec_pool_gpl_story ?? []));
        }
      } else if (bond?.type === BOND_TYPE.ALL) {
        shouldChangeBondType && setBondType(bond.type);
        setBondName(bond.name);
      }
    },
    [metadata]
  );

  React.useEffect(() => {
    if (!isOpen) return;

    const timer = setTimeout(() => {
      setBondType(BOND_TYPE.ALL);
      setBondName("");
      autoSuggestRef.current?.focus();
    }, 50);

    return () => {
      clearTimeout(timer);
    };
  }, [isOpen]);

  React.useEffect(() => {
    const bondObj: { [key: string]: string } | undefined = parseBondName(selectedBond ?? "");
    setDefaultBondValues(bondObj, true);
  }, [selectedBond, setDefaultBondValues, isOpen]);

  const onAutoSuggestBondSelect = async (selectedValue: string) => {
    const bondName = selectedValue.toUpperCase().trim();
    await validateBond(
      { bond_name: bondName },
      {
        onSuccess: () => {
          setBondName(bondName);
          onBondSelect(bondName);
        },
        onError: (e) => {
          const message = getAPIErrorMessage(e);
          message && showWarningToast("Error", message);
          setBondName(selectedBond);
        },
      }
    );
  };

  const onBondTypeChangeHandler = (type: string) => {
    setBondType(type);
    const bondObj: { [key: string]: string } | undefined = parseBondName(selectedBond ?? "");
    if (bondObj?.type === type) {
      setDefaultBondValues(bondObj ?? "", undefined, true);
    } else if (BOND_TYPE.COHORTS === type) {
      setAgency(undefined);
      setCoupon(undefined);
      setOrigYear(undefined);
      setStory(undefined);
    } else if (BOND_TYPE.TBA === type) {
      setAgency(undefined);
      setCoupon(undefined);
      setMonth(undefined);
    }
  };

  const runBond = async (_bondName: string) => {
    const bondName = _bondName.toUpperCase().trim();
    await validateBond(
      { bond_name: bondName },
      {
        onSuccess: () => {
          setBondName(bondName);
          onBondSelect(bondName);
        },
        onError: (e) => {
          const message = getAPIErrorMessage(e);
          message && showWarningToast("Error", message);
        },
      }
    );
  };

  const onSubmitHandler = (e: React.SyntheticEvent) => {
    e.preventDefault();
    // prepare bond name here
    let bondName = "";
    switch (bondType) {
      case BOND_TYPE.ALL:
        if (!validateBondName(BOND_TYPE.ALL, bond_name ?? "")) {
          showErrorToast("Error", "Please enter a bond name.");
          break;
        }
        runBond(bond_name ?? "");
        break;

      case BOND_TYPE.COHORTS:
        if (agency?.value === AGENCY_GPL) {
          bondName = `${agency?.value ?? ""}.${origYear?.value ?? ""}.${story?.value ?? ""}`;
        } else {
          bondName = `${agency?.value ?? ""}.${coupon?.value ?? ""}.${origYear?.value ?? ""}${story?.value ?? ""}`;
        }
        if (!validateBondName(BOND_TYPE.COHORTS, bondName)) {
          showErrorToast("Invalid Bond", invalidBondError);
          break;
        }
        runBond(bondName);
        break;

      case BOND_TYPE.TBA:
        bondName = `${agency?.value ?? ""}${coupon?.value ?? ""}-TBA-${month?.value ?? ""}`;
        if (!validateBondName(BOND_TYPE.TBA, bondName)) {
          showErrorToast("Invalid Bond", invalidBondError);
          break;
        }
        runBond(bondName);
        break;
    }
  };

  const clearBondSelection = () => {
    setBondType(BOND_TYPE.ALL);
    setBondName("");
    setTimeout(() => {
      autoSuggestRef.current?.focus();
    }, 50);
  };

  const isClearButtonVisible = (bondType: string) => {
    if (bondType === BOND_TYPE.COHORTS) {
      return agency?.value || coupon?.value || origYear?.value || story?.value;
    } else if (bondType === BOND_TYPE.TBA) {
      return agency?.value || coupon?.value || month?.value;
    }
  };

  if (!isOpen) return null;

  return (
    <chakra.form onSubmit={onSubmitHandler}>
      <InputGroup
        size="md"
        height={isMobile && bondType !== BOND_TYPE.ALL ? "5.125rem" : "2.5rem"}
        justifyContent="center"
      >
        <InputLeftAddon p={0} height="inherit" border="none">
          <Menu variant="primary" gutter={0} autoSelect={false}>
            <MenuButton
              zIndex={1}
              ref={bondTypeMenuRef}
              as={Button}
              variant="menuPrimary"
              pl={isMobile ? 1.5 : 2.5}
              pr={1.5}
              width={{
                base: BOND_TYPE.COHORTS === bondType ? "4.5rem" : "3.75rem",
                sm: BOND_TYPE.COHORTS === bondType ? "4.5rem" : "3.75rem",
                md: "5.25rem",
              }}
              height="inherit"
              borderTopRightRadius="0"
              borderBottomRightRadius="0"
              rightIcon={<CAIcon as={IoChevronDown} variant="light" />}
            >
              {bondType}
            </MenuButton>
            <MenuList minW="5.25rem">
              {Object.values(BOND_TYPE).map((type) => (
                <MenuItem key={type} onClick={() => onBondTypeChangeHandler(type)}>
                  {type}
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
        </InputLeftAddon>
        {bondType === BOND_TYPE.ALL && (
          <Box w={{ base: "100%", sm: "100%", md: "20rem", lg: "25rem" }}>
            <CAInputAutoSuggest
              ref={autoSuggestRef}
              name="bond_name"
              placeholder={S.MODULES.PRICER.ENTER_BOND}
              inputValue={bond_name ?? ""}
              onChange={(_, { newValue }) => setBondName(newValue)}
              onClear={clearBondSelection}
              onSelect={onAutoSuggestBondSelect}
              autoSuggestValues={autoSuggestBondList}
              title={S.MODULES.PRICER.ENTER_BOND}
              variant="header"
            />
          </Box>
        )}
        {bondType === BOND_TYPE.COHORTS && (
          <Flex
            wrap="wrap"
            minW={{ base: "12.5rem", sm: "12.5rem", md: "25rem" }}
            bg={inputGutterBackgroundColor}
            alignItems="center"
            justifyContent={isMobile ? "center" : "flex-start"}
            px={isMobile ? "0" : "0.5rem"}
          >
            <RenderOptionsDropdown
              name="cohort-agency"
              defaultLabel="Agency"
              hasDescription
              selectedOption={agency}
              onSelect={(option) => {
                setAgency(option);
                if (option?.value === AGENCY_GPL || (agency?.value === AGENCY_GPL && option?.value !== AGENCY_GPL)) {
                  setStory(undefined);
                  setOrigYear(undefined);
                  setCoupon(undefined);
                } else {
                  setStory(undefined);
                }
              }}
              options={metadata?.basic_search_settings?.spec_pool_agency ?? []}
            />
            {agency?.value !== AGENCY_GPL && (
              <RenderOptionsDropdown
                name="cohort-coupon"
                defaultLabel="Coupon"
                selectedOption={coupon}
                onSelect={setCoupon}
                options={metadata?.basic_search_settings?.spec_pool_coupon ?? []}
              />
            )}
            <RenderOptionsDropdown
              name="cohort-origYear"
              defaultLabel="Orig Year"
              selectedOption={origYear}
              onSelect={setOrigYear}
              options={metadata?.basic_search_settings?.spec_pool_year ?? []}
            />
            <RenderOptionsDropdown
              name="cohort-story"
              defaultLabel="Story"
              hasDescription
              selectedOption={story}
              onSelect={setStory}
              options={
                agency?.value === AGENCY_GPL
                  ? metadata?.basic_search_settings?.spec_pool_gpl_story ?? []
                  : metadata?.basic_search_settings?.spec_pool_story?.filter((s) =>
                      s?.supported_agencies?.includes(agency?.value ?? "")
                    ) ?? []
              }
            />
          </Flex>
        )}
        {bondType === BOND_TYPE.TBA && (
          <Flex
            wrap="wrap"
            minW={{ base: "13rem", sm: "13rem", md: "25rem" }}
            bg={inputGutterBackgroundColor}
            alignItems="center"
            justifyContent={isMobile ? "center" : "flex-start"}
            px="0.5rem"
            w="full"
          >
            <RenderOptionsDropdown
              name="tba-agency"
              defaultLabel="Agency"
              hasDescription
              selectedOption={agency}
              onSelect={setAgency}
              options={agencyOptions ?? []}
            />
            <RenderOptionsDropdown
              name="tba-coupon"
              defaultLabel="Coupon"
              selectedOption={coupon}
              onSelect={setCoupon}
              options={metadata?.basic_search_settings?.tba_coupon ?? []}
            />
            <RenderOptionsDropdown
              name="tba-month"
              defaultLabel="Month"
              selectedOption={month}
              onSelect={setMonth}
              options={metadata?.basic_search_settings?.tba_month ?? []}
            />
          </Flex>
        )}

        {bondType !== BOND_TYPE.ALL && isClearButtonVisible(bondType) && (
          <Center pr="0.5rem" verticalAlign={"center"}>
            <IconButton
              className="clear-button"
              onClick={clearBondSelection}
              aria-label="Clear"
              size="xs"
              variant="primary"
              background="transparent"
              border="0"
              color="celloBlue.200"
              icon={<CAIcon as={IoClose} variant="default" />}
            />
          </Center>
        )}

        <InputRightAddon p={0} overflow="hidden" height="inherit" border="none">
          <IconButton
            data-testid="load-bond"
            aria-label={S.MODULES.PRICER.LOAD_BOND}
            size="lg"
            variant="primary"
            height="inherit"
            type="submit"
            isLoading={isLoadingBond}
            isDisabled={isLoadingBond}
            icon={<CAIcon as={IoSearch} variant="default" boxSize={5} />}
            borderTopLeftRadius="0"
            borderBottomLeftRadius="0"
          />
        </InputRightAddon>
      </InputGroup>
    </chakra.form>
  );
};

export default BondSearch;
