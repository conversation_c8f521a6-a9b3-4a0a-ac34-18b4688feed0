import { Text } from "@chakra-ui/layout";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>lex,
  H<PERSON>tack,
  Spacer,
  VStack,
  chakra,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import * as React from "react";
import { Controller, useForm } from "react-hook-form";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { EmpiricalTrackingUserSettingsType } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext/EmpiricalTrackingModuleContextTypes";
import CAHeadingOriginal from "@/design-system/atoms/CAHeading";
import CAInput from "@/design-system/molecules/CAInput";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { isSuperAdmin } from "@/utils/helpers";
import useResetFormData from "@/hooks/useResetFormData";
import { searchByKeywords } from "@/hooks/useKeywordsSearch";
import { useEmpiricalTrackingModule } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext";
import EmpiricalTrackingValueDisplayWrapper from "../EmpiricalTrackingValueDisplayWrapper/EmpiricalTrackingValueDisplayWrapper";

export type EmpiricalTrackingSettingsDrawerProps = {
  onClose: () => void;
  isOpen: boolean;
};

const SectionHeading: React.FC<React.PropsWithChildren & { allCaps?: boolean }> = ({ children, allCaps = true }) => (
  <Box pt={2}>
    <CAHeadingOriginal as={"h3"} variant={"subHeading"} textTransform={allCaps ? "uppercase" : "none"}>
      {children}
    </CAHeadingOriginal>
  </Box>
);

const EmpiricalTrackingSettingsDrawer: React.FC<EmpiricalTrackingSettingsDrawerProps> = ({
  onClose,
  isOpen,
}: EmpiricalTrackingSettingsDrawerProps) => {
  const {
    state: { userData },
  } = useAuthentication();
  const {
    state: { userSettings, activeSettingsDrawerKey },
    action: { updateEmpiricalTrackingUserSettings },
  } = useEmpiricalTrackingModule();
  const [searchKey, setSearchKey] = React.useState("");
  const isMobile = useBreakpointValue({ sm: true, md: false });

  const { register, handleSubmit, reset, control } = useForm<EmpiricalTrackingUserSettingsType>();

  useResetFormData({ reset, formData: userSettings });

  const onSubmit = (data: EmpiricalTrackingUserSettingsType) => {
    updateEmpiricalTrackingUserSettings(data);
    closeDrawer(data);
  };

  const setDisplayForField = (fieldName: string) => {
    return searchByKeywords(searchKey, fieldName) ? "block" : "none";
  };

  const closeDrawer = (data?: EmpiricalTrackingUserSettingsType) => {
    setSearchKey("");
    reset(data ? data : userSettings);
    onClose();
  };

  const isActiveKey = (settingsKey: string) => {
    return settingsKey === activeSettingsDrawerKey && !isMobile;
  };

  return (
    <Drawer autoFocus={false} placement="right" onClose={() => closeDrawer()} isOpen={isOpen} size={"xs"}>
      <DrawerOverlay>
        <DrawerContent overflowY="scroll">
          <chakra.form p={4} onSubmit={handleSubmit(onSubmit)} position="relative">
            <VStack alignItems="stretch" pt={5}>
              <SectionHeading>Application</SectionHeading>
              <EmpiricalTrackingValueDisplayWrapper
                name="Version"
                value={process.env.NEXT_PUBLIC_GIT_TAG || process.env.NEXT_PUBLIC_GIT_HASH || "-"}
                _key="application_version"
              />
              <Box>
                <HStack justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text _hover={{ cursor: "pointer" }} variant="primary">
                      Use Cache
                    </Text>
                  </Box>
                  <Box>
                    <Controller
                      name={"useCache"}
                      control={control}
                      render={({ field: { name, onChange, value } }) => {
                        return (
                          <CASwitchInput
                            mr="-2"
                            name={name}
                            hideLabel={true}
                            value={"useCache"}
                            defaultChecked={value}
                            onChange={onChange}
                          />
                        );
                      }}
                    />
                  </Box>
                </HStack>
              </Box>
              {isSuperAdmin(userData) && (
                <Box display={setDisplayForField("Timeout Minutes")}>
                  <CAInput
                    type="number"
                    inputType="positive-integer"
                    label={"Timeout (min)"}
                    {...register("timeoutMinutes", {
                      valueAsNumber: true,
                      required: true,
                    })}
                    {...{ autoFocus: isActiveKey("Timeout Minutes") }}
                  />
                </Box>
              )}
            </VStack>
            <Flex
              direction="row"
              mt={4}
              pt={3}
              pb={3}
              position="sticky"
              bottom={0}
              bg={useColorModeValue("white", "celloBlue.900")}
            >
              <Spacer />
              <HStack spacing={2}>
                <Button variant="secondary" size="sm" onClick={() => closeDrawer()}>
                  Cancel
                </Button>
                <Button variant="primary" size="sm" type="submit">
                  Save
                </Button>
              </HStack>
            </Flex>
          </chakra.form>
        </DrawerContent>
      </DrawerOverlay>
    </Drawer>
  );
};

export default EmpiricalTrackingSettingsDrawer;
