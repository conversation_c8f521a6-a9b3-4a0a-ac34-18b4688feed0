import * as React from "react";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { getDefaultsOfMetadata, getDisplayValueByKey } from "@/utils/helpers";
import { useEmpiricalTrackingModule } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext";
import EmpiricalTrackingValueDisplay from "../EmpiricalTrackingValueDisplay/EmpiricalTrackingValueDisplay";

export type EmpiricalTrackingValueDisplayWrapperProps = {
  name: string;
  link?: boolean;
  _key?: string;
  runid?: string;
  value: string | React.JSX.Element;
  dateFormatter?: (dateString: Date | string | undefined | null) => void;
  drawerKey?: string;
  ignoreChanges?: boolean;
};

const EmpiricalTrackingValueDisplayWrapper: React.FC<EmpiricalTrackingValueDisplayWrapperProps> = ({
  name,
  link,
  _key,
  value,
  dateFormatter,
  drawerKey,
  ...rest
}: EmpiricalTrackingValueDisplayWrapperProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const {
    state: { userSettings, userSettingsCopy },
  } = useEmpiricalTrackingModule();
  const key = _key?.split(".")[1] ? _key?.split(".")[1] : _key ?? "";
  const settings = userSettings as { [key: string]: string | undefined };
  const settingsCopy = userSettingsCopy as { [key: string]: string | undefined };

  //Message if value changes
  const hasChanged = settings[key]?.toString() !== settingsCopy[key]?.toString() && settingsCopy[key] !== undefined;
  const changedMsg = `Value changed from ${
    dateFormatter ? dateFormatter(settingsCopy[key]) : getDisplayValueByKey(settingsCopy[key], metadata)
  } to ${dateFormatter ? dateFormatter(settings[key]) : getDisplayValueByKey(settings[key], metadata)}`;

  //Get default Values from metadata based on `is_default` field
  const defaultValues = getDefaultsOfMetadata(_key, metadata?.pricer_settings);
  const defaultValue = dateFormatter
    ? new Date(defaultValues?.value || "").toString()
    : defaultValues?.value?.toString();

  //Show this message if settings values are different from default values from metadata
  const hasOutdated = settings[key]?.toString() !== defaultValue && !hasChanged && defaultValue !== undefined;

  const msg2 = hasOutdated
    ? `Default value is ${dateFormatter ? dateFormatter(defaultValue) : defaultValues?.display_value}`
    : "Click run to update your calculation";

  return (
    <EmpiricalTrackingValueDisplay
      name={name}
      drawerKey={drawerKey}
      link={link}
      value={value}
      info={{
        show: hasChanged || hasOutdated,
        msg1: hasChanged ? changedMsg : undefined,
        msg2: msg2,
        type: hasChanged ? "changed" : hasOutdated ? "outdated" : "unset",
      }}
      {...rest}
    />
  );
};

export default EmpiricalTrackingValueDisplayWrapper;
