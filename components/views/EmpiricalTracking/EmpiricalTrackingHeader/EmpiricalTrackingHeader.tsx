import * as React from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Box, Flex, HStack, useBreakpointValue, useColorModeValue } from "@chakra-ui/react";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { blurActiveElement } from "@/utils/helpers";
import { RunInfo, RunTag, RunTagButtonGroup } from "@/design-system/molecules/RunTag/RunTag";
import { runToastManager } from "@/utils/run-toast-manager";
import RunAlertButton from "@/components/helpers/RunAlertButton";
import { useScrollDirection } from "@/hooks/useScrollDirection";
import { useEmpiricalTrackingModule } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext";

type EmpiricalTrackingHeader = {
  onRunClick?: (opts: { no_cache: boolean }) => void;
  stopWatch?: React.JSX.Element;
  progressIndicator?: React.JSX.Element;
  stopRunning?: React.JSX.Element;
  runInfo?: RunInfo;
  withRun?: boolean;
  rightSideContent?: React.JSX.Element;
  middleElement?: React.ReactNode;
};

const EmpiricalTrackingHeader: React.FC<EmpiricalTrackingHeader> = ({
  onRunClick,
  stopWatch,
  progressIndicator,
  stopRunning,
  runInfo,
  withRun = true,
  rightSideContent,
  middleElement,
}: EmpiricalTrackingHeader) => {
  const scrollDir = useScrollDirection();
  const {
    state: { isTimerRunning },
  } = useEmpiricalTrackingModule();
  const bg = useColorModeValue("celloBlue.75", "celloBlue.1200");
  const isMobile = useBreakpointValue({ sm: true, md: false });

  const onRun = React.useCallback(() => {
    runToastManager.removeToasts();
    onRunClick?.({ no_cache: true });
  }, [onRunClick]);

  useHotkeys("Ctrl+Enter, Command+Enter", onRun, {
    enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
  });

  useHotkeys(
    "Ctrl+Shift+Enter, Command+Shift+Enter",
    () => {
      blurActiveElement();
      runToastManager.removeToasts();
      onRunClick?.({ no_cache: true });
    },
    {
      enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
    }
  );

  const isProgressIndicatorVisible = isTimerRunning && progressIndicator;
  const isScrollingUp = scrollDir === "up";

  return (
    <>
      <Flex
        px={{ base: "4", sm: "5", md: "7" }}
        py={2}
        mb={-2}
        alignItems="center"
        justifyContent="space-between"
        minH="1.75rem"
        bg={bg}
        position="sticky"
        top={{ base: !withRun ? 0 : isScrollingUp ? "116px" : "0", lg: "64px" }}
        zIndex={1}
        flexWrap="wrap"
        gap={2}
      >
        <Flex flex="1" />

        <Flex justifyContent="center" alignItems="center">
          <HStack>{middleElement}</HStack>
        </Flex>

        <Flex
          flex="1"
          alignItems="center"
          justifyContent="flex-end"
          flexDirection={{ base: "row-reverse", md: "row" }}
          gap={runInfo?.id ? 2 : { base: 0, md: 4 }}
          display={{ base: "none", md: "flex" }}
        >
          {!isMobile && (
            <HStack display={isProgressIndicatorVisible ? "inline" : "none"} w="full">
              {progressIndicator}
            </HStack>
          )}
          {rightSideContent}
          <Box display={isProgressIndicatorVisible ? "none" : "block"}>
            <RunTagButtonGroup>
              <RunAlertButton />
              {runInfo?.id && <RunTag runInfo={runInfo} />}
            </RunTagButtonGroup>
          </Box>
          {withRun && !isMobile ? (
            <Flex alignItems="center" gap={4}>
              {stopWatch}
              {isTimerRunning && stopRunning ? (
                stopRunning
              ) : (
                <CARun onClick={onRun} disabled={isTimerRunning} title={S.MODULES.EMPIRICAL_TRACKING.RUN} />
              )}
            </Flex>
          ) : (
            stopWatch
          )}
        </Flex>
      </Flex>

      {withRun && isMobile && (
        <Flex alignItems="center" gap={4}>
          {isTimerRunning && stopRunning ? (
            stopRunning
          ) : (
            <CARun onClick={onRun} disabled={isTimerRunning} title={S.MODULES.EMPIRICAL_TRACKING.RUN} />
          )}
        </Flex>
      )}
    </>
  );
};

export default EmpiricalTrackingHeader;
