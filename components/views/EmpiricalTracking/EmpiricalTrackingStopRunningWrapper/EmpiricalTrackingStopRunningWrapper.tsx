import * as React from "react";

import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { cancel } from "@/utils/cancel";
import { useEmpiricalTrackingPage } from "@/contexts/PageContexts/EmpiricalTrackingPageContext/EmpiricalTrackingApp";
import { getParam } from "@/utils/helpers";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { useEmpiricalTrackingModule } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext";
import { useEmpiricalTrackingData } from "../EmpiricalTrackingView/EmpiricalTrackingView";

type EmpiricalTrackingRequestStopProps = {
  token: string | undefined;
};

export const EmpiricalTrackingRequestStopRunningWrapper: React.FC<EmpiricalTrackingRequestStopProps> = ({ token }) => {
  const {
    action: { setIsTimerRunning },
  } = useEmpiricalTrackingModule();
  const {
    action: { resetRun },
  } = useEmpiricalTrackingPage();
  const { replaceWithoutRendering } = useQueryParameters();
  const { setToken } = useEmpiricalTrackingData();

  const triggerCancel = React.useCallback(async () => {
    resetRun();
    setIsTimerRunning(false);
    const userEmpiricalTrackingIdParam = getParam("id");
    replaceWithoutRendering({ id: userEmpiricalTrackingIdParam ?? undefined }, true);
    setToken(undefined);
    await cancel(token);
  }, [setIsTimerRunning, replaceWithoutRendering, token, resetRun, setToken]);

  if (!token) {
    return <CARun disabled title={S.MODULES.PRICER.RUN} />;
  }
  return <CARun onClick={triggerCancel} isRunning={true} disabled={!token} title={S.MODULES.PRICER.STOP} />;
};
