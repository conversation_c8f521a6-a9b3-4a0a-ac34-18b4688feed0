import {
  Box,
  Center,
  Flex,
  HStack,
  IconButton,
  Input,
  InputGroup,
  InputLeftAddon,
  Popover,
  PopoverAnchor,
  PopoverBody,
  PopoverContent,
  Text,
  Tooltip,
  VStack,
  chakra,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import { useOutsideClick } from "@chakra-ui/react";
import React from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { AiOutlinePlusCircle, AiOutlineSearch } from "react-icons/ai";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { useRouter } from "next/router";
import { useSearchParams } from "next/navigation";
import { MdOpenInNew, MdPeople } from "react-icons/md";
import { create } from "zustand";
import CAIcon from "@/design-system/atoms/CAIcon";
import CATable, { CATableData, CATableItem } from "@/design-system/molecules/CATable";
import { getFormattedLocaleDate, getFormattedTime } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTracking,
  CA_Mastr_Models_v1_0_Models_EntityPermissionRole,
  CA_Mastr_Models_v1_0_Models_Page,
} from "@/utils/openapi";
import {
  useDeleteUserEmpiricalTrackingMutation,
  useUserEmpiricalTrackingsQuery,
} from "@/utils/swr-hooks/UserEmpiricalTracking";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import { useEmpiricalTrackingPage } from "@/contexts/PageContexts/EmpiricalTrackingPageContext/EmpiricalTrackingApp";
import { APP_ROUTES_V2, VALID_APPLICATIONS } from "@/constants/enums";
import { Tags } from "@/design-system/organisms/Tags/Tags";
import { ColumnHeader } from "@/design-system/molecules/CATable/CATableHeader";
import { useSortTable } from "@/hooks/useSortTable";
import { TagFilter } from "@/design-system/organisms/TagFilter/TagFilter";
import { useTagFilter } from "@/hooks/useTagFilter";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";

export const useLoadingQuery = create<{ isLoading: boolean; resetLoading: () => void }>((set) => ({
  isLoading: false,
  resetLoading: () => {
    set(() => ({ isLoading: true }));
    setTimeout(() => {
      set(() => ({ isLoading: false }));
    }, 1000);
  },
}));

export function UserEmpiricalTrackingSearch() {
  const {
    action: { setOldRun },
  } = useEmpiricalTrackingPage();
  const params = useSearchParams();
  const activeUserEmpiricalTrackingId = params.get("id");

  const { data: userEmpiricalTrackingListData, isLoading: isLoadingUserEmpiricalTrackingList } =
    useUserEmpiricalTrackingsQuery();
  const { trigger: deleteUserEmpiricalTracking } = useDeleteUserEmpiricalTrackingMutation();

  const tags = userEmpiricalTrackingListData?.user_empirical_trackings?.flatMap((empirical) => empirical.tags);
  const uniqueTags = [...new Set(tags)].filter(Boolean) as string[];

  const { filteredList, getInputProps, resetSearch } = useKeywordsSearch({
    getSearchableText: (item) =>
      `${item.user_empirical_tracking_name} ${item.user?.firstName} ${item.user?.lastName} ${item.tags?.join(" ")}`,
    list: userEmpiricalTrackingListData?.user_empirical_trackings,
  });
  const { getTableProps, sortedData } = useSortTable({ data: filteredList });

  const { data: filteredDataByTags, getTagFilterProps } =
    useTagFilter<CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTracking>({
      data: sortedData,
    });

  const ref = React.useRef<HTMLDivElement>(null);
  const inputRef = React.useRef<HTMLInputElement>(null);

  const router = useRouter();
  const { isOpen, onToggle, onOpen, onClose } = useDisclosure();
  const [searchText, setSearchText] = React.useState<string>();
  const [showTags, setShowTags] = React.useState(false);

  const resetLoading = useLoadingQuery((state) => state.resetLoading);

  const headers: ColumnHeader[] = [
    {
      header: "Query",
      accessor: "user_empirical_tracking_name",
      rightEl: (
        <Box
          w="5rem"
          pt="4px"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <CASwitchInput
            isChecked={showTags}
            onChange={(e) => {
              e.stopPropagation();
              const checked = e.currentTarget.checked;
              setShowTags(checked);
            }}
            label="Show Tags"
            labelPosition="left"
            labelStyle={{
              fontWeight: "normal",
              fontSize: "2xs",
            }}
            transform={"scale(0.9)"}
          />
        </Box>
      ),
    },
    { header: "Created by", accessor: "user.firstName" },
    {
      header: "Created",
      accessor: "inserted",
      style: {
        paddingLeft: 0,
      },
    },
    {
      header: "Updated",
      accessor: "updated",
      style: {
        paddingLeft: 0,
      },
    },
    {
      header: "Run",
      accessor: "user_empirical_tracking_result.run_date",
      style: {
        paddingLeft: 0,
      },
    },
    { header: "Role", accessor: "role" },
    { header: "" },
  ];

  const inputColor = useColorModeValue("charcoal.200", "whiteAlpha.600");
  const bgColor = useColorModeValue("white", "celloBlue.1100");
  const inputBg = useColorModeValue("celloBlue.50", "celloBlue.1200");

  useOutsideClick({
    ref,
    handler: () => {
      onPopoverClose();
    },
  });

  useHotkeys(
    "Ctrl+B, Command+B",
    () => {
      inputRef.current?.focus();
      onToggle();
    },
    []
  );

  const onPopoverClose = () => {
    onClose();
    resetSearch();
  };

  const handleUserEmpiricalTrackingSelection = (_: CATableItem, index: number) => {
    if (filteredList?.length) {
      const selectedUserEmpiricalTracking = filteredList[index];
      setSearchText("");
      onPopoverClose();
      setOldRun(null);
      resetLoading();
      safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID);
      const userEmpiricalTrackingId = selectedUserEmpiricalTracking.user_empirical_tracking_id?.toString();

      if (userEmpiricalTrackingId) {
        safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID, userEmpiricalTrackingId);
        router.push({
          pathname:
            APP_ROUTES_V2[VALID_APPLICATIONS.EMPIRICAL_TRACKING]?.[CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING],
          query: {
            id: userEmpiricalTrackingId,
          },
        });
      }
    }
  };

  const handleDeleteUserEmpiricalTracking = async (
    userEmpiricalTracking: CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTracking
  ) => {
    const userEmpiricalTrackingId = userEmpiricalTracking.user_empirical_tracking_id;

    await deleteUserEmpiricalTracking({
      userEmpiricalTrackingId,
      lastUpdated: userEmpiricalTracking.updated ?? undefined,
    });

    if (userEmpiricalTrackingId === Number(activeUserEmpiricalTrackingId)) {
      const route =
        APP_ROUTES_V2[VALID_APPLICATIONS.EMPIRICAL_TRACKING]?.[CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING];

      if (route) {
        router.push(route);
      } else {
        console.error("Route is undefined");
      }
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.currentTarget.value;
    setSearchText(value);
  };

  const handleOpenNewQuery = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent> | React.MouseEvent<HTMLButtonElement, MouseEvent>,
    queryId?: number
  ) => {
    e.stopPropagation();
    /**
     * This is a workaround to avoid session storage to be copied to the new tab.
     * We store the last query id in the session storage and remove it before opening the new tab.
     * Then we restore it after the new tab is opened.
     * This is needed because the new tab will have the same session storage as the current tab which we don't want.
     */

    const lastRunId = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID);
    const lastQueryId = safeSessionStorage.getItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID);

    safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID);
    safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID);

    window.open(
      queryId
        ? `${
            APP_ROUTES_V2[VALID_APPLICATIONS.EMPIRICAL_TRACKING]?.[CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING]
          }?id=${queryId}`
        : APP_ROUTES_V2[VALID_APPLICATIONS.EMPIRICAL_TRACKING]?.[CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING],
      "_blank"
    );

    if (lastRunId) {
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID, lastRunId);
    }
    if (lastQueryId) {
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_QUERY_ID, lastQueryId);
    }
  };

  const tableData: CATableData | undefined = filteredDataByTags?.map((userEmpiricalTracking, index) => ({
    name: (
      <HStack minWidth="15rem">
        <Center
          title="Open in a new tab"
          p={0.5}
          onClick={(e) => handleOpenNewQuery(e, userEmpiricalTracking.user_empirical_tracking_id)}
        >
          <CAIcon as={MdOpenInNew} variant="secondary" />
        </Center>
        <VStack alignItems="flex-start" py="0.4rem" justifyContent="center">
          <Tooltip
            isDisabled={showTags || !userEmpiricalTracking.tags?.length}
            label={`Tags: ${userEmpiricalTracking.tags?.join(", ")}`}
          >
            <Flex gap={3}>
              <Text
                variant="tableLeft"
                noOfLines={1}
                maxWidth={"25rem"}
                overflowWrap={"anywhere"}
                wordBreak={"break-all"}
              >
                {userEmpiricalTracking.user_empirical_tracking_name ?? "N/A"}
              </Text>
              {userEmpiricalTracking.is_shared && <CAIcon ml={-1} as={MdPeople} variant="secondary" />}
            </Flex>
          </Tooltip>
          {userEmpiricalTracking.tags?.length && showTags ? (
            <Tags
              wrapperStyle={{ flexWrap: "wrap", maxW: "28rem" }}
              key="tags"
              tags={userEmpiricalTracking.tags}
              rowIndex={index}
            />
          ) : null}
        </VStack>
      </HStack>
    ),
    values: [
      `Lirak Haxhikadrija`,
      <Tooltip
        key={`${getFormattedLocaleDate(userEmpiricalTracking.inserted)}`}
        label={`${getFormattedLocaleDate(userEmpiricalTracking.inserted)} ${getFormattedTime(
          userEmpiricalTracking.inserted
        )}`}
      >
        <Text>{getFormattedLocaleDate(userEmpiricalTracking.inserted)}</Text>
      </Tooltip>,
      <Tooltip
        key={`${getFormattedLocaleDate(userEmpiricalTracking.updated)}`}
        label={`${getFormattedLocaleDate(userEmpiricalTracking.updated)} ${getFormattedTime(
          userEmpiricalTracking.updated
        )}`}
      >
        <Text>{getFormattedLocaleDate(userEmpiricalTracking.updated)}</Text>
      </Tooltip>,
      userEmpiricalTracking?.user_empirical_tracking_result?.run_date ? (
        <Tooltip
          key={`${getFormattedLocaleDate(userEmpiricalTracking?.user_empirical_tracking_result?.run_date)}`}
          label={`${getFormattedLocaleDate(
            userEmpiricalTracking?.user_empirical_tracking_result?.run_date
          )} ${getFormattedTime(userEmpiricalTracking?.user_empirical_tracking_result?.run_date)}`}
        >
          <Text>{getFormattedLocaleDate(userEmpiricalTracking?.user_empirical_tracking_result?.run_date)}</Text>
        </Tooltip>
      ) : (
        "-"
      ),
      userEmpiricalTracking.role ?? "",
      userEmpiricalTracking.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER &&
      !isLoadingUserEmpiricalTrackingList ? (
        <Center
          p={0.5}
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteUserEmpiricalTracking(userEmpiricalTracking);
          }}
        >
          <CAIcon as={RiDeleteBin6Fill} variant="secondary" />
        </Center>
      ) : (
        <Box />
      ),
    ],
  }));

  return (
    <Flex justifyContent="center">
      <Popover placement="bottom" isOpen={isOpen} autoFocus={false}>
        <chakra.div ref={ref}>
          <PopoverAnchor>
            <InputGroup
              bg={inputBg}
              w={{ base: "full", md: "220px" }}
              cursor="pointer"
              onClick={onOpen}
              border="none"
              rounded="3xl"
            >
              <InputLeftAddon h="36px" bg={inputBg} border={0} rounded="3xl" pl={3} pr={2}>
                <CAIcon color={inputColor} size={5} as={AiOutlineSearch} />
              </InputLeftAddon>
              <Input
                ref={inputRef}
                data-testid="user-empirical-tracking-search"
                h="36px"
                w="200px"
                type="text"
                cursor="pointer"
                placeholder="Search Query (Ctrl + B)"
                border="none"
                rounded="3xl"
                value={searchText}
                onChange={handleSearchChange}
                pl="0"
                _focus={{
                  boxShadow: "none",
                }}
                _placeholder={{
                  color: inputColor,
                }}
                {...getInputProps()}
              />
            </InputGroup>
          </PopoverAnchor>
          <PopoverContent
            w={{
              base: "90vw",
              xl: "auto",
            }}
            overflow={"hidden"}
          >
            <PopoverBody>
              <Flex>
                <TagFilter tags={uniqueTags} {...getTagFilterProps} />
                <Box pb="2" backgroundColor={bgColor} width="auto" maxH="30rem" minH="15rem" overflowX="auto">
                  <CATable
                    w="63rem"
                    headers={headers}
                    data={tableData ?? []}
                    variant="caFullCentered"
                    onItemClick={handleUserEmpiricalTrackingSelection}
                    headerStyles={{
                      backgroundColor: bgColor,
                      stickyPosition: "0",
                      textAlign: "left",
                    }}
                    columnsToLeftAlign={[0, 1, 2, 3, 4, 5]}
                    {...getTableProps()}
                  />
                </Box>
              </Flex>
            </PopoverBody>
          </PopoverContent>
        </chakra.div>
      </Popover>
      <IconButton
        ml="2"
        aria-label="New Query"
        title="New Query"
        h="36px"
        minW="36px"
        variant="ghost"
        onClick={handleOpenNewQuery}
        rounded="full"
        icon={<CAIcon size={5} as={AiOutlinePlusCircle} variant="secondary" />}
      />
    </Flex>
  );
}
