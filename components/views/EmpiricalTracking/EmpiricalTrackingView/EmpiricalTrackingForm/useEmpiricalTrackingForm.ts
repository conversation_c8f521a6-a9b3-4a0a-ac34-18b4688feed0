import { useForm } from "react-hook-form";
import Router from "next/router";
import {
  CA_Mastr_Api_v1_0_Models_Activity_RunActivityResponse,
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigInfo_Attribute,
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigInfo_Slice,
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigurationResponse,
  CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTrackingResponse,
  CA_Mastr_Models_v1_0_Models_Application,
  CA_Mastr_Models_v1_0_Models_ConfigurationType,
  CA_Mastr_Models_v1_0_Models_EmpiricalTracking_EmpiricalTrackingPayload,
  CA_Mastr_Models_v1_0_Models_EmpiricalTracking_Settings,
  CA_Mastr_Models_v1_0_Models_Operator,
  CA_Mastr_Models_v1_0_Models_Page,
} from "@/utils/openapi";
import { convertLocalDateToSameTimeISO, getDateFromNumericYYYYMMDD } from "@/utils/helpers";
import { getOperators, operatorMetaData } from "@/constants/slicer";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";

export type OperatorType = { symbol: string; value: CA_Mastr_Models_v1_0_Models_Operator };

export interface DimensionType extends CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigInfo_Slice {
  name?: string | null;
  values?: string[] | null;
  enabled?: boolean;
  display_name?: string | undefined | null;
  operator?: OperatorType;
  default_selections?: Array<string> | null;
}

export type ColumnType = CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigInfo_Attribute & {
  value?: string | null | undefined;
  enabled?: boolean | undefined;
  dimension?: string | null | undefined;
  values?: string[] | null | undefined;
};

export interface AlgoSeriesType {
  agency?: string | null;
  bond_name?: string | null;
  dimensions?: DimensionType[] | null;
  enabled?: boolean | undefined;
  algo_series_name?: string | null | undefined;
}

export interface EmpiricalTrackingFormType {
  settings?: CA_Mastr_Models_v1_0_Models_EmpiricalTracking_Settings;
  algo_series?: AlgoSeriesType[];
  columns?: ColumnType[];
}

export interface EmpiricalTrackingRunRequest {
  empirical_tracking_payload?: CA_Mastr_Models_v1_0_Models_EmpiricalTracking_EmpiricalTrackingPayload;
  run_id?: string;
  app?: CA_Mastr_Models_v1_0_Models_Application;
  no_cache?: boolean;
  page?: CA_Mastr_Models_v1_0_Models_Page;
}

export const dimensionDefaultValue: DimensionType[] = [
  {
    name: "",
    values: [],
    enabled: true,
    display_name: "",
    type: CA_Mastr_Models_v1_0_Models_ConfigurationType.VALUE,
  },
];

export const algoSeriesDefaultValues = [
  {
    algo_series_name: "All Dimensions",
    dimensions: [],
    enabled: true,
  },
];

export const defaultValues: EmpiricalTrackingFormType = {
  algo_series: algoSeriesDefaultValues,
  columns: [],
  settings: {
    use_pre_factor_date_balance: true,
  },
};

export const getDefaultOperator = (value: DimensionType) => {
  if (value?.type !== CA_Mastr_Models_v1_0_Models_ConfigurationType.VALUE) {
    const operators = getOperators(value?.type, {
      includeLookBack: value?.default_look_back !== undefined,
    });
    if (value.default_operator === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK) {
      return operators.find((operator) => operator.value === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK);
    }
    return operators[0];
  }
  return;
};

export const getDefaultValuesForOptions = (value: DimensionType, agency?: string) => {
  if (value.default_operator === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK) {
    return [value.default_look_back?.toString() ?? "1"];
  }
  if (value.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE) {
    const lowerBoundDate = getDateFromNumericYYYYMMDD(value.default_lower_bound);
    const upperBoundDate = getDateFromNumericYYYYMMDD(value.default_upper_bound);

    if ("agency_upper_bounds" in value) {
      const agencyUpperBound = value.agency_upper_bounds?.find((agencyUpperBound) => agencyUpperBound.name === agency);

      const agencyUpperBoundValue = getDateFromNumericYYYYMMDD(agencyUpperBound?.value);

      if (lowerBoundDate && agencyUpperBoundValue) {
        return [
          convertLocalDateToSameTimeISO(lowerBoundDate).substring(0, 10),
          convertLocalDateToSameTimeISO(agencyUpperBoundValue).substring(0, 10),
        ];
      }
    }

    if (lowerBoundDate && upperBoundDate) {
      return [
        convertLocalDateToSameTimeISO(lowerBoundDate).substring(0, 10),
        convertLocalDateToSameTimeISO(upperBoundDate).substring(0, 10),
      ];
    }
  }

  if (value.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST) {
    return value.default_selections;
  }

  return;
};

export const getDefaultValuesFromRequest = (
  values: CA_Mastr_Models_v1_0_Models_EmpiricalTracking_EmpiricalTrackingPayload,
  config: CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigurationResponse | undefined
): EmpiricalTrackingFormType => {
  if (!values) return defaultValues;

  return {
    algo_series: values?.algo_series?.map((algoSery) => {
      return {
        ...algoSery,
        dimensions: algoSery.dimensions
          ?.map((dimension) => {
            const configSlice = config?.empirical_tracking_config?.slices?.find(
              (slice) => slice.dimension === dimension.name
            );
            if (configSlice) {
              const operator = dimension.operator
                ? operatorMetaData[dimension.operator]
                : getDefaultOperator(configSlice);
              return {
                ...configSlice,
                operator,
                values: dimension.values,
                default_selections: dimension.values,
                enabled: dimension.enabled,
              };
            }
          })
          .filter(Boolean) as AlgoSeriesType[],
      };
    }),
    columns: values?.columns?.map((column) => {
      const obj2 = config?.empirical_tracking_config?.columns?.find((col) => col.dimension === column.dimension);
      const operator = obj2 ? getDefaultOperator(obj2) : null;
      const values = obj2 && getDefaultValuesForOptions(obj2);
      return { ...column, ...obj2, default_selections: column.values, operator, values: column.values ?? values };
    }),
    settings: values.settings,
  };
};

export const useEmpiricalTrackingForm = ({
  config,
}: {
  config: CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigurationResponse;
}) => {
  const routerQuery = Router.query;
  const form = useForm<EmpiricalTrackingFormType>({
    defaultValues: defaultValues,
  });

  const resetFormWithOldRunData = (data: CA_Mastr_Api_v1_0_Models_Activity_RunActivityResponse | undefined) => {
    if (!data) return;
    const runRequest = data?.run_activity?.run_request
      ? (JSON.parse(data?.run_activity?.run_request) as EmpiricalTrackingRunRequest)
      : undefined;

    const defaultValues = getDefaultValuesFromRequest(
      {
        algo_series: runRequest?.empirical_tracking_payload?.algo_series || [],
        columns: runRequest?.empirical_tracking_payload?.columns,
      },
      config
    );
    form.reset(defaultValues, {
      keepDefaultValues: true,
    });
  };

  const resetFormWithUserEmpiricalTrackingInputs = (
    data: CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTrackingResponse | undefined
  ) => {
    const runId = data?.user_empirical_tracking?.user_empirical_tracking_result?.run_id;
    if (runId) {
      safeSessionStorage.setItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID, runId);
      Router.push(
        {
          pathname: Router.pathname,
          query: {
            ...routerQuery,
            run_id: runId,
          },
        },
        undefined,
        { shallow: true }
      );
    }
    if (data?.user_empirical_tracking?.user_empirical_tracking_input) {
      const defaultValues = getDefaultValuesFromRequest(
        data?.user_empirical_tracking.user_empirical_tracking_input,
        config
      );
      form.reset(defaultValues);
    }
  };

  return {
    form,
    resetFormWithOldRunData,
    resetFormWithUserEmpiricalTrackingInputs,
  };
};
