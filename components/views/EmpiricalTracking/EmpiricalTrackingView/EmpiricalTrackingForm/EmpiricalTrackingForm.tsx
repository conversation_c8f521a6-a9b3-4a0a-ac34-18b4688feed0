import React from "react";
import { UseFormReturn } from "react-hook-form";
import { Box, GridItem } from "@chakra-ui/react";
import { Collapse } from "@/design-system/molecules/Collapse/Collapse";
import { EmpiricalTrackingAlgo } from "./components/EmpiricalTrackingAlgo/EmpiricalTrackingAlgo";
import { EmpiricalTrackingColumns } from "./components/EmpiricalTrackingColumns/EmpiricalTrackingColumns";
import { EmpiricalTrackingSettings } from "./components/EmpiricalTrackingSettings";
import { EmpiricalTrackingFormType } from "./useEmpiricalTrackingForm";

interface EmpiricalTrackingFormProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
}
export const EmpiricalTrackingForm = ({ form }: EmpiricalTrackingFormProps) => {
  const [isCollapsed, setIsCollapsed] = React.useState(false);

  return (
    <GridItem
      area="form"
      mr={{ base: isCollapsed ? "5" : "auto", lg: "unset" }}
      ml={{ base: isCollapsed ? "5" : "auto", lg: "5" }}
    >
      <Collapse isCollapsed={isCollapsed} onExpandClick={() => setIsCollapsed(false)}>
        <Box w="500px" h="calc(100vh - 11rem)" overflow="auto">
          <EmpiricalTrackingAlgo form={form} onCollapseClick={() => setIsCollapsed(true)} />
          <EmpiricalTrackingColumns form={form} />
          <EmpiricalTrackingSettings form={form} />
        </Box>
      </Collapse>
    </GridItem>
  );
};
