import React from "react";
import {
  Box,
  Button,
  Checkbox,
  Grid,
  GridItem,
  HStack,
  List,
  ListItem,
  ModalBody,
  ModalFooter,
  Stack,
  Text,
  Tooltip,
  useColorModeValue,
} from "@chakra-ui/react";
import { debounce } from "lodash";
import CASearch from "@/design-system/molecules/CASearch";
import { CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute } from "@/utils/openapi";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { UNIQUE_SEPERATOR, useNestedCheckbox } from "@/hooks/useNestedCheckbox";
import { normalizeColumnsForEmpiricalTrackingSelectionModal } from "@/utils/helpers/slicer";
import { SelectionTooltip } from "@/components/views/Slicer/SlicerQueryView/SlicerQueryFormComponents/SelectionTooltip";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";

export type ColumnSelectionOptionType = {
  label: string;
  value?: string;
} & CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute;

const toggleButtonItems = [
  {
    label: "FNM",
    value: "FNM",
  },
  {
    label: "FHL",
    value: "FHL",
  },
  {
    label: "GNM",
    value: "GNM",
  },
  {
    label: "GPL",
    value: "GPL",
  },
];

export const EmpiricalTrackingColumnsSelectionForm = ({
  defaultSelectedColumns,
  onModalClose,
  toggleIsOpen,
}: {
  defaultSelectedColumns: string[];
  onModalClose: (columns: string[]) => void;
  toggleIsOpen: (isOpen: boolean) => void;
}) => {
  const { data } = useEmpiricalTrackingConfiguration();
  const columnList = data?.empirical_tracking_config?.columns;
  const {
    state: { checkedItems },
    action: { onChildChange, onParentChange },
  } = useNestedCheckbox({
    defaultValues: defaultSelectedColumns,
  });

  const [agency, setAgency] = React.useState("FNM");
  const [search, setSearch] = React.useState("");

  const filteredColumns = React.useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return normalizeColumnsForEmpiricalTrackingSelectionModal(columnList as any, [agency]);
  }, [columnList, agency]);

  const onSubmit = () => {
    onModalClose(checkedItems);
    toggleIsOpen(false);
  };

  const displayCategory = (category: string, level: string) => {
    const matchesAnyChild = filteredColumns[level]
      ?.find((c) => c.category === category)
      ?.options?.find((option) => option?.display_name?.toLowerCase().includes(search.toLowerCase()));
    return matchesAnyChild ? "block" : "none";
  };

  const displayField = (groupLabel: string) => {
    if (!search) return "inherit";
    return groupLabel.toLowerCase().includes(search.toLowerCase()) ? "inherit" : "none";
  };

  return (
    <>
      <ModalBody backgroundColor={useColorModeValue("white", "celloBlue.1000")}>
        <HStack alignItems="center" justifyContent="space-between" wrap="wrap" rowGap={4} my={3}>
          <Box minW={{ base: "13rem", md: "20rem" }}>
            <CASearch
              name="search-columns"
              placeholder={"Quick Filter"}
              onChange={debounce(({ target: { value } }) => setSearch(value.trim()), 300)}
            />
          </Box>
          <ToggleButtonGroup selectedButton={agency} onChange={setAgency} buttons={toggleButtonItems} />
        </HStack>

        <Grid templateColumns={{ base: "repeat(1, 1fr)", md: "auto auto" }} mt={2} gridGap={12} overflow="auto">
          {Object.keys(filteredColumns).map((level) => (
            <GridItem key={level} mt={6}>
              <Box p={2}>
                <Text variant="tableLeft" fontSize="xl" fontWeight="bold">
                  {level}
                </Text>
              </Box>
              <List
                css={{
                  columnCount: 1,
                }}
              >
                {filteredColumns[level].map(({ category, options }) => {
                  const matchesCategory = category.toLowerCase().includes(search.toLowerCase());
                  const childrens = options.map((child) => child.dimension ?? "");
                  const allChecked = childrens.every((i) => checkedItems.includes(i));
                  const isIndeterminate = childrens.some((i) => checkedItems.includes(i)) && !allChecked;

                  return (
                    <ListItem
                      key={category}
                      p={1}
                      mb={3.5}
                      display={matchesCategory ? "block" : displayCategory(category as string, level)}
                      css={{
                        breakInside: "avoid",
                      }}
                    >
                      <Checkbox
                        isChecked={allChecked}
                        isIndeterminate={isIndeterminate}
                        value={childrens.join(UNIQUE_SEPERATOR)}
                        onChange={onParentChange}
                      >
                        <Text variant="tableHead">{category.replace(/(--([\w ])*)/g, "").trim()}</Text>
                      </Checkbox>
                      <Stack pl={6} mt={1} spacing={1}>
                        {options.map((child) => {
                          const displayName = (child.display_name ?? "").match(/.+\s(\d+m($|\s.+))/i);
                          return (
                            <Checkbox
                              display={matchesCategory ? "inherit" : displayField(child.display_name as string)}
                              key={child.dimension}
                              isChecked={checkedItems.includes(child.dimension ?? "")}
                              onChange={onChildChange}
                              value={child.dimension ?? undefined}
                            >
                              <Tooltip
                                label={
                                  <SelectionTooltip agenciesSupported={child.agencies} selections={child.selections} />
                                }
                                placement="right-end"
                              >
                                <Text variant="default">
                                  {displayName?.[2] || displayName?.[1] || child.display_name}
                                </Text>
                              </Tooltip>
                            </Checkbox>
                          );
                        })}
                      </Stack>
                    </ListItem>
                  );
                })}
              </List>
            </GridItem>
          ))}
        </Grid>
      </ModalBody>
      <ModalFooter gap={3}>
        <Button size="sm" variant="secondary" onClick={() => toggleIsOpen(false)}>
          Close
        </Button>
        <Button size="sm" variant="primary" onClick={onSubmit}>
          Apply
        </Button>
      </ModalFooter>
    </>
  );
};
