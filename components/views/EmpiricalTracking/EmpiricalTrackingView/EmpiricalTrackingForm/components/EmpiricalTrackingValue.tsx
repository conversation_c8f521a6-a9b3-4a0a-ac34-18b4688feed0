import React from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";

import { CA_Mastr_Models_v1_0_Models_ConfigurationType, CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";
import { transformStringToOptionType } from "../utils";
import { DimensionType, EmpiricalTrackingFormType } from "../useEmpiricalTrackingForm";
import { EmpiricalTrackingRangeInput } from "./EmpiricalTrackingAlgo/EmpiricalTrackingDimension/EmpiricalTrackingRangeInput";
import { EmpiricalTrackingLookBackInput } from "./EmpiricalTrackingAlgo/EmpiricalTrackingDimension/EmpiricalTrackingLookBackInput";

interface EmpiricalTrackingValueProps {
  dimension: DimensionType;
  index: number;
  dimensionIndex: number;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  isDisabled: boolean;
}
export const EmpiricalTrackingValue = ({
  dimension,
  form,
  index,
  dimensionIndex,
  isDisabled,
}: EmpiricalTrackingValueProps) => {
  const options = dimension?.selections?.map(transformStringToOptionType) ?? [];

  const operator = useWatch({
    control: form.control,
    name: `algo_series.${index}.dimensions.${dimensionIndex}.operator`,
  });

  if (dimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST)
    return (
      <Controller
        control={form.control}
        name={`algo_series.${index}.dimensions.${dimensionIndex}.values`}
        render={({ field: { onChange, value } }) => (
          <CAMultiSelectDropdown
            name={`algo_series.${index}.dimensions.${dimensionIndex}.values`}
            id="list"
            isMultiSelect
            shouldOnlyReturnValue
            options={options}
            value={value ?? undefined}
            onChange={onChange}
            isDisabled={isDisabled}
          />
        )}
      />
    );

  if (dimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE) {
    if (operator?.value === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK) {
      return (
        <EmpiricalTrackingLookBackInput
          format={dimension.format ?? undefined}
          index={index}
          dimensionIndex={dimensionIndex}
          form={form}
          isDisabled={isDisabled}
        />
      );
    }
    return (
      <EmpiricalTrackingRangeInput form={form} index={index} dimensionIndex={dimensionIndex} isDisabled={isDisabled} />
    );
  }

  return <CAMultiSelectDropdown name="values" value={[]} options={[]} isMultiSelect={false} />;
};
