import React from "react";
import { Box, Flex, useColorModeValue } from "@chakra-ui/react";
import { FieldArrayWithId, UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { HiOutlineChevronLeft } from "react-icons/hi";
import CAInput from "@/design-system/molecules/CAInput";
import CACard from "@/design-system/molecules/CACard";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { FilterCardPrimaryMenu } from "@/components/views/Slicer/SlicerQueryView/SlicerQueryFormComponents/FilterSeries/FilterCardPrimaryMenu";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import {
  ColumnType,
  DimensionType,
  EmpiricalTrackingFormType,
  algoSeriesDefaultValues,
  getDefaultOperator,
  getDefaultValuesForOptions,
} from "../../useEmpiricalTrackingForm";
import { EmpiricalTrackingDimensions } from "./EmpiricalTrackingDimension/EmpiricalTrackingDimensions";
import { EmpiricalTrackingAlgoMenu } from "./EmpiricalTrackingAlgoMenu";

export interface EmpiricalTrackingAlgoProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
  onCollapseClick: () => void;
}

export const EmpiricalTrackingAlgo = ({ form, onCollapseClick }: EmpiricalTrackingAlgoProps) => {
  const { data } = useEmpiricalTrackingConfiguration();
  const {
    fields: algos,
    append,
    remove,
  } = useFieldArray({
    control: form.control,
    name: "algo_series",
  });

  const addNewSlice = () => {
    append({
      algo_series_name: `All Dimensions ${algos.length + 1}`,
      enabled: true,
    });
  };

  const duplicateCurrentAlgoSeries = (i: number) => {
    const algoSerie = form.getValues(`algo_series.${i}`);
    append({ ...algoSerie, algo_series_name: `Copy of ${algoSerie.algo_series_name}` });
  };

  const removeColumnsAfterAlgoSeriesChange = (i: number) => {
    const columns = form.getValues("columns");
    const filteredAlgos = algos.filter((_, index) => index !== i);

    const newColumns: ColumnType[] = [];

    columns?.forEach((column) => {
      const columnAgency = column.agencies;
      const isAgencySupported = filteredAlgos.some((algo) => columnAgency?.includes(algo?.agency ?? ""));

      if (isAgencySupported) {
        newColumns.push(column);
      }
    });

    form.setValue("columns", newColumns);
  };

  const removeAlgoSerie = (i: number) => {
    if (algos.length === 1) {
      form.setValue("algo_series", algoSeriesDefaultValues);
      return;
    }
    removeColumnsAfterAlgoSeriesChange(i);
    remove(i);
    if (i === 0) {
      form.setValue("columns", []);
    }
  };

  const resetFormToInitialValues = () => {
    form.setValue("algo_series", algoSeriesDefaultValues);
  };

  const autoFillDimensions = (index: number) => {
    const agency = form.getValues(`algo_series.${index}.agency`);
    const slicesBySupportedAgency = agency
      ? data?.empirical_tracking_config?.slices?.filter((slice) => slice.agencies?.includes(agency))
      : [];

    const dimensions: DimensionType[] | undefined = slicesBySupportedAgency?.map((slice) => {
      const operator = getDefaultOperator(slice);
      const values = getDefaultValuesForOptions(slice);
      return {
        ...slice,
        name: slice.dimension,
        values: values,
        enabled: true,
        display_name: slice.display_name,
        operator: operator,
      };
    });

    form.setValue(`algo_series.${index}.dimensions`, dimensions);
  };

  return (
    <CACard
      title="MBS Universe Matching Algorithm"
      cardBodyStyle={{ p: 0 }}
      borderBottomRadius={0}
      headerStyle={{ height: "30px" }}
      headingRight={
        <>
          <Box
            mr="1"
            cursor="pointer"
            borderRadius="full"
            bg={useColorModeValue("celloBlue.50", "celloBlue.800")}
            p={1}
            onClick={onCollapseClick}
          >
            <HiOutlineChevronLeft />
          </Box>
          <FilterCardPrimaryMenu onAddClick={addNewSlice} onDeleteClick={resetFormToInitialValues} />
        </>
      }
    >
      <Flex flexDirection="column" ml={0.8} flex={1} pl="0.8rem" pr="1rem" pb="1rem" gap="1rem">
        {algos.map((algo, i) => {
          return (
            <AlgoRow
              key={algo.id}
              form={form}
              algo={algo}
              index={i}
              actionMenu={
                <EmpiricalTrackingAlgoMenu
                  onDuplicateClick={() => duplicateCurrentAlgoSeries(i)}
                  onDeleteClick={() => removeAlgoSerie(i)}
                  onResetClick={() => autoFillDimensions(i)}
                />
              }
            />
          );
        })}
      </Flex>
    </CACard>
  );
};

interface AlgoRowProps {
  algo: FieldArrayWithId<EmpiricalTrackingFormType, "algo_series", "id">;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  actionMenu: React.ReactNode;
  index: number;
}

const AlgoRow = ({ algo, form, actionMenu, index }: AlgoRowProps) => {
  const isEnabled = useWatch({ name: `algo_series.${index}.enabled`, control: form.control });
  return (
    <Flex key={algo.id} w="full">
      <Box w="1rem" mx={0.5} mt={1}>
        <CASwitchInput {...form.register(`algo_series.${index}.enabled`)} />
      </Box>
      <Box flex={1} ml="1rem" opacity={!isEnabled ? 0.5 : 1} pointerEvents={!isEnabled ? "none" : "auto"}>
        <Flex w="full" alignItems="center">
          <Box w="full" mr="0.5rem">
            <CAInput {...form.register(`algo_series.${index}.algo_series_name`)} isDisabled={!isEnabled} />
          </Box>
          {actionMenu}
        </Flex>
        <EmpiricalTrackingDimensions
          key={algo.id}
          isAlgoSerieEnabled={isEnabled}
          defaultSupportedAgency={algo.agency}
          index={index}
          form={form}
        />
      </Box>
    </Flex>
  );
};
