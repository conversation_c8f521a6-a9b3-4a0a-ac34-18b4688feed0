import React from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import { Flex, Skeleton } from "@chakra-ui/react";
import { useRouter } from "next/router";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import { BondSearchPopover } from "@/design-system/organisms/BondSearchPopover/BondSearchPopover";
import { useValidateBondSWR } from "@/utils/swr-hooks/Util";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { useEffectOnce } from "@/hooks/useEffectOnce";
import { showWarningToast } from "@/design-system/theme/toast";
import { OperatorsType } from "@/types/slicer";
import {
  ColumnType,
  DimensionType,
  EmpiricalTrackingFormType,
  dimensionDefaultValue,
  getDefaultOperator,
  getDefaultValuesForOptions,
} from "../../useEmpiricalTrackingForm";

type FormColumnsType = Array<
  ColumnType & {
    name?: string | null | undefined;
    operator?: OperatorsType | undefined;
  }
>;

interface BondSelectionProps {
  index: number;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  selectAgency: (agency: string | null | undefined) => void;
  supportedAgency: string | null | undefined;
}

const DEFAULT_DISABLED_COLUMNS = ["CRR", "CDR", "DTI", "ISSUER"];

export const BondSelection = ({ index, form, selectAgency, supportedAgency }: BondSelectionProps) => {
  const { trigger: validateBond, isMutating } = useValidateBondSWR();
  const { replaceWithoutRendering } = useQueryParameters();

  const { data } = useEmpiricalTrackingConfiguration();
  const router = useRouter();

  const isAlgoEnabled = useWatch({ name: `algo_series.${index}.enabled`, control: form.control });
  const runId = router.query.run_id as string | undefined;
  const bondName = router.query.bond_name as string | undefined;
  const userEmpiricalTrackingId = router.query.id as string | undefined;

  const autoFillDimensions = (agency: string | undefined | null) => {
    const slicesBySupportedAgency = agency
      ? data?.empirical_tracking_config?.slices?.filter((slice) => slice.agencies?.includes(agency))
      : [];

    const dimensions: DimensionType[] | undefined = slicesBySupportedAgency?.map((slice) => {
      const operator = getDefaultOperator(slice);
      const values = getDefaultValuesForOptions(slice, agency ?? undefined);
      return {
        ...slice,
        name: slice.dimension,
        values: values,
        enabled: true,
        display_name: slice.display_name,
        operator: operator,
      };
    });

    form.setValue(`algo_series.${index}.dimensions`, dimensions);
  };

  const mergeOrAddColumnsOfMultipleAlgoSeries = () => {
    const algos = form.getValues("algo_series");
    const formColumns = form.getValues("columns");
    const configColumns = data?.empirical_tracking_config?.columns;
    const newColumns: FormColumnsType | undefined = [];

    algos?.forEach((algo) => {
      const agency = algo.agency;

      if (!agency) {
        return;
      }

      const supportedColumns = configColumns?.filter((col) => col.agencies?.includes(agency));

      supportedColumns?.forEach((col) => {
        const existingCol = newColumns?.find((c) => c.dimension === col.dimension);
        const existingColPrev = formColumns?.find((c) => c.dimension === col.dimension);

        if (existingColPrev && !existingCol) {
          newColumns.push(existingColPrev);
          return;
        }

        if (!existingCol) {
          newColumns.push({
            ...col,
            name: col.dimension,
            enabled: col.dimension && DEFAULT_DISABLED_COLUMNS.includes(col.dimension) ? false : true,
            values: getDefaultValuesForOptions(col),
            operator: getDefaultOperator(col),
          });
          return;
        }
      });
    });

    return newColumns;
  };

  const autoFillColumns = () => {
    const columns = mergeOrAddColumnsOfMultipleAlgoSeries();
    form.setValue("columns", columns);
  };

  const addBondAndAgencyToSerie = async (value: string) => {
    const res = await validateBond({
      bond_name: value,
    });
    let agency = res?.security_info?.sub_type;
    if (!["FHL", "FNM", "GNM", "GPL"].includes(agency ?? "")) {
      // Use security_info?.agency instead of sub_type when it's not one of them
      agency = res?.security_info?.agency;

      if (!["FHL", "FNM", "GNM", "GPL"].includes(agency ?? "")) {
        showWarningToast("Not Supported", "This Bond is currently not supported.");
        return;
      }
    }

    form.setValue(`algo_series.${index}.bond_name`, value, {
      shouldDirty: true,
    });
    const prevSelectedAgency = form.getValues(`algo_series.${index}.agency`);
    if (agency === prevSelectedAgency) return;
    form.setValue(`algo_series.${index}.agency`, agency);
    form.setValue(`algo_series.${index}.dimensions`, dimensionDefaultValue);
    replaceWithoutRendering(
      {
        run_id: runId,
        id: userEmpiricalTrackingId,
      },
      true
    );
    selectAgency(agency);
    autoFillDimensions(agency);
    autoFillColumns();
  };

  useEffectOnce(() => {
    if (!userEmpiricalTrackingId && !runId && bondName && !supportedAgency) {
      addBondAndAgencyToSerie(bondName);
    }
  });

  if (isMutating) {
    return <Skeleton h={30} w="90%" />;
  }
  return (
    <Flex gap={2} mr="2rem">
      <CAMultiSelectDropdown
        value={{ label: "Bond Name", value: "bond_name" }}
        name=""
        shouldOnlyReturnValue={false}
        isMultiSelect={false}
        readonly
        options={[]}
        isDisabled={!isAlgoEnabled}
      />
      <Controller
        name={`algo_series.${index}.bond_name`}
        control={form.control}
        render={({ field: { value } }) => (
          <BondSearchPopover
            bondName={value ?? ""}
            onBondSelected={addBondAndAgencyToSerie}
            triggerElWrapperProps={{
              h: "fit-content",
              w: "full",
            }}
            wrapperProps={{
              w: "full",
            }}
            triggerEl={
              <CAMultiSelectDropdown
                value={{
                  label: value ?? "",
                  value: value ?? "",
                }}
                name={`algo_series.${index}.bond_name`}
                shouldOnlyReturnValue={false}
                isMultiSelect={false}
                options={[]}
                isCreatable
                isSearchable={false}
                onCreateNewOption={addBondAndAgencyToSerie}
                isDisabled={!isAlgoEnabled}
              />
            }
            canOpenNew={false}
          />
        )}
      />
    </Flex>
  );
};
