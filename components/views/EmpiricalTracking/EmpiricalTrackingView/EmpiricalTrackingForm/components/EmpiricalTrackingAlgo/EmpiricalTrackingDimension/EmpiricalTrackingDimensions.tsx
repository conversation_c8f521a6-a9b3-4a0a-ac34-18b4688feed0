import { Box } from "@chakra-ui/react";
import React from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import { groupByKey } from "@/design-system/molecules/CAGrid/helpers";
import { transformColToOptionType } from "../../../utils";
import {
  DimensionType,
  EmpiricalTrackingFormType,
  getDefaultOperator,
  getDefaultValuesForOptions,
} from "../../../useEmpiricalTrackingForm";
import { BondSelection } from "../BondSelection";
import { EmpiricalTrackingDimensionRow } from "./EmpiricalTrackingDimensionRow";

interface EmpiricalTrackingDimensionsProps {
  index: number;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  defaultSupportedAgency?: string | null | undefined;
  isAlgoSerieEnabled: boolean | undefined;
}

export const EmpiricalTrackingDimensions = ({
  form,
  index,
  defaultSupportedAgency,
  isAlgoSerieEnabled,
}: EmpiricalTrackingDimensionsProps) => {
  const { data } = useEmpiricalTrackingConfiguration();
  const [supportedAgency, setSupportedAgency] = React.useState<string | null | undefined>(defaultSupportedAgency);

  const {
    fields: dimensions,
    update,
    append,
    remove,
  } = useFieldArray({
    control: form.control,
    name: `algo_series.${index}.dimensions`,
  });

  const slicesBySupportedAgency = supportedAgency
    ? data?.empirical_tracking_config?.slices?.filter((slice) => slice.agencies?.includes(supportedAgency))
    : [];

  const nonSelectedColumns = slicesBySupportedAgency?.filter(
    (slice) => !dimensions.find((dim) => slice.dimension === dim.dimension)
  );

  const slicesByCategory = groupByKey(nonSelectedColumns ?? [], (item) => item.category).map(transformColToOptionType);

  const updateDimension = (dimensionIndex: number, value: DimensionType) => {
    const operator = getDefaultOperator(value);
    const values = getDefaultValuesForOptions(value);

    update(dimensionIndex, {
      ...value,
      enabled: true,
      values: values,
      operator: operator,
    });
  };

  const addNewDimension = (value: DimensionType) => {
    const operator = getDefaultOperator(value);
    const values = getDefaultValuesForOptions(value);

    append({
      ...value,
      enabled: true,
      values: values,
      operator: operator,
    });
  };

  return (
    <Box>
      <BondSelection supportedAgency={supportedAgency} index={index} form={form} selectAgency={setSupportedAgency} />
      {dimensions.map((dimension, dimensionIndex) => {
        return (
          <EmpiricalTrackingDimensionRow
            key={dimension.id}
            algoIndex={index}
            dimensionIndex={dimensionIndex}
            dimension={dimension}
            form={form}
            onDeleteClick={remove}
            onDimensionChange={updateDimension}
            options={slicesByCategory}
          />
        );
      })}
      {slicesByCategory.length ? (
        <Box mr="2rem">
          <CAMultiSelectDropdown
            name={`algo_series.${index}.dimensions`}
            shouldOnlyReturnValue={false}
            isMultiSelect={false}
            options={slicesByCategory}
            value={{
              label: "",
              value: "",
            }}
            onChange={(value) => addNewDimension(value as DimensionType)}
            isDisabled={!isAlgoSerieEnabled}
          />
        </Box>
      ) : null}
    </Box>
  );
};
