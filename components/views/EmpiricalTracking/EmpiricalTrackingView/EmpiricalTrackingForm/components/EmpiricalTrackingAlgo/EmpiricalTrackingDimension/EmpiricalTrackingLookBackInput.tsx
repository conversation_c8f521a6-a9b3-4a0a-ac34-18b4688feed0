import React from "react";
import { UseFormReturn, get } from "react-hook-form";
import CAInput from "@/design-system/molecules/CAInput";
import { EmpiricalTrackingFormType } from "../../../useEmpiricalTrackingForm";

interface EmpiricalTrackingLookBackInputProps {
  format: string | undefined;
  index: number;
  dimensionIndex: number;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  isDisabled: boolean;
}

export const EmpiricalTrackingLookBackInput: React.FC<EmpiricalTrackingLookBackInputProps> = ({
  format,
  index,
  dimensionIndex,
  form,
  isDisabled,
}) => {
  const {
    register,
    formState: { errors },
  } = form;

  const lookbackUpperBound = format === "year" ? 30 : 360;
  const lookbackUnit = format === "year" ? "year" : "month";
  const lookbackTooltip =
    format === "year" ? "Enter number of years to look back." : "Enter number of months to look back.";

  return (
    <CAInput
      type="number"
      step="any"
      max={lookbackUpperBound}
      min={1}
      title={lookbackTooltip}
      isDisabled={isDisabled}
      border={1}
      {...register(`algo_series.${index}.dimensions.${dimensionIndex}.values.0`, {
        max: {
          value: lookbackUpperBound,
          message: `Please limit the look back to ${lookbackUpperBound} ${lookbackUnit}(s).`,
        },
        min: {
          value: 1,
          message: `Please specify the number of ${lookbackUnit}s to look back.`,
        },
      })}
      info={{
        show: !!get(errors, `algo_series.${index}.dimensions.${dimensionIndex}.values.0.message`),
        msg1: get(errors, `algo_series.${index}.dimensions.${dimensionIndex}.values.0.message`),
      }}
      height="30px"
    />
  );
};
