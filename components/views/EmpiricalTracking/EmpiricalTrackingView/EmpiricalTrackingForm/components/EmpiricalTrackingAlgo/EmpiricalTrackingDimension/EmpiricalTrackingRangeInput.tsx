import React from "react";
import { Flex } from "@chakra-ui/react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import CADateInput from "@/design-system/molecules/CADateInput";
import { LATEST_DATE, WITHIN_RANGE_INPUTS } from "@/constants/slicer";
import {
  convertLocalDateToSameTimeISO,
  convertUTCTimeToSameTimeLocalDate,
  getDateFromNumericYYYYMMDD,
} from "@/utils/helpers";
import { SlicerConfigFormat } from "@/types/slicer";
import SlicerLatestDateHeader from "@/components/views/Slicer/SlicerQueryDetails/helpers/SlicerLatestDateHeader";
import { EmpiricalTrackingFormType } from "../../../useEmpiricalTrackingForm";

interface EmpiricalTrackingRangeInputProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
  index: number;
  dimensionIndex: number;
  isDisabled: boolean;
}
export const EmpiricalTrackingRangeInput = ({
  form,
  index,
  dimensionIndex,
  isDisabled,
}: EmpiricalTrackingRangeInputProps) => {
  const dimension = useWatch({
    control: form.control,
    name: `algo_series.${index}.dimensions.${dimensionIndex}`,
  });

  if (!dimension.operator?.value) return null;
  const agency = form.getValues(`algo_series.${index}.agency`);
  const lowerBoundByAgency = dimension?.agency_lower_bounds?.find((bound) => bound.name === agency);
  const upperBoundByAgency = dimension?.agency_upper_bounds?.find((bound) => bound.name === agency);
  const lowerBoundDate = getDateFromNumericYYYYMMDD(lowerBoundByAgency?.value ?? dimension.lower_bound);
  const upperBoundDate = getDateFromNumericYYYYMMDD(upperBoundByAgency?.value ?? dimension.upper_bound);

  if (WITHIN_RANGE_INPUTS.includes(dimension.operator?.value)) {
    return (
      <Flex gap={2} w="full">
        <Controller
          control={form.control}
          name={`algo_series.${index}.dimensions.${dimensionIndex}.values.0`}
          render={({ field: { value, onChange, ...rest } }) => (
            <CADateInput
              className="empirical-tracking-date-input"
              showYearPicker={dimension?.format === SlicerConfigFormat.YEAR}
              showMonthYearPicker={dimension?.format === SlicerConfigFormat.YEAR_MONTH}
              {...rest}
              selectedDate={value ? convertUTCTimeToSameTimeLocalDate(new Date(value)) : undefined}
              minDate={lowerBoundDate}
              maxDate={upperBoundDate}
              onChange={(date) => {
                if (date) {
                  onChange(convertLocalDateToSameTimeISO(date).substring(0, 10));
                }
              }}
              disabled={isDisabled}
            />
          )}
        />
        <Controller
          control={form.control}
          name={`algo_series.${index}.dimensions.${dimensionIndex}.values.1`}
          render={({ field: { value, onChange, ...rest } }) => (
            <CADateInput
              className="empirical-tracking-date-input"
              showYearPicker={dimension?.format === SlicerConfigFormat.YEAR}
              showMonthYearPicker={dimension?.format === SlicerConfigFormat.YEAR_MONTH}
              {...rest}
              value={value === LATEST_DATE ? LATEST_DATE : undefined}
              selectedDate={
                value && value !== LATEST_DATE ? convertUTCTimeToSameTimeLocalDate(new Date(value)) : undefined
              }
              minDate={lowerBoundDate}
              maxDate={upperBoundDate}
              onChange={(date) => {
                if (date) {
                  onChange(convertLocalDateToSameTimeISO(date).substring(0, 10));
                }
              }}
              disabled={isDisabled}
              headerEl={({ closeDatePicker }) => (
                <SlicerLatestDateHeader
                  isSelected={value === LATEST_DATE}
                  onChange={(value) => {
                    onChange(value);
                    rest.onBlur();
                  }}
                  closeDatePicker={closeDatePicker}
                />
              )}
            />
          )}
        />
      </Flex>
    );
  }
  return (
    <Controller
      control={form.control}
      name={`algo_series.${index}.dimensions.${dimensionIndex}.values.0`}
      render={({ field: { value, onChange, ...rest } }) => (
        <CADateInput
          className="empirical-tracking-date-input"
          showYearPicker={dimension?.format === SlicerConfigFormat.YEAR}
          showMonthYearPicker={dimension?.format === SlicerConfigFormat.YEAR_MONTH}
          {...rest}
          value={value === LATEST_DATE ? LATEST_DATE : undefined}
          selectedDate={value && value !== LATEST_DATE ? convertUTCTimeToSameTimeLocalDate(new Date(value)) : undefined}
          minDate={lowerBoundDate}
          maxDate={upperBoundDate}
          onChange={(date) => {
            if (date) {
              onChange(convertLocalDateToSameTimeISO(date).substring(0, 10));
            }
          }}
          headerEl={({ closeDatePicker }) => (
            <SlicerLatestDateHeader
              isSelected={value === LATEST_DATE}
              onChange={(value) => {
                onChange(value);
                rest.onBlur();
              }}
              closeDatePicker={closeDatePicker}
            />
          )}
        />
      )}
    />
  );
};
