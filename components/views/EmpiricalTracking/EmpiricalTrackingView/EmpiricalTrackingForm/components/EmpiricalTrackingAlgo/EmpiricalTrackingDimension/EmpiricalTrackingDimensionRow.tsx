import { Box, Flex, Icon<PERSON>utton } from "@chakra-ui/react";
import React from "react";
import { IoTrashOutline } from "react-icons/io5";
import { FieldArrayWithId, UseFormReturn, useWatch } from "react-hook-form";
import CACheckboxInput from "@/design-system/molecules/CACheckboxInput";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { CA_Mastr_Models_v1_0_Models_ConfigurationType } from "@/utils/openapi";
import CAIcon from "@/design-system/atoms/CAIcon";
import { EmpiricalTrackingOperator } from "../../EmpiricalTrackingOperator";
import { DimensionType, EmpiricalTrackingFormType } from "../../../useEmpiricalTrackingForm";
import { EmpiricalTrackingValue } from "../../EmpiricalTrackingValue";
import { OptionType } from "../../../utils";

interface EmpiricalTrackingDimensionRowProps {
  dimension: FieldArrayWithId<EmpiricalTrackingFormType, `algo_series.${number}.dimensions`, "id">;
  algoIndex: number;
  dimensionIndex: number;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  onDimensionChange: (index: number, value: DimensionType) => void;
  options: OptionType[];
  onDeleteClick: (index: number) => void;
}

export const EmpiricalTrackingDimensionRow = ({
  dimension,
  dimensionIndex,
  algoIndex,
  form,
  onDimensionChange,
  options,
  onDeleteClick,
}: EmpiricalTrackingDimensionRowProps) => {
  const [isAlgoSerieEnabled, isDimensionEnabled] = useWatch({
    name: [`algo_series.${algoIndex}.enabled`, `algo_series.${algoIndex}.dimensions.${dimensionIndex}.enabled`],
    control: form.control,
  });

  const isEnabled = isAlgoSerieEnabled ? isDimensionEnabled : false;
  return (
    <Flex gap={2} role="group" alignItems="center">
      <Box w="full" position="relative">
        <CACheckboxInput
          formProps={{
            w: "1rem",
            position: "absolute",
            left: "-20px",
            top: "7px",
          }}
          {...form.register(`algo_series.${algoIndex}.dimensions.${dimensionIndex}.enabled`)}
          variant="small"
          hideLabel
          visibility={!isAlgoSerieEnabled ? "hidden" : isEnabled ? "hidden" : "visible"}
          _groupHover={{
            visibility: "visible",
          }}
        />
        <CAMultiSelectDropdown
          tooltipText={dimension.display_name ?? undefined}
          name={`algo_series.${algoIndex}.dimensions.${dimensionIndex}.dimension`}
          shouldOnlyReturnValue={false}
          isMultiSelect={false}
          options={options}
          value={{
            label: dimension.display_name ?? "",
            value: dimension.dimension ?? "",
          }}
          onChange={(value) => onDimensionChange(dimensionIndex, value as DimensionType)}
          isDisabled={!isEnabled}
        />
      </Box>
      {dimension.type !== CA_Mastr_Models_v1_0_Models_ConfigurationType.VALUE && (
        <Flex gap={2} w="full" alignItems="center">
          <EmpiricalTrackingOperator
            index={algoIndex}
            dimensionIndex={dimensionIndex}
            dimensionType={dimension.type}
            form={form}
            isDisabled={!isEnabled}
            includeLookback={dimension.format === "year-month" || dimension.format === "year"}
          />
          <EmpiricalTrackingValue
            index={algoIndex}
            dimensionIndex={dimensionIndex}
            dimension={dimension}
            form={form}
            isDisabled={!isEnabled}
          />
        </Flex>
      )}
      <IconButton
        visibility="hidden"
        _groupHover={{ visibility: "visible" }}
        aria-label="remove-column"
        size="xs"
        variant="unstyled"
        icon={<CAIcon as={IoTrashOutline} verticalAlign="middle" variant="secondary" boxSize={4} />}
        onClick={() => onDeleteClick(dimensionIndex)}
      />
    </Flex>
  );
};
