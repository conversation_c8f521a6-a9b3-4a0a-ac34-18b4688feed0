import { ButtonGroup, IconButton, Skeleton } from "@chakra-ui/react";
import React from "react";
import { IoGridOutline, IoListOutline } from "react-icons/io5";
import { useEmpiricalTrackingQuery } from "@/utils/swr-hooks/EmpiricalTracking";
import {
  CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables,
  CA_Mastr_Models_v1_0_Models_Stage,
} from "@/utils/openapi";
import CACard from "@/design-system/molecules/CACard";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { EmpiricalTrackingTable } from "./EmpiricalTrackingTable";
import { EmpiricalTrackingGrid } from "./EmpiricalTrackingGrid";

interface EmpiricalTrackingDisplayProps {
  token?: string | null | undefined;
  isOwner: boolean;
  apiRequestVariables: (CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables | null | undefined)[];
}

export const EmpiricalTrackingDisplay = ({ token, isOwner, apiRequestVariables }: EmpiricalTrackingDisplayProps) => {
  const { data, isLoading, error } = useEmpiricalTrackingQuery(token);
  const [tabIndex, setTabIndex] = React.useState(0);

  const isLoadingData = error
    ? false
    : isLoading || data?.some((d) => d?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING) || false;

  // try to get the api request variables from the data, if not available use the one passed as prop from old run if it exists
  const apiReqVariables = data?.flatMap((d) => d?.api_request_variables)?.filter(Boolean) || apiRequestVariables;

  const headerActionButtons = React.useMemo(() => {
    if (isLoadingData || !data) return <></>;
    return (
      <ButtonGroup size="xs" isAttached variant="outline">
        <IconButton
          icon={<IoListOutline />}
          onClick={() => setTabIndex(0)}
          size="sm"
          fontSize="xl"
          mr="-1px"
          variant={tabIndex === 0 ? "primary" : "secondary"}
          title={`Grid`}
          aria-label={`Grid`}
        />
        <IconButton
          icon={<IoGridOutline />}
          onClick={() => setTabIndex(1)}
          size="sm"
          fontSize="xl"
          mr="-1px"
          variant={tabIndex === 1 ? "primary" : "secondary"}
          title={`Table`}
          aria-label={`Table`}
        />
      </ButtonGroup>
    );
  }, [tabIndex, isLoadingData, data]);

  // We need loading in order to prevent column defs change to flash the entire grid and reduce re-rendering grid. Also it resolves conditional render problems
  if (isLoadingData) {
    return (
      <CACard minH="calc(100vh - 13rem)">
        <Skeleton mt="8" h={30} w="full" />
      </CACard>
    );
  }

  if (error) {
    return (
      <CACard
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minH="calc(100vh - 13rem)"
      >
        <CAAlertCard status="error" title={error.heading} description={error.message} />
      </CACard>
    );
  }

  return (
    <>
      {tabIndex === 0 && (
        <EmpiricalTrackingTable
          headerActionButtons={headerActionButtons}
          isLoading={isLoadingData}
          data={data}
          apiRequestVariables={apiReqVariables}
        />
      )}

      {tabIndex === 1 && (
        <EmpiricalTrackingGrid
          headerActionButtons={headerActionButtons}
          isLoading={isLoadingData}
          isOwner={isOwner}
          data={data}
          apiRequestVariables={apiReqVariables}
        />
      )}
    </>
  );
};
