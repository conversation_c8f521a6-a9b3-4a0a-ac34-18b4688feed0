import React from "react";
import { ColDef } from "ag-grid-community";
import { useSearchParams } from "next/navigation";
import { Box } from "@chakra-ui/react";
import { EmpiricalTrackingReturnType, empiricalTrackingColDefs } from "@/utils/grid/EmpiricalTrackingColumnData";
import {
  CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables,
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingResponse,
} from "@/utils/openapi";
import { showWarningToast } from "@/design-system/theme/toast";
import { getParam } from "@/utils/helpers";
import { useEmpiricalTrackingPage } from "@/contexts/PageContexts/EmpiricalTrackingPageContext/EmpiricalTrackingApp";
import { CAGridWithoutStore } from "@/design-system/molecules/CAGrid/CAGrid";
import { useEmpiricalTrackingStore } from "../useEmpiricalTrackingStore";
import { EmpiricalTrackingApiVariables } from "./EmpiricalTrackingApiVariables";

interface EmpiricalTrackingGridProps {
  headerActionButtons: React.JSX.Element;
  isLoading: boolean;
  data: CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingResponse[] | undefined;
  isOwner: boolean;
  apiRequestVariables: (CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables | null | undefined)[];
}

const autoGroupColumnDef: ColDef<EmpiricalTrackingReturnType> = {
  initialWidth: 200,
  cellRendererParams: {
    suppressCount: true, // turn off the row count
  },
};

export const getEmpiricalTrackingGridKey = (userEmpiricalTrackingId: string | null) => {
  if (!userEmpiricalTrackingId) return;
  return `empiricalTrackingQuery-${userEmpiricalTrackingId}-v31`;
};
export const EmpiricalTrackingGrid = ({
  headerActionButtons,
  data,
  isOwner,
  isLoading,
  apiRequestVariables,
}: EmpiricalTrackingGridProps) => {
  const { setHasQueryChanges } = useEmpiricalTrackingStore();
  const params = useSearchParams();
  const {
    state: { isRequestStopped },
    refs: { detailGridRef },
  } = useEmpiricalTrackingPage();
  const activeUserEmpiricalTrackingId = params.get("id");

  const parsedData: EmpiricalTrackingReturnType[] | undefined = React.useMemo(
    () =>
      data?.flatMap(
        (d) =>
          d?.results &&
          JSON.parse(d.results).flatMap((el: EmpiricalTrackingReturnType) => ({
            ...el,
            algo_series_name: d.algo_series_name,
          }))
      ),
    [data]
  );

  const colDefs = React.useMemo(() => {
    const dataFields = parsedData?.[0] && Object.keys(parsedData[0]);
    return dataFields ? (empiricalTrackingColDefs.filter((col) => dataFields.includes(col.field)) as ColDef[]) : [];
  }, [parsedData]);

  const gridType = getEmpiricalTrackingGridKey(activeUserEmpiricalTrackingId ?? null);

  const getShouldDisableSave = () => {
    const userEmpiricalTrackingId = getParam("id");
    if (userEmpiricalTrackingId) return false;
    else {
      showWarningToast("Unsaved Query", "Please save the query first in order to save grid view");
      return true;
    }
  };

  const hasRun = data?.every((el) => el.status);

  const handleChanges = (hasChanges: boolean, gridType?: string) => {
    if (gridType) {
      setHasQueryChanges(hasChanges, gridType);
    }
  };

  return (
    <Box borderRadius="2xl" overflow="hidden">
      <CAGridWithoutStore
        ref={detailGridRef}
        cardProps={{
          title: " ",
          cardKey: "empirical-tracking-grid",
          borderTopRadius: 0,
          variant: "square",
          cardBodyStyle: { p: 0 },
          className: "SlicerQueryGrid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
          style: {
            borderRadius: "1rem",
          },
        }}
        hasRun={hasRun && !isRequestStopped}
        wrapperStyles={{
          h: "calc(100vh - 18rem)",
        }}
        stackStyles={{
          h: "calc(100vh - 15.2rem)",
        }}
        gridType={gridType}
        hideSearch
        initialMessage={"Click on ▷ to load Empirical Tracking."}
        headerActionButtons={headerActionButtons}
        gridProps={{
          columnDefs: colDefs,
          rowData: parsedData,
          groupDefaultExpanded: 1,
          autoGroupColumnDef,
          loading: isLoading,
        }}
        shouldDisableSave={getShouldDisableSave}
        allowViewSave={false}
        canCreateOrModifyViews={isOwner}
        customEl={
          apiRequestVariables?.length === 0 || parsedData?.length === 0 ? undefined : (
            <EmpiricalTrackingApiVariables apiRequestVariables={apiRequestVariables} />
          )
        }
        onHasChangesCallback={handleChanges}
      />
    </Box>
  );
};
