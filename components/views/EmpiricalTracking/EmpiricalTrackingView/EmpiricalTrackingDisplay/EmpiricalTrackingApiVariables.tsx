import { FC } from "react";
import { HStack, Text } from "@chakra-ui/react";
import { CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables } from "@/utils/openapi";
import { getDisplayNameFromVariable, getMonthYearFromNumericYYYYMM } from "@/utils/helpers";

type Props = {
  apiRequestVariables: (CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables | null | undefined)[];
};

export const EmpiricalTrackingApiVariables: FC<Props> = ({ apiRequestVariables }) => {
  return (
    <HStack spacing={4} wrap="wrap">
      {apiRequestVariables.map((variable) => (
        <HStack key={variable?.field}>
          <Text variant="primary" whiteSpace="nowrap">
            {variable?.name} ({getDisplayNameFromVariable(variable?.field ?? "-")}) :
          </Text>
          <Text variant="secondary" whiteSpace="nowrap">
            {variable?.field === "model_version"
              ? variable.value
              : (variable?.value?.length || 0) >= 6
              ? getMonthYearFromNumericYYYYMM(variable?.value)
              : variable?.value}
          </Text>
        </HStack>
      ))}
    </HStack>
  );
};
