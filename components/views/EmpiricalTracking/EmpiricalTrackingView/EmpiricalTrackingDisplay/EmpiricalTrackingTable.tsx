import { Box, HStack, Text } from "@chakra-ui/react";
import React, { useEffect } from "react";
import { ColDef, RowClassParams } from "ag-grid-community";
import CACard from "@/design-system/molecules/CACard";
import CADateInput from "@/design-system/molecules/CADateInput";
import {
  CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables,
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingResponse,
} from "@/utils/openapi";
import { EmpiricalTrackingReturnType, empiricalTrackingTableColDefs } from "@/utils/grid/EmpiricalTrackingColumnData";
import { getFormattedNumberFixed } from "@/utils/helpers";
import CAGrid from "@/design-system/molecules/CAGrid";
import CASelectDropdown, { SelectOptionType } from "@/design-system/molecules/CASelectDropdown";
import { useEmpiricalTrackingPage } from "@/contexts/PageContexts/EmpiricalTrackingPageContext/EmpiricalTrackingApp";
import { EmpiricalTrackingApiVariables } from "./EmpiricalTrackingApiVariables";

const colDefs: ColDef[] = [
  {
    headerName: "Attribute",
    field: "attribute",
    flex: 1,
  },
  {
    headerName: "Bond",
    field: "bond",
    flex: 1,
  },
  {
    headerName: "Cohort",
    field: "cohort",
    flex: 1,
  },
  {
    headerName: "Diff (Bond - Cohort)",
    field: "difference",
    flex: 1,
  },
];
interface EmpiricalTrackingTableProps {
  headerActionButtons: React.JSX.Element;
  isLoading: boolean;
  data: CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingResponse[] | undefined;
  apiRequestVariables: (CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables | null | undefined)[];
}

export const EmpiricalTrackingTable = ({
  data,
  isLoading,
  apiRequestVariables,
  headerActionButtons,
}: EmpiricalTrackingTableProps) => {
  const [selectedSeries, setSelectedSeries] = React.useState<string | undefined>();
  const {
    state: { isRequestStopped },
  } = useEmpiricalTrackingPage();

  const parsedData: EmpiricalTrackingReturnType[] | undefined = React.useMemo(() => {
    const filteredDataBySerie = data?.find(
      (d) => d.algo_series_name === (selectedSeries ?? data?.[0].algo_series_name)
    );
    return filteredDataBySerie?.results && JSON.parse(filteredDataBySerie?.results);
  }, [data, selectedSeries]);

  const { firstDate, lastDate } = React.useMemo(() => {
    if (!parsedData) return {};
    const firstDate = new Date(parsedData[0]?.asof);
    const lastDate = new Date(parsedData[parsedData.length - 1]?.asof);

    return { firstDate, lastDate };
  }, [parsedData]);

  const [factorDate, setFactorDate] = React.useState<Date>(lastDate ?? new Date());

  useEffect(() => {
    if (!lastDate) return;
    setFactorDate(lastDate);
  }, [lastDate]);

  const dataFilteredByDate = parsedData?.find(
    (el) =>
      new Date(el.asof).getMonth() === factorDate.getMonth() &&
      new Date(el.asof).getFullYear() === factorDate.getFullYear()
  );

  const tableData =
    dataFilteredByDate &&
    empiricalTrackingTableColDefs
      .map((def) => {
        const bondKey = `bond_${def.field}` as keyof Omit<EmpiricalTrackingReturnType, "asof">;
        const cohortKey = `cohort_${def.field}` as keyof Omit<EmpiricalTrackingReturnType, "asof">;
        const bondValue = dataFilteredByDate?.[bondKey];
        const cohortValue = dataFilteredByDate?.[cohortKey];
        if (typeof bondValue !== "number" || typeof cohortValue !== "number") return;
        return {
          attribute: def.label,
          bond: getFormattedNumberFixed(def.fractionDigits)(bondValue),
          cohort: getFormattedNumberFixed(def.fractionDigits)(cohortValue),
          difference: getFormattedNumberFixed(def.fractionDigits)(bondValue - cohortValue),
        };
      })
      .filter(Boolean);

  const handleDateChange = (date: Date | null) => {
    if (!date) return;
    setFactorDate(date);
  };

  const algoSeriesOptions: SelectOptionType[] =
    data?.map((el) => ({
      id: el.algo_series_name ?? "",
      value: el.algo_series_name ?? "",
      displayValue: el.algo_series_name ?? "",
    })) ?? [];

  const createClassNamesForGridRows = (params: RowClassParams) => {
    if (params.data?.attribute.includes("CPR")) {
      return "empirical-table-cpr-row";
    }
    if (params.data?.attribute.includes("CDR")) {
      return "empirical-table-cdr-row";
    }
    if (params.data?.attribute.includes("CRR")) {
      return "empirical-table-crr-row";
    }
    return "empirical-table-default-row";
  };

  return (
    <CACard borderRadius="2xl" cardBodyStyle={{ p: 0 }} allowCollapse title=" " cardKey="empirical-tracking-table">
      <HStack
        ml={{
          base: "0",
          md: "1rem",
        }}
        mb={{
          base: "5rem",
          md: "1rem",
        }}
        marginTop={"0.5rem"}
        className="EmpiricalTrackingTableHeader"
      >
        {parsedData?.length ? (
          <>
            <Box w="7rem" mr="1rem">
              <CASelectDropdown
                name="algo-serie"
                options={algoSeriesOptions}
                onChange={(e) => setSelectedSeries(e.target.value)}
              />
            </Box>
            <Text variant="tableLeft">Factor Month</Text>
            <Box w="5.5rem">
              <CADateInput
                selectedDate={factorDate}
                minDate={firstDate}
                maxDate={lastDate}
                onChange={handleDateChange}
                showMonthYearPicker
                centered
              />
            </Box>
          </>
        ) : null}
        <Box ml="auto" mr="1rem">
          {headerActionButtons}
        </Box>
      </HStack>
      <CACard variant="square" cardBodyStyle={{ p: 0 }}>
        <CAGrid
          cardProps={{
            borderTopRadius: 0,
          }}
          wrapperStyles={{
            h: "calc(100vh - 18rem)",
          }}
          hasRun={data?.every((el) => el.status) && !isRequestStopped}
          hideHeader
          enableUserGridViews={false}
          initialMessage={"Click on ▷ to load Empirical Tracking."}
          gridProps={{
            columnDefs: colDefs,
            rowData: tableData,
            className: "empirical-tracking-table",
            getRowClass: createClassNamesForGridRows,
            enableCharts: false,
            cellSelection: false,
            sideBar: false,
            // domLayout: "autoHeight",
            loading: isLoading,
          }}
          customEl={
            apiRequestVariables?.length === 0 || tableData?.length === 0 ? undefined : (
              <EmpiricalTrackingApiVariables apiRequestVariables={apiRequestVariables} />
            )
          }
          stackStyles={{
            h: "calc(100vh - 18.5rem)",
          }}
        />
      </CACard>
    </CACard>
  );
};
