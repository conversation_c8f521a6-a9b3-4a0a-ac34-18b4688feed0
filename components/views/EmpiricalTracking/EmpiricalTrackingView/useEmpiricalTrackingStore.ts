import { create } from "zustand";

type Key = string;

type Store = {
  gridViewsChanges: Record<Key, boolean>;
  setHasQueryChanges: (hasQueryChanges: boolean, gridType: string) => void;
  resetAllGridViewsChanges: () => void;
};

export const useEmpiricalTrackingStore = create<Store>()((set) => ({
  gridViewsChanges: {},
  setHasQueryChanges: (hasChanges, gridType) =>
    set((state) => ({
      ...state,
      gridViewsChanges: {
        ...state.gridViewsChanges,
        [gridType]: hasChanges,
      },
    })),
  resetAllGridViewsChanges: () =>
    set((state) => {
      const newState = Object.keys(state.gridViewsChanges).reduce((acc, key) => {
        acc[key] = false;
        return acc;
      }, {} as Record<Key, boolean>);

      return {
        ...state,
        gridViewsChanges: newState,
      };
    }),
}));
