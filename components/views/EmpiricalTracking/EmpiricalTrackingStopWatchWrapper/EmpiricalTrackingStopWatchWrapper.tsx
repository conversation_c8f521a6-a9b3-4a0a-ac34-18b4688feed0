import * as React from "react";

import StopWatch from "@/design-system/molecules/StopWatch";

import { useEmpiricalTrackingModule } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext";

const StopWatchWrapper = ({ startDate }: { startDate: string | undefined }) => {
  const {
    state: { isTimerRunning },
  } = useEmpiricalTrackingModule();
  return <StopWatch startDate={startDate} isTimerRunning={isTimerRunning} />;
};

type EmpiricalTrackingStopWatchProps = {
  lastRun: string | undefined;
};

export const EmpiricalTrackingStopWatchWrapper: React.FC<EmpiricalTrackingStopWatchProps> = ({
  lastRun,
}: EmpiricalTrackingStopWatchProps) => {
  return <StopWatchWrapper startDate={lastRun} />;
};
