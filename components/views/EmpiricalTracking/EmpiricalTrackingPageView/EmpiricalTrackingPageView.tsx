import { Box, IconButton } from "@chakra-ui/react";
import { IoSettings } from "react-icons/io5";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import MainLayout from "@/components/layouts/MainLayout";
import CAIcon from "@/design-system/atoms/CAIcon";
import { EmpiricalTrackingView } from "@/components/views/EmpiricalTracking/EmpiricalTrackingView/EmpiricalTrackingView";
import { UserEmpiricalTrackingSearch } from "@/components/views/EmpiricalTracking/EmpiricalTrackingView/UserViewSearch";
import EmpiricalTrackingSettingsDrawer from "@/components/views/EmpiricalTracking/EmpiricalTrackingSettingsDrawer/EmpiricalTrackingSettingsDrawer";
import { useEmpiricalTrackingModule } from "@/contexts/ModuleContexts/EmpiricalTrackingModuleContext";

const EmpiricalTrackingPageView = () => {
  const { data: config } = useEmpiricalTrackingConfiguration();
  const {
    state: { empiricalTrackingSettingsDrawerOpen },
    action: { toggleEmpiricalTrackingSettingsDrawer },
  } = useEmpiricalTrackingModule();

  return (
    <MainLayout
      title={"Empirical Tracking"}
      headerMiddle={<UserEmpiricalTrackingSearch />}
      headerSettings={
        <IconButton
          data-testid="toggle-settings-drawer"
          aria-label="Toggle Settings Drawer"
          icon={<CAIcon as={IoSettings} variant="secondary" />}
          variant="ghost"
          onClick={toggleEmpiricalTrackingSettingsDrawer}
        />
      }
    >
      <Box height={"100%"}>
        <Box>{config && <EmpiricalTrackingView formConfig={config} />}</Box>
        <EmpiricalTrackingSettingsDrawer
          onClose={toggleEmpiricalTrackingSettingsDrawer}
          isOpen={empiricalTrackingSettingsDrawerOpen}
        />
      </Box>
    </MainLayout>
  );
};

export default EmpiricalTrackingPageView;
