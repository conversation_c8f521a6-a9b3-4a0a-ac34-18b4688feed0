import React from "react";
import { <PERSON><PERSON>, <PERSON>lex } from "@chakra-ui/react";
import { useRouter } from "next/navigation";
import { useCreateUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import {
  CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions,
  CA_Mastr_Api_v1_0_Models_Util_PortfolioCalculations,
} from "@/utils/openapi";
import { CALCULATION_ASSUMPTIONS_CONFIG } from "../Portfolio/calculationAssumptionsConfig";

export const getDefaultCalculationAssumptions = (
  metadataDefaultCalculcationAssumptions: CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions | undefined
) => {
  const defaults = Object.entries(metadataDefaultCalculcationAssumptions || {})
    ?.map(([key, val]) => {
      const fieldFromConfig = CALCULATION_ASSUMPTIONS_CONFIG.find((config) => config.metadataKey === key)?.field;

      if (Array.isArray(val)) {
        const defaultValue = val.find((el) => "is_default" in el && el.is_default);
        return {
          value: defaultValue && "value" in defaultValue && defaultValue.value,
          label: fieldFromConfig,
        };
      }

      return {
        value: val && "is_default" in val && val.is_default ? val.value : undefined,
        label: fieldFromConfig,
      };
    })
    .filter(Boolean);
  return Object.fromEntries(defaults.map((option) => [option.label, option.value]));
};

const getDefaultCalculations = (
  metadataDefaultCalculations: CA_Mastr_Api_v1_0_Models_Util_PortfolioCalculations | undefined
) => {
  const defaults = Object.entries(metadataDefaultCalculations || {})?.map(([key, val]) => ({
    value: val as boolean,
    label: key as keyof CA_Mastr_Api_v1_0_Models_Util_PortfolioCalculations,
  }));
  return Object.fromEntries(defaults.map((option) => [option.label, option.value]));
};

export const AddPortfolio = () => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const router = useRouter();
  const { trigger: addPortfolio, isMutating } = useCreateUserPortfolio();

  const handleAddClick = async () => {
    const defaultCalculations = getDefaultCalculations(metadata?.portfolio_settings?.calculations);
    const defaultCalculationAssumptions = getDefaultCalculationAssumptions(
      metadata?.portfolio_settings?.calculation_assumptions
    );

    const portfolio = await addPortfolio({
      user_portfolio_name: "Untitled",
      user_portfolio_input: {
        calculations: defaultCalculations,
        calculation_assumptions: defaultCalculationAssumptions,
      },
      is_ad_hoc: true,
    });
    router.push(`/portfolio/${portfolio?.user_portfolio?.user_portfolio_id}`);
  };

  return (
    <Flex w="full" justifyContent="flex-end">
      <Button variant="primary" my="2" onClick={handleAddClick} isLoading={isMutating}>
        New
      </Button>
    </Flex>
  );
};
