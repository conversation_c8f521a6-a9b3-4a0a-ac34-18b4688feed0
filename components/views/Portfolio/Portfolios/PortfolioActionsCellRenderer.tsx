import React from "react";
import { CustomCellRendererProps } from "ag-grid-react";
import { <PERSON>con<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON>utton, <PERSON>u<PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, <PERSON> } from "@chakra-ui/react";
import { IoEllipsisVerticalOutline, IoTrashOutline } from "react-icons/io5";
import { VscSaveAs } from "react-icons/vsc";
import { useCreateUserPortfolio, useDeleteUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import CAIcon from "@/design-system/atoms/CAIcon";
import { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio } from "@/utils/openapi";

export const PortfolioActionsCellRenderer = (
  params: CustomCellRendererProps<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio>
) => {
  const { data: userPortfolio } = params;
  const { trigger: copyPortfolio, isMutating: isCopying } = useCreateUserPortfolio();
  const { trigger: deletePortfolio, isMutating: isDeleting } = useDeleteUserPortfolio();

  const userPortfolioId = userPortfolio?.user_portfolio_id;

  const handleDeletePortfolio = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!userPortfolioId) return;
    await deletePortfolio({ id: userPortfolioId });
  };

  const handleCopyPortfolio = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!userPortfolioId) return;
    await copyPortfolio({
      ...userPortfolio,
      user_portfolio_name: `Copy of ${userPortfolio?.user_portfolio_name}`,
    });
  };

  const isLoading = isCopying || isDeleting;

  if (!userPortfolio || !userPortfolio.user_portfolio_id) {
    return null;
  }
  return (
    <Menu placement="right-start" isLazy closeOnSelect>
      <MenuButton
        as={IconButton}
        aria-label="filter-parent-menu"
        size="xs"
        icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
        variant="ghost"
        onClick={(e) => e.stopPropagation()}
        isLoading={isLoading}
      />
      <Portal>
        <MenuList zIndex="popover" minW="8rem">
          <MenuItem icon={<CAIcon as={VscSaveAs} variant="secondary" />} onClick={handleCopyPortfolio}>
            Make a copy
          </MenuItem>
          <MenuItem icon={<CAIcon as={IoTrashOutline} variant="secondary" />} onClick={handleDeletePortfolio}>
            Delete
          </MenuItem>
        </MenuList>
      </Portal>
    </Menu>
  );
};
