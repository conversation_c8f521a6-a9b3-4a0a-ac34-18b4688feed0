import React from "react";
import { Box } from "@chakra-ui/react";
import { useUserPortfolios } from "@/utils/swr-hooks/UserPortfolio";
import CAGrid from "@/design-system/molecules/CAGrid";
import { PortfoliosColumnDefs } from "@/utils/grid/portfolio/PortfoliosColumnDefs";

export const PortfoliosGrid = () => {
  const { data, isLoading } = useUserPortfolios();
  return (
    <Box>
      <CAGrid
        gridProps={{
          columnDefs: PortfoliosColumnDefs,
          rowData: data?.user_portfolios || [],
          loading: isLoading,
          enableCharts: false,
        }}
        hasRun
        enableUserGridViews={false}
        stackStyles={{
          h: "calc(100vh - 12rem)",
        }}
      />
    </Box>
  );
};
