import React from "react";
import { <PERSON>con<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON>utt<PERSON>, Menu<PERSON>tem, MenuList, Portal } from "@chakra-ui/react";
import { IoEllipsisVerticalOutline, IoSaveOutline, IoTrashOutline } from "react-icons/io5";
import { VscSaveAs } from "react-icons/vsc";
import { BiPencil } from "react-icons/bi";
import { useRouter } from "next/router";
import { useCreateUserPortfolio, usePortfolioActions } from "@/utils/swr-hooks/UserPortfolio";
import CAIcon from "@/design-system/atoms/CAIcon";
import { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio } from "@/utils/openapi";
import { usePortfolioModals } from "./PortfolioModalsContext";
import { usePricingInputsStore } from "./PricingInputs/PricingInputsContext";

export const PortfolioActionsDropdown = ({
  user<PERSON>ort<PERSON>lio,
}: {
  userPortfolio?: CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio;
}) => {
  const router = useRouter();
  const { openModal } = usePortfolioModals();
  const { trigger: copyPortfolio, isMutating: isCopying } = useCreateUserPortfolio();
  const { savePortfolio, deletePortfolio, isMutating } = usePortfolioActions();
  const hasChanges = usePricingInputsStore((s) => s.hasChanges);
  const userPortfolioId = userPortfolio?.user_portfolio_id;

  const handleDeletePortfolio = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!userPortfolioId) return;
    await deletePortfolio(userPortfolioId);
    router.push("/portfolio");
  };

  const handleCopyPortfolio = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!userPortfolioId) return;
    const newUserPortfolio = await copyPortfolio({
      ...userPortfolio,
      user_portfolio_name: `Copy of ${userPortfolio?.user_portfolio_name}`,
    });
    router.push(`/portfolio/${newUserPortfolio.user_portfolio?.user_portfolio_id}`);
  };

  const handleSavePortfolio = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (userPortfolio?.is_ad_hoc) {
      openModal({ type: "rename" });
      return;
    }
    await savePortfolio();
  };

  const handleOpenEditModal = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (userPortfolio) {
      openModal({ type: "edit" });
    }
  };

  const isLoading = isCopying || isMutating;
  return (
    <>
      <Menu placement="right-start" isLazy closeOnSelect>
        <MenuButton
          as={IconButton}
          aria-label="filter-parent-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
          isLoading={isLoading}
        />
        <Portal>
          <MenuList zIndex="popover" minW="8rem">
            <MenuItem icon={<CAIcon as={BiPencil} variant="secondary" />} onClick={handleOpenEditModal}>
              Edit
            </MenuItem>
            <MenuItem
              icon={<CAIcon as={IoSaveOutline} variant="secondary" />}
              onClick={handleSavePortfolio}
              disabled={hasChanges}
            >
              Save
            </MenuItem>
            <MenuItem icon={<CAIcon as={VscSaveAs} variant="secondary" />} onClick={handleCopyPortfolio}>
              Make a copy
            </MenuItem>
            <MenuItem icon={<CAIcon as={IoTrashOutline} variant="secondary" />} onClick={handleDeletePortfolio}>
              Delete
            </MenuItem>
          </MenuList>
        </Portal>
      </Menu>
    </>
  );
};
