import React from "react";
import { ColDef, GetRowIdFunc, RowSelectionOptions, SelectionColumnDef } from "ag-grid-enterprise";
import {
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult,
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse,
} from "@/utils/openapi";
import CAGrid from "@/design-system/molecules/CAGrid";
import { getPortfolioResultsColumnDefs } from "@/utils/grid/portfolio/PortfolioResultsColumnDefs";
import { defaultColDef } from "@/design-system/molecules/CAGrid/constants";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useGridSelectionSync } from "../hooks/useGridSelectionSync";

const rowSelection: RowSelectionOptions<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse> = {
  mode: "multiRow",
  enableSelectionWithoutKeys: true,
  enableClickSelection: false,
};

const selectionColumnDef: SelectionColumnDef = {
  pinned: "left",
};

const defaultColDefs: ColDef = {
  ...defaultColDef,
  lockPinned: true,
};

export const ResultsGrid = ({
  results,
  isLoading,
}: {
  results: CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse | undefined;
  isLoading: boolean;
}) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const { handleSelectionChanged, gridRef } =
    useGridSelectionSync<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>();

  const getRowId: GetRowIdFunc<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult> = React.useCallback(
    (params) => params.data?.security_record_id ?? "",
    []
  );

  const PortfolioResultsColumnDefs = getPortfolioResultsColumnDefs(metadata ?? {});

  return (
    <CAGrid
      ref={gridRef}
      gridProps={{
        defaultColDef: defaultColDefs,
        columnDefs: PortfolioResultsColumnDefs,
        rowData: results?.security_record_results,
        loading: isLoading,
        statusBar: undefined,
        getRowId,
        enableCharts: false,
        rowSelection: rowSelection,
        onSelectionChanged: handleSelectionChanged,
        selectionColumnDef,
      }}
      initialMessage="Click on ▷ to load Results."
      hasRun={isLoading ? true : !!results?.status}
      enableUserGridViews
      gridType="portfolio-results"
      stackStyles={{
        h: "calc(100vh - 17rem)",
      }}
    />
  );
};
