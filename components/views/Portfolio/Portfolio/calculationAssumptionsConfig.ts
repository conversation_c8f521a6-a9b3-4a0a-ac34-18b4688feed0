import {
  CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions,
  CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions,
} from "@/utils/openapi";

export type CalculationAssumptionConfigType = {
  field: keyof CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions;
  metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions | undefined;
  label: string;
  type: "select" | "date" | "number" | "object" | "month";
  hide?: boolean;
};

export const CALCULATION_ASSUMPTIONS_CONFIG: CalculationAssumptionConfigType[] = [
  { field: "market_data_source", label: "Market Data Source", type: "select", metadataKey: "market_data_sources" },
  { field: "curve_date", label: "Curve Date", type: "date", metadataKey: "curve_date" },
  { field: "pricing_date", label: "Pricing Date", type: "date", metadataKey: "curve_date" },
  { field: "settle_date", label: "Settle Date", type: "date", metadataKey: "settle_date" },
  { field: "model_version", label: "Prepay Model Version", type: "select", metadataKey: "version" },
  { field: "current_coupon_model_name", label: "Current Coupon", type: "select", metadataKey: "current_coupon_model" },
  {
    field: "mortgage_model_type",
    label: "Primary-Secondary Spread",
    type: "select",
    metadataKey: "primary_secondary_spread",
  },
  { field: "repline_algorithm", label: "Repline Algo", type: "select", metadataKey: "repline_level" },
  { field: "cello_cdu_date", label: "Cello CDU Date", type: "month", metadataKey: undefined },
  {
    field: "ignore_cdu_paydate_after",
    label: "Ignore Paydate After",
    type: "date",
    metadataKey: "ignore_paydate_after",
  },
  { field: "projection_paths", label: "Paths", type: "number", metadataKey: "interest_rate_paths" },
  { field: "interest_rate_model_name", label: "Type", type: "select", metadataKey: "type" },
  { field: "interest_rate_calibration_method", label: "Calibration", type: "select", metadataKey: "calibration" },
  { field: "yield_curve_model_name", label: "Yield Curve Model", type: "select", metadataKey: "yield_curve_model" },
  { field: "key_rate_points", label: "Key Rates", type: "select", metadataKey: "key_rate_points" },
  { field: "key_rate_shift", label: "Key Rate BP Shift", type: "number", metadataKey: "key_rate_bp_shift" },
  { field: "num_dist_workers", label: "VCPU Per OAS", type: "select", metadataKey: "vcpu_per_oas" },
];
