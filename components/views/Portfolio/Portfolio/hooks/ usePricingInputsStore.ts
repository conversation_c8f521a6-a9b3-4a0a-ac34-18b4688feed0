import { create } from "zustand";
import { CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord } from "@/utils/openapi";

export interface PricingInputsInitProps {
  rowData: CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord[];
}
export interface PricingInputsStore extends PricingInputsInitProps {
  // State
  hasChanges: boolean;
  selectedRows: string[] | undefined;

  // Actions
  setRowData: (rowData: CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord[]) => void;
  setHasChanges: (hasChanges: boolean) => void;
  setSelectedRows: (selectedRows: string[] | undefined) => void;
}

export const createPricingInputsStore = (initProps: PricingInputsInitProps) => {
  const DEFAULT_PROPS: PricingInputsInitProps = { rowData: [{}] };
  return create<PricingInputsStore>()((set) => ({
    ...DEFAULT_PROPS,
    ...initProps,
    selectedRows: undefined,
    hasChanges: false,
    setSelectedRows: (selectedRows) => set({ selectedRows: selectedRows }),
    setRowData: (rowData) => {
      set((state) => {
        return {
          ...state,
          rowData: rowData,
        };
      });
    },
    setHasChanges: (hasChanges) => {
      set((state) => {
        return {
          ...state,
          hasChanges: hasChanges,
        };
      });
    },
  }));
};

export type PricingInputsStoreType = ReturnType<typeof createPricingInputsStore>;
