import React, { useCallback, useRef } from "react";
import { AgGridReact } from "ag-grid-react";
import { SelectionChangedEvent } from "ag-grid-enterprise";
import { CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult } from "@/utils/openapi";
import { usePricingInputsStore } from "../PricingInputs/PricingInputsContext";

export const useGridSelectionSync = <T extends { security_record_id?: string | null }>() => {
  const gridRef = useRef<AgGridReact<T>>(null);

  const { selectedRows, setSelectedRows } = usePricingInputsStore((s) => ({
    selectedRows: s.selectedRows,
    setSelectedRows: s.setSelectedRows,
  }));

  const getSecurityRecordId = useCallback(
    (data: CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult) => data?.security_record_id ?? "",
    []
  );

  // Handle selection changes from this grid
  const handleSelectionChanged = React.useCallback(
    (event: SelectionChangedEvent<T>) => {
      const rowsSelected = event.selectedNodes
        ?.map((node) => (node.data ? getSecurityRecordId(node.data) : null))
        .filter(Boolean) as string[];

      const newSelection = rowsSelected.length ? rowsSelected : undefined;
      setSelectedRows(newSelection);
    },
    [setSelectedRows, getSecurityRecordId]
  );

  React.useEffect(() => {
    const gridApi = gridRef.current?.api;
    if (gridApi == null) return;

    gridApi.forEachNode((node) => {
      const securityRecordId = node.data?.security_record_id;
      if (securityRecordId && selectedRows?.includes(securityRecordId)) {
        node.setSelected(true);
      } else {
        node.setSelected(false);
      }
    });
  }, [selectedRows]);

  return { gridRef, handleSelectionChanged };
};
