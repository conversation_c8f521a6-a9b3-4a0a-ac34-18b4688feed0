import React from "react";
import {
  CellEditRequestEvent,
  IsRowSelectable,
  ProcessDataFromClipboardParams,
  RowDragEndEvent,
  RowSelectionOptions,
} from "ag-grid-enterprise";
import { useParams } from "next/navigation";
import CAGrid from "@/design-system/molecules/CAGrid";
import { getPortfolioPricingInputsColumnDefs } from "@/utils/grid/portfolio/PortfolioPricingInputsColumnDefs";
import { useUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import { CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord } from "@/utils/openapi";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { addEmptyRow, updatePortfolioSecurityRow, updateRowsFromClipboard } from "../../helpers";
import { useGridSelectionSync } from "../hooks/useGridSelectionSync";
import { usePricingInputsStore } from "./PricingInputsContext";

// Grid config
const IS_READ_ONLY_EDIT = true;
const STATUS_BAR = undefined;
const ENABLE_CHARTS = false;
const ROW_DRAG_MANAGED = true;
const SUPRESS_MOVE_WHEN_DRAGGING = true;

export const PricingInputsGrid = () => {
  const { id } = useParams();
  const {
    state: { metadata },
  } = useGlobalContext();

  const { data: userPortfolioData, isLoading } = useUserPortfolio(Number(id));
  const { rowData, setRowData, setHasChanges, hasChanges } = usePricingInputsStore((s) => ({
    rowData: s.rowData,
    setRowData: s.setRowData,
    setHasChanges: s.setHasChanges,
    hasChanges: s.hasChanges,
  }));

  const processDataFromClipboard = React.useCallback(
    (
      params: ProcessDataFromClipboardParams<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>
    ): string[][] | null => {
      const data = [...params.data];

      // Get the focused cell's row index to determine where to start pasting
      const focusedCell = params?.api?.getFocusedCell();
      const startRowIndex = focusedCell?.rowIndex ?? 0;
      const columnId =
        (focusedCell?.column.getColId() as keyof CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord) ?? "name";

      // Update existing rows from the paste position
      const updatedRowData = updateRowsFromClipboard(rowData, data, startRowIndex, columnId);
      const updatedRowDataWithEmptyRow = addEmptyRow(updatedRowData.filter((el) => !!el.security_record_id));
      setRowData(updatedRowDataWithEmptyRow);
      if (!hasChanges) {
        setHasChanges(true);
      }
      return null;
    },
    [hasChanges, rowData, setHasChanges, setRowData]
  );

  const handleCellEditRequest = React.useCallback(
    (event: CellEditRequestEvent<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>) => {
      const isLastRow = event.rowIndex === rowData.length - 1;

      const newData = updatePortfolioSecurityRow(event);
      const updatedRowData = rowData.map((row, index) => (index === event.rowIndex ? newData : row));

      setRowData(isLastRow ? addEmptyRow(updatedRowData) : updatedRowData);
      if (!hasChanges) {
        setHasChanges(true);
      }
    },
    [hasChanges, rowData, setHasChanges, setRowData]
  );

  const { handleSelectionChanged, gridRef } =
    useGridSelectionSync<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>();

  const isRowSelectable: IsRowSelectable<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord> = React.useCallback(
    (node) => !!node.data?.security_record_id,
    []
  );

  const rowSelectionOptions: RowSelectionOptions<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord> = React.useMemo(
    () => ({
      mode: "multiRow",
      isRowSelectable,
      hideDisabledCheckboxes: true,
    }),
    [isRowSelectable]
  );

  const columnDefs = React.useMemo(
    () =>
      metadata
        ? getPortfolioPricingInputsColumnDefs(
            metadata,
            userPortfolioData?.user_portfolio?.user_portfolio_input?.calculation_assumptions ?? {}
          )
        : [],
    [metadata, userPortfolioData?.user_portfolio?.user_portfolio_input?.calculation_assumptions]
  );

  const handleRowDragEnd = React.useCallback(
    (e: RowDragEndEvent<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>) => {
      // Get the sorted row data from the grid after drag operation
      const sortedRowData: CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord[] = [];
      e.api.forEachNodeAfterFilterAndSort((node) => {
        if (node.data) {
          sortedRowData.push(node.data);
        }
      });

      // Update the state with the new sorted order
      setRowData(sortedRowData);

      if (!hasChanges) {
        setHasChanges(true);
      }
    },
    [hasChanges, setHasChanges, setRowData]
  );

  if (!columnDefs.length) {
    return null;
  }

  return (
    <CAGrid
      ref={gridRef}
      gridProps={{
        columnDefs: columnDefs,
        rowData: rowData,
        processDataFromClipboard,
        rowSelection: rowSelectionOptions,
        loading: isLoading,
        onCellEditRequest: handleCellEditRequest,
        enableCharts: ENABLE_CHARTS,
        readOnlyEdit: IS_READ_ONLY_EDIT,
        statusBar: STATUS_BAR,
        onSelectionChanged: handleSelectionChanged,
        rowDragManaged: ROW_DRAG_MANAGED,
        suppressMoveWhenRowDragging: SUPRESS_MOVE_WHEN_DRAGGING,
        onRowDragEnd: handleRowDragEnd,
      }}
      hideHeader
      hasRun
      enableUserGridViews={false}
      stackStyles={{
        h: "calc(100vh - 17rem)",
      }}
    />
  );
};
