import { createContext, useContext, useRef } from "react";
import { useStoreWithEqualityFn } from "zustand/traditional";
import {
  PricingInputsInitProps,
  PricingInputsStore,
  PricingInputsStoreType,
  createPricingInputsStore,
} from "../hooks/ usePricingInputsStore";

export const PricingInputsContext = createContext<PricingInputsStoreType | null>(null);

type PricingInputsProps = React.PropsWithChildren<PricingInputsInitProps>;

export function PricingInputsProvider({ children, ...props }: PricingInputsProps) {
  const storeRef = useRef<PricingInputsStoreType | null>(null);

  if (!storeRef.current) {
    storeRef.current = createPricingInputsStore(props);
  }
  return <PricingInputsContext.Provider value={storeRef.current}>{children}</PricingInputsContext.Provider>;
}

export function usePricingInputsStore<T>(
  selector: (state: PricingInputsStore) => T,
  equalityFn?: (left: T, right: T) => boolean
): T {
  const store = useContext(PricingInputsContext);
  if (!store) throw new Error("Missing PricingInputsProvider in the tree");
  return useStoreWithEqualityFn(store, selector, equalityFn);
}
