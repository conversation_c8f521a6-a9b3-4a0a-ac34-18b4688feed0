import React from "react";
import { <PERSON><PERSON>, Flex } from "@chakra-ui/react";
import CAModal from "@/design-system/molecules/CAModal";
import { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio } from "@/utils/openapi";
import CAInput from "@/design-system/molecules/CAInput";
import { usePortfolioActions } from "@/utils/swr-hooks/UserPortfolio";

export const RenamePortfolioModal = ({
  isOpen,
  onClose,
  userPortfolio,
}: {
  isOpen: boolean;
  onClose: () => void;
  userPortfolio: CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio;
}) => {
  const defaultName = userPortfolio?.user_portfolio_name;
  const [name, setName] = React.useState(defaultName ?? "Untitled");
  const { renamePortfolio, isMutating } = usePortfolioActions();

  const handleSubmit = () => {
    if (!name) return;
    if (name !== defaultName) {
      renamePortfolio(name);
    }
    onClose();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.currentTarget.value;
    setName(val);
  };
  return (
    <CAModal modalHeader="Save" isOpen={isOpen} onClose={onClose}>
      <CAInput type="text" value={name} onChange={handleChange} />
      <Flex my={4}>
        <Button ml="auto" variant="primary" type="submit" isLoading={isMutating} onClick={handleSubmit}>
          Submit
        </Button>
      </Flex>
    </CAModal>
  );
};
