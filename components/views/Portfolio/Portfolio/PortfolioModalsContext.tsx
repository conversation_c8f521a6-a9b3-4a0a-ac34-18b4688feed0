import React, { ReactN<PERSON>, createContext, useContext, useState } from "react";
import { useParams } from "next/navigation";
import { useUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import { EditPortfolioModal } from "./EditPortfolioModal";
import { RenamePortfolioModal } from "./RenamePortfolioModal";

type ModalStateType = "edit" | "rename";

interface PortfolioModalsContextType {
  openModal: ({ type }: { type: ModalStateType }) => void;
  closeModals: () => void;
}

const PortfolioModalsContext = createContext<PortfolioModalsContextType | undefined>(undefined);

export const usePortfolioModals = () => {
  const context = useContext(PortfolioModalsContext);
  if (!context) {
    throw new Error("usePortfolioModals must be used within a PortfolioModalsProvider");
  }
  return context;
};

interface PortfolioModalsProviderProps {
  children: ReactNode;
}

export const PortfolioModalsProvider = ({ children }: PortfolioModalsProviderProps) => {
  const [modalState, setModalState] = useState<{
    type: ModalStateType | null;
  }>({
    type: null,
  });

  const { id } = useParams();
  const { data } = useUserPortfolio(Number(id));

  const closeModals = () => {
    setModalState({ type: null });
  };

  const userPortfolio = data?.user_portfolio;
  return (
    <PortfolioModalsContext.Provider value={{ openModal: setModalState, closeModals }}>
      {children}
      {modalState.type === "edit" && userPortfolio && (
        <EditPortfolioModal userPortfolio={userPortfolio} isOpen={modalState.type === "edit"} onClose={closeModals} />
      )}
      {modalState.type === "rename" && userPortfolio && (
        <RenamePortfolioModal
          isOpen={modalState.type === "rename"}
          onClose={closeModals}
          userPortfolio={userPortfolio}
        />
      )}
    </PortfolioModalsContext.Provider>
  );
};
