import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON><PERSON>ck,
  <PERSON>r,
  VStack,
  chakra,
  useColorModeValue,
} from "@chakra-ui/react";
import { Text } from "@chakra-ui/layout";
import { Controller, useForm } from "react-hook-form";
import { IoSyncCircleOutline } from "react-icons/io5";
import CASearch from "@/design-system/molecules/CASearch";
import { isSuperAdmin } from "@/utils/helpers";
import CAHeadingOriginal from "@/design-system/atoms/CAHeading";
import CAInput from "@/design-system/molecules/CAInput";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import ValueDisplay from "@/components/helpers/ValueDisplay";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { searchByKeywords } from "@/hooks/useKeywordsSearch";
import { usePortfolioSettings } from "@/contexts/PageContexts/PortfolioSettingsPageContext";
import { PortfolioSettingsPageSettingsType } from "@/contexts/PageContexts/PortfolioSettingsPageContext/PortfolioSettingsPageContextTypes";

const SectionHeading: React.FC<React.PropsWithChildren> = ({ children }) => (
  <Box pt={2}>
    <CAHeadingOriginal as={"h3"} variant={"subHeading"}>
      {children}
    </CAHeadingOriginal>
  </Box>
);

const PortfolioSettingsDrawer = () => {
  const {
    state: { userData },
  } = useAuthentication();
  const {
    state: { isDrawerOpen },
    action: { updateSettings, closeDrawer },
  } = usePortfolioSettings();

  const [searchKey, setSearchKey] = React.useState("");

  const { register, handleSubmit, control } = useForm<PortfolioSettingsPageSettingsType>();

  const onSubmit = (data: PortfolioSettingsPageSettingsType) => {
    updateSettings(data);
    closeDrawer();
  };

  const onReset = () => {
    updateSettings({});
    setSearchKey("");
  };

  const onSearchTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKey(e.target.value);
  };

  const setDisplayForField = (fieldName: string) => {
    return searchByKeywords(searchKey, fieldName) ? "block" : "none";
  };

  const handleClose = () => {
    setSearchKey("");
    closeDrawer();
  };

  return (
    <Drawer autoFocus={false} placement="right" onClose={handleClose} isOpen={isDrawerOpen} size={"xs"}>
      <DrawerOverlay>
        <DrawerContent overflowY="scroll">
          <chakra.form p={4} onSubmit={handleSubmit(onSubmit)}>
            <Box pt={3} pb={2}>
              <CASearch
                placeholder="Filter"
                hideLabel={true}
                name={"searchSettings"}
                onChange={onSearchTextChange}
                bg={useColorModeValue("celloBlue.25", "celloBlue.1000")}
              />
            </Box>
            <VStack alignItems="stretch" pt={5}>
              <SectionHeading>Application</SectionHeading>
              <ValueDisplay
                name="Version"
                value={process.env.NEXT_PUBLIC_GIT_TAG || process.env.NEXT_PUBLIC_GIT_HASH || "-"}
              />
              <Box>
                <HStack justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text _hover={{ cursor: "pointer" }} variant="primary">
                      Use Cache
                    </Text>
                  </Box>
                  <Box>
                    <Controller
                      name={"useCache"}
                      control={control}
                      render={({ field: { name, onChange, value } }) => {
                        return (
                          <CASwitchInput
                            mr="-2"
                            name={name}
                            hideLabel={true}
                            defaultChecked={value}
                            isChecked={value}
                            onChange={onChange}
                          />
                        );
                      }}
                    />
                  </Box>
                </HStack>
              </Box>
              {isSuperAdmin(userData) && (
                <Box display={setDisplayForField("Timeout Minutes")}>
                  <CAInput
                    type="number"
                    inputType="positive-integer"
                    label={"Timeout (min)"}
                    {...register("timeoutMinutes", {
                      valueAsNumber: true,
                      required: true,
                    })}
                  />
                </Box>
              )}
            </VStack>
            <Flex
              direction="row"
              mt={4}
              pt={3}
              pb={3}
              position="sticky"
              bottom={0}
              bg={useColorModeValue("white", "celloBlue.900")}
            >
              <Button
                leftIcon={<IoSyncCircleOutline />}
                onClick={onReset}
                size="sm"
                fontSize="3xl"
                variant="secondary"
                title={`Defaults`}
                aria-label={`Defaults`}
                pr="2"
                pl="1"
              >
                <Text>Defaults</Text>
              </Button>
              <Spacer />
              <HStack spacing={2}>
                <Button variant="secondary" size="sm" onClick={handleClose}>
                  Cancel
                </Button>
                <Button variant="primary" size="sm" type="submit">
                  Save
                </Button>
              </HStack>
            </Flex>
          </chakra.form>
        </DrawerContent>
      </DrawerOverlay>
    </Drawer>
  );
};

export default PortfolioSettingsDrawer;
