import React from "react";
import { useParams } from "next/navigation";
import { Box, Button, Flex, Grid, GridItem, Input, Skeleton, Tooltip, useColorModeValue } from "@chakra-ui/react";
import { IoSaveOutline } from "react-icons/io5";
import { HiOutlineChevronLeft } from "react-icons/hi";
import { useRouter } from "next/router";
import { usePortfolioActions, useUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import CARun from "@/design-system/molecules/CARun";
import StopWatch from "@/design-system/molecules/StopWatch";
import CAIcon from "@/design-system/atoms/CAIcon";
import { RunInfo, RunTag, RunTagButtonGroup } from "@/design-system/molecules/RunTag/RunTag";
import RunAlertButton from "@/components/helpers/RunAlertButton";
import { PortfolioActionsDropdown } from "./PortfolioActionsDropdown";
import { usePortfolioModals } from "./PortfolioModalsContext";
import { usePricingInputsStore } from "./PricingInputs/PricingInputsContext";

interface PortfolioHeaderProps {
  onSubmit: () => void;
  isLoading: boolean;
  runInfo: RunInfo & { endDate?: string | null };
}

export const PortfolioHeader = ({ onSubmit, isLoading: isRunning, runInfo }: PortfolioHeaderProps) => {
  const { id } = useParams();
  const router = useRouter();
  const { data, isLoading } = useUserPortfolio(Number(id));
  const { openModal } = usePortfolioModals();
  const { savePortfolio, isMutating: isSaving } = usePortfolioActions();
  const iconBg = useColorModeValue("celloBlue.100", "celloBlue.800");
  const hasChanges = usePricingInputsStore((s) => s.hasChanges);

  if (isLoading || !data?.user_portfolio)
    return (
      <Flex w="full" justifyContent="center" alignItems="center" mb={4}>
        <Skeleton height="30px" width="200px" />
      </Flex>
    );

  const handleSavePortfolio = async (e: React.MouseEvent) => {
    e.stopPropagation();

    if (data?.user_portfolio?.is_ad_hoc) {
      openModal({ type: "rename" });
      return;
    }
    await savePortfolio();
  };

  return (
    <Grid
      templateColumns={{ base: "auto 1fr", md: "1fr 1fr 1fr" }}
      templateRows={{ base: "auto auto", md: "1fr" }}
      alignItems="center"
      gap={6}
      mb={4}
    >
      <GridItem colSpan={{ base: 1, md: 1 }}>
        <Button
          variant="ghost"
          w="8"
          h="8"
          cursor="pointer"
          borderRadius="full"
          bg={iconBg}
          px={0}
          minW="unset"
          onClick={() => router.push("/portfolio")}
        >
          <HiOutlineChevronLeft size="16" />
        </Button>
      </GridItem>
      <GridItem colSpan={{ base: 1, md: 1 }}>
        <Flex gap={4} justifyContent={{ base: "flex-end", md: "center" }} alignItems="center">
          <Button
            disabled={isSaving}
            onClick={handleSavePortfolio}
            paddingStart={0}
            paddingEnd={0}
            minWidth={4}
            height={4}
            backgroundColor={"transparent"}
            textColor={"safetyOrange.500"}
            isLoading={isSaving}
            visibility={hasChanges ? "visible" : "hidden"}
          >
            <CAIcon as={IoSaveOutline} boxSize={4} variant="default" cursor={"pointer"} />
          </Button>
          <PortolioNameInput defaultName={data.user_portfolio.user_portfolio_name ?? "Untitled"} />
          <PortfolioActionsDropdown userPortfolio={data.user_portfolio} />
        </Flex>
      </GridItem>
      <GridItem colSpan={{ base: 2, md: 1 }}>
        <Flex justifyContent="flex-end" gap={2} alignItems="center">
          <Box display={isRunning ? "none" : "block"}>
            <RunTagButtonGroup>
              <RunAlertButton runId={runInfo.id ?? undefined} />
              {runInfo?.id && <RunTag runInfo={runInfo} />}
            </RunTagButtonGroup>
          </Box>
          <StopWatch
            startDate={runInfo.date ?? undefined}
            endDate={runInfo.endDate ?? undefined}
            isTimerRunning={isRunning}
          />
          <CARun onClick={onSubmit} disabled={isRunning} />
        </Flex>
      </GridItem>
    </Grid>
  );
};

const PortolioNameInput = ({ defaultName }: { defaultName: string }) => {
  const { id } = useParams();
  const [name, setName] = React.useState(defaultName);
  const { data: userPortfolio } = useUserPortfolio(Number(id));
  const { renamePortfolio, isMutating } = usePortfolioActions();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    setName(val);
  };

  React.useEffect(() => {
    setName(defaultName);
  }, [defaultName]);

  const handleBlur = () => {
    if (!name) return;
    if (name !== defaultName) {
      renamePortfolio(name);
    }
  };
  return (
    <Tooltip
      label="Ad Hoc portfolios cannot be renamed before saving"
      hidden={!userPortfolio?.user_portfolio?.is_ad_hoc}
    >
      <Input
        w={`${Math.max(name.length, 20)}ch`}
        minW="8ch"
        maxW="40ch"
        border="none"
        h="30px"
        value={name}
        onChange={handleChange}
        onBlur={handleBlur}
        disabled={isMutating || userPortfolio?.user_portfolio?.is_ad_hoc}
      />
    </Tooltip>
  );
};
