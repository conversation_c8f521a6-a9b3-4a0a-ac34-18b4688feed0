import React, { useState } from "react";
import {
  Box,
  Button,
  Flex,
  Icon,
  IconButton,
  Skeleton,
  Tab,
  <PERSON>b<PERSON>ist,
  TabPanel,
  TabPane<PERSON>,
  Ta<PERSON>,
  Text,
} from "@chakra-ui/react";
import { IoRemoveCircleSharp, IoSettings } from "react-icons/io5";
import { useParams } from "next/navigation";
import MainLayout from "@/components/layouts/MainLayout";
import CAIcon from "@/design-system/atoms/CAIcon";
import { usePortfolioSettings } from "@/contexts/PageContexts/PortfolioSettingsPageContext";
import { usePortfolioActions, useUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import { useRunUserPortfolio, useRunUserPortfolioResults } from "@/utils/swr-hooks/UserPortfolioRun";
import {
  CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioRun,
  CA_Mastr_Models_v1_0_Models_Stage,
} from "@/utils/openapi";
import { getUuid } from "@/utils/helpers";
import PortfolioSettingsDrawer from "./PortfolioSettingsDrawer";
import { PortfolioHeader } from "./PortfolioHeader";
import { PricingInputsGrid } from "./PricingInputs/PricingInputsGrid";
import { PricingInputsProvider, usePricingInputsStore } from "./PricingInputs/PricingInputsContext";
import { ResultsGrid } from "./ResultsGrid/ResultsGrid";
import { PortfolioModalsProvider } from "./PortfolioModalsContext";

export const PortfolioView = ({ userPortfolioId }: { userPortfolioId: string }) => {
  const {
    action: { openDrawer },
  } = usePortfolioSettings();
  const { data, isLoading } = useUserPortfolio(Number(userPortfolioId));

  if (isLoading) {
    return <Skeleton w="full" h={30} />;
  }

  const securityRecords = data?.user_portfolio?.user_portfolio_input?.security_records;

  return (
    <MainLayout
      headerSettings={
        <IconButton
          data-testid="toggle-settings-drawer"
          aria-label="Toggle Settings Drawer"
          icon={<CAIcon as={IoSettings} variant="secondary" />}
          variant="ghost"
          onClick={openDrawer}
        />
      }
      title="Portfolio"
    >
      <Box mx="6" my="4">
        <PricingInputsProvider rowData={securityRecords ? [...securityRecords, {}] : [{}]}>
          <PortfolioModalsProvider>
            <PortfolioDisplay lastRunInfo={data?.user_portfolio?.last_run} />
          </PortfolioModalsProvider>
        </PricingInputsProvider>
      </Box>
      <PortfolioSettingsDrawer />
    </MainLayout>
  );
};

const PortfolioDisplay = ({
  lastRunInfo,
}: {
  lastRunInfo?: CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioRun | undefined;
}) => {
  const { id } = useParams();
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [newRunDate, setNewRunDate] = useState("");
  const [newRunId, setNewRunId] = useState("");

  // Mutations
  const { savePortfolio, isMutating: isUpdating } = usePortfolioActions();
  const { trigger: initiateRun, isMutating } = useRunUserPortfolio();

  const hasOldRun = !!lastRunInfo;
  const isNewRun = !!newRunDate;
  const shouldFetchResults = hasOldRun || isNewRun;
  // Queries
  const {
    data: results,
    isLoading: isLoadingResults,
    mutate: mutateResults,
  } = useRunUserPortfolioResults(
    {
      user_portfolio_id: shouldFetchResults ? Number(id) : undefined,
    },
    newRunId
  );

  const { rowData, selectedRows, setRowData, setHasChanges } = usePricingInputsStore((s) => ({
    rowData: s.rowData,
    setRowData: s.setRowData,
    selectedRows: s.selectedRows,
    setHasChanges: s.setHasChanges,
  }));

  const handleSubmit = async () => {
    setSelectedTabIndex(1);
    setNewRunDate(new Date().toISOString());

    await savePortfolio();
    await initiateRun({
      user_portfolio_id: Number(id),
      security_record_ids: selectedRows,
    });
    setNewRunId(getUuid());
  };

  const handleDeleteRows = () => {
    if (!selectedRows?.length) return;

    const filteredRowData = rowData.filter((row) => !selectedRows.includes(row.security_record_id ?? ""));
    setRowData(filteredRowData);
    setHasChanges(true);
    mutateResults({
      ...results,
      security_record_results: results?.security_record_results?.filter(
        (sec) => !selectedRows.includes(sec.security_record_id ?? "")
      ),
    });
  };

  const isLoading = isMutating || isLoadingResults || isUpdating;
  return (
    <>
      <PortfolioHeader
        runInfo={{
          id: results?.user_portfolio_run_id,
          date: isNewRun ? newRunDate : lastRunInfo?.start_time,
          endDate: isNewRun ? undefined : lastRunInfo?.end_time,
          isOldRun: hasOldRun && !isNewRun,
        }}
        onSubmit={handleSubmit}
        isLoading={isLoading || results?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING}
      />
      <Tabs index={selectedTabIndex} onChange={setSelectedTabIndex} variant="line">
        <Flex w="full" alignItems="center" justifyContent="space-between">
          <TabList w="fit-content" rounded="md">
            <Tab>Pricing Inputs</Tab>
            <Tab>Results</Tab>
          </TabList>
          <Button
            height={8}
            disabled={!selectedRows?.length}
            leftIcon={<Icon as={IoRemoveCircleSharp} fontSize="20" />}
            variant="iconButton"
            onClick={handleDeleteRows}
            visibility={selectedRows?.length ? "visible" : "hidden"}
          >
            <Text color="inherit">Delete Rows</Text>
          </Button>
        </Flex>

        <TabPanels p="0" rounded="2xl" mt="4">
          <TabPanel p="0" rounded="2xl">
            <PricingInputsGrid />
          </TabPanel>
          <TabPanel p="0" rounded="2xl">
            <ResultsGrid isLoading={isLoading} results={results} />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </>
  );
};
