import React from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { Button, Flex, FormControl, FormLabel, Grid, GridItem, Text } from "@chakra-ui/react";
import CAModal from "@/design-system/molecules/CAModal";
import {
  CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio,
  CA_Mastr_Api_v1_0_Models_Util_IntegerOption,
  CA_Mastr_Api_v1_0_Models_Util_PortfolioCalculations,
  CA_Mastr_Api_v1_0_Models_Util_StringOption,
  CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions,
  CA_Mastr_Models_v1_0_Models_TagType,
} from "@/utils/openapi";
import CAInput from "@/design-system/molecules/CAInput";
import CADateInput from "@/design-system/molecules/CADateInput";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useUpdateUserPortfolio } from "@/utils/swr-hooks/UserPortfolio";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { convertSnakeToTitleCase, parseDateToYYYYMMDD } from "@/utils/helpers";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { EditTagSelector } from "@/design-system/organisms/TagSelector/EditTagSelector";

interface EditPortfolioModalProps {
  isOpen: boolean;
  onClose: () => void;
  userPortfolio: CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio;
}

interface PortfolioFormInputs {
  as_of_date?: string;
  tags?: string[];
  user_portfolio_name: string;
  user_portfolio_input: {
    calculations: CA_Mastr_Api_v1_0_Models_Util_PortfolioCalculations;
    calculation_assumptions: CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions;
  };
}

export const EditPortfolioModal = ({ isOpen, onClose, userPortfolio }: EditPortfolioModalProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const { trigger: updateUser, isMutating } = useUpdateUserPortfolio();

  const { register, handleSubmit, control, setValue } = useForm<PortfolioFormInputs>({
    defaultValues: {
      user_portfolio_name:
        userPortfolio.user_portfolio_name === "Untitled" ? "" : userPortfolio.user_portfolio_name ?? "",
      tags: userPortfolio.tags ?? [],
      as_of_date: userPortfolio.as_of_date ?? new Date().toISOString(),
      user_portfolio_input: {
        calculations: userPortfolio.user_portfolio_input?.calculations,
        calculation_assumptions: userPortfolio.user_portfolio_input?.calculation_assumptions,
      },
    },
  });

  const interestRateModel = useWatch({
    name: "user_portfolio_input.calculation_assumptions.interest_rate_model_name",
    control,
  });

  const submitForm = handleSubmit(async (data) => {
    await updateUser({
      ...data,
      user_portfolio_id: userPortfolio.user_portfolio_id,
      last_updated: userPortfolio.updated ?? "",
      user_portfolio_input: { ...userPortfolio.user_portfolio_input, ...data.user_portfolio_input },
    });
    onClose();
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInterestRateModelChange = (e: any) => {
    const val = e.target.value;
    setValue("user_portfolio_input.calculation_assumptions.projection_paths", val === "LMM2F" ? 256 : 200);
  };

  return (
    <CAModal size="3xl" isOpen={isOpen} onClose={onClose} modalHeader="Edit Portfolio" showCloseIcon>
      <form onSubmit={submitForm}>
        <Grid templateColumns={"repeat(2, auto)"} gap={4} p={4}>
          <GridItem maxW="200px">
            <FormControl>
              <FormLabel as={Text} htmlFor="user_portfolio_name" variant="tableLeft" fontSize="sm">
                Portfolio Name
              </FormLabel>
              <CAInput {...register("user_portfolio_name", { required: true })} />
            </FormControl>
            <FormControl mt={4}>
              <FormLabel as={Text} htmlFor="as_of_date" variant="tableLeft" fontSize="sm">
                As of Date
              </FormLabel>
              <Controller
                name="as_of_date"
                control={control}
                render={({ field: { name, value, onChange, ref } }) => (
                  <CADateInput
                    ref={ref}
                    selectedDate={value ? new Date(value) : undefined}
                    onChange={(value) => onChange(parseDateToYYYYMMDD(value))}
                    name={name}
                  />
                )}
              />
            </FormControl>
            <FormControl mt={4}>
              <FormLabel as={Text} htmlFor="tags" variant="tableLeft" fontSize="sm">
                Tags
              </FormLabel>
              <Controller
                name="tags"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <EditTagSelector
                    tagType={CA_Mastr_Models_v1_0_Models_TagType.USER_PORTFOLIO}
                    onSubmit={(value) => onChange(value)}
                    setSelectedTags={(value) => onChange(value)}
                    tags={value ?? []}
                    style={{
                      w: "full",
                    }}
                    autoFocus={false}
                  />
                )}
              />
            </FormControl>
            <FormControl mt={4} maxW="200px">
              <FormLabel as={Text} htmlFor="user_portfolio_input.calculations" variant="tableLeft" fontSize="sm">
                Calculations
              </FormLabel>
              {Object.keys(metadata?.portfolio_settings?.calculations ?? {}).map((key) => (
                <CASwitchInput
                  key={key}
                  label={convertSnakeToTitleCase(key)}
                  {...register(
                    `user_portfolio_input.calculations.${
                      key as keyof CA_Mastr_Api_v1_0_Models_Util_PortfolioCalculations
                    }`
                  )}
                />
              ))}
            </FormControl>
          </GridItem>

          <GridItem>
            <FormLabel as={Text} variant="tableLeft" fontSize="sm">
              Calculation Defaults
            </FormLabel>
            <Flex flexDirection="column" columnGap={4} wrap="wrap" maxH="700px">
              {/* Market Data Source */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.market_data_source"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Market Data Source
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.market_data_source")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.market_data_sources as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Curve Date */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.curve_date"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Curve Date
                </FormLabel>
                <Controller
                  name="user_portfolio_input.calculation_assumptions.curve_date"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      selectedDate={value ? new Date(value as string) : undefined}
                      onChange={(date) => onChange(parseDateToYYYYMMDD(date))}
                      name={name}
                    />
                  )}
                />
              </FormControl>

              {/* Pricing Date */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.pricing_date"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Pricing Date
                </FormLabel>
                <Controller
                  name="user_portfolio_input.calculation_assumptions.pricing_date"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      selectedDate={value ? new Date(value as string) : undefined}
                      onChange={(date) => onChange(parseDateToYYYYMMDD(date))}
                      name={name}
                    />
                  )}
                />
              </FormControl>

              {/* Settle Date */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.settle_date"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Settle Date
                </FormLabel>
                <Controller
                  name="user_portfolio_input.calculation_assumptions.settle_date"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      selectedDate={value ? new Date(value as string) : undefined}
                      onChange={(date) => onChange(parseDateToYYYYMMDD(date))}
                      name={name}
                    />
                  )}
                />
              </FormControl>

              {/* Prepay Model Version */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.model_version"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Prepay Model Version
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.model_version")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.version as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Current Coupon */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.current_coupon_model_name"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Current Coupon
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.current_coupon_model_name")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.current_coupon_model as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Primary-Secondary Spread */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.mortgage_model_type"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Primary-Secondary Spread
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.mortgage_model_type")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.primary_secondary_spread as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Repline Algo */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.repline_algorithm"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Repline Algo
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.repline_algorithm")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.repline_level as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Cello CDU Date */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.cello_cdu_date"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Cello CDU Date
                </FormLabel>
                <Controller
                  name="user_portfolio_input.calculation_assumptions.cello_cdu_date"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      showMonthYearPicker
                      maxDate={new Date()}
                      selectedDate={value ? new Date(value as string) : undefined}
                      onChange={(val) => onChange(parseDateToYYYYMMDD(val))}
                      name={name}
                    />
                  )}
                />
              </FormControl>

              {/* Ignore Paydate After */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.ignore_cdu_paydate_after"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Ignore Paydate After
                </FormLabel>
                <Controller
                  name="user_portfolio_input.calculation_assumptions.ignore_cdu_paydate_after"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      selectedDate={value ? new Date(value as string) : undefined}
                      onChange={(date) => onChange(parseDateToYYYYMMDD(date))}
                      name={name}
                    />
                  )}
                />
              </FormControl>

              {/* Paths */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.projection_paths"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Paths
                </FormLabel>
                <CAInput
                  type="number"
                  disabled={interestRateModel === "LMM2F"}
                  {...register("user_portfolio_input.calculation_assumptions.projection_paths", {
                    valueAsNumber: true,
                  })}
                />
              </FormControl>

              {/* Type */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.interest_rate_model_name"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Type
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.interest_rate_model_name", {
                    onChange: handleInterestRateModelChange,
                  })}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.type as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Calibration */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.interest_rate_calibration_method"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Calibration
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.interest_rate_calibration_method")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.calibration as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Yield Curve Model */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.yield_curve_model_name"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Yield Curve Model
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.yield_curve_model_name")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.yield_curve_model as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>
                    )?.map((opt) => ({
                      id: opt.value,
                      displayValue: opt.display_value,
                      value: opt.value,
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Key Rates */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.key_rate_points"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Key Rates
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.key_rate_points")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.key_rate_points as Array<CA_Mastr_Api_v1_0_Models_Util_IntegerOption>
                    )?.map((opt) => ({
                      id: opt.value.toString(),
                      displayValue: opt.value.toString(),
                      value: opt.value.toString(),
                    })) ?? []
                  }
                />
              </FormControl>

              {/* Key Rate BP Shift */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.key_rate_shift"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  Key Rate BP Shift
                </FormLabel>
                <CAInput
                  type="number"
                  {...register("user_portfolio_input.calculation_assumptions.key_rate_shift", { valueAsNumber: true })}
                />
              </FormControl>

              {/* VCPU Per OAS */}
              <FormControl mt={4} maxW="200px">
                <FormLabel
                  as={Text}
                  htmlFor="user_portfolio_input.calculation_assumptions.num_dist_workers"
                  variant="tableLeft"
                  fontSize="sm"
                >
                  VCPU Per OAS
                </FormLabel>
                <CASelectDropdown
                  {...register("user_portfolio_input.calculation_assumptions.num_dist_workers")}
                  options={
                    (
                      metadata?.portfolio_settings?.calculation_assumptions
                        ?.vcpu_per_oas as Array<CA_Mastr_Api_v1_0_Models_Util_IntegerOption>
                    )?.map((opt) => ({
                      id: opt.value.toString(),
                      displayValue: opt.value.toString(),
                      value: opt.value.toString(),
                    })) ?? []
                  }
                />
              </FormControl>
            </Flex>
          </GridItem>
        </Grid>
        <Flex gap={2} my={4}>
          <Button ml="auto" variant="secondary" type="button" onClick={onClose}>
            Close
          </Button>
          <Button variant="primary" type="submit" isLoading={isMutating}>
            Submit
          </Button>
        </Flex>
      </form>
    </CAModal>
  );
};
