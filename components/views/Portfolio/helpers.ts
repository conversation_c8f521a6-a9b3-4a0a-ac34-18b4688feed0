import { CellEditRequestEvent } from "ag-grid-enterprise";
import { CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord } from "@/utils/openapi";
import { getUuid } from "@/utils/helpers";

export const updatePortfolioSecurityRow = (
  event: CellEditRequestEvent<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>
): CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord => {
  const oldData = event.data;
  const field = event.colDef.field;
  const newValue = event.newValue;

  if (!field) return oldData;

  const newData = { ...oldData };

  // Handle nested field paths like "calculation_assumptions_override.current_coupon_model_name"
  if (field.includes(".")) {
    const fieldParts = field.split(".");

    if (fieldParts[0] === "calculation_assumptions_override" && fieldParts.length === 2) {
      // Ensure calculation_assumptions_override exists
      if (!newData.calculation_assumptions_override) {
        newData.calculation_assumptions_override = {};
      }

      // Type-safe assignment for calculation assumptions fields
      const calcAssumptions = newData.calculation_assumptions_override;
      const calcField = fieldParts[1] as keyof typeof calcAssumptions;

      // Type-safe assignment based on the field
      (calcAssumptions as Record<string, typeof newValue>)[calcField] = newValue;
    }
  } else {
    // Handle direct properties of Portfolio_Security
    const directField = field as keyof CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord;
    (newData as Record<string, typeof newValue>)[directField] = newValue;
  }

  // Generate a new security_record_id whenever any cell is changed
  newData.security_record_id = getUuid();

  return newData;
};

export const updateRowsFromClipboard = (
  existingRowData: CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord[],
  clipboardData: string[][],
  startRowIndex: number,
  columnId: keyof CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord
): CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord[] => {
  const updatedRowData = [...existingRowData];

  clipboardData.forEach((row, index) => {
    const targetRowIndex = startRowIndex + index;

    // If we're updating beyond existing rows, add new rows
    if (targetRowIndex >= updatedRowData.length) {
      updatedRowData.push({
        [columnId]: row[0],
        calculation_assumptions_override: {},
        security_record_id: getUuid(),
      });
    } else {
      // Update existing row
      updatedRowData[targetRowIndex] = {
        ...updatedRowData[targetRowIndex],
        [columnId]: row[0],
        security_record_id: getUuid(),
      };
    }
  });

  return updatedRowData;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function addEmptyRow<T extends any[]>(data: T) {
  return [...data, {}];
}
