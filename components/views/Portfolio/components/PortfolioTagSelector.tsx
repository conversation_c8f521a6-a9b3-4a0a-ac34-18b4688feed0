import React from "react";
import {
  CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTracking,
  CA_Mastr_Models_v1_0_Models_TagType,
} from "@/utils/openapi";
import { usePatchUserEmpiricalTrackingMutation } from "@/utils/swr-hooks/UserEmpiricalTracking";
import { TagSelector } from "@/design-system/organisms/TagSelector/TagSelector";

export type TMode = "VIEW" | "EDIT";

const EmpiricalTrackingTagSelector: React.FC<
  Pick<
    CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTracking,
    "role" | "user_empirical_tracking_id" | "updated" | "tags"
  >
> = ({ role, user_empirical_tracking_id, updated, tags }) => {
  const {
    trigger: patchUserEmpiricalTracking,
    isMutating,
    data: userPatchData,
  } = usePatchUserEmpiricalTrackingMutation("edit");

  const lastUpdated = userPatchData?.user_empirical_tracking?.updated;

  const handleSubmit = (selections: string[]) => {
    patchUserEmpiricalTracking({
      user_empirical_tracking_id: user_empirical_tracking_id,
      tags: selections,
      last_updated: lastUpdated ?? updated ?? undefined,
    });
  };

  const handleRemoveClick = (tags: string[]) => {
    patchUserEmpiricalTracking({
      user_empirical_tracking_id: user_empirical_tracking_id,
      tags: tags,
      last_updated: lastUpdated ?? updated ?? undefined,
    });
  };

  if (!role || !tags) return null;
  return (
    <TagSelector
      tagType={CA_Mastr_Models_v1_0_Models_TagType.USER_PORTFOLIO}
      isDeleting={isMutating}
      onRemoveClick={handleRemoveClick}
      onSubmit={handleSubmit}
      role={role}
      tags={tags}
    />
  );
};

export default EmpiricalTrackingTagSelector;
