import * as React from "react";
import { Button, Center, Flex, FormControl, FormLabel, Stack, VStack, chakra } from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import { useColorModeValue } from "@chakra-ui/react";
import { useSearchParams } from "next/navigation";
import Link from "@/design-system/atoms/Link";
import Logo from "@/design-system/atoms/Logo";
import S from "@/constants/strings";
import CAInput from "@/design-system/molecules/CAInput";
import { CA_Mastr_Api_v1_0_Models_Auth_LoginRequest } from "@/utils/openapi";

export type LoginFormData = CA_Mastr_Api_v1_0_Models_Auth_LoginRequest;

export type LoginProps = {
  onLogin: (data: LoginFormData, cb: (hasFailed: boolean) => void) => Promise<void>;
};

const Login: React.FC<LoginProps> = ({ onLogin }: LoginProps) => {
  const search = useSearchParams();
  const email = search.get("email");

  const {
    register,
    handleSubmit,
    watch,
    formState: { isSubmitting },
  } = useForm<LoginFormData>({
    defaultValues: { username: email ?? undefined },
  });

  const watch_username = watch("username");

  const [hasLoginError, setLoginError] = React.useState(false);
  const onSubmit = handleSubmit(async (data) => {
    await onLogin(data, setLoginError);
  });
  return (
    <chakra.form onSubmit={onSubmit}>
      <Flex position="absolute" inset={0} p={8} flex={1} align={"center"} justify={"center"}>
        <Stack spacing={4} w={"full"} maxW={"sm"}>
          <VStack mb={10}>
            <Center>
              <Logo size="lg" logo="cello" />
            </Center>
          </VStack>
          <FormControl isInvalid={hasLoginError}>
            <FormLabel color={useColorModeValue("celloBlue.500", "gemBlue.500")}>{S.PAGES.LOGIN.EMAIL}</FormLabel>
            <CAInput {...register("username", { required: true })} data-testid="username" type="email" />
          </FormControl>
          <FormControl isInvalid={hasLoginError}>
            <FormLabel color={useColorModeValue("celloBlue.500", "gemBlue.500")}>{S.PAGES.LOGIN.PASSWORD}</FormLabel>
            <CAInput {...register("password", { required: true })} data-testid="password" type="password" />
          </FormControl>
          <Stack spacing={5}>
            <Link variant="link" href={`/forgot-password${watch_username ? `?email=${watch_username}` : ""}`}>
              Forgot password?
            </Link>
            <Button
              isLoading={isSubmitting}
              mt={3}
              data-testid="submit"
              variant="primary"
              type="submit"
              alignSelf={"center"}
            >
              SIGN IN
            </Button>
          </Stack>
        </Stack>
      </Flex>
    </chakra.form>
  );
};

export default Login;
