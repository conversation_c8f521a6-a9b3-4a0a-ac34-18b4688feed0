// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Login works 1`] = `
.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: absolute;
  inset: 0px;
  padding: var(--chakra-space-8);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--chakra-space-4);
  width: var(--chakra-sizes-full);
  max-width: var(--chakra-sizes-sm);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: var(--chakra-space-10);
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-5 {
  width: 197.5px;
  height: 57.5px;
}

.emotion-6 {
  width: 100%;
  position: relative;
}

.emotion-7 {
  display: block;
  text-align: start;
  font-size: var(--chakra-fontSizes-md);
  -webkit-margin-end: var(--chakra-space-3);
  margin-inline-end: var(--chakra-space-3);
  margin-bottom: var(--chakra-space-2);
  font-weight: var(--chakra-fontWeights-medium);
  transition-property: var(--chakra-transition-property-common);
  transition-duration: var(--chakra-transition-duration-normal);
  opacity: 1;
  color: var(--chakra-colors-celloBlue-500);
}

.emotion-7:disabled,
.emotion-7[disabled],
.emotion-7[aria-disabled=true],
.emotion-7[data-disabled] {
  opacity: 0.4;
}

.emotion-8 {
  width: 100%;
  position: relative;
  padding-top: var(--chakra-space-1);
  padding-bottom: var(--chakra-space-1);
}

.emotion-9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  position: relative;
  width: 100%;
}

.emotion-11::after {
  content: "";
  height: 2px;
  width: 0;
  bottom: 0px;
  position: absolute;
  background: #12CFCF;
  right: 50%;
  -webkit-transition: width 0.5s ease;
  transition: width 0.5s ease;
}

.emotion-11::before {
  content: "";
  height: 2px;
  width: 0;
  bottom: 0px;
  position: absolute;
  background: #12CFCF;
  left: 50%;
  z-index: 1;
  -webkit-transition: width 0.5s ease;
  transition: width 0.5s ease;
}

.emotion-11:focus-within::before {
  width: 50%;
}

.emotion-11:focus-within::after {
  width: 50%;
}

.emotion-11:focus-visible,
.emotion-11[data-focus-visible] {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.emotion-12 {
  width: 100%;
  height: 26px;
  -webkit-padding-start: var(--input-padding);
  padding-inline-start: var(--input-padding);
  -webkit-padding-end: var(--input-padding);
  padding-inline-end: var(--input-padding);
  min-width: 0px;
  outline: 2px solid transparent;
  outline-offset: 2px;
  position: relative;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  transition-property: var(--chakra-transition-property-common);
  transition-duration: var(--chakra-transition-duration-normal);
  --input-font-size: var(--chakra-fontSizes-md);
  --input-padding: var(--chakra-space-4);
  --input-border-radius: var(--chakra-radii-md);
  --input-height: var(--chakra-sizes-10);
  border: 1px solid;
  border-color: inherit;
  background: inherit;
  color: var(--chakra-colors-misty-900);
  font-weight: var(--chakra-fontWeights-normal);
  border-width: 0;
  border-bottom-width: 1px;
  border-bottom-color: var(--chakra-colors-celloBlue-300);
  padding: var(--chakra-space-2);
  padding-top: var(--chakra-space-1);
  padding-bottom: var(--chakra-space-1);
  border-radius: var(--chakra-radii-base);
  border-bottom-left-radius: var(--chakra-radii-none);
  border-bottom-right-radius: var(--chakra-radii-none);
  background-color: var(--chakra-colors-misty-300);
  cursor: auto;
  line-height: var(--chakra-lineHeights-normal);
  font-size: var(--chakra-fontSizes-sm);
  padding-left: var(--chakra-space-2);
}

.emotion-12:disabled,
.emotion-12[disabled],
.emotion-12[aria-disabled=true],
.emotion-12[data-disabled] {
  opacity: 0.4;
  cursor: not-allowed;
}

.emotion-12[aria-readonly=true],
.emotion-12[readonly],
.emotion-12[data-readonly] {
  box-shadow: var(--chakra-shadows-none)!important;
  -webkit-user-select: all;
  -moz-user-select: all;
  -ms-user-select: all;
  user-select: all;
}

.emotion-12[aria-invalid=true],
.emotion-12[data-invalid] {
  border-color: #E53E3E;
  box-shadow: 0 0 0 1px #E53E3E;
}

.chakra-ui-dark .emotion-12 {
  color: #FFF;
}

.emotion-12:hover,
.emotion-12[data-hover] {
  background-color: var(--chakra-colors-balticGray-100);
}

.emotion-12:focus,
.emotion-12[data-focus] {
  background-color: var(--chakra-colors-balticGray-200);
  box-shadow: var(--chakra-shadows-none);
}

.emotion-12:focus-visible,
.emotion-12[data-focus-visible] {
  box-shadow: var(--chakra-shadows-none);
}

.emotion-20 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--chakra-space-5);
}

.emotion-22 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  position: relative;
  white-space: nowrap;
  vertical-align: middle;
  outline: 2px solid transparent;
  outline-offset: 2px;
  line-height: 1.2;
  border-radius: var(--chakra-radii-base);
  font-weight: var(--chakra-fontWeights-medium);
  transition-property: var(--chakra-transition-property-common);
  transition-duration: var(--chakra-transition-duration-normal);
  height: var(--chakra-sizes-10);
  min-width: var(--chakra-sizes-10);
  font-size: var(--chakra-fontSizes-md);
  -webkit-padding-start: var(--chakra-space-4);
  padding-inline-start: var(--chakra-space-4);
  -webkit-padding-end: var(--chakra-space-4);
  padding-inline-end: var(--chakra-space-4);
  border: 1px solid;
  border-color: var(--chakra-colors-celloBlue-500);
  background: var(--chakra-colors-celloBlue-500);
  color: var(--chakra-colors-white);
  text-transform: uppercase;
  margin-top: var(--chakra-space-3);
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
}

.emotion-22:focus-visible,
.emotion-22[data-focus-visible] {
  box-shadow: var(--chakra-shadows-outline);
}

.emotion-22:disabled,
.emotion-22[disabled],
.emotion-22[aria-disabled=true],
.emotion-22[data-disabled] {
  opacity: 0.4;
  cursor: not-allowed;
  box-shadow: var(--chakra-shadows-none);
  pointer-events: none;
}

.emotion-22:hover,
.emotion-22[data-hover] {
  border-color: var(--chakra-colors-celloBlue-600);
  background: var(--chakra-colors-celloBlue-600);
}

.emotion-22:hover:disabled,
.emotion-22[data-hover]:disabled,
.emotion-22:hover[disabled],
.emotion-22[data-hover][disabled],
.emotion-22:hover[aria-disabled=true],
.emotion-22[data-hover][aria-disabled=true],
.emotion-22:hover[data-disabled],
.emotion-22[data-hover][data-disabled] {
  background: initial;
}

.emotion-22:active,
.emotion-22[data-active] {
  border-color: var(--chakra-colors-celloBlue-600);
  background: var(--chakra-colors-celloBlue-600);
}

<div>
  <form
    class="emotion-0"
    data-theme="light"
  >
    <div
      class="emotion-1"
      data-theme="light"
    >
      <div
        class="chakra-stack emotion-2"
        data-theme="light"
      >
        <div
          class="chakra-stack emotion-3"
          data-theme="light"
        >
          <div
            class="emotion-4"
            data-theme="light"
          >
            <img
              alt="Cello Analytics"
              class="chakra-image emotion-5"
              data-theme="light"
              src="/logo/cello.svg"
            />
          </div>
        </div>
        <div
          class="chakra-form-control emotion-6"
          data-theme="light"
          role="group"
        >
          <label
            class="chakra-form__label emotion-7"
            data-theme="light"
            for="field-:r0:"
            id="field-:r0:-label"
          >
            Email *
          </label>
          <div
            class="chakra-form-control emotion-8"
            data-testid="ca-form-control"
            data-theme="light"
            role="group"
          >
            <div
              class="emotion-9"
              data-theme="light"
            >
              <div
                class="emotion-10"
                data-theme="light"
              >
                <div
                  class="emotion-11"
                  data-theme="light"
                >
                  <input
                    autocomplete="on"
                    class="chakra-input emotion-12"
                    data-testid="username"
                    data-theme="light"
                    id="field-:r1:"
                    name="username"
                    type="email"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="chakra-form-control emotion-6"
          data-theme="light"
          role="group"
        >
          <label
            class="chakra-form__label emotion-7"
            data-theme="light"
            for="field-:r2:"
            id="field-:r2:-label"
          >
            Password *
          </label>
          <div
            class="chakra-form-control emotion-8"
            data-testid="ca-form-control"
            data-theme="light"
            role="group"
          >
            <div
              class="emotion-9"
              data-theme="light"
            >
              <div
                class="emotion-10"
                data-theme="light"
              >
                <div
                  class="emotion-11"
                  data-theme="light"
                >
                  <input
                    autocomplete="off"
                    class="chakra-input emotion-12"
                    data-testid="password"
                    data-theme="light"
                    id="field-:r3:"
                    name="password"
                    type="password"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="chakra-stack emotion-20"
          data-theme="light"
        >
          <a
            class="emotion-0"
            data-theme="light"
            href="/forgot-password"
            variant="link"
          >
            Forgot password?
          </a>
          <button
            class="chakra-button emotion-22"
            data-testid="submit"
            data-theme="light"
            type="submit"
          >
            SIGN IN
          </button>
        </div>
      </div>
    </div>
  </form>
  <span
    hidden=""
    id="__chakra_env"
  />
</div>
`;
