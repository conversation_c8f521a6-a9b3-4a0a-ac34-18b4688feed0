import * as React from "react";
import QRCode from "react-qr-code";
import { Box, Center, HStack, Input, ListItem, OrderedList, Text, chakra } from "@chakra-ui/react";
import { Controller, useForm } from "react-hook-form";
import Login, { LoginFormData } from "@/components/views/Global/Login";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import {
  ApiError,
  AuthenticationService,
  CA_Mastr_Api_v1_0_Models_Auth_LoginRequest,
  CA_Mastr_Api_v1_0_Models_Auth_LoginResponse,
  OpenAPI,
} from "@/utils/openapi";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { showErrorToast } from "@/design-system/theme/toast";
import { getAPIErrorMessage, getErrorMessageFromStatus } from "@/utils/helpers";
import { safeLocalStorage } from "@/utils/local-storage";
import CAModal from "@/design-system/molecules/CAModal";
import BlankLayout from "../../../layouts/BlankLayout";

const LoginView: React.FC = () => {
  const {
    action: { signIn },
  } = useAuthentication();

  const [openedModal, openModal] = React.useState<"QR" | "OTP" | null>(null);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null); // Reference for the timeout

  const { control, handleSubmit, watch, formState, reset, trigger } = useForm<{ otp: string }>();
  const watchOTP = watch("otp");

  const signInResponseRef = React.useRef<CA_Mastr_Api_v1_0_Models_Auth_LoginResponse>();
  const userDetails = React.useRef<{ username?: string }>();

  const clearTimeoutRef = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const startTimeout = React.useCallback(() => {
    clearTimeoutRef(); // Make sure no existing timeout is running

    timeoutRef.current = setTimeout(() => {
      showErrorToast(
        "Session Timeout",
        "Your login session has timed out. Please login again and enter code within 3 minutes.",
        {
          duration: 10000,
        }
      );
      openModal(null);
    }, 180000);
  }, [clearTimeoutRef]);

  const otpSubmitHandler = React.useCallback(
    async (data: { otp: string }) => {
      const type = signInResponseRef.current?.mfa_verified ? "verify" : "setup";
      try {
        clearTimeoutRef();

        if (type === "verify") {
          const { token } = await AuthenticationService.postVerifyOtp({
            mfa_code: data.otp,
            cognito_session: signInResponseRef.current?.cognito_session as string,
            username: userDetails.current?.username as string,
          });

          signIn(token);
        } else if (type === "setup") {
          OpenAPI.TOKEN = signInResponseRef.current?.token as string;
          await AuthenticationService.postSetupOtp(data.otp);

          signIn(signInResponseRef.current?.token);
        }
      } catch (err) {
        reset({
          otp: "",
        });
        startTimeout();
        if (err instanceof ApiError) {
          const errMsg = getErrorMessageFromStatus(err?.body?.status);
          if (errMsg === "Invalid session for the user, session is expired.") {
            showErrorToast("Session Timeout", "Your login session is invalid. Please login again.");
            return;
          }
          if (errMsg === "Invalid code received for user") {
            showErrorToast("Invalid Code", "Please try again.");
            return;
          }
          showErrorToast("Error", getAPIErrorMessage(err));
        } else {
          showErrorToast("Error", "Something went wrong. Please try again.");
        }
      }
    },
    [signIn, reset, clearTimeoutRef, startTimeout]
  );

  const signInHandler = async (data: CA_Mastr_Api_v1_0_Models_Auth_LoginRequest, cb: (hasFailed: boolean) => void) => {
    userDetails.current = { username: data.username ?? "" };
    try {
      const { token, mfa_verified, mfa_required, mfa_secret, cognito_session } = await AuthenticationService.postSignIn(
        data
      );

      signInResponseRef.current = { token, mfa_verified, mfa_required, mfa_secret, cognito_session };

      reset();
      if (mfa_verified) {
        openModal("OTP");
        startTimeout();
      } else if (mfa_required) {
        openModal("QR");
        startTimeout();
      } else {
        signIn(token);
      }

      cb(true);
    } catch (err) {
      cb(true);
      OpenAPI.TOKEN = "";
      safeLocalStorage.removeItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      showErrorToast("Error", getAPIErrorMessage(err));
    }
  };

  React.useEffect(() => {
    if (watchOTP?.length === 6) {
      trigger();
      handleSubmit(otpSubmitHandler)();
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchOTP]);

  React.useEffect(() => {
    if (!openedModal) {
      clearTimeoutRef();
    }
  }, [openedModal, clearTimeoutRef]);

  return (
    <BlankLayout title="Login">
      <Login onLogin={(data: LoginFormData, cb: (hasFailed: boolean) => void) => signInHandler(data, cb)} />

      <CAModal
        isOpen={!!openedModal}
        size="md"
        onClose={() => {
          openModal(null);
          clearTimeoutRef();
        }}
        modalHeader={openedModal === "QR" ? "2-Step Authentication Setup" : "2-Step Verification"}
      >
        {openedModal === "QR" ? (
          <Box>
            <OrderedList>
              <ListItem>
                <Text>
                  Download your preferred authenticator app from the Google Play Store or the Apple App Store to your
                  smartphone (any will work).
                </Text>
              </ListItem>
              <ListItem>
                <Text>Use your app to scan the QR code. </Text>
                <Center my={6} flexDirection="column">
                  <Box p={2} bg="white">
                    <QRCode
                      value={`otpauth://totp/${userDetails.current?.username}?secret=${signInResponseRef.current?.mfa_secret}&issuer=MASTR`}
                    />
                  </Box>
                  <Text mt={4}>Please scan the code above</Text>
                </Center>
              </ListItem>
              <ListItem>
                <Text>Enter the 6-digit code provided by your app and then click the Verify Code button.</Text>
              </ListItem>
            </OrderedList>
          </Box>
        ) : (
          <Text>To continue, verify your identity by entering the one-time code from your authenticator app.</Text>
        )}
        <Center flexDirection="column">
          <chakra.form onSubmit={handleSubmit(otpSubmitHandler)}>
            <Center my={6} flexDirection="column">
              <HStack>
                <Controller
                  control={control}
                  name="otp"
                  rules={{ required: true }}
                  render={({ field }) => (
                    <Input
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      autoComplete="one-time-code"
                      maxLength={6}
                      fontSize={24}
                      paddingY={1}
                      width={"168px"}
                      paddingRight={0}
                      placeholder="○○○○○○"
                      letterSpacing={"10px"}
                      disabled={formState.isSubmitting}
                      _placeholder={{
                        letterSpacing: "9px",
                      }}
                      {...field}
                    />
                  )}
                />
              </HStack>
            </Center>
          </chakra.form>
        </Center>
      </CAModal>
    </BlankLayout>
  );
};

export default LoginView;
