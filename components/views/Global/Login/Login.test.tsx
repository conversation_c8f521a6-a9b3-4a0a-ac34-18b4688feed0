import * as React from "react";
import { fireEvent, render, waitFor } from "@/tests/test-utils";

import Login from ".";

jest.mock("next/navigation", () => ({
  useSearchParams: () => new URLSearchParams(),
}));

describe("Login", () => {
  it("works", async () => {
    const onLogin = jest.fn();
    const { container, getByTestId } = render(<Login onLogin={onLogin} />);

    expect(container).toMatchSnapshot();

    const usernameInput = getByTestId("username") as HTMLInputElement;
    const passwordInput = getByTestId("password") as HTMLInputElement;
    const submitButton = getByTestId("submit") as HTMLButtonElement;

    expect(usernameInput.value).toStrictEqual("");
    expect(passwordInput.value).toStrictEqual("");

    fireEvent.change(usernameInput, { target: { value: "<EMAIL>" } });
    fireEvent.change(passwordInput, { target: { value: "Pa55w0rd" } });

    expect(usernameInput.value).toStrictEqual("<EMAIL>");
    expect(passwordInput.value).toStrictEqual("Pa55w0rd");

    fireEvent.submit(submitButton);

    await waitFor(() => {
      expect(onLogin).toHaveBeenCalled();
    });

    expect(onLogin).toHaveBeenCalledWith({ username: "<EMAIL>", password: "Pa55w0rd" }, expect.anything());
  });
});
