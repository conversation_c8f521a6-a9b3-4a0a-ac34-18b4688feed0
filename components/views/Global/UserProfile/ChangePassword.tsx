import React from "react";
import { Submit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { Button, chakra } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import S from "@/constants/strings";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";

interface ChangePasswordFields {
  current_password: string;
  new_password: string;
  confirm_password: string;
}
export const ChangePassword = () => {
  const {
    register,
    handleSubmit,
    reset,
    formState: { isSubmitting },
  } = useForm<ChangePasswordFields>();
  const {
    action: { changePassword },
  } = useAuthentication();

  const onSubmit: SubmitHandler<ChangePasswordFields> = async (data) => {
    if (data.new_password !== data.confirm_password) {
      return showErrorToast("Error", S.PAGES.USERPROFILE.CHANGE_PASSWORD.PASSWORDS_NOT_MATCH);
    }
    await changePassword(data, (hasError) => {
      if (hasError) return;
      showSuccessToast("Success", S.PAGES.USERPROFILE.CHANGE_PASSWORD.PASSWORD_CHANGED_SUCCESSFULLY);
      reset();
    });
  };

  return (
    <CACard maxW="19.25rem" title={S.PAGES.USERPROFILE.CHANGE_PASSWORD.TITLE}>
      <chakra.form display="flex" flexDirection="column" onSubmit={handleSubmit(onSubmit)}>
        <CAInput
          {...register("current_password", { required: true })}
          type="password"
          autoComplete="current-password"
          label={S.PAGES.USERPROFILE.CHANGE_PASSWORD.CURRENT_PASSWORD}
        />

        <CAInput
          label={S.PAGES.USERPROFILE.CHANGE_PASSWORD.PASSWORD}
          {...register("new_password", { required: true })}
          type="password"
          autoComplete="new-password"
        />

        <CAInput
          label={S.PAGES.USERPROFILE.CHANGE_PASSWORD.CONFIRM_PASSWORD}
          {...register("confirm_password", { required: true })}
          type="password"
          autoComplete="new-password"
        />

        <Button isLoading={isSubmitting} type="submit" variant="primary" marginTop={5} size="sm" marginLeft="auto">
          {S.COMMON.SAVE}
        </Button>
      </chakra.form>
    </CACard>
  );
};
