import {
  // But<PERSON>,
  Text,
  chakra,
} from "@chakra-ui/react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import CAFormControl from "@/design-system/molecules/CAFormControl";
import S from "@/constants/strings";

interface MyProfileFields {
  email: string | null;
  firstName: string | null;
  lastName: string | null;
}

export const MyProfile = () => {
  const {
    state: { userData },
  } = useAuthentication();

  const { register, handleSubmit } = useForm<MyProfileFields>({
    defaultValues: {
      firstName: userData?.user?.first_name,
      lastName: userData?.user?.last_name,
    },
  });

  const onSubmit: SubmitHandler<MyProfileFields> = () => {
    //TODO: Connect with openapi once the endpoint is ready
  };

  return (
    <CACard maxW="19.25rem" title={S.PAGES.USERPROFILE.MY_PROFILE.TITLE}>
      <chakra.form display="flex" flexDirection="column" onSubmit={handleSubmit(onSubmit)}>
        <CAFormControl
          overflow="hidden"
          display="grid"
          py={0}
          height="2.625rem"
          label={S.PAGES.USERPROFILE.MY_PROFILE.EMAIL}
        >
          <Text ml="1rem" title={userData?.user?.email ?? undefined}>
            {userData?.user?.email}
          </Text>
        </CAFormControl>

        <CAInput
          label={S.PAGES.USERPROFILE.MY_PROFILE.FIRST_NAME}
          {...register("firstName", { required: true })}
          disabled
        />

        <CAInput
          label={S.PAGES.USERPROFILE.MY_PROFILE.LAST_NAME}
          {...register("lastName", { required: true })}
          disabled
        />

        {/* <Button type="submit" size="sm" variant="primary" marginTop="2rem" marginLeft="auto">
          {S.COMMON.SAVE}
        </Button> */}
      </chakra.form>
    </CACard>
  );
};
