import * as React from "react";
import { Flex } from "@chakra-ui/react";
import { useUserProfilePage } from "@/contexts/PageContexts/UserProfilePageContext";
import CAHeading from "@/design-system/atoms/CAHeading";
import MainLayout from "../../../layouts/MainLayout";
import { MyProfile } from "./MyProfile";
import { ChangePassword } from "./ChangePassword";

const UserProfile: React.FC = () => {
  const {
    state: { title },
  } = useUserProfilePage();

  return (
    <MainLayout title={title}>
      <CAHeading px="7" minH="1.75rem" mt={4} as="h1">
        {title}
      </CAHeading>
      <Flex flexWrap="wrap" mx="1.25rem" gridGap="1.25rem" justifyContent={{ sm: "center", md: "start" }} mb="1.75rem">
        <MyProfile />
        <ChangePassword />
      </Flex>
    </MainLayout>
  );
};

export default UserProfile;
