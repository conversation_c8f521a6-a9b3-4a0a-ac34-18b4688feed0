import * as React from "react";
import { Box, Button, Center, Flex, FormControl, FormLabel, Stack, Text, VStack, chakra } from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import Router, { useRouter } from "next/router";
import { useColorModeValue } from "@chakra-ui/react";
import Logo from "@/design-system/atoms/Logo";
import S from "@/constants/strings";
import CAHeading from "@/design-system/atoms/CAHeading";
import Link from "@/design-system/atoms/Link";
import { showErrorToast } from "@/design-system/theme/toast";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import CAInput from "@/design-system/molecules/CAInput";

export type ResetPasswordFormData = {
  email: string;
  password: string;
  confirm_password: string;
  confirmation_code: string;
};

export type ResetPasswordFormDataRequest = {
  username: string;
  password: string;
  confirmation_code: string;
};

export type ResetPasswordProps = {
  onResetPassword: (data: ResetPasswordFormDataRequest, cb: (hasFailed: boolean) => void) => Promise<void>;
};

const ResetPassword: React.FC<ResetPasswordProps> = ({ onResetPassword }: ResetPasswordProps) => {
  const router = useRouter();
  const { email } = router?.query ?? {};

  const color = useColorModeValue("celloBlue.500", "gemBlue.500");

  const {
    register,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm<ResetPasswordFormData>();
  const [isSuccessPage, setSuccessPage] = React.useState(false);

  if (!email) {
    router.push("/login");
    return null;
  }

  const onSubmit = handleSubmit(async (data) => {
    if (data.password !== data.confirm_password) {
      showErrorToast("Error", "Passwords do not match!");
    } else if (email) {
      const requestData: ResetPasswordFormDataRequest = {
        password: data.password,
        username: email as string,
        confirmation_code: data.confirmation_code,
      };
      await onResetPassword(requestData, (hasError) => {
        setSuccessPage(!hasError);
      });
    }
  });

  if (isSuccessPage) {
    return (
      <Flex position="absolute" inset={0} p={8} flex={1} align={"center"} justify={"center"}>
        <Stack spacing={4} w={"full"} maxW={"sm"}>
          <VStack mb={10}>
            <Center>
              <Logo size="lg" logo="cello" />
            </Center>
          </VStack>
          <Box>
            <CAInfo
              status="success"
              title={"Password Changed"}
              description={`Your Password has been changed successfully`}
            />
          </Box>

          <Flex justifyContent={"space-around"}>
            <Button
              mt={3}
              variant="primary"
              onClick={() => {
                Router.push({ pathname: "/login", query: { email: email } });
              }}
            >
              Sign In
            </Button>
          </Flex>
        </Stack>
      </Flex>
    );
  } else
    return (
      <chakra.form onSubmit={onSubmit}>
        <Flex position="absolute" inset={0} p={8} flex={1} align={"center"} justify={"center"}>
          <Stack spacing={4} w={"full"} maxW={"sm"}>
            <VStack mb={10}>
              <Center>
                <Logo size="lg" logo="cello" />
              </Center>
            </VStack>
            <Flex flexDirection={"column"} align={"center"} justify={"center"}>
              <CAHeading as="h1" mb={2}>
                Reset Password
              </CAHeading>
              <Text mb={4}>{email}</Text>
            </Flex>

            <FormControl>
              <FormLabel color={color}>{S.PAGES.RESETPASSWORD.CONFIRMATION_CODE}</FormLabel>
              <CAInput {...register("confirmation_code", { required: true })} type="number" />
            </FormControl>
            <FormControl>
              <FormLabel color={color}>{S.PAGES.RESETPASSWORD.PASSWORD}</FormLabel>
              <CAInput {...register("password", { required: true })} type="password" autoComplete="new-password" />
            </FormControl>
            <FormControl>
              <FormLabel color={color}>{S.PAGES.RESETPASSWORD.CONFIRM_PASSWORD}</FormLabel>
              <CAInput
                {...register("confirm_password", { required: true })}
                type="password"
                autoComplete="new-password"
              />
            </FormControl>
            <Link variant="link" href={`/forgot-password?email=${email?.toString()}`}>
              Resend code
            </Link>
            <Flex justifyContent={"space-around"} width={"70%"} alignSelf={"center"}>
              <Button
                mt={3}
                variant="secondary"
                onClick={() => {
                  Router.push((Router.query.next as string) || "/login");
                }}
              >
                Cancel
              </Button>
              <Button isLoading={isSubmitting} mt={3} variant="primary" type="submit">
                Reset Password
              </Button>
            </Flex>
          </Stack>
        </Flex>
      </chakra.form>
    );
};

export default ResetPassword;
