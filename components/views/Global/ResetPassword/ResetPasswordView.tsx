import * as React from "react";
import ResetPassword, { ResetPasswordFormDataRequest } from "@/components/views/Global/ResetPassword";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import BlankLayout from "../../../layouts/BlankLayout";

const ResetPasswordView: React.FC = () => {
  const {
    action: { resetPassword },
  } = useAuthentication();

  return (
    <BlankLayout title="Reset Password">
      <ResetPassword
        onResetPassword={(data: ResetPasswordFormDataRequest, cb: (hasFailed: boolean) => void) =>
          resetPassword(data, cb)
        }
      />
    </BlankLayout>
  );
};

export default ResetPasswordView;
