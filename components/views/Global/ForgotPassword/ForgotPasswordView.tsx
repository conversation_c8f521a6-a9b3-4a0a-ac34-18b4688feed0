import * as React from "react";
import ForgotPassword, { ForgotPasswordFormData } from "@/components/views/Global/ForgotPassword";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import BlankLayout from "../../../layouts/BlankLayout";

const ForgotPasswordView: React.FC = () => {
  const {
    action: { forgotPassword },
  } = useAuthentication();

  return (
    <BlankLayout title="Forgot Password">
      <ForgotPassword onForgotPassword={(data: ForgotPasswordFormData) => forgotPassword(data.username)} />
    </BlankLayout>
  );
};

export default ForgotPasswordView;
