import * as React from "react";
import { Button, Center, Flex, FormControl, FormLabel, Stack, Text, VStack, chakra } from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import Router, { useRouter } from "next/router";
import { useColorModeValue } from "@chakra-ui/react";
import Logo from "@/design-system/atoms/Logo";
import S from "@/constants/strings";
import CAHeading from "@/design-system/atoms/CAHeading";
import CAInput from "@/design-system/molecules/CAInput";

export type ForgotPasswordFormData = {
  username: string;
};

export type ForgotPasswordProps = {
  onForgotPassword: (data: ForgotPasswordFormData) => Promise<void>;
};

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onForgotPassword }: ForgotPasswordProps) => {
  const router = useRouter();
  const { email } = router?.query ?? {};
  const {
    register,
    handleSubmit,
    formState: { isSubmitting },
  } = useForm<ForgotPasswordFormData>({
    defaultValues: { username: email?.toString() },
  });
  const onSubmit = handleSubmit(async (data) => {
    await onForgotPassword(data);
  });

  return (
    <chakra.form onSubmit={onSubmit}>
      <Flex position="absolute" inset={0} p={8} flex={1} align={"center"} justify={"center"}>
        <Stack spacing={4} w={"full"} maxW={"sm"}>
          <VStack mb={10}>
            <Center>
              <Logo size="lg" logo="cello" />
            </Center>
          </VStack>
          <Flex flexDirection={"column"} align={"center"} justify={"center"}>
            <CAHeading as="h1" mb={5}>
              Forgot Password?
            </CAHeading>
            <Text mb={5} align={"center"}>
              Enter your email and we will send you a code to reset your password.
            </Text>
          </Flex>
          <FormControl>
            <FormLabel color={useColorModeValue("celloBlue.500", "gemBlue.500")}>
              {S.PAGES.FORGOTPASSWORD.EMAIL}
            </FormLabel>
            <CAInput {...register("username", { required: true })} type="email" />
          </FormControl>
          <Flex justifyContent={"space-around"} width={"60%"} alignSelf={"center"}>
            <Button
              mt={3}
              variant="secondary"
              onClick={() => {
                Router.push((Router.query.next as string) || "/login");
              }}
            >
              Cancel
            </Button>
            <Button isLoading={isSubmitting} mt={3} variant="primary" type="submit">
              Send Code
            </Button>
          </Flex>
        </Stack>
      </Flex>
    </chakra.form>
  );
};

export default ForgotPassword;
