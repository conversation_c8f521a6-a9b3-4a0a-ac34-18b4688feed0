import * as React from "react";
import { Flex, IconButton } from "@chakra-ui/react";
import { IoSettings } from "react-icons/io5";
import MainLayout from "@/components/layouts/MainLayout";
import { QuerySearch } from "@/components/overlay/QuerySearch/QuerySearch";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import SlicerSettingsDrawer from "./SlicerSettingsDrawer";

export interface SlicerModuleProps {
  pageTitle?: string;
}

const SlicerModule: React.FC<React.PropsWithChildren<SlicerModuleProps>> = ({ pageTitle = "", children }) => {
  const {
    state: { slicerSettingsDrawerOpen, query },
    action: { toggleSlicerSettingsDrawer },
  } = useSlicerModule();

  return (
    <MainLayout
      headerMiddle={<QuerySearch />}
      headerSettings={
        <IconButton
          data-testid="toggle-settings-drawer"
          aria-label="Toggle Settings Drawer"
          icon={<CAIcon as={IoSettings} variant="secondary" />}
          variant="ghost"
          onClick={toggleSlicerSettingsDrawer}
        />
      }
      title={query.name ? `${pageTitle} | ${query.name}` : pageTitle}
      mb={{ base: "10", sm: "0" }}
    >
      <Flex direction="column">{children}</Flex>
      <SlicerSettingsDrawer onClose={toggleSlicerSettingsDrawer} isOpen={slicerSettingsDrawerOpen} />
    </MainLayout>
  );
};

export default React.memo(SlicerModule);
