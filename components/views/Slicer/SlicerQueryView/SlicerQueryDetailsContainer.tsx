import React from "react";
import { Box, Skeleton } from "@chakra-ui/react";
import {
  ApiError,
  CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables,
  CA_Mastr_Api_v1_0_Models_Slicer_LoanQueryResponse,
} from "@/utils/openapi";
import { getAvailableKeys, removeDuplicatesFromArray } from "@/utils/helpers";
import getSlicerQueryColumnData from "@/utils/grid/SlicerQueryColumnData";
import { TLoanLevelLastRunDetails } from "@/types/slicer";
import { getResponseDataFromSlicerOutput } from "@/utils/helpers/slicer";
import { getLoanLevelSlicerQueryResults, useGetUserSlicerSWR } from "@/utils/swr-hooks";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { Tokens, useSWRIncremental } from "@/hooks/useSWRIncremental";
import { useGridStoreContext } from "@/design-system/molecules/CAGrid/GridContext";
import CACard from "@/design-system/molecules/CACard";
import SlicerQueryGrid from "../SlicerQueryDetails/SlicerQueryGrid";

export interface LoanLevelRequestResponse {
  name: string;
  data: CA_Mastr_Api_v1_0_Models_Slicer_LoanQueryResponse;
}

interface SlicerQueryDetailsContainerProps {
  lastRunInfo?: TLoanLevelLastRunDetails | undefined;
  error?: ApiError | undefined;
  getFilterNames: () => string[];
  tokens: Tokens[] | undefined;
  queryType?: string;
  usePreFactorDateBalance?: boolean | null | undefined;
}

const SlicerQueryDetailsContainer: React.FC<SlicerQueryDetailsContainerProps> = ({
  lastRunInfo,
  error,
  getFilterNames,
  tokens,
  usePreFactorDateBalance: formUsePreFactorDateBalance,
  queryType: formQueryType,
}: SlicerQueryDetailsContainerProps) => {
  const {
    state: { query, lastRun, isOldRun, userSettings },
    action: { setIsTimerRunning },
  } = useSlicerModule();

  const reloadGridState = useGridStoreContext((state) => state.reloadGridState);

  const loanQueryResponse = useSWRIncremental<CA_Mastr_Api_v1_0_Models_Slicer_LoanQueryResponse>(
    tokens ?? [],
    getLoanLevelSlicerQueryResults,
    {
      timeoutMinutes: userSettings.timeoutMinutes,
      lastRun,
      onRequestEnd: () => {
        setIsTimerRunning(false);
        setTimeout(() => {
          reloadGridState();
        }, 200);
      },
    }
  );

  const { loanLevelRequestData } = lastRunInfo || {};

  const { data: userSlicerInfo } = useGetUserSlicerSWR(query?.server_id);

  const lastRunData = React.useMemo(
    () => userSlicerInfo?.user_slicer?.user_slicer_result?.user_slicer_outputs,
    [userSlicerInfo]
  );
  const allData = React.useMemo(() => {
    return !lastRun && lastRunData && isOldRun
      ? getResponseDataFromSlicerOutput(lastRunData, getFilterNames())
      : (loanQueryResponse.data?.filter((d) => !!d) as LoanLevelRequestResponse[]);
    // Not a good idea to stringify it but the data coming from useSWRIncremental causes this to be recalculated
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(loanQueryResponse.data)]);

  const apiRequestVariables = React.useMemo(() => {
    // Get api request variables from the request or user slicer info
    const apiRequestVariables = loanQueryResponse.data[0]?.name
      ? loanQueryResponse.data?.map((d) => d?.data?.api_request_variables)
      : userSlicerInfo?.user_slicer?.user_slicer_result?.user_slicer_outputs?.[0].api_request_variables;

    if (!apiRequestVariables) return [];
    const filteredApiRequestVariables = apiRequestVariables
      .flat()
      .filter(Boolean) as CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables[];

    const uniqueApiRequestVariables = removeDuplicatesFromArray(filteredApiRequestVariables, "field");

    return uniqueApiRequestVariables;
  }, [loanQueryResponse.data, userSlicerInfo?.user_slicer?.user_slicer_result?.user_slicer_outputs]);

  const { results } = React.useMemo(() => {
    const combinedSetsData = allData
      ?.map((d) => {
        const results = d?.data?.results ? JSON.parse(d.data.results) : [];
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return results.map((r: any) => ({ ...r, filter_set: d.name }));
      })
      ?.flat();

    let results = combinedSetsData ?? [];

    // Process the results
    // - value should *100 if key starts with `pct_`
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    results = results.map((result: any) => {
      return Object.fromEntries(
        Object.entries(result).map(([key, value]) => {
          let newValue = value;
          if (key.startsWith("pct_") && typeof value === "number") {
            newValue = value * 100;
          }
          return [key, newValue];
        })
      );
    });

    return { results };
  }, [allData]);

  const columns = React.useMemo(() => {
    const columnKeysJSON = JSON.stringify([...getAvailableKeys(results)]);

    const allKeysInResult: string[] = JSON.parse(columnKeysJSON);
    const sortedKeys = [
      "filter_set",
      ...allKeysInResult.filter((key) => key.includes("groupbycolumn_") || key.includes("curve_")),
      ...allKeysInResult.filter((key) => !key.includes("groupbycolumn_") && !key.includes("curve_")),
    ];

    const columns = getSlicerQueryColumnData(sortedKeys)
      // Remove column defs whose corresponding field is not available in results received from API
      .filter((c) => sortedKeys.includes(c.field as string))
      // sort keys such that slicer_groups comes first before slicer_columns
      .sort((a, b) => sortedKeys.indexOf(a.field as string) - sortedKeys.indexOf(b.field as string));

    return columns ?? [];
  }, [results]);

  const hasRun = !!lastRun || allData?.length !== 0;
  // We need loading in order to prevent column defs change to flash the entire grid and reduce re-rendering grid. Also it resolves conditional render problems
  if (!loanQueryResponse.isLoaded && hasRun) {
    return (
      <CACard minH="calc(100vh - 13rem)">
        <Skeleton mt="8" h={30} w="full" />
      </CACard>
    );
  }

  return (
    <Box h="full" className="SlicerQueryGrid">
      <SlicerQueryGrid
        columns={columns}
        allData={allData}
        results={results}
        lastRun={lastRun}
        error={error}
        isPartiallyLoaded={loanQueryResponse.isPartiallyLoaded}
        usePreFactorDateBalance={
          loanLevelRequestData?.slicer_payload?.settings?.use_pre_factor_date_balance ?? formUsePreFactorDateBalance
        }
        queryType={loanLevelRequestData?.slicer_payload?.query_type ?? formQueryType}
        apiRequestVariables={apiRequestVariables}
      />
    </Box>
  );
};

SlicerQueryDetailsContainer.displayName = "SlicerQueryDetailsContainer";
export default SlicerQueryDetailsContainer;
