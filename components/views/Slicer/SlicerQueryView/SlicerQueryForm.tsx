import * as React from "react";
import { UseFormReturn, useForm } from "react-hook-form";
import { Box, Stack, chakra, useColorModeValue } from "@chakra-ui/react";
import { HiOutlineChevronLeft } from "react-icons/hi";
import { create } from "zustand";
import { TSlicerQueryForm } from "@/types/slicer";

import CACard, { CACardRef } from "@/design-system/molecules/CACard";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import VerticalCollapsedCardPill from "@/components/helpers/VerticalCollapsedCard";
import SlicerColumn from "./SlicerQueryFormComponents/SlicerColumn";
import SlicerGroups from "./SlicerQueryFormComponents/SlicerGroups";
import SlicerFilters from "./SlicerQueryFormComponents/SlicerFilterSet";
import { SlicerQuerySettings } from "./SlicerQueryFormComponents/SlicerQuerySettings";
import { SlicerDataset } from "./SlicerQueryFormComponents/SlicerDataset";

export const useFormChanges = create<{ hasFormChanges: boolean; setHasChanges: (hasChanges: boolean) => void }>(
  (set) => ({
    hasFormChanges: false,
    setHasChanges: (hasChanges: boolean) => set({ hasFormChanges: hasChanges }),
  })
);

export type FormRefHandler = { formMethods: UseFormReturn<TSlicerQueryForm> };

interface UserSlicerQueryFormProps {
  formData: TSlicerQueryForm;
}

const UserSlicerQueryForm: React.ForwardRefRenderFunction<FormRefHandler, UserSlicerQueryFormProps> = (
  { formData },
  ref
) => {
  const {
    state: { query },
  } = useSlicerModule();

  const [formExpanded, setIsFormExpanded] = React.useState<boolean>(true);
  const queryCardRef = React.useRef<CACardRef>();

  const setHasChanges = useFormChanges((state) => state.setHasChanges);

  const formMethods = useForm<TSlicerQueryForm>({
    defaultValues: formData,
    mode: "onBlur",
    reValidateMode: "onBlur",
  });

  const {
    formState: { isDirty, dirtyFields },
  } = formMethods;
  const isDirtyAlt = !!Object.keys(dirtyFields).length;

  React.useEffect(() => {
    if (isDirtyAlt && isDirty) {
      setHasChanges(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query.key, isDirtyAlt, isDirty]);

  React.useImperativeHandle(ref, () => ({
    formMethods,
  }));

  const expandQueryCard = () => {
    setIsFormExpanded(true);
    queryCardRef.current?.expandCard?.();
  };

  return (
    <>
      <VerticalCollapsedCardPill title="Query Settings" isCollapsed={!formExpanded} expandCard={expandQueryCard} />
      <Box
        display={formExpanded ? "block" : "none"}
        width={{
          base: "100%",
          md: "500px",
        }}
        h={{ base: "100%", lg: "calc(100vh - 13rem)" }}
        overflow="auto"
        css={{
          "&::-webkit-scrollbar": {
            width: "0px",
          },
        }}
        mx={{ base: "auto", lg: "unset" }}
      >
        <chakra.form>
          <CACard
            ref={queryCardRef}
            title="Dataset"
            cardKey="slicer-query"
            cardBodyStyle={{ p: 0 }}
            headerStyle={{ height: "30px" }}
            variant="default"
            onTitleClick={() => setIsFormExpanded(false)}
            headingRight={
              <Box cursor="pointer" borderRadius="full" bg={useColorModeValue("celloBlue.50", "celloBlue.800")} p={1}>
                <HiOutlineChevronLeft />
              </Box>
            }
          >
            <Stack direction="column" spacing={0} overflow="none">
              <SlicerDataset formMethods={formMethods} />
              <SlicerFilters formMethods={formMethods} />
              <SlicerGroups formMethods={formMethods} />
              <SlicerColumn formMethods={formMethods} />
              <SlicerQuerySettings formMethods={formMethods} />
            </Stack>
          </CACard>
        </chakra.form>
      </Box>
    </>
  );
};

export default React.forwardRef(UserSlicerQueryForm);
