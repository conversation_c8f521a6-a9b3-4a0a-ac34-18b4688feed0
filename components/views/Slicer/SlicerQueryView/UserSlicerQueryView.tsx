import React from "react";
import { <PERSON>, <PERSON>, HStack, Spinner, Text } from "@chakra-ui/react";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { getAPIErrorMessage } from "@/utils/helpers";
import { useGetUserSlicerSWR } from "@/utils/swr-hooks";
import { useDefaultFormData } from "@/utils/swr-hooks/Slicer";
import { TLoanLevelLastRunDetails } from "@/types/slicer";
import { slicerQueryFormRefManager } from "../SlicerQueryDetails/slicerFormRefManager";
import SlicerTabContentBody from "./SlicerTabContentBody";

const UserSlicerQueryView: React.FC = () => {
  const {
    state: { query, groupMode },
    action: { setGroupMode },
  } = useSlicerModule();

  // We DO NOT want to refetch (which usually happens after save) so that we don't lose the loaded grid view
  const shouldFetch = query?.server_id;
  const { data } = useGetUserSlicerSWR(shouldFetch ? query?.server_id : undefined);

  const { data: defaultFormData, isLoading, error } = useDefaultFormData();
  const [lastRunInfo, setLastRunInfo] = React.useState<TLoanLevelLastRunDetails>();

  React.useEffect(() => {
    const isAdvancedMode = defaultFormData?.groups?.find((group) => group.operator && group.values);
    if (groupMode !== "ADVANCED") {
      isAdvancedMode && setGroupMode("ADVANCED");
    }
  }, [defaultFormData?.groups, groupMode, setGroupMode]);

  if (error) {
    return (
      <Center h="25rem">
        <CAAlertCard title="Error" description={getAPIErrorMessage(error) ?? "Failed to Load Query"} status="error" />
      </Center>
    );
  }

  if (!defaultFormData || isLoading) {
    return (
      <Center h="80vh" w="100vw">
        <HStack>
          <Spinner mr={4} emptyColor="celloBlue.200" />
          <Text fontSize="2xl" fontWeight="bold" textAlign="center" data-testid="loading-slicer">
            Loading Slicer...
          </Text>
        </HStack>
      </Center>
    );
  }

  return (
    <Box px={5} py={2} flexGrow={1}>
      <SlicerTabContentBody
        ref={(ref) => {
          if (ref) slicerQueryFormRefManager.storeFormRef(ref);
        }}
        formData={defaultFormData}
        slicerData={data?.user_slicer}
        lastRunInfo={lastRunInfo}
        setLastRunInfo={setLastRunInfo}
      />
    </Box>
  );
};

export default UserSlicerQueryView;
