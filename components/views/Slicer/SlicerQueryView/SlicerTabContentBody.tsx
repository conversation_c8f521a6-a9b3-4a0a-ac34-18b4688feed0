import React from "react";
import { Grid, GridItem } from "@chakra-ui/react";
import { UseFormGetValues, UseFormHandleSubmit, UseFormReset, UseFormSetValue } from "react-hook-form";
import { useSearchParams } from "next/navigation";
import { TLoanLevelLastRunDetails, TSlicerQueryForm } from "@/types/slicer";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import {
  convertSlicerServerPayloadToForm,
  getDefaultSlicerFormValues,
  prepareSlicerRequestPayload,
} from "@/utils/helpers/slicer";
import { getUuid } from "@/utils/helpers";
import { useGetLoanLevelSlicerQueryTokensSWR } from "@/utils/swr-hooks";
import { CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer } from "@/utils/openapi";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import StopWatch from "@/design-system/molecules/StopWatch";
import { showErrorToast } from "@/design-system/theme/toast";
import { GridStoreProvider } from "@/design-system/molecules/CAGrid/GridContext";
import { userGridViewServiceKey } from "@/utils/swr-hooks/UserGridView";
import SlicerHeader, { RunClickOptParams } from "../SlicerHeader";
import { SlicerStopRunningWrapper } from "../SlicerStopRunningWrapper";
import { SlicerTagSelector } from "../SlicerTagSelector/SlicerTagSelector";
import SlicerQueryDetailsContainer from "./SlicerQueryDetailsContainer";
import SlicerForm, { FormRefHandler } from "./SlicerQueryForm";

export interface TSlicerTabContentBodyRef {
  handleSubmit?: UseFormHandleSubmit<TSlicerQueryForm>;
  lastRun?: string;
  resetForm?: (payload: CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer | undefined) => void;
  setValue: UseFormSetValue<TSlicerQueryForm> | undefined;
  getValues: UseFormGetValues<TSlicerQueryForm> | undefined;
  reset?: UseFormReset<TSlicerQueryForm>;
}

interface TSlicerTabContentBodyProps {
  formData: TSlicerQueryForm;
  slicerData: CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer | undefined;
  lastRunInfo: TLoanLevelLastRunDetails | undefined;
  setLastRunInfo: React.Dispatch<React.SetStateAction<TLoanLevelLastRunDetails | undefined>>;
}

const SlicerTabContentBody: React.ForwardRefRenderFunction<TSlicerTabContentBodyRef, TSlicerTabContentBodyProps> = (
  { formData, slicerData, lastRunInfo, setLastRunInfo }: TSlicerTabContentBodyProps,
  ref
) => {
  const { replaceWithoutRendering } = useQueryParameters();
  const {
    state: {
      app,
      page,
      groupMode,
      loan_level_slicer_config,
      isTimerRunning,
      query,
      isOldRun,
      lastRun,
      runId,
      lastRunDate,
      userSettings,
    },
    action: { setIsTimerRunning, setGroupMode, run },
  } = useSlicerModule();

  const formRef = React.useRef<FormRefHandler>(null);
  const IS_NEW = !query.server_id && !isOldRun;

  const params = useSearchParams();
  const activeUserSlicerQueryId = params.get("id");

  const { data: tokenData, error: activityError } = useGetLoanLevelSlicerQueryTokensSWR(
    lastRunInfo?.loanLevelRequestData,
    isOldRun ? runId : undefined
  );

  React.useImperativeHandle(ref, () => ({
    lastRun,
    handleSubmit: formRef.current?.formMethods.handleSubmit,
    resetForm: resetHandler,
    reset: formRef.current?.formMethods.reset,
    setValue: formRef.current?.formMethods.setValue,
    getValues: formRef.current?.formMethods.getValues,
  }));

  const resetHandler = (payload: CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer | undefined) => {
    if (IS_NEW && !payload) {
      formRef.current?.formMethods.reset({ ...getDefaultSlicerFormValues({ loan_level_slicer_config }) });
    } else {
      const dataToReset = payload?.user_slicer_input ?? slicerData?.user_slicer_input;

      if (!dataToReset) {
        formRef.current?.formMethods.reset(formData);
        return;
      }

      const resetDataPayload = convertSlicerServerPayloadToForm(
        dataToReset,
        payload?.user_slicer_name ?? slicerData?.user_slicer_name ?? "",
        loan_level_slicer_config
      );

      formRef.current?.formMethods.reset(resetDataPayload);

      const isAdvancedMode = resetDataPayload?.groups?.find((group) => group.operator && group.values);
      setGroupMode(isAdvancedMode ? "ADVANCED" : "BASIC");
    }
  };

  const onRunQuery = (opts: RunClickOptParams) => {
    const { no_cache, runId, isOldRun } = opts || {};

    formRef.current?.formMethods.handleSubmit?.((data) => {
      if (!data.filter_sets?.length) return;
      if (!data.settings?.model_versions?.length && data.columns?.some((col) => col.dimension?.includes("TRACKING_"))) {
        showErrorToast("Error", "Please select prepay model version.");
        return;
      }

      const { payload: requestData, error } = prepareSlicerRequestPayload(data, loan_level_slicer_config, groupMode);

      if (error) {
        showErrorToast("Error", error);
        return;
      }

      if (!requestData) {
        return;
      } else {
        setIsTimerRunning(true);

        const newRunId = runId ?? getUuid();
        run({
          isOldRun,
          lastRun: new Date().toISOString(),
          runId: newRunId,
        });
        setLastRunInfo({
          loanLevelRequestData: {
            app,
            page,
            run_id: newRunId,
            no_cache: !!no_cache,
            user_slicer_name: data.user_slicer_name || undefined,
            user_slicer_id: query?.server_id,
            slicer_payload: requestData,
            timeout_minutes: userSettings.timeoutMinutes,
          },
        });

        replaceWithoutRendering({
          id: query?.server_id?.toString(),
          run_id: newRunId,
        });
      }
    })();
  };

  const getFilterNames = React.useCallback(() => {
    const filters = formRef.current?.formMethods?.getValues("filter_sets") ?? [];
    return filters.map((filter) => filter.filter_set_name ?? "-");
  }, []);

  return (
    <Grid
      templateAreas={{
        base: `"header"
             "form"
             "grid"`,
        lg: `"header header"
                    "form grid"`,
      }}
      templateColumns={{ base: "1fr", sm: "1fr", lg: "auto 1fr" }}
      templateRows={{ base: "auto", sm: "auto", lg: "auto 1fr" }}
      gap="3"
      mb="5"
    >
      <GridStoreProvider
        gridType={
          activeUserSlicerQueryId
            ? `${userGridViewServiceKey.getSlicerGridKey(activeUserSlicerQueryId ?? query?.key)}`
            : undefined
        }
      >
        <GridItem area={"header"}>
          <SlicerHeader
            onRunClick={onRunQuery}
            tokens={tokenData?.tokens}
            runInfo={{
              id: runId ?? undefined,
              date: lastRunDate ?? lastRun ?? undefined,
              isOldRun: isOldRun,
            }}
            stopWatch={<StopWatch startDate={lastRun} isTimerRunning={isTimerRunning} />}
            stopRunning={<SlicerStopRunningWrapper lastRunInfo={lastRunInfo} setLastRunInfo={setLastRunInfo} />}
            tagSelectorEl={<SlicerTagSelector {...slicerData} />}
          />
        </GridItem>
        <GridItem
          ml={{
            base: "0",
            lg: "2",
          }}
          area="grid"
          h={{ base: "100%", lg: "calc(100vh - 13rem)" }}
          overflow={"hidden"}
        >
          <SlicerQueryDetailsContainer
            lastRunInfo={lastRunInfo}
            error={activityError}
            getFilterNames={getFilterNames}
            tokens={tokenData?.tokens}
            usePreFactorDateBalance={formData?.settings?.use_pre_factor_date_balance}
            queryType={formData.query_type}
          />
        </GridItem>
      </GridStoreProvider>
      <GridItem area="form">
        <SlicerForm ref={formRef} formData={formData} />
      </GridItem>
    </Grid>
  );
};

export default React.forwardRef(SlicerTabContentBody);
