import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>uList, useDisclosure } from "@chakra-ui/react";
import { IoEllipsisVerticalOutline, IoTrashOutline } from "react-icons/io5";
import { MdOutlineEditNote } from "react-icons/md";
import CAIcon from "@/design-system/atoms/CAIcon";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute,
  CA_Mastr_Models_v1_0_Models_QueryType,
} from "@/utils/openapi";
import { menuItemIconProps } from "../FilterSeries/FilterCardPrimaryMenu";
import SlicerColumnSelectionModal from "../SlicerColumnSelection/SlicerColumnSelectionModal";

interface SlicerColumnsProps {
  onColumnsClick: () => void;
  onDeleteAllClick: () => void;
  onColumnSelectionModalClose: (columns: string[]) => void;
  selectedColumns?: string[];
  columnList: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute[];
  queryType: CA_Mastr_Models_v1_0_Models_QueryType;
}

export const SlicerColumnsMenuActions = ({
  selectedColumns,
  columnList,
  onColumnSelectionModalClose,
  onDeleteAllClick,
  onColumnsClick,
  queryType,
}: SlicerColumnsProps) => {
  const { isOpen, onToggle } = useDisclosure();

  return (
    <>
      <Menu placement="right-start" isLazy closeOnSelect>
        <MenuButton
          as={IconButton}
          aria-label="query-options-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
        />
        <MenuList zIndex="popover" minW="10rem">
          <MenuItem
            icon={<CAIcon as={MdOutlineEditNote} {...menuItemIconProps} />}
            onClick={(e) => {
              e.stopPropagation();
              onColumnsClick();
              onToggle();
            }}
          >
            Modify
          </MenuItem>
          <MenuItem
            icon={<CAIcon as={IoTrashOutline} {...menuItemIconProps} />}
            onClick={(e) => {
              e.stopPropagation();
              onDeleteAllClick();
            }}
          >
            Delete All
          </MenuItem>
        </MenuList>
      </Menu>
      {isOpen && (
        <SlicerColumnSelectionModal
          isOpen={isOpen}
          toggleIsOpen={onToggle}
          onModalClose={onColumnSelectionModalClose}
          selectedColumns={selectedColumns}
          columnList={columnList}
          queryType={queryType}
        />
      )}
    </>
  );
};
