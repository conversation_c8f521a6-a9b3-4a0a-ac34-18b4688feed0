import React from "react";
import {
  Box,
  Checkbox,
  CheckboxGroup,
  Flex,
  Grid,
  GridItem,
  Text,
  Tooltip,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { FieldArrayWithId } from "react-hook-form";
import CAModal from "@/design-system/molecules/CAModal";
import CASearch from "@/design-system/molecules/CASearch";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { getCategoryLevelUniqueName, normalizeSlicerConfigOptions } from "@/utils/helpers/slicer";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute,
  CA_Mastr_Models_v1_0_Models_Operator,
  CA_Mastr_Models_v1_0_Models_QueryType,
} from "@/utils/openapi";
import { showWarningToast } from "@/design-system/theme/toast";
import { ConfigLevelPrecedence } from "@/constants/slicer";
import { TSlicerQueryForm } from "@/types/slicer";
import { MAX_GROUPS_ALLOWED } from "./SlicerGroups";
import { SelectionTooltip } from "./SelectionTooltip";

export type GroupSelectionOptionType = {
  label: string;
  value?: string;
} & CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute;

interface SlicerGroupSelectionModalProps {
  onModalClose: (normalizedSelection?: string[] | undefined, selection?: Record<string, string[]>) => void;
  groups: FieldArrayWithId<TSlicerQueryForm, "groups", "id">[];
  selectedAgencies: string[];
  queryType: CA_Mastr_Models_v1_0_Models_QueryType;
}

const SlicerGroupSelectionModal = ({
  onModalClose,
  groups,
  selectedAgencies,
  queryType,
}: SlicerGroupSelectionModalProps) => {
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();

  const [searchText, setSearchText] = React.useState("");
  const [checkboxSelections, updateSelection] = React.useState<Record<string, string[]>>(
    () => getDefaultSelections(groups, loan_level_slicer_config?.group_by ?? []) ?? {}
  );
  const checkboxSelectionsArr = Object.values(checkboxSelections).flat();
  const hasExceededSelectionLength = checkboxSelectionsArr.length >= MAX_GROUPS_ALLOWED;

  //   DATA
  const filteredGroups = React.useMemo(() => {
    return normalizeGroupsForSelectionModal(loan_level_slicer_config?.group_by ?? [], selectedAgencies, queryType);
  }, [loan_level_slicer_config?.group_by, selectedAgencies, queryType]);

  const displayCategory = (category: string, level: string) => {
    const matchesAnyChild = filteredGroups[level]
      ?.find((c) => c.category === category)
      ?.options?.find((option) => option?.display_name?.toLowerCase().includes(searchText.toLowerCase()));
    return matchesAnyChild ? "block" : "none";
  };

  const displayField = (groupLabel: string) => {
    if (!searchText) return "inherit";
    return groupLabel.toLowerCase().includes(searchText.toLowerCase()) ? "inherit" : "none";
  };

  const selectGroupHandler = (groupName: string, level: string) => {
    const allChildren = filteredGroups[level].find((d) => d.category === groupName)?.options ?? [];
    const allChecked = checkboxSelections?.[groupName]?.length === allChildren.length;
    if (allChecked) {
      updateSelection({ ...checkboxSelections, [groupName]: [] });
    } else {
      const values = allChildren.map((c) => c.value).filter(Boolean) as string[];
      const updatedSelections = { ...checkboxSelections, [groupName]: values };

      if (!validateGroupLength(updatedSelections)) return;

      updateSelection(updatedSelections);
    }
  };

  const validateGroupLength = (updatedSelection?: Record<string, string[]>) => {
    const totalSelections = updatedSelection
      ? Object.values(updatedSelection).flat().length
      : Object.values(checkboxSelections).flat().length;
    if (totalSelections >= MAX_GROUPS_ALLOWED) {
      showWarningToast("Selection limit exceeded", `You can not add/select more than ${MAX_GROUPS_ALLOWED} groups`);
      return false;
    }

    return true;
  };

  const submitSelection = () => {
    const normalizedSelection = Object.values(checkboxSelections).flat();
    onModalClose(normalizedSelection, checkboxSelections);
  };

  return (
    <CAModal
      size={{ base: "auto", md: "3xl" }}
      modalHeader={`Groups (Max ${MAX_GROUPS_ALLOWED})`}
      showCloseIcon
      isOpen={true}
      onClose={onModalClose}
      modalBodyProps={{
        overflowX: "auto",
      }}
      headerStyle={{
        color: useColorModeValue("celloBlue.500", "turmericRoot.500"),
        textTransform: "uppercase",
        py: "1rem",
        px: "1.7rem",
      }}
      modalBackground={useColorModeValue("celloBlue.50", "celloBlue.900")}
      modalFooter={
        !Object.keys(filteredGroups).length
          ? undefined
          : [
              {
                title: "Cancel",
                variant: "secondary",
                onClick: onModalClose,
              },
              {
                title: "Apply",
                variant: "primary",
                onClick: submitSelection,
              },
            ]
      }
    >
      <Box p="1.7rem" background={useColorModeValue("white", "celloBlue.1000")}>
        {!Object.keys(filteredGroups).length ? (
          <Box>
            <Text fontWeight="medium" fontSize="lg">
              Please select agency first under Filters.
            </Text>
          </Box>
        ) : (
          <>
            <Box maxW={{ base: "13rem", md: "20rem" }}>
              <CASearch
                name="search-groups"
                placeholder="Quick Search"
                onChange={({ target: { value } }) => setSearchText(value.trim())}
              />
            </Box>
            <Grid
              templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(2, 1fr)" }}
              mt={2}
              gridGap={12}
              overflow="auto"
            >
              {Object.keys(filteredGroups).map((level) => (
                <GridItem key={level} mt={6}>
                  <Box p={2}>
                    <Text variant="tableLeft" fontSize="xl" fontWeight="bold">
                      {level}
                    </Text>
                  </Box>
                  <Flex
                    flexDirection="column"
                    flexWrap="wrap"
                    maxH="auto"
                    columnGap={6}
                    pt={3}
                    alignContent="flex-start"
                  >
                    {filteredGroups[level].map(({ category, options }) => {
                      const matchesCategory = category.toLowerCase().includes(searchText.toLowerCase());

                      return (
                        <Box
                          key={category}
                          mb={3.5}
                          display={matchesCategory ? "block" : displayCategory(category as string, level)}
                        >
                          <Checkbox
                            isChecked={checkboxSelections?.[category]?.length === options?.length}
                            isIndeterminate={
                              checkboxSelections?.[category] &&
                              checkboxSelections?.[category]?.length !== options.length &&
                              checkboxSelections?.[category]?.length !== 0
                            }
                            isDisabled={hasExceededSelectionLength}
                            onChange={() => selectGroupHandler(category, level)}
                          >
                            <Text variant="tableHead" whiteSpace="nowrap">
                              {category.replace(/(--([\w ])*)/g, "").trim()}
                            </Text>
                          </Checkbox>
                          <Box pl={6} mt={2.5}>
                            <CheckboxGroup
                              value={checkboxSelections?.[category]}
                              onChange={(e) => {
                                const isDeselect = e.length < checkboxSelections[category]?.length;
                                if (!isDeselect && !validateGroupLength()) return;
                                updateSelection({ ...checkboxSelections, [category]: e.map(String) });
                              }}
                            >
                              <VStack alignItems="flex-start">
                                {options.map((c) => (
                                  <Checkbox
                                    key={c.value}
                                    value={c.value}
                                    display={matchesCategory ? "inherit" : displayField(c.display_name as string)}
                                    isDisabled={
                                      !!c.value &&
                                      hasExceededSelectionLength &&
                                      !checkboxSelectionsArr.includes(c.value)
                                    }
                                  >
                                    <Tooltip
                                      label={
                                        <SelectionTooltip agenciesSupported={c.agencies} selections={c.selections} />
                                      }
                                    >
                                      <Text display="inline" variant="default" whiteSpace="nowrap">
                                        {c.label}
                                      </Text>
                                    </Tooltip>
                                  </Checkbox>
                                ))}
                              </VStack>
                            </CheckboxGroup>
                          </Box>
                        </Box>
                      );
                    })}
                  </Flex>
                </GridItem>
              ))}
            </Grid>
          </>
        )}
      </Box>
    </CAModal>
  );
};

export default SlicerGroupSelectionModal;

// HELPERS
const normalizeGroupsForSelectionModal = (
  groups: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute[],
  selectedAgencies: string[],
  queryType: CA_Mastr_Models_v1_0_Models_QueryType
) => {
  const groupedByLevel: Record<string, CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute[]> = {};

  for (let i = 0; i < groups.length; i++) {
    const level = groups[i].level;

    if (!level) continue;

    if (groupedByLevel[level]) {
      groupedByLevel[level].push(groups[i]);
    } else {
      groupedByLevel[level] = [groups[i]];
    }
  }

  const normalizedGroups: Record<
    string,
    {
      category: string;
      options: GroupSelectionOptionType[];
    }[]
  > = {};
  Object.keys(groupedByLevel)
    .sort((a, b) => ConfigLevelPrecedence.indexOf(a) - ConfigLevelPrecedence.indexOf(b))
    .forEach((level) => {
      const groupedByCategoryGroups = normalizeSlicerConfigOptions(
        groupedByLevel[level] ?? [],
        selectedAgencies,
        CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
        queryType
      ).map((option) => {
        return {
          category: getCategoryLevelUniqueName(option.label, level),
          options: option.options as GroupSelectionOptionType[],
        };
      });

      if (groupedByCategoryGroups.length) {
        normalizedGroups[level] = groupedByCategoryGroups;
      }
    });

  return normalizedGroups;
};

const getDefaultSelections = (
  groups: FieldArrayWithId<TSlicerQueryForm, "groups", "id">[],
  groupsConfig: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute[] | undefined
) => {
  const selections = groups.map((condition) => condition.dimension);

  // bind categories
  const selectionsWithCategory = selections
    .map((selection) => {
      const config = groupsConfig?.find((group) => group.dimension === selection);
      return {
        value: selection,
        category: config?.category && config?.level ? getCategoryLevelUniqueName(config.category, config?.level) : "",
      };
    })
    .filter((config) => config.value);

  return selectionsWithCategory.reduce<Record<string, string[]>>((acc, item) => {
    if (item.category) {
      if (acc[item.category]) {
        acc[item.category] = [...acc[item.category as string], item.value as string];
      } else {
        acc[item.category] = [item.value as string];
      }
    }

    return acc;
  }, {});
};
