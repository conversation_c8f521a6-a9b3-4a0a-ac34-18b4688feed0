import React from "react";
import { Box, HStack, Icon, useColorModeValue } from "@chakra-ui/react";
import { UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { DragEndEvent } from "@dnd-kit/core";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import CAInput from "@/design-system/molecules/CAInput";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";
import { SlicerDataSetOptions, TSlicerQueryForm } from "@/types/slicer";
import CACard, { CACardRef } from "@/design-system/molecules/CACard";
import { getDefaultOperatorAndValueForFilters } from "@/utils/helpers/slicer";
import { CADragDrop, CADragDropContainer } from "@/design-system/molecules/CADragDrop";
import { ReorderIcon } from "@/design-system/icons";
import { slicerSecondaryCardCommonProps } from "../../SlicerQueryDetails/helpers/SlicerChartCardPropsGetter";
import { SlicerFilterSelectionModal } from "./SlicerFilterSelection";
import FilterConditions from "./SlicerFilterConditions";
import { FilterCardPrimaryMenu } from "./FilterSeries/FilterCardPrimaryMenu";
import { FilterSeriesMenuActions } from "./FilterSeries/FilterSeriesMenuActions";

interface SlicerFilterSetProps {
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

const SlicerFilters = ({ formMethods }: SlicerFilterSetProps) => {
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();

  const { control, getValues } = formMethods;

  const {
    fields: filterSets,
    append,
    remove,
    update,
    move,
  } = useFieldArray({
    control,
    name: "filter_sets",
  });

  const [filterSelectionActiveIndex, setFilterSelectionActiveIndex] = React.useState<number | null>(null);

  const cardRef = React.useRef<CACardRef>(null);

  React.useEffect(() => {
    if (filterSets.length === 0) {
      addNewSeriesHandler();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterSets, append, getValues]);

  // Handlers
  const addNewSeriesHandler = () => {
    const dataset = getValues("dataset_type");
    const filterSeries = getValues("filter_sets") ?? [];

    append({
      filter_set_name: `Filters ${filterSeries.length + 1}`,
      enabled: true,
      conditions: [
        {
          enabled: true,
          dimension: "AGENCY",
          operator:
            dataset === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL
              ? CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO
              : CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
          list_values: [""],
          values: dataset === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL ? ["GPL"] : [""],
        },
      ],
    });
    cardRef.current?.expandCard?.();
  };

  const duplicateSeries = (index: number) => {
    const series = getValues(`filter_sets.${index}`);
    append({
      // using json to get rid of reference issues
      ...JSON.parse(JSON.stringify(series)),
      filter_set_name: `Copy of ${series.filter_set_name}`,
    });
  };

  const onSelectionModalClose = (
    normalizedSelections: string[] | undefined,
    selections: Record<string, string[]> | undefined
  ) => {
    if (!normalizedSelections || !selections) {
      setFilterSelectionActiveIndex(null);
      return;
    }

    const filterSeries = getValues("filter_sets");

    // prepare the entire object
    if (filterSelectionActiveIndex !== null) {
      const currentFilterSeries = filterSeries?.[filterSelectionActiveIndex] ?? {};
      const currentConditions = currentFilterSeries.conditions ?? [];
      const agencyRow = currentFilterSeries.conditions?.[0] ?? {};

      const updatedConditions = normalizedSelections.map((selection) => {
        const filterCondition = currentConditions.find((c) => c.dimension === selection);
        if (filterCondition) {
          return filterCondition;
        }
        return {
          dimension: selection,
          enabled: true,
          ...getDefaultOperatorAndValueForFilters(loan_level_slicer_config?.filters, selection),
        };
      });

      update(filterSelectionActiveIndex, {
        ...currentFilterSeries,
        conditions: [
          agencyRow,
          ...updatedConditions,
          { dimension: "", operator: undefined, values: [], enabled: true, list_values: [] },
        ],
      });
    }
    setFilterSelectionActiveIndex(null);
  };

  const dragColumnHandler = (event: DragEndEvent) => {
    const source = event.active.data.current?.sortable.index;
    const destination = event.over?.data.current?.sortable.index;
    if (!destination?.toString()) {
      return;
    }

    move(source, destination);
  };

  return (
    <>
      <CACard
        ref={cardRef}
        title="Filters"
        headingRight={<FilterCardPrimaryMenu onAddClick={addNewSeriesHandler} onDeleteClick={remove} />}
        {...slicerSecondaryCardCommonProps}
        cardBodyStyle={{ p: 0, pb: 4 }}
      >
        <CADragDropContainer onDragEnd={dragColumnHandler} items={filterSets.map((el) => el.id)}>
          {filterSets.map((fs, index) => {
            return (
              <CADragDrop
                key={fs.id}
                id={fs?.id?.toString()}
                activatorElement={
                  <Box
                    w="1.25rem"
                    mr={0.5}
                    mt={2}
                    cursor="move"
                    visibility="visible"
                    _groupHover={{ visibility: "visible" }}
                  >
                    <Icon as={ReorderIcon} />
                  </Box>
                }
                boxStyle={{ alignItems: "flex-start", gap: 0 }}
              >
                <FilterSetRow
                  index={index}
                  formMethods={formMethods}
                  remove={remove}
                  duplicateSeries={duplicateSeries}
                  filterSetsLength={filterSets.length}
                  setFilterSelectionActiveIndex={setFilterSelectionActiveIndex}
                />
              </CADragDrop>
            );
          })}
        </CADragDropContainer>
      </CACard>

      {filterSelectionActiveIndex !== null && (
        <SlicerFilterSelectionModal
          filterSelectionActiveIndex={filterSelectionActiveIndex}
          filterSets={getValues("filter_sets") ?? []}
          onModalClose={onSelectionModalClose}
          queryType={getValues("query_type")}
        />
      )}
    </>
  );
};

const FilterSetRow = ({
  index,
  formMethods,
  remove,
  duplicateSeries,
  setFilterSelectionActiveIndex,
  filterSetsLength,
}: {
  index: number;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
  remove: (index: number) => void;
  duplicateSeries: (index: number) => void;
  setFilterSelectionActiveIndex: (index: number) => void;
  filterSetsLength: number;
}) => {
  const {
    register,
    formState: { errors },
    getValues,
    setError,
    clearErrors,
  } = formMethods;

  const isEnabled = useWatch({ control: formMethods.control, name: `filter_sets.${index}.enabled` }) ?? [];
  const isDisabled = !isEnabled;

  const bgColor = useColorModeValue("white", "celloBlue.1000");
  return (
    <Box pb={filterSetsLength - 1 === index ? 0 : "0.875rem"} w="full">
      <HStack pr="0.5rem" spacing={0} alignItems="center" role="group" bg={bgColor}>
        <Box w="1rem" mx={0.5}>
          <CASwitchInput {...formMethods.register(`filter_sets.${index}.enabled`)} />
        </Box>
        <Box
          ml={0.8}
          flex={1}
          pl="0.8rem"
          pr="0.5rem"
          opacity={isDisabled ? 0.5 : 1}
          pointerEvents={isDisabled ? "none" : "auto"}
        >
          <CAInput
            info={{
              show: errors?.["filter_sets"]?.[index]?.filter_set_name?.type === "Duplicate",
              msg1: "Error",
              msg2: errors?.["filter_sets"]?.[index]?.filter_set_name?.message,
            }}
            {...register(`filter_sets.${index}.filter_set_name`, {
              required: true,
              validate: (value) => {
                const names = getValues("filter_sets")?.filter((set) => set.filter_set_name === value) ?? [];
                if (names.length > 1) {
                  setError(`filter_sets.${index}.filter_set_name`, {
                    type: "Duplicate",
                    message: `Name "${value}" already exists.`,
                  });
                  return false;
                }
                if (errors?.["filter_sets"]?.[index]?.filter_set_name?.type === "Duplicate") {
                  clearErrors(`filter_sets.${index}.filter_set_name`);
                }
                return true;
              },
            })}
          />
        </Box>
        <FilterSeriesMenuActions
          isDisabled={isDisabled}
          onFiltersClick={() => setFilterSelectionActiveIndex(index)}
          onDuplicateClick={() => duplicateSeries(index)}
          onDeleteClick={() => remove(index)}
        />
      </HStack>
      <Box ml="-0.68rem" opacity={isDisabled ? 0.5 : 1} pointerEvents={isDisabled ? "none" : "auto"} bg={bgColor}>
        <FilterConditions seriesIndex={index} formMethods={formMethods} filterSetEnabled={!isDisabled} />
      </Box>
    </Box>
  );
};
export default SlicerFilters;
