import React from "react";
import { <PERSON>, HStack, Icon, I<PERSON><PERSON>utton } from "@chakra-ui/react";
import { IoTrashOutline } from "react-icons/io5";
import { UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { DragEndEvent } from "@dnd-kit/core";
import { ReorderIcon } from "@/design-system/icons";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import CACard from "@/design-system/molecules/CACard";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getDefaultColumns } from "@/utils/helpers/slicer";
import { CADragDrop, CADragDropContainer } from "@/design-system/molecules/CADragDrop";
import { TSlicerQueryForm } from "@/types/slicer";
import CACheckboxInput from "@/design-system/molecules/CACheckboxInput";
import { slicerSecondaryCardCommonProps } from "../../SlicerQueryDetails/helpers/SlicerChartCardPropsGetter";
import { SlicerColumnsMenuActions } from "./SlicerColumns/SlicerColumnsMenuActions";
import SlicerColumnRowInput from "./SlicerColumnRowInput";

interface SlicerColumnProps {
  formMethods: UseFormReturn<TSlicerQueryForm>;
}

const SlicerColumn = ({ formMethods }: SlicerColumnProps) => {
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();

  const { control, getValues, setValue } = formMethods;
  const watchColumns = useWatch({ control, name: "columns" });

  const {
    fields: columns,
    append,
    remove,
    move,
    replace,
  } = useFieldArray({
    control,
    name: "columns",
  });
  const [selectedColumns, setSelectedColumns] = React.useState<string[]>([]);

  const watchGroups = getValues("groups") ?? [];
  const groupIncludesTicker = watchGroups.find((group) => group.dimension === "TICKER");

  const columnsOptions = loan_level_slicer_config?.columns ?? [];

  //   validators
  const canEnableOrDisable = (index: number) => {
    const allColumns = getValues("columns") ?? [];
    const lastRow = allColumns.at(-1);
    const isLastRowEmpty = !lastRow?.dimension;
    return !(index === columns.length - 1 && isLastRowEmpty);
  };

  const canDrag = (index: number) => {
    const allColumns = getValues("columns") ?? [];
    const lastRow = allColumns.at(-1);
    const isLastRowEmpty = !lastRow?.dimension;
    return !(index === columns.length - 1 && isLastRowEmpty);
  };

  const isDeleteDisabled = (index: number) => {
    const allColumns = getValues("columns") ?? [];
    const lastRow = allColumns.at(-1);
    const isLastRowEmpty = !lastRow?.dimension;
    return columns.length <= 1 || (index === columns.length - 1 && isLastRowEmpty);
  };

  //   Handlers
  const appendNewColumnHandler = React.useCallback(() => {
    if (columns.length !== columnsOptions.length) {
      append({ dimension: "", values: [], enabled: true });
    }
  }, [append, columns.length, columnsOptions.length]);

  const dragColumnHandler = (event: DragEndEvent) => {
    const source = event.active.data.current?.sortable.index;
    const destination = event.over?.data.current?.sortable.index;
    if (!destination?.toString()) {
      return;
    }

    const allColumns = getValues("columns") ?? [];
    const lastRow = allColumns.at(-1);
    const isLastRowEmpty = !lastRow?.dimension;
    if (!(destination === columns.length - 1 && isLastRowEmpty)) {
      move(source, destination);
    }
  };

  const defaultColumns = getDefaultColumns({
    hasTicker: !!groupIncludesTicker,
    queryType: getValues("query_type"),
  });

  const applyColumnSelectionHandler = (columns: string[]) => {
    if (columns.length === 0) {
      setValue("columns", [{ dimension: "", values: [], enabled: true }]);
      return;
    }
    const currentValues = getValues("columns")?.filter((c) => !!c.dimension) ?? [];
    const updatedColumns = columns.map((dimension) => ({
      enabled: currentValues?.find((v) => v.dimension === dimension)?.enabled ?? true,
      dimension,
      values:
        currentValues?.find((v) => v.dimension === dimension)?.values ??
        columnsOptions?.find((option) => option.dimension === dimension)?.default_selections ??
        [],
    }));
    replace([...updatedColumns]);
  };

  const handleColumnSelectionClick = () => {
    const selectedColumns =
      (getValues("columns")
        ?.map((c) => c.dimension)
        ?.filter(Boolean) as string[]) ?? [];
    setSelectedColumns(selectedColumns);
  };

  return (
    <>
      <CACard
        title="columns"
        headingRight={
          <SlicerColumnsMenuActions
            onColumnsClick={handleColumnSelectionClick}
            onDeleteAllClick={remove}
            onColumnSelectionModalClose={applyColumnSelectionHandler}
            selectedColumns={selectedColumns}
            columnList={columnsOptions}
            queryType={getValues("query_type")}
          />
        }
        {...slicerSecondaryCardCommonProps}
        cardBodyStyle={{ p: 0, pb: 4 }}
      >
        {defaultColumns?.map((col) => (
          <Box key={col.id}>
            <HStack ml="2.8rem" mr="2.5rem" role="group">
              <CASelectDropdown name={col.value} value={col.displayValue} options={defaultColumns} readOnly />
            </HStack>
          </Box>
        ))}
        <CADragDropContainer onDragEnd={dragColumnHandler} items={columns.map((el) => el.id)}>
          {columns.map((column, index) => {
            const isEnabled = watchColumns?.[index]?.enabled ?? true;

            return (
              <HStack key={column.id} role="group" mr="0.5rem">
                <CADragDrop
                  id={column.id}
                  activatorElement={
                    <Box w="0.8rem" cursor="move">
                      <Icon as={ReorderIcon} />
                    </Box>
                  }
                  activatorElemStyle={{
                    visibility: "hidden",
                    _groupHover: { visibility: canDrag(index) ? "visible" : "hidden" },
                    mt: "2.5",
                  }}
                  boxStyle={{
                    display: "flex",
                    w: "full",
                    alignItems: "start",
                  }}
                  disabled={!canDrag(index)}
                >
                  <Box
                    w="1rem"
                    visibility={isEnabled ? "hidden" : "visible"}
                    _groupHover={{ visibility: canEnableOrDisable(index) ? "visible" : "hidden" }}
                    mt="2"
                  >
                    <CACheckboxInput {...formMethods.register(`columns.${index}.enabled`)} variant="small" hideLabel />
                  </Box>
                  <Box w="full" opacity={isEnabled ? "1" : "0.5"} pointerEvents={isEnabled ? "auto" : "none"}>
                    <SlicerColumnRowInput
                      inputIndex={index}
                      dimensionOptions={columnsOptions}
                      appendNewRow={appendNewColumnHandler}
                      formMethods={formMethods}
                    />
                  </Box>
                  <IconButton
                    visibility="hidden"
                    _groupHover={{ visibility: "visible" }}
                    isDisabled={isDeleteDisabled(index)}
                    aria-label="remove-column"
                    size="xs"
                    variant="unstyled"
                    icon={<CAIcon as={IoTrashOutline} variant="secondary" boxSize={4} />}
                    onClick={() => remove(index)}
                    mt="2.5"
                  />
                </CADragDrop>
              </HStack>
            );
          })}
        </CADragDropContainer>
      </CACard>
    </>
  );
};

export default SlicerColumn;
