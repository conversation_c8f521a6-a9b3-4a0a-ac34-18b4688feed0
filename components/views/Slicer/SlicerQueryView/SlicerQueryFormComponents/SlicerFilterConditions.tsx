import React from "react";
import { Box, HStack, Icon, I<PERSON><PERSON>utton } from "@chakra-ui/react";
import { IoTrashOutline } from "react-icons/io5";
import { UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { DragEndEvent } from "@dnd-kit/core";
import { ReorderIcon } from "@/design-system/icons";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { SlicerDataSetOptions, TSlicerQueryForm } from "@/types/slicer";
import { findIndexesOfNonSupportedAgencies, tickerSelectionFilter } from "@/utils/helpers/slicer";
import { CADragDrop, CADragDropContainer } from "@/design-system/molecules/CADragDrop";
import CACheckboxInput from "@/design-system/molecules/CACheckboxInput";
import { CA_Mastr_Models_v1_0_Models_ConfigurationType } from "@/utils/openapi";
import SlicerRowInput from "./SlicerFilterRowInput";

interface SlicerFilterConditionsProps {
  seriesIndex: number;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
  filterSetEnabled: boolean;
}

const FilterConditions: React.FC<SlicerFilterConditionsProps> = ({
  seriesIndex,
  formMethods,
  filterSetEnabled,
}: SlicerFilterConditionsProps) => {
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();

  const { control } = formMethods;
  const {
    fields: conditions,
    append,
    move,
    remove,
  } = useFieldArray({ control, name: `filter_sets.${seriesIndex}.conditions` });

  const watchDatasetType = useWatch({
    control,
    name: "dataset_type",
  });

  const watchConditions = useWatch({ control, name: `filter_sets.${seriesIndex}.conditions` });

  const AgencyOption = React.useMemo(() => {
    return {
      category: " ",
      type: CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY,
      dimension: "AGENCY",
      display_name: "Agency",
      selections:
        watchDatasetType === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL
          ? ["GPL"]
          : loan_level_slicer_config?.data_set?.asset_types?.filter((asset) => asset !== "GPL"),
    };
  }, [loan_level_slicer_config?.data_set?.asset_types, watchDatasetType]);

  const filterOptions = React.useMemo(
    () =>
      [{ ...AgencyOption }, ...(loan_level_slicer_config?.filters ?? [])].map((filter) => {
        if (filter.dimension === "TICKER") {
          return {
            ...filter,
            selectionsFilterFn: (selected_agency: string[] = ["FHL"]) => {
              return tickerSelectionFilter(filter, selected_agency);
            },
          };
        }
        return filter;
      }),
    [AgencyOption, loan_level_slicer_config?.filters]
  );

  // validators
  const canEnableOrDisable = (index: number) => index !== 0 && index !== conditions.length - 1;
  const canDrag = (index: number) => index !== 0 && index !== conditions.length - 1;
  const isDeleteDisabled = (dimension: string) => dimension === "AGENCY";

  // Handlers
  const appendNewConditionHandler = React.useCallback(() => {
    append({ dimension: "", operator: undefined, values: [], enabled: true }, { focusIndex: 0 });
  }, [append]);

  const resetConditions = React.useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (values?: any) => {
      const filtersFromConfig = loan_level_slicer_config?.filters;
      const conditionsWithAgenciesSupported = filtersFromConfig?.length
        ? conditions?.map((condition) => filtersFromConfig.find((gr) => gr.dimension === condition?.dimension))
        : [];

      const fieldIndexes = findIndexesOfNonSupportedAgencies(conditionsWithAgenciesSupported, values).filter(Boolean);

      if (fieldIndexes.length && values.length) {
        remove(fieldIndexes);
      }
    },
    [conditions, loan_level_slicer_config?.filters, remove]
  );

  const dragColumnHandler = (event: DragEndEvent) => {
    const source = event.active.data.current?.sortable.index;
    const destination = event.over?.data.current?.sortable.index;
    if (!destination?.toString()) {
      return;
    }

    destination !== 0 && destination !== conditions.length - 1 && move(source, destination);
  };

  const handleRemove = (index: number) => {
    remove(index);
    if (index === conditions.length - 1) {
      appendNewConditionHandler();
    }
  };

  return (
    <CADragDropContainer onDragEnd={dragColumnHandler} items={conditions.map((el) => el.id)}>
      {conditions.map((condition, index) => {
        const isEnabled = filterSetEnabled ? watchConditions?.[index]?.enabled ?? true : false;

        return (
          <HStack key={condition.id} role="group" mr="0.5rem">
            <CADragDrop
              id={condition?.id?.toString()}
              disabled={!canDrag(index)}
              activatorElement={
                <Box w="0.8rem" cursor="move">
                  <Icon as={ReorderIcon} />
                </Box>
              }
              activatorElemStyle={{
                visibility: "hidden",
                _groupHover: { visibility: canDrag(index) ? "visible" : "hidden" },
                mt: "2.5",
              }}
              boxStyle={{
                display: "flex",
                w: "full",
                alignItems: "start",
              }}
            >
              <Box
                w="1rem"
                visibility={!filterSetEnabled || watchConditions?.[index]?.enabled ? "hidden" : "visible"}
                _groupHover={{ visibility: canEnableOrDisable(index) ? "visible" : "hidden" }}
                mt="2"
              >
                <CACheckboxInput
                  {...formMethods.register(`filter_sets.${seriesIndex}.conditions.${index}.enabled`)}
                  variant="small"
                  hideLabel
                />
              </Box>

              <Box w="full" opacity={isEnabled ? "1" : "0.5"} pointerEvents={isEnabled ? "auto" : "none"}>
                <SlicerRowInput
                  registerFieldPath={`filter_sets.${seriesIndex}.conditions`}
                  inputIndex={index}
                  dimensionOptions={filterOptions}
                  appendNewRow={appendNewConditionHandler}
                  resetConditions={resetConditions}
                  formMethods={formMethods}
                />
              </Box>

              <IconButton
                visibility="hidden"
                _groupHover={{ visibility: "visible" }}
                isDisabled={isDeleteDisabled(condition?.dimension ?? "")}
                aria-label="remove-column"
                size="xs"
                variant="unstyled"
                icon={<CAIcon as={IoTrashOutline} variant="secondary" boxSize={4} />}
                onClick={() => handleRemove(index)}
                mt="2.5"
              />
            </CADragDrop>
          </HStack>
        );
      })}
    </CADragDropContainer>
  );
};

export default FilterConditions;
