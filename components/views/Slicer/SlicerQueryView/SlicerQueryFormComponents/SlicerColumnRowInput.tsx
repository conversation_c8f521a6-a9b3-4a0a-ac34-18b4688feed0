import React from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import { Box, HStack } from "@chakra-ui/layout";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import {
  ConfigOptionsType,
  getConfigByDimensionValue,
  normalizeSlicerConfigOptions,
  normalizedSlicerSelectionsOptions,
} from "@/utils/helpers/slicer";
import { COLUMNS_DEPENDANT_ON_FACTOR_DATE, DimensionsWithSecondaryOptionLabel } from "@/constants/slicer";
import { SelectedDimensionObjectType, TSlicerQueryForm } from "@/types/slicer";
import { CA_Mastr_Models_v1_0_Models_ConfigurationType } from "@/utils/openapi";

interface SlicerColumnRowInputProps {
  dimensionOptions: ConfigOptionsType;
  appendNewRow: () => void;
  inputIndex: number;
  resetConditions?: () => void;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

const SlicerColumnRowInput: React.FC<SlicerColumnRowInputProps> = ({
  dimensionOptions,
  appendNewRow,
  inputIndex,
  formMethods,
}: SlicerColumnRowInputProps) => {
  const { control, setValue, getValues } = formMethods;

  const [selectedDimension, setSelectedDimension] = React.useState<SelectedDimensionObjectType>(
    {} as SelectedDimensionObjectType
  );

  const [watchColumns] = useWatch({
    control,
    name: ["columns"],
  });

  const normalizedDimensionOptions = React.useMemo(() => {
    return normalizeSlicerConfigOptions(dimensionOptions, [], "", getValues("query_type")) ?? [];
  }, [dimensionOptions, getValues]);

  // Set default values on load
  React.useEffect(() => {
    const dimensionConfig = getValues(`columns.${inputIndex}`) ?? {};

    const dimension = dimensionConfig?.dimension;
    const dimensionConfigObj = getConfigByDimensionValue(dimension, dimensionOptions) ?? {};
    if (dimension) {
      setSelectedDimension(dimensionConfigObj);
    }

    validateRow(dimensionConfigObj);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // VALIDATORS
  const validateRow = (currentDimension?: SelectedDimensionObjectType) => {
    currentDimension = currentDimension ?? selectedDimension;
    // get all rows
    const rows = [...(getValues("columns") ?? [])];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const lastRow = rows.pop() as any;

    let isLastRowValid;

    switch (currentDimension.type) {
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.VALUE:
        isLastRowValid = !!lastRow.dimension;
        break;
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST:
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE: {
        const values =
          lastRow?.values && lastRow?.values?.length > 0 ? lastRow?.values : currentDimension.default_selections;
        isLastRowValid = !!lastRow.dimension && values && values?.length > 0;
        break;
      }
    }

    if (isLastRowValid) {
      appendNewRow();
    }
  };

  // HANDLERS
  const onDimensionChange = (value: SelectedDimensionObjectType) => {
    setSelectedDimension(value);

    const groups = getValues("groups") ?? [];
    const isFactorDateInGroup = groups.some((group) => group?.dimension === "FACTORDATE");
    const defaultSelections = isFactorDateInGroup
      ? value.default_selections?.filter((el) => !COLUMNS_DEPENDANT_ON_FACTOR_DATE.includes(el))
      : value.default_selections;

    // clear-out/reset inputs
    setValue(`columns.${inputIndex}.values`, defaultSelections ?? []);

    // validate row
    if (value.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.VALUE || !!defaultSelections?.length) {
      validateRow(value);
    }
  };

  const options = React.useMemo(
    () =>
      normalizedSlicerSelectionsOptions({
        selections: selectedDimension?.selections ?? [],
        selectionsDescription: selectedDimension?.selection_descriptions ?? [],
      }),
    [selectedDimension?.selection_descriptions, selectedDimension?.selections]
  );

  return (
    <HStack w="full" justifyContent="center" alignItems="start">
      <Box flex={1.1}>
        <Controller
          control={control}
          name={`columns.${inputIndex}.dimension`}
          render={({ field: { name, value, onChange } }) => (
            <CAMultiSelectDropdown
              value={getConfigByDimensionValue(value, dimensionOptions) ?? {}}
              name={name}
              shouldOnlyReturnValue={false}
              isMultiSelect={false}
              tooltipText={selectedDimension.display_name ?? ""}
              isDisabled={selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY}
              options={normalizedDimensionOptions}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              onChange={(selectedOption: any) => {
                onChange(selectedOption.dimension);
                onDimensionChange(selectedOption);
              }}
              isOptionDisabled={(option) => !!watchColumns?.some((el) => el.dimension === option.dimension)}
            />
          )}
        />
      </Box>

      <Box
        display={
          !selectedDimension.type || selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.VALUE
            ? "none"
            : "flex"
        }
        flex={1.8}
      >
        {/* Input for LIST AND AGENCY - MULTISELECT */}
        {selectedDimension.dimension &&
          selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST && (
            <Controller
              control={control}
              name={`columns.${inputIndex}.values`}
              defaultValue={selectedDimension?.default_selections ?? []}
              render={({ field: { name, onChange, value } }) => (
                <CAMultiSelectDropdown
                  name={name}
                  value={value as string[]}
                  isMultiSelect
                  hasOptionDescription={
                    !!selectedDimension.dimension &&
                    DimensionsWithSecondaryOptionLabel.includes(selectedDimension.dimension)
                  }
                  options={options}
                  onChange={(values) => {
                    onChange(values);
                    validateRow(selectedDimension);
                  }}
                />
              )}
            />
          )}

        {selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE && (
          <Controller
            control={control}
            name={`columns.${inputIndex}.values`}
            render={({ field: { name, value } }) => (
              <CAMultiSelectDropdown
                isCreatable
                isMultiSelect
                name={name}
                value={value as string[]}
                options={(value as string[])?.map((v) => ({ label: v, value: v })) ?? []}
                onCreateNewOption={async (newValue: string) => {
                  if (newValue.trim()) {
                    setValue(`columns.${inputIndex}.values`, [...(value ?? []), newValue?.toUpperCase()]);
                  }
                  validateRow();
                }}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                onChange={(values: any) => {
                  setValue(`columns.${inputIndex}.values`, [...values]);
                  validateRow();
                }}
              />
            )}
          />
        )}
      </Box>
    </HStack>
  );
};

export default SlicerColumnRowInput;
