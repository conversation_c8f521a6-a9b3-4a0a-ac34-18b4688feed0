import { Box } from "@chakra-ui/react";
import React, { useEffect } from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import ValueDisplay from "@/components/helpers/ValueDisplay";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { TSlicerQueryForm } from "@/types/slicer";
import { getFormattedStringOptions } from "@/utils/helpers";
import { showWarningToast } from "@/design-system/theme/toast";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { CA_Mastr_Models_v1_0_Models_QueryType } from "@/utils/openapi";
import { slicerSecondaryCardCommonProps } from "../../SlicerQueryDetails/helpers/SlicerChartCardPropsGetter";

interface SlicerQuerySettingsProps {
  formMethods: UseFormReturn<TSlicerQueryForm>;
}

const CREATABLE_WITH_BOND_VALIDATION = ["BONDNAME", "POOLNUMBER"];

export const SlicerQuerySettings = ({ formMethods }: SlicerQuerySettingsProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const [filtersSets] = useWatch({
    control: formMethods.control,
    name: ["filter_sets"],
  });

  const filters = JSON.stringify(filtersSets);
  const {
    setValue,
    getValues,
    formState: { dirtyFields },
    control,
  } = formMethods;

  const isMinimumBalanceDirty = dirtyFields.settings?.minimum_balance;

  React.useEffect(() => {
    const isPoolMnemonicOrSecurityNameSelected = filtersSets.some((filter) =>
      filter.conditions.some(
        (condition) => condition.dimension && CREATABLE_WITH_BOND_VALIDATION.includes(condition.dimension)
      )
    );
    const minBalanceValue = getValues("settings.minimum_balance");
    if (minBalanceValue === 1000 && isPoolMnemonicOrSecurityNameSelected) {
      setValue("settings.minimum_balance", 0);
    } else if (minBalanceValue === 0 && !isPoolMnemonicOrSecurityNameSelected) {
      setValue("settings.minimum_balance", 1000);
    }
  }, [setValue, filters, filtersSets, isMinimumBalanceDirty, getValues]);

  useEffect(() => {
    const modelVersions = getValues("settings.model_versions");

    const filteredModelVersions = modelVersions?.filter((version) =>
      metadata?.slicer_settings?.version?.some((v) => v.value === version)
    );

    if (filteredModelVersions?.length === 0) {
      const defaultModelVersion = metadata?.slicer_settings?.version?.find((v) => v.is_default);
      if (defaultModelVersion) {
        setValue("settings.model_versions", [defaultModelVersion.value]);
        showWarningToast(
          "Model Version Deprecated",
          `${modelVersions} is no longer supported. Defaulting to ${defaultModelVersion.display_value} version for new runs.`
        );
      }

      return;
    }

    if (filteredModelVersions?.length !== modelVersions?.length) {
      const removedVersions = modelVersions?.filter((version) => !filteredModelVersions?.includes(version));
      if (removedVersions && removedVersions.length > 0) {
        showWarningToast(
          "Model Version Deprecated",
          `Model versions ${removedVersions.join(", ")} are no longer supported.`
        );
      }
    }

    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getValues, metadata]);

  return (
    <CACard title="Settings" {...slicerSecondaryCardCommonProps}>
      {getValues("query_type") === CA_Mastr_Models_v1_0_Models_QueryType.PREPAY && (
        <Controller
          control={control}
          name="settings.model_versions"
          shouldUnregister={true}
          render={({ field: { name, value, onChange } }) => (
            <CAMultiSelectDropdown
              options={getFormattedStringOptions(metadata?.slicer_settings?.version)}
              width={"12rem"}
              value={value ?? undefined}
              name={name}
              onChange={onChange}
              label={"Prepay Models"}
              isMultiSelect
            />
          )}
        />
      )}
      {/* <CASelectDropdown
        label={"Prepay Model"}
        {...formMethods.register("settings.model_versions")}
        options={getFormattedStringOptions(metadata?.["slicer_settings.version"])}
      /> */}
      <CAInput
        type="number"
        step="any"
        width={"6rem"}
        label={"Group Min Balance ($M)"}
        {...formMethods.register("settings.minimum_balance", {
          valueAsNumber: true,
          required: true,
        })}
      />
      <ValueDisplay
        name="Pre-Factor Date Balance"
        value={
          <Box w="1.6rem">
            <Controller
              name={"settings.use_pre_factor_date_balance"}
              control={formMethods.control}
              render={({ field: { name, onChange, value } }) => {
                return <CASwitchInput name={name} hideLabel isChecked={value ?? undefined} onChange={onChange} />;
              }}
            />
          </Box>
        }
      />
    </CACard>
  );
};
