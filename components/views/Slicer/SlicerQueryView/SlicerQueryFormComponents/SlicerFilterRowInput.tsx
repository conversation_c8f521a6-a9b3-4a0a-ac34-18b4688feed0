import React from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import { Box, Flex, HStack } from "@chakra-ui/layout";
import CAMultiSelectDropdown, { MultiSelectOptions } from "@/design-system/molecules/CAMultiSelectDropdown";
import CAInput from "@/design-system/molecules/CAInput";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter,
  CA_Mastr_Models_v1_0_Models_ConfigurationType,
  CA_Mastr_Models_v1_0_Models_Operator,
  CA_Mastr_Models_v1_0_Models_QueryType,
} from "@/utils/openapi";
import {
  ConfigOptionsType,
  getConfigByDimensionValue,
  getDefaultValuesBasedOnOperator,
  getOperator,
  normalizeSlicerConfigOptions,
  normalizedSlicerSelectionsOptions,
} from "@/utils/helpers/slicer";
import {
  CREATABLE_WITH_BOND_VALIDATION,
  DimensionsWithSecondaryOptionLabel,
  WITHIN_RANGE_INPUTS,
} from "@/constants/slicer";
import {
  FilterCondition,
  OperatorsType,
  SelectedDimensionObjectType,
  SlicerConfigFormat,
  SlicerDataSetOptions,
  TSlicerQueryForm,
} from "@/types/slicer";
import { showErrorToast } from "@/design-system/theme/toast";
import { getOperators } from "@/constants/slicer";
import { useValidateBondSWR } from "@/utils/swr-hooks/Util";
import { getDateFromNumericYYYYMMDD } from "@/utils/helpers";
import SlicerRangeInput from "./SlicerRangeInput";
import SlicerOperatorInput from "./SlicerOperatorInput";
import { SlicerLookBackInput } from "./SlicerLookBackInput";

interface SlicerFilterRowInputProps {
  dimensionOptions: ConfigOptionsType;
  registerFieldPath: `filter_sets.${number}.conditions`;
  appendNewRow: () => void;
  inputIndex: number;
  resetConditions?: (values?: string | MultiSelectOptions | MultiSelectOptions[]) => void;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

const rangeDateOperators = [
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE,
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE,
  CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE,
];

const singleDateOperators = [
  CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO,
  CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN,
  CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO,
  CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN,
  CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO,
];

const elementOperators = [
  CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
  CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF,
  CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE,
];

const shouldResetToDefaultValues = (
  currentOperator: CA_Mastr_Models_v1_0_Models_Operator | undefined,
  incomingOperator: CA_Mastr_Models_v1_0_Models_Operator | undefined
) => {
  if (!currentOperator || !incomingOperator) return false;

  if (
    currentOperator === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK &&
    incomingOperator == CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK
  )
    return false;

  if (rangeDateOperators.includes(currentOperator) && rangeDateOperators.includes(incomingOperator)) return false;

  if (singleDateOperators.includes(currentOperator) && singleDateOperators.includes(incomingOperator)) return false;

  if (elementOperators.includes(currentOperator) && elementOperators.includes(incomingOperator)) return false;

  return true;
};

const SlicerFilterRowInput: React.FC<SlicerFilterRowInputProps> = ({
  dimensionOptions,
  registerFieldPath,
  appendNewRow,
  inputIndex,
  resetConditions,
  formMethods,
}: SlicerFilterRowInputProps) => {
  const { trigger: validateBond } = useValidateBondSWR();
  const { unregister, control, setValue, getValues } = formMethods;

  const [selectedDimension, setSelectedDimension] = React.useState<SelectedDimensionObjectType>(
    {} as SelectedDimensionObjectType
  );

  const [selectedOperator, setSelectedOperator] = React.useState<OperatorsType | null>(null);

  const [watchDataset, watchAgency, watchConditions, watchQueryType]: [
    string | null | undefined,
    FilterCondition,
    FilterCondition[],
    CA_Mastr_Models_v1_0_Models_QueryType | undefined
  ] = useWatch({
    control,
    name: ["dataset_type", `${registerFieldPath}.0`, registerFieldPath, "query_type"],
  });

  const selectedAgenciesArr = React.useMemo(
    () =>
      (watchAgency?.operator === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF
        ? watchAgency?.["list_values"] ?? []
        : watchAgency?.["values"] ?? []
      ).filter(Boolean),

    // Doing JSON.stringify for comparison as useMemo is not triggering on *watchAgency* field values changes for some reason
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [JSON.stringify(watchAgency)]
  );

  const normalizedDimensionOptions = React.useMemo(() => {
    return (
      normalizeSlicerConfigOptions(
        dimensionOptions,
        selectedAgenciesArr as string[],
        CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
        watchQueryType
      ) ?? []
    );
  }, [dimensionOptions, selectedAgenciesArr, watchQueryType]);

  // Set default values on load
  React.useEffect(() => {
    const dimensionConfig = getValues(`${registerFieldPath}.${inputIndex}`) ?? {};

    const dimension = dimensionConfig?.dimension;
    const dimensionConfigObj = getConfigByDimensionValue(dimension, dimensionOptions) ?? {};
    if (dimension) {
      setSelectedDimension(dimensionConfigObj);
    }

    const operator = dimensionConfig?.operator;
    const allowedOperators =
      getOperators(dimensionConfigObj.type, {
        isVector: !!dimensionConfigObj.default_vector_values,
        includeLookBack:
          dimensionConfigObj.format === SlicerConfigFormat.YEAR ||
          dimensionConfigObj.format === SlicerConfigFormat.YEAR_MONTH,
      }) ?? [];
    if (operator) {
      setSelectedOperator(allowedOperators.find((o: OperatorsType) => o.value === operator) ?? null);
    }

    validateRow(dimensionConfigObj);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // VALIDATORS
  const validateRow = (currentDimension?: SelectedDimensionObjectType) => {
    currentDimension = currentDimension ?? selectedDimension;
    // get all rows
    const rows = [...(getValues(`${registerFieldPath}`) ?? [])];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const lastRow = rows.pop() as any;

    let isLastRowValid;
    const isValidOperator = !!lastRow?.operator;
    const valuesFieldName =
      lastRow?.operator === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF ? "list_values" : "values";

    switch (currentDimension.type) {
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY:
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.SINGLE:
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_SINGLE:
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.MULTIPLE: {
        const values = lastRow?.[valuesFieldName]?.filter(Boolean);
        isLastRowValid = !!lastRow?.dimension && isValidOperator && values && values?.length > 0;
        break;
      }
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST:
      case CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE: {
        const values =
          lastRow?.list_values && lastRow?.list_values?.length > 0
            ? lastRow?.list_values
            : currentDimension.default_selections;
        isLastRowValid = !!lastRow.dimension && isValidOperator && values && values?.length > 0;
        break;
      }

      case CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE: {
        const values = lastRow?.values?.filter(Boolean) ?? [];

        if (selectedOperator?.value && WITHIN_RANGE_INPUTS.includes(selectedOperator?.value)) {
          const minRange = !!values?.[0]?.toString();
          const maxRange = !!values?.[1]?.toString();
          const hasStepSize = !!currentDimension?.step_size;
          isLastRowValid =
            !!lastRow?.dimension &&
            isValidOperator &&
            minRange &&
            maxRange &&
            (hasStepSize ? !!lastRow?.step_size?.toString() : true);
        } else {
          isLastRowValid = !!lastRow?.dimension && isValidOperator && values.length > 0;
        }
        break;
      }
    }

    if (isLastRowValid) {
      appendNewRow();
    }
  };

  const getSelectedAgenciesByLowerBound = React.useCallback(
    (dimension: SelectedDimensionObjectType | CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter) => {
      const filterSets = getValues(registerFieldPath);
      const agenciesOnFilterSets = new Set(filterSets.flatMap((filter) => filter.list_values));

      const agenciesSelected = dimension?.agency_lower_bounds?.filter((agency) =>
        agenciesOnFilterSets.has(agency.name ?? "")
      );

      if (!agenciesSelected) return;

      return Math.max(...agenciesSelected.map((item) => item.value ?? dimension.default_lower_bound ?? 0));
    },
    [getValues, registerFieldPath]
  );

  const getSelectedAgenciesByUpperBound = React.useCallback(
    (dimension: SelectedDimensionObjectType | CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter) => {
      const filterSets = getValues(registerFieldPath);
      const agenciesOnFilterSets = new Set(filterSets.flatMap((filter) => filter.list_values));

      const agenciesSelected = dimension?.agency_upper_bounds?.filter((agency) =>
        agenciesOnFilterSets.has(agency.name ?? "")
      );

      if (!agenciesSelected) return;

      return Math.min(...agenciesSelected.map((item) => item.value ?? dimension.default_upper_bound ?? 0));
    },
    [getValues, registerFieldPath]
  );

  // HANDLERS
  const onDimensionChange = (value: SelectedDimensionObjectType) => {
    unregister(`${registerFieldPath}.${inputIndex}.values`);
    setSelectedDimension(value);
    const activeLowerBound = getSelectedAgenciesByLowerBound(value);
    // set operator
    const defaultOperator = getOperator(value);
    setSelectedOperator(defaultOperator);
    setValue(`${registerFieldPath}.${inputIndex}.operator`, defaultOperator?.value);
    setValue(`${registerFieldPath}.${inputIndex}.agencies`, value.agencies);
    // clear-out/reset inputs
    if (value.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE) {
      // set default min-max range
      const defaultValues = getDefaultValuesBasedOnOperator(defaultOperator, value, activeLowerBound) ?? [];
      if (defaultValues.length === 1) {
        setValue(`${registerFieldPath}.${inputIndex}.values`, [defaultValues[0]]);
      } else {
        setValue(`${registerFieldPath}.${inputIndex}.values`, defaultValues);
      }

      validateRow(value);
    } else {
      setValue(`${registerFieldPath}.${inputIndex}.list_values`, value.default_selections ?? []);
      setValue(`${registerFieldPath}.${inputIndex}.values`, ["", ""]);
      setValue(`${registerFieldPath}.${inputIndex}.agencies`, value.agencies);
    }

    if (value.default_selections) {
      validateRow(value);
    }
  };

  const onAgencyChange = (values?: string | MultiSelectOptions | MultiSelectOptions[]) => {
    if (Array.isArray(values)) {
      resetConditions?.(values);
    }
    const conditions = getValues(registerFieldPath);

    conditions.forEach((condition, conditionIndex) => {
      const dimension = dimensionOptions.find((d) => d.dimension === condition.dimension);
      if (!dimension) return;
      if ("agency_lower_bounds" in dimension) {
        const lowerBound = getSelectedAgenciesByLowerBound(dimension);
        const lowerBoundDate = getDateFromNumericYYYYMMDD(lowerBound);
        const upperBound = getSelectedAgenciesByUpperBound(dimension);
        const upperBoundDate = getDateFromNumericYYYYMMDD(upperBound);
        const currentConditionValues = condition.values;

        if (condition.operator === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK) {
          setValue(`${registerFieldPath}.${conditionIndex}.values`, [
            currentConditionValues?.[0] ?? dimension?.default_look_back?.toString(),
          ]);
          return;
        }
        if (currentConditionValues?.length === 1) {
          if (condition.operator === CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO) {
            setValue(`${registerFieldPath}.${conditionIndex}.values`, [upperBoundDate ?? currentConditionValues?.[0]]);
            return;
          }
          setValue(`${registerFieldPath}.${conditionIndex}.values`, [lowerBoundDate ?? currentConditionValues?.[0]]);
          return;
        } else {
          if ("agency_upper_bounds" in dimension) {
            const currentConditionValues = condition.values;
            setValue(`${registerFieldPath}.${conditionIndex}.values`, [
              lowerBoundDate ?? currentConditionValues?.[0],
              upperBoundDate ?? currentConditionValues?.[1],
            ]);
          }
        }
      }
    });
  };

  const validateNewBondNameOption = async (bondName: string) => {
    if (!bondName?.trim()) {
      return;
    }
    return await validateBond(
      { bond_name: bondName },
      {
        onSuccess: (res) => {
          const subType = res?.security_info?.sub_type;
          const supported_agencies =
            watchAgency?.operator === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF
              ? watchAgency?.list_values ?? []
              : watchAgency?.values ?? [];
          if (subType && supported_agencies.includes(subType)) {
            return true;
          } else {
            showErrorToast("Error", `Bond "${bondName}" is not supported with current selected agency.`);
            return false;
          }
        },
        onError: () => {
          showErrorToast("Error", `Bond "${bondName}" is invalid.`);
        },
      }
    );
  };

  const onOperatorChange = (operator: OperatorsType) => {
    const prevValue: FilterCondition | undefined = { ...getValues(`${registerFieldPath}.${inputIndex}`) };

    setSelectedOperator(operator);

    const resetToDefaultValues = shouldResetToDefaultValues(prevValue?.operator, operator?.value);

    if (resetToDefaultValues) {
      unregister(`${registerFieldPath}.${inputIndex}.values`);
      unregister(`${registerFieldPath}.${inputIndex}.list_values`);
    }

    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY) {
      onAgencyChange([]);
    }
    setTimeout(() => {
      validateRow();
    }, 0);

    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE) {
      return;
    }
    const activeLowerBound = getSelectedAgenciesByLowerBound(selectedDimension);
    const activeUpperBound = getSelectedAgenciesByUpperBound(selectedDimension);

    const defaultValues =
      getDefaultValuesBasedOnOperator(operator, selectedDimension, activeLowerBound, activeUpperBound) ?? [];

    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE) {
      if (operator.value === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK) {
        if (resetToDefaultValues) {
          setValue(`${registerFieldPath}.${inputIndex}.values`, [defaultValues[0]]);
        }

        return;
      }
      if (defaultValues.length === 1) {
        setTimeout(() => {
          if (resetToDefaultValues) {
            setValue(`${registerFieldPath}.${inputIndex}.values`, [defaultValues[0]]);
            return;
          }
        }, 0);
      } else {
        if (resetToDefaultValues) {
          setValue(`${registerFieldPath}.${inputIndex}.values`, defaultValues);
          return;
        }
      }
    } else {
      if (resetToDefaultValues) {
        setValue(`${registerFieldPath}.${inputIndex}.list_values`, defaultValues as string[]);
        setValue(`${registerFieldPath}.${inputIndex}.values`, []);
      }
    }
  };

  const options = React.useMemo(
    () =>
      normalizedSlicerSelectionsOptions({
        selections: selectedDimension?.selections ?? [],
        selectionsDescription: selectedDimension?.selection_descriptions ?? [],
      }),
    [selectedDimension?.selection_descriptions, selectedDimension?.selections]
  );
  return (
    <HStack w="full" justifyContent="center" alignItems="start">
      <Box flex={1.1}>
        <Controller
          control={control}
          name={`${registerFieldPath}.${inputIndex}.dimension`}
          render={({ field: { name, value, onChange } }) => (
            <CAMultiSelectDropdown
              value={getConfigByDimensionValue(value, dimensionOptions) ?? {}}
              name={name}
              shouldOnlyReturnValue={false}
              isMultiSelect={false}
              tooltipText={selectedDimension.display_name ?? ""}
              readonly={selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY}
              options={normalizedDimensionOptions}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              onChange={(selectedOption: any) => {
                onChange(selectedOption.dimension);
                onDimensionChange(selectedOption);
              }}
              isOptionDisabled={(option) => !!watchConditions?.some((el) => el.dimension === option.dimension)}
            />
          )}
        />
      </Box>

      <Box>
        <SlicerOperatorInput
          registerFieldPath={`${registerFieldPath}.${inputIndex}`}
          selectedOperator={selectedOperator}
          setSelectedOperator={onOperatorChange}
          selectedDimension={selectedDimension}
          formMethods={formMethods}
        />
      </Box>

      <Box display="flex" flex={1.5}>
        {/* Placeholder component, in case no dimension is selected */}
        {!selectedDimension.type && (
          <CAMultiSelectDropdown name="values" value={[]} options={[]} isMultiSelect={false} />
        )}

        {/* Input for LIST AND AGENCY - MULTISELECT */}
        {selectedDimension.dimension &&
          // List
          (selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST ||
            // List Like and Element Of
            (selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE &&
              selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF) ||
            // List Like and Not Element Of
            (selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE &&
              selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF) ||
            // Agency and Element Of
            (selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY &&
              selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF)) &&
          !CREATABLE_WITH_BOND_VALIDATION.includes(selectedDimension.dimension) && (
            <Controller
              control={control}
              name={`${registerFieldPath}.${inputIndex}.list_values`}
              defaultValue={selectedDimension?.default_selections ?? []}
              render={({ field: { name, onChange, value } }) => (
                <CAMultiSelectDropdown
                  name={name}
                  value={value as string[]}
                  isMultiSelect
                  hasOptionDescription={
                    !!selectedDimension.dimension &&
                    DimensionsWithSecondaryOptionLabel.includes(selectedDimension.dimension)
                  }
                  options={normalizedSlicerSelectionsOptions(
                    selectedDimension?.selectionsFilterFn?.(selectedAgenciesArr) ?? {
                      selections: selectedDimension?.selections ?? [],
                      selectionsDescription: selectedDimension?.selection_descriptions ?? [],
                    },
                    value as string[],
                    selectedDimension?.type
                  )}
                  onChange={(values) => {
                    onChange(values);
                    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY) {
                      onAgencyChange(values);
                    }
                    validateRow(selectedDimension);
                  }}
                />
              )}
            />
          )}

        {/* Input for LIST_SINGLE AND AGENCY - SINGLE_SELECT */}
        {(selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_SINGLE ||
          (selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY &&
            selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO)) && (
          <Controller
            control={control}
            name={`${registerFieldPath}.${inputIndex}.values.0`}
            render={({ field: { name, onChange, value } }) => {
              return (
                <CAMultiSelectDropdown
                  id={name}
                  name={name}
                  value={value}
                  isMultiSelect={false}
                  hasOptionDescription={
                    !!selectedDimension.dimension &&
                    DimensionsWithSecondaryOptionLabel.includes(selectedDimension.dimension)
                  }
                  options={options}
                  readonly={
                    selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY &&
                    watchDataset === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL
                  }
                  onChange={(values) => {
                    onChange(values);
                    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY) {
                      onAgencyChange(values);
                    }
                    validateRow();
                  }}
                />
              );
            }}
          />
        )}

        {/* Input for CREATABLE WITH BOND VALIDATION */}
        {selectedDimension.dimension &&
          CREATABLE_WITH_BOND_VALIDATION.includes(selectedDimension.dimension) &&
          (selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO ? (
            <Controller
              control={control}
              name={`${registerFieldPath}.${inputIndex}.values.0`}
              render={({ field: { name, value, onChange } }) => {
                return (
                  <CAInput
                    name={name}
                    value={value}
                    height="30px"
                    onChange={(e) => {
                      onChange(e.target.value?.toUpperCase());
                    }}
                    onBlur={async (e) => {
                      const validBond = await validateNewBondNameOption(e.target.value);
                      if (!validBond) {
                        e.target.value = "";
                        setValue(`${registerFieldPath}.0.values.0`, "");
                      }
                      validateRow();
                    }}
                  />
                );
              }}
            />
          ) : selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF ||
            selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.NOT_ELEMENT_OF ? (
            <Controller
              control={control}
              name={`${registerFieldPath}.${inputIndex}.list_values`}
              render={({ field: { name, value, onChange } }) => {
                return (
                  <CAMultiSelectDropdown
                    isCreatable
                    isMultiSelect
                    name={name}
                    value={value as string[]}
                    options={(value as string[])?.map((v) => ({ label: v, value: v })) ?? []}
                    onCreateNewOption={async (newValue: string) => {
                      const validOption = await validateNewBondNameOption(newValue);
                      if (validOption) {
                        onChange([...(value ?? []), newValue?.toUpperCase()]);
                      }
                      validateRow();
                    }}
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    onChange={(values: any) => {
                      onChange([...values]);
                      validateRow();
                    }}
                  />
                );
              }}
            />
          ) : null)}

        {selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE &&
          selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_LIKE && (
            <Controller
              control={control}
              name={`${registerFieldPath}.${inputIndex}.list_values`}
              render={({ field: { name, value } }) => (
                <CAMultiSelectDropdown
                  isCreatable
                  isMultiSelect
                  name={name}
                  value={value as string[]}
                  options={(value as string[])?.map((v) => ({ label: v, value: v })) ?? []}
                  onCreateNewOption={async (newValue: string) => {
                    if (newValue.trim()) {
                      setValue(`${registerFieldPath}.${inputIndex}.list_values`, [
                        ...(value ?? []),
                        newValue?.toUpperCase(),
                      ]);
                    }
                    validateRow();
                  }}
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  onChange={(values: any) => {
                    setValue(`${registerFieldPath}.${inputIndex}.list_values`, [...values]);
                    validateRow();
                  }}
                />
              )}
            />
          )}

        <Flex
          display={
            selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE ||
            selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.SINGLE
              ? "flex"
              : "none"
          }
          alignItems="center"
          w="full"
        >
          {selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.SINGLE && (
            <Controller
              control={control}
              name={`${registerFieldPath}.${inputIndex}.values.0`}
              render={({ field: { name, value, onChange } }) => {
                return (
                  <CAInput
                    name={name}
                    type="number"
                    step="any"
                    value={value}
                    height="30px"
                    onChange={({ target: { value } }) => {
                      onChange(value);
                      if (value.length === 1) {
                        validateRow();
                      }
                    }}
                  />
                );
              }}
            />
          )}

          {selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE &&
            selectedOperator?.value !== CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK && (
              <SlicerRangeInput
                registerFieldPath={`${registerFieldPath}.${inputIndex}`}
                selectedDimension={selectedDimension}
                validateRowHandler={validateRow}
                operator={selectedOperator?.value}
                formMethods={formMethods}
              />
            )}

          {selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK && (
            <SlicerLookBackInput
              registerFieldPath={`${registerFieldPath}.${inputIndex}`}
              selectedDimension={selectedDimension}
              formMethods={formMethods}
            />
          )}
        </Flex>
      </Box>
    </HStack>
  );
};

export default SlicerFilterRowInput;
