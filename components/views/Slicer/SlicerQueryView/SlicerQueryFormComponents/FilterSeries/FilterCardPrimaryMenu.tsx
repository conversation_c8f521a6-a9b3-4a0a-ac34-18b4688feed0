import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON> } from "@chakra-ui/react";
import { IoAddCircleOutline, IoEllipsisVerticalOutline, IoTrashOutline } from "react-icons/io5";
import CAIcon, { CAIconVariant } from "@/design-system/atoms/CAIcon";

export const menuItemIconProps = {
  boxSize: 4,
  variant: "default" as CAIconVariant,
};

export const FilterCardPrimaryMenu = ({
  onAddClick,
  onDeleteClick,
}: {
  onAddClick: () => void;
  onDeleteClick: () => void;
}) => {
  return (
    <Menu placement="right-start" isLazy closeOnSelect>
      <MenuButton
        as={IconButton}
        aria-label="filter-parent-menu"
        size="xs"
        icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
        variant="ghost"
        onClick={(e) => e.stopPropagation()}
      />
      <MenuList zIndex="popover" minW="10rem">
        <MenuItem
          icon={<CAIcon as={IoAddCircleOutline} {...menuItemIconProps} />}
          onClick={(e) => {
            e.stopPropagation();
            onAddClick();
          }}
        >
          Add
        </MenuItem>
        <MenuItem
          icon={<CAIcon as={IoTrashOutline} {...menuItemIconProps} />}
          onClick={(e) => {
            e.stopPropagation();
            onDeleteClick();
          }}
        >
          Delete All
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
