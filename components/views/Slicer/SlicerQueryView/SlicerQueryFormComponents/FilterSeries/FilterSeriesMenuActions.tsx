import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON>u, <PERSON><PERSON><PERSON><PERSON>on, <PERSON>u<PERSON><PERSON>, MenuList } from "@chakra-ui/react";
import { IoCopyOutline, IoEllipsisVerticalOutline, IoTrashOutline } from "react-icons/io5";
import { MdOutlineEditNote } from "react-icons/md";
import CAIcon from "@/design-system/atoms/CAIcon";
import { menuItemIconProps } from "./FilterCardPrimaryMenu";

interface FilterSeriesMenuActionsProps {
  onDuplicateClick: React.MouseEventHandler<HTMLButtonElement>;
  onFiltersClick: React.MouseEventHandler<HTMLButtonElement>;
  onDeleteClick: React.MouseEventHandler<HTMLButtonElement>;
  isDisabled: boolean;
}

export const FilterSeriesMenuActions = ({
  onDuplicateClick,
  onFiltersClick,
  onDeleteClick,
  isDisabled,
}: FilterSeriesMenuActionsProps) => {
  return (
    <Menu placement="right-start" isLazy closeOnSelect>
      <MenuButton
        as={IconButton}
        aria-label="filter-menu"
        size="xs"
        icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
        variant="ghost"
      />
      <MenuList zIndex="popover" minW="10rem">
        <MenuItem
          isDisabled={isDisabled}
          icon={<CAIcon as={MdOutlineEditNote} {...menuItemIconProps} />}
          onClick={onFiltersClick}
        >
          Modify
        </MenuItem>
        <MenuItem icon={<CAIcon as={IoCopyOutline} {...menuItemIconProps} />} onClick={onDuplicateClick}>
          Duplicate
        </MenuItem>
        <MenuItem icon={<CAIcon as={IoTrashOutline} {...menuItemIconProps} />} onClick={onDeleteClick}>
          Delete
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
