import React from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import { Box, HStack } from "@chakra-ui/layout";
import { Flex } from "@chakra-ui/react";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { CA_Mastr_Models_v1_0_Models_ConfigurationType, CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";
import {
  ConfigOptionsType,
  getConfigByDimensionValue,
  getDefaultValuesBasedOnOperator,
  getOperator,
  normalizeSlicerConfigOptions,
} from "@/utils/helpers/slicer";
import { OperatorsType, SelectedDimensionObjectType, TSlicerQueryForm } from "@/types/slicer";
import { getOperators } from "@/constants/slicer";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import SlicerStepSizeInput from "./SlicerStepSizeInput";
import SlicerOperatorInput from "./SlicerOperatorInput";
import SlicerVectorInput from "./SlicerVectorInput/SlicerVectorInput";
import SlicerRangeInput from "./SlicerRangeInput";

interface SlicerGroupRowInputProps {
  dimensionOptions: ConfigOptionsType;
  appendNewRow: () => void;
  inputIndex: number;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

export const PREPAYS_COLUMNS = ["CPR", "CDR", "CRR", "TRACKING_CPR", "TRACKING_CDR", "TRACKING_CRR"];
export const ALL_PREPAY_COLUMN_VALUES = ["1m", "3m", "6m", "12m", "24m"];

const SlicerGroupRowInput: React.FC<SlicerGroupRowInputProps> = ({
  dimensionOptions,
  appendNewRow,
  inputIndex,
  formMethods,
}: SlicerGroupRowInputProps) => {
  const {
    state: { groupMode },
  } = useSlicerModule();

  const { unregister, control, setValue, getValues } = formMethods;

  const [selectedDimension, setSelectedDimension] = React.useState<SelectedDimensionObjectType>(
    {} as SelectedDimensionObjectType
  );
  const [selectedOperator, setSelectedOperator] = React.useState<OperatorsType | null>(null);

  const showAdvancedFields = selectedDimension.step_size && groupMode === "ADVANCED";

  const [watchFilterSets, groups, watchQueryType] = useWatch({
    control,
    name: ["filter_sets", "groups", "query_type"],
  });

  const selectedAgenciesArr = watchFilterSets
    ?.flatMap((series) => series.conditions)
    ?.filter((x) => x?.dimension === "AGENCY")
    ?.flatMap((x) => (x?.operator === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF ? x?.list_values : x?.values))
    ?.filter((x): x is string => !!x);

  const normalizedDimensionOptions = normalizeSlicerConfigOptions(
    dimensionOptions,
    selectedAgenciesArr,
    CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
    watchQueryType
  );

  // Set default values on load
  React.useEffect(() => {
    const dimensionConfig = getValues(`groups.${inputIndex}`) ?? {};

    const dimension = dimensionConfig?.dimension;
    if (dimension) {
      const dimensionConfigObj = getConfigByDimensionValue(dimension, dimensionOptions) ?? {};
      setSelectedDimension(dimensionConfigObj);

      const operator = dimensionConfig.operator;
      const allowedOperators =
        getOperators(dimensionConfigObj.type, {
          isVector: !!dimensionConfigObj?.default_vector_values,
        }) ?? [];
      if (operator) {
        setSelectedOperator(allowedOperators.find((o: OperatorsType) => o.value === operator) ?? null);
      }
    }

    validateRow();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // VALIDATORS
  const validateRow = () => {
    // get all rows
    const rows = [...(getValues("groups") ?? [])];
    const lastRow = rows.pop();

    const isLastRowValid = !!lastRow?.dimension;

    if (isLastRowValid) {
      appendNewRow();
    }
  };

  // HANDLERS
  const onDimensionChange = (value: SelectedDimensionObjectType) => {
    setSelectedDimension(value);
    validateRow();

    if (value.step_size) {
      setValue(`groups.${inputIndex}.step_size`, value.step_size);

      // SET OPERATOR
      const defaultOperator = getOperator(value);
      setSelectedOperator(defaultOperator);
      setValue(`groups.${inputIndex}.operator`, defaultOperator?.value);
      setValue(`groups.${inputIndex}.agencies`, value.agencies);
      // SET DEFAULT VALUE
      const defaultValues = getDefaultValuesBasedOnOperator(defaultOperator, value) ?? [];
      if (defaultValues.length === 1) {
        setValue(`groups.${inputIndex}.values.0`, defaultValues[0] as string);
      } else {
        setValue(`groups.${inputIndex}.values`, defaultValues as string[]);
      }
      setValue(`groups.${inputIndex}.values`, [...defaultValues] as string[]);
    } else {
      unregister(`groups.${inputIndex}.step_size`);
      unregister(`groups.${inputIndex}.values`);
      unregister(`groups.${inputIndex}.operator`);
    }

    if (value.dimension === "FACTORDATE") {
      const formValues = getValues();
      const colsWithModifiedValuesOfPrepays = formValues.columns?.map((col) => {
        if (col.dimension && PREPAYS_COLUMNS.includes(col.dimension)) {
          return {
            ...col,
            values: ALL_PREPAY_COLUMN_VALUES,
          };
        }
        return col;
      });
      setValue("columns", colsWithModifiedValuesOfPrepays);
    }
  };

  const onOperatorChange = (operator: OperatorsType) => {
    unregister(`groups.${inputIndex}.values`);
    setSelectedOperator(operator);

    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST_LIKE) {
      return;
    }

    const defaultValues = getDefaultValuesBasedOnOperator(operator, selectedDimension) ?? [];
    if (selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE) {
      if (operator.value === CA_Mastr_Models_v1_0_Models_Operator.VECTOR) {
        setValue(`groups.${inputIndex}.values`, defaultValues as string[]);
        setValue(`groups.${inputIndex}.vector_steps`, selectedDimension.default_vector_steps);
        setValue(`groups.${inputIndex}.vector_inclusion`, selectedDimension.default_vector_inclusion);

        return;
      }

      if (defaultValues.length === 1) {
        setTimeout(() => {
          setValue(`groups.${inputIndex}.values.0`, defaultValues[0] as string);
        }, 0);
      } else {
        setValue(`groups.${inputIndex}.values`, defaultValues as string[]);
      }
    }
  };

  return (
    <HStack w="full" justifyContent="center" alignItems="center">
      <Box flex={1}>
        <Controller
          control={control}
          name={`groups.${inputIndex}.dimension`}
          render={({ field: { name, value, onChange } }) => (
            <CAMultiSelectDropdown
              value={getConfigByDimensionValue(value, dimensionOptions) ?? {}}
              name={name}
              shouldOnlyReturnValue={false}
              isMultiSelect={false}
              tooltipText={selectedDimension.display_name ?? ""}
              isDisabled={selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY}
              options={normalizedDimensionOptions}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              onChange={(selectedOption: any) => {
                onChange(selectedOption.dimension);
                onDimensionChange(selectedOption);
              }}
              isOptionDisabled={(option) => !!groups?.some((el) => el.dimension === option.dimension)}
            />
          )}
        />
      </Box>

      {showAdvancedFields && (
        <Box>
          <SlicerOperatorInput
            registerFieldPath={`groups.${inputIndex}`}
            selectedOperator={selectedOperator}
            setSelectedOperator={onOperatorChange}
            selectedDimension={selectedDimension}
            formMethods={formMethods}
            excludeInclusiveOperators
          />
        </Box>
      )}

      {showAdvancedFields &&
        selectedDimension?.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.RANGE &&
        selectedOperator?.value !== CA_Mastr_Models_v1_0_Models_Operator.VECTOR && (
          <Box flex={1}>
            <Flex alignItems="center" w="full">
              <SlicerRangeInput
                registerFieldPath={`groups.${inputIndex}`}
                selectedDimension={selectedDimension}
                validateRowHandler={validateRow}
                operator={selectedOperator?.value}
                formMethods={formMethods}
              />
            </Flex>
          </Box>
        )}

      {selectedOperator?.value === CA_Mastr_Models_v1_0_Models_Operator.VECTOR && showAdvancedFields && (
        <Box flex={1}>
          <Flex alignItems="center" w="full">
            <SlicerVectorInput
              selectedDimension={selectedDimension}
              registerFieldPath={`groups.${inputIndex}`}
              formMethods={formMethods}
            />
          </Flex>
        </Box>
      )}

      {(showAdvancedFields
        ? selectedDimension.step_size &&
          selectedOperator?.value !== CA_Mastr_Models_v1_0_Models_Operator.VECTOR &&
          selectedOperator?.value !== CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO
        : selectedDimension.step_size) && (
        <SlicerStepSizeInput
          control={control}
          setValue={setValue}
          selectedDimension={selectedDimension}
          registerFieldPath={`groups.${inputIndex}`}
        />
      )}
    </HStack>
  );
};

export default SlicerGroupRowInput;
