import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Icon<PERSON>utton,
  <PERSON>u,
  <PERSON>uButton,
  MenuItem,
  MenuList,
  <PERSON><PERSON>,
  <PERSON>overB<PERSON>,
  <PERSON>over<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>over<PERSON>rigger,
  Portal,
  Spacer,
  Text,
  Tooltip,
  VStack,
  chakra,
  useBreakpointValue,
  useDisclosure,
} from "@chakra-ui/react";
import { IoAddSharp, IoEllipsisVerticalOutline, IoSyncCircleOutline } from "react-icons/io5";
import { UseFormReturn, useFieldArray, useForm } from "react-hook-form";
import CAInput from "@/design-system/molecules/CAInput";
import CAIcon from "@/design-system/atoms/CAIcon";
import { showWarningToast } from "@/design-system/theme/toast";
import { CA_Mastr_Models_v1_0_Models_Slicer_Group, CA_Mastr_Models_v1_0_Models_VectorInclusion } from "@/utils/openapi";
import { TSlicerQueryForm } from "@/types/slicer";
import { SelectedDimensionObjectType } from "../SlicerOperatorInput";
import VectorRow from "./SlicerVectorRow";

interface TSlicerVectorInputProps {
  selectedDimension: SelectedDimensionObjectType;
  registerFieldPath: `groups.${number}`;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

export interface TSlicerVectorInput {
  vectors: {
    value: number;
    stepSize: number | null | undefined;
  }[];
  vector_inclusion: CA_Mastr_Models_v1_0_Models_VectorInclusion | undefined;
  newValue: number | null;
}

const getDefaultValues = (groupsDimensions: CA_Mastr_Models_v1_0_Models_Slicer_Group) => {
  return {
    vectors:
      groupsDimensions.values?.map((v, i) => ({
        value: Number(v),
        stepSize: groupsDimensions?.vector_steps?.[i],
      })) ?? [],
    vector_inclusion: groupsDimensions?.vector_inclusion,
  };
};

const SlicerVectorInput = ({ selectedDimension, registerFieldPath, formMethods }: TSlicerVectorInputProps) => {
  const [showStepSizes, toggleShowStepSizes] = React.useState(false);

  const { isOpen, onToggle, onClose } = useDisclosure();

  const vectorFormMethods = useForm<TSlicerVectorInput>({
    defaultValues: getDefaultValues(formMethods.getValues(registerFieldPath)),
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const watchInclusion = vectorFormMethods.watch("vector_inclusion");

  const {
    fields: vectorFields,
    remove: removeVectorField,
    insert: insertVectorField,
    move: moveVectorFields,
    replace: replaceVectorFields,
  } = useFieldArray({
    control: vectorFormMethods.control,
    name: "vectors",
  });

  const openPopoverHandler = () => {
    onToggle();

    if (isOpen) {
      const defaultValues = formMethods.getValues(registerFieldPath);

      replaceVectorFields(
        defaultValues.values?.map((v, i) => ({
          value: Number(v),
          stepSize: defaultValues?.vector_steps?.[i],
        })) ?? []
      );

      const hasStepSizes = defaultValues?.vector_steps?.find((v) => typeof v === "number");
      if (hasStepSizes) {
        toggleShowStepSizes(true);
      }
      vectorFormMethods.setValue("vector_inclusion", defaultValues?.vector_inclusion);
    }
  };

  const addNewVector = () => {
    const newValue = vectorFormMethods.getValues("newValue");

    if (!newValue) {
      vectorFormMethods.setFocus("newValue");
      return;
    }

    const hasDuplicate = vectorFields?.some((vector) => vector.value === newValue);
    if (hasDuplicate) {
      showWarningToast("Duplicate Entry", `Value you are trying to add has already been added.`);
      return;
    }

    const isOutsideRange =
      (selectedDimension?.lower_bound && newValue < selectedDimension?.lower_bound) ||
      (selectedDimension?.upper_bound && newValue > selectedDimension?.upper_bound);

    if (isOutsideRange) {
      showWarningToast(
        "Out of range value",
        `Please enter a value between the range of ${selectedDimension?.lower_bound} and ${selectedDimension?.upper_bound} `
      );
      return;
    }

    const values = vectorFields.map((v) => v.value);
    let index = values.findIndex((v) => v > newValue);

    if (index === -1) {
      const minValue = Math.min(...values);
      if (newValue < minValue) {
        index = 0;
      } else {
        index = values.length + 1;
      }
    }

    insertVectorField(
      index,
      {
        value: newValue,
        stepSize: null,
      },
      { shouldFocus: false }
    );

    vectorFormMethods.setValue("newValue", null);
  };

  const resetVectors = () => {
    const hasStepSizes = selectedDimension?.default_vector_steps?.find((v) => typeof v === "number");
    toggleShowStepSizes(!!hasStepSizes);

    replaceVectorFields(
      selectedDimension?.default_vector_values?.map((v, i) => ({
        value: Number(v),
        stepSize: selectedDimension?.default_vector_steps?.[i],
      })) ?? []
    );
    vectorFormMethods.setValue("vector_inclusion", selectedDimension?.default_vector_inclusion);
  };

  const applyVectors = (data: TSlicerVectorInput) => {
    const vectors = data.vectors;

    formMethods.setValue(
      `${registerFieldPath}.values`,
      vectors.map((v) => v.value?.toString()),
      {
        shouldDirty: true,
      }
    );
    formMethods.setValue(
      `${registerFieldPath}.vector_steps`,
      showStepSizes ? vectors.map((v) => v.stepSize ?? null).slice(0, -1) : []
    );
    formMethods.setValue(`${registerFieldPath}.vector_inclusion`, data?.vector_inclusion);
    onClose();
  };

  const getDisplayVectorValue = (tooltip = false) => {
    const watchValues = formMethods.getValues(registerFieldPath);
    const openingBracket = watchInclusion === CA_Mastr_Models_v1_0_Models_VectorInclusion.LEFT ? "[" : "(";
    const closingBracket = watchInclusion === CA_Mastr_Models_v1_0_Models_VectorInclusion.LEFT ? ")" : "]";

    const formattedValue = watchValues.values?.map((value, index, values) => {
      if (index === 0) {
        return `(Min - ${value}${closingBracket}\n`;
      }

      let range = `${values[index - 1]} - ${value}`;

      if (watchValues?.vector_steps?.[index - 1]) {
        range += `, ${watchValues?.vector_steps?.[index - 1]}`;
      }

      return `${openingBracket}${range}${closingBracket}\n`;
    });
    formattedValue?.push(`${`${openingBracket}${watchValues.values?.at(-1)} - Max)`}`);

    return formattedValue?.join(tooltip ? "" : " ");
  };

  return (
    <Popover
      isOpen={isOpen}
      onClose={onClose}
      placement={useBreakpointValue({ base: "bottom", md: "right" })}
      variant="card"
    >
      <PopoverTrigger>
        <Box w="full">
          <Tooltip label={<chakra.pre overflow="hidden">{getDisplayVectorValue(true)}</chakra.pre>}>
            <CAInput
              name={`${registerFieldPath}.values`}
              value={getDisplayVectorValue()}
              height="30px"
              clickOnly
              onClick={openPopoverHandler}
            />
          </Tooltip>
        </Box>
      </PopoverTrigger>

      <Portal>
        <PopoverContent minW="250px" maxW="min-content">
          <chakra.form onSubmit={vectorFormMethods.handleSubmit(applyVectors)}>
            <PopoverHeader>
              <HStack justifyContent="space-between">
                <Text fontSize="lg" fontWeight="bold" variant="tableHead">
                  Vector
                </Text>
                <Menu placement="right-start" isLazy closeOnSelect>
                  <MenuButton
                    as={IconButton}
                    aria-label="vector-options-menu"
                    size="xs"
                    icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={5} variant="secondary" />}
                    variant="ghost"
                  />
                  <MenuList zIndex="popover" minW="10rem">
                    <MenuItem
                      onClick={() => {
                        vectorFormMethods.setValue(
                          "vector_inclusion",
                          watchInclusion === CA_Mastr_Models_v1_0_Models_VectorInclusion.RIGHT
                            ? CA_Mastr_Models_v1_0_Models_VectorInclusion.LEFT
                            : CA_Mastr_Models_v1_0_Models_VectorInclusion.RIGHT
                        );
                      }}
                    >
                      {watchInclusion === CA_Mastr_Models_v1_0_Models_VectorInclusion.RIGHT
                        ? "Include Left Endpoints [ , )"
                        : "Include Right Endpoints ( , ]"}
                    </MenuItem>
                    <MenuItem
                      onClick={() => {
                        toggleShowStepSizes(!showStepSizes);
                      }}
                    >
                      {showStepSizes ? "Hide Step Sizes" : "Show Step Sizes"}
                    </MenuItem>
                  </MenuList>
                </Menu>
              </HStack>
            </PopoverHeader>
            <PopoverBody p={5} maxH="70vh" overflowY="auto">
              <VStack mb={5} spacing={1} alignItems="flex-start">
                <HStack>
                  <Text variant="secondary" fontSize="md" whiteSpace="nowrap">
                    {selectedDimension.display_name}
                  </Text>
                </HStack>
                <HStack>
                  <Text variant="primary">Lower Bound:</Text>
                  <Text variant="secondary">{selectedDimension.lower_bound}</Text>
                </HStack>
                <HStack>
                  <Text variant="primary">Upper Bound:</Text>
                  <Text variant="secondary">{selectedDimension.upper_bound}</Text>
                </HStack>
              </VStack>
              <HStack spacing={"7rem"} mb={5}>
                <Text variant="tableLeft">Range</Text>
                {showStepSizes && <Text variant="tableLeft">Step Size</Text>}
              </HStack>

              <VectorRow
                index={0}
                isFirstRow
                isLeftInclusion={watchInclusion === CA_Mastr_Models_v1_0_Models_VectorInclusion.LEFT}
                showStepSizes={showStepSizes}
                vectorFormMethods={vectorFormMethods}
                selectedDimension={selectedDimension}
              />

              {vectorFields.map((vector, valueIndex) => (
                <VectorRow
                  key={vector.id}
                  index={valueIndex}
                  isLeftInclusion={watchInclusion === CA_Mastr_Models_v1_0_Models_VectorInclusion.LEFT}
                  showStepSizes={showStepSizes}
                  vectorFormMethods={vectorFormMethods}
                  moveVectorFields={moveVectorFields}
                  removeVectorField={removeVectorField}
                  selectedDimension={selectedDimension}
                />
              ))}

              <HStack mt={4} ml="10px" spacing={0} width="85px">
                <CAInput
                  {...vectorFormMethods.register("newValue", { valueAsNumber: true })}
                  type="number"
                  step="any"
                  borderBottomRightRadius={0}
                  borderTopRightRadius={0}
                  onKeyDown={(e) => {
                    if (e.code === "Enter") {
                      addNewVector();
                      e.preventDefault();
                    }
                  }}
                />
                <IconButton
                  h="26px"
                  onClick={addNewVector}
                  aria-label="ADD"
                  size="xs"
                  fontSize="xs"
                  variant="primary"
                  fontWeight="bold"
                  borderTopLeftRadius={0}
                  borderBottomLeftRadius={0}
                  icon={<CAIcon as={IoAddSharp} variant="default" />}
                />
              </HStack>
            </PopoverBody>
            <PopoverFooter>
              <HStack justifyContent="space-between">
                <IconButton
                  icon={<IoSyncCircleOutline />}
                  onClick={resetVectors}
                  size="sm"
                  fontSize="3xl"
                  variant="secondary"
                  title="Reset"
                  aria-label="Reset"
                />
                <Spacer />
                <HStack spacing={2}>
                  <Button variant="secondary" size="sm" type="button" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button variant="primary" size="sm" type="submit">
                    Apply
                  </Button>
                </HStack>
              </HStack>
            </PopoverFooter>
          </chakra.form>
        </PopoverContent>
      </Portal>
    </Popover>
  );
};

export default SlicerVectorInput;
