import { Box, HStack, Icon<PERSON>utton, useColorModeValue } from "@chakra-ui/react";
import { UseFieldArrayMove, UseFieldArrayRemove, UseFormReturn } from "react-hook-form";
import { IoTrashOutline } from "react-icons/io5";
import { showWarningToast } from "@/design-system/theme/toast";
import CAInput from "@/design-system/molecules/CAInput";
import CAIcon from "@/design-system/atoms/CAIcon";
import colors from "@/design-system/theme/colors";
import { SelectedDimensionObjectType } from "../SlicerOperatorInput";
import { TSlicerVectorInput } from "./SlicerVectorInput";

interface TVectorRowProps {
  index: number;
  isLeftInclusion: boolean;
  isFirstRow?: boolean;
  showStepSizes: boolean;
  vectorFormMethods: UseFormReturn<TSlicerVectorInput, object>;
  moveVectorFields?: UseFieldArrayMove;
  removeVectorField?: UseFieldArrayRemove;
  selectedDimension: SelectedDimensionObjectType;
}

const VectorRow = ({
  index,
  isFirstRow,
  isLeftInclusion,
  showStepSizes,
  vectorFormMethods,
  moveVectorFields,
  removeVectorField,
  selectedDimension,
}: TVectorRowProps) => {
  const { watch, register } = vectorFormMethods;
  const alternateRowColor = useColorModeValue(colors.misty[300], colors.celloBlue[900]);

  const vectors = watch("vectors");
  const isLastRow = index === vectors.length - 1;

  if (isFirstRow) {
    return (
      <HStack maxW="120px">
        <Box>(</Box>
        <Box minW="60px" maxW="60px">
          Min
        </Box>
        <Box>, </Box>
        <Box minW="25px">{vectors[0].value?.toString()}</Box>
        <Box>{isLeftInclusion ? ")" : "]"} </Box>
      </HStack>
    );
  }

  const handleBlur = (e: React.FocusEvent<HTMLInputElement, Element>) => {
    const value = e.target.valueAsNumber;
    const isOutsideRange =
      (selectedDimension.lower_bound && value < selectedDimension.lower_bound) ||
      (selectedDimension.upper_bound && value > selectedDimension.upper_bound);
    if (isOutsideRange) {
      showWarningToast(
        "Out of range value",
        `Please enter a value between the range of ${selectedDimension.lower_bound} and ${selectedDimension.upper_bound} `
      );
      const prevValue = vectorFormMethods.getValues(`vectors.${index}.value`);
      vectorFormMethods.setValue(`vectors.${index}.value`, prevValue);
    } else {
      vectorFormMethods.setValue(`vectors.${index}.value`, value);
    }

    const updatedVectors = vectors.map((v) => v.value).sort((a, b) => (a > b ? 1 : -1));
    const updatedIndex = updatedVectors.findIndex((v) => v === e.target.valueAsNumber);
    moveVectorFields?.(index, updatedIndex);
  };

  return (
    <HStack
      my={1}
      role="group"
      alignItems="center"
      bg={index % 2 === 0 ? alternateRowColor : "inherit"}
      // For full bleed background. Ref - https://www.youtube.com/shorts/81pnuZFarRw
      boxShadow={index % 2 === 0 ? `0 0 0 2vmax ${alternateRowColor}` : ""}
      clipPath="inset(0 -2vmax)"
    >
      <Box>{isLeftInclusion ? "[" : "("}</Box>
      <Box minW="60px" maxW="60px">
        <CAInput
          type="number"
          step="any"
          {...register(`vectors.${index}.value`, {
            valueAsNumber: true,
          })}
          // [INFO] Overriding the RHF onChange as we need access to the previous value on blur to check range
          onChange={undefined}
          onBlur={handleBlur}
        />
      </Box>
      <Box>, </Box>
      <Box minW="25px">{isLastRow ? "Max" : vectors[index + 1].value.toString()}</Box>
      <Box>{isLastRow ? ")" : isLeftInclusion ? ")" : "]"} </Box>

      {showStepSizes && !isFirstRow && !isLastRow && (
        <Box pl={4} minW="60px" maxW="60px">
          <CAInput
            type="number"
            step="any"
            placeholder={Number((vectors[index + 1].value - vectors[index].value)?.toFixed(2))?.toString()}
            {...register(`vectors.${index}.stepSize`, { valueAsNumber: true })}
          />
        </Box>
      )}

      <IconButton
        visibility="hidden"
        _groupHover={{ visibility: "visible" }}
        aria-label="remove-vector"
        size="xs"
        variant="unstyled"
        icon={<CAIcon as={IoTrashOutline} variant="secondary" boxSize={4} />}
        onClick={() => removeVectorField?.(index)}
      />
    </HStack>
  );
};

export default VectorRow;
