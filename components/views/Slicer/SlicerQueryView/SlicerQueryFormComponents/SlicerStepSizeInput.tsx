import React from "react";
import { Box } from "@chakra-ui/layout";
import { Control, Controller, UseFormSetValue } from "react-hook-form";
import CANumberInput from "@/design-system/molecules/CANumberInput";
import { SelectedDimensionObjectType } from "./SlicerOperatorInput";

interface SlicerStepSizeInputProps {
  selectedDimension: SelectedDimensionObjectType;
  registerFieldPath: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setValue: UseFormSetValue<any>;
}

const SlicerStepSizeInput: React.FC<SlicerStepSizeInputProps> = ({
  selectedDimension,
  registerFieldPath,
  control,
  setValue,
}) => {
  const minStepSize = selectedDimension?.min_step_size ?? 0.1;

  const onBlurHandler = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = +e.target.value;

    if (selectedDimension?.min_step_size && value < minStepSize) {
      setValue(`${registerFieldPath}.step_size`, minStepSize);
    } else {
      setValue(`${registerFieldPath}.step_size`, parseFloat(value.toFixed(3)));
    }
  };

  return (
    <Box maxW="3.75rem">
      <Controller
        control={control}
        name={`${registerFieldPath}.step_size`}
        render={({ field: { name, value, onChange } }) => {
          return (
            <CANumberInput
              min={minStepSize}
              tooltip="Enter step size"
              inputFieldHeight="30px"
              name={name}
              value={value}
              step={selectedDimension?.step_size ?? undefined}
              clampValueOnBlur={false}
              onChange={(valueAsString) => {
                onChange({
                  target: {
                    value: valueAsString,
                  },
                });
              }}
              onBlur={onBlurHandler}
            />
          );
        }}
      />
    </Box>
  );
};

export default SlicerStepSizeInput;
