import React from "react";
import { UseFormReturn, get } from "react-hook-form";
import { SelectedDimensionObjectType, SlicerConfigFormat, TSlicerQueryForm } from "@/types/slicer";
import CAInput from "@/design-system/molecules/CAInput";

interface SlicerLookBackInputProps {
  selectedDimension: SelectedDimensionObjectType;
  registerFieldPath: `filter_sets.${number}.conditions.${number}`;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

export const SlicerLookBackInput: React.FC<SlicerLookBackInputProps> = ({
  selectedDimension,
  registerFieldPath,
  formMethods,
}) => {
  const {
    register,
    formState: { errors },
  } = formMethods;
  const lookbackUpperBound = selectedDimension.format === SlicerConfigFormat.YEAR ? 30 : 360;
  const lookbackUnit = selectedDimension.format === SlicerConfigFormat.YEAR ? "year" : "month";
  const lookbackTooltip =
    selectedDimension.format === SlicerConfigFormat.YEAR
      ? "Enter number of years to look back."
      : "Enter number of months to look back.";

  return (
    <CAInput
      type="number"
      step="any"
      max={lookbackUpperBound}
      min={1}
      title={lookbackTooltip}
      border={1}
      {...register(`${registerFieldPath}.values.0`, {
        max: {
          value: lookbackUpperBound,
          message: `Please limit the look back to ${lookbackUpperBound} ${lookbackUnit}(s).`,
        },
        min: {
          value: 1,
          message: `Please specify the number of ${lookbackUnit}s to look back.`,
        },
      })}
      info={{
        show: !!get(errors, `${registerFieldPath}.values.0.message`),
        msg1: get(errors, `${registerFieldPath}.values.0.message`),
      }}
      height="30px"
    />
  );
};
