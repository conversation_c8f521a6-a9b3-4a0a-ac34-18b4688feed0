import React from "react";
import {
  Box,
  Button,
  Checkbox,
  Grid,
  GridItem,
  HStack,
  List,
  ListItem,
  ModalBody,
  ModalFooter,
  Stack,
  Text,
  Tooltip,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import { debounce } from "lodash";
import CASearch from "@/design-system/molecules/CASearch";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute,
  CA_Mastr_Models_v1_0_Models_QueryType,
} from "@/utils/openapi";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { UNIQUE_SEPERATOR, useNestedCheckbox } from "@/hooks/useNestedCheckbox";
import { normalizeColumnsForSelectionModal } from "@/utils/helpers/slicer";
import { SelectionTooltip } from "../SelectionTooltip";

export type ColumnSelectionOptionType = {
  label: string;
  value?: string;
} & CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute;

export const SlicerSelectionForm = ({
  defaultSelectedColumns,
  onModalClose,
  toggleIsOpen,
  columnList,
  queryType,
}: {
  defaultSelectedColumns: string[];
  onModalClose: (columns: string[]) => void;
  toggleIsOpen: (isOpen: boolean) => void;
  columnList: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute[];
  queryType: CA_Mastr_Models_v1_0_Models_QueryType;
}) => {
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();
  const {
    state: { checkedItems },
    action: { onChildChange, onParentChange },
  } = useNestedCheckbox({
    defaultValues: defaultSelectedColumns,
  });

  const [agency, setAgency] = React.useState("FNM");
  const [search, setSearch] = React.useState("");

  const columnCount: { [key: string]: number } | undefined = useBreakpointValue({
    sm: { "Pool Level": agency === "GPL" ? 1 : 1, "Loan Level": 1 },
    md: { "Pool Level": agency === "GPL" ? 2 : 1, "Loan Level": 2 },
    lg: { "Pool Level": agency === "GPL" ? 3 : 1, "Loan Level": 3 },
    xl: { "Pool Level": agency === "GPL" ? 4 : 1, "Loan Level": 3 },
  });

  const filteredColumns = React.useMemo(() => {
    return normalizeColumnsForSelectionModal(columnList ?? [], [agency], queryType);
  }, [columnList, agency, queryType]);

  const toggleButtonItems =
    loan_level_slicer_config?.data_set?.asset_types?.map((asset) => ({ label: asset, value: asset })) ?? [];

  const onSubmit = () => {
    onModalClose(checkedItems);
    toggleIsOpen(false);
  };

  const displayCategory = (category: string, level: string) => {
    const matchesAnyChild = filteredColumns[level]
      ?.find((c) => c.category === category)
      ?.options?.find((option) => option?.display_name?.toLowerCase().includes(search.toLowerCase()));
    return matchesAnyChild ? "block" : "none";
  };

  const displayField = (groupLabel: string) => {
    if (!search) return "inherit";
    return groupLabel.toLowerCase().includes(search.toLowerCase()) ? "inherit" : "none";
  };

  return (
    <>
      <ModalBody backgroundColor={useColorModeValue("white", "celloBlue.1000")}>
        <HStack alignItems="center" justifyContent="space-between" wrap="wrap" rowGap={4} my={3}>
          <Box minW={{ base: "13rem", md: "20rem" }}>
            <CASearch
              name="search-columns"
              placeholder={"Quick Filter"}
              onChange={debounce(({ target: { value } }) => setSearch(value.trim()), 300)}
            />
          </Box>
          <ToggleButtonGroup selectedButton={agency} onChange={setAgency} buttons={toggleButtonItems} />
        </HStack>

        <Grid templateColumns={{ base: "repeat(1, 1fr)", md: "auto auto" }} mt={2} gridGap={12} overflow="auto">
          {Object.keys(filteredColumns).map((level) => (
            <GridItem key={level} mt={6}>
              <Box p={2}>
                <Text variant="tableLeft" fontSize="xl" fontWeight="bold">
                  {level}
                </Text>
              </Box>
              <List
                css={{
                  columnCount: columnCount?.[level] || 0,
                }}
              >
                {filteredColumns[level].map(({ category, options }) => {
                  const matchesCategory = category.toLowerCase().includes(search.toLowerCase());
                  const childrens = options.map((child) => child.dimension ?? "");
                  const allChecked = childrens.every((i) => checkedItems.includes(i));
                  const isIndeterminate = childrens.some((i) => checkedItems.includes(i)) && !allChecked;

                  return (
                    <ListItem
                      key={category}
                      p={1}
                      mb={3.5}
                      display={matchesCategory ? "block" : displayCategory(category as string, level)}
                      css={{
                        breakInside: "avoid",
                      }}
                    >
                      <Checkbox
                        isChecked={allChecked}
                        isIndeterminate={isIndeterminate}
                        value={childrens.join(UNIQUE_SEPERATOR)}
                        onChange={onParentChange}
                      >
                        <Text variant="tableHead">{category.replace(/(--([\w ])*)/g, "").trim()}</Text>
                      </Checkbox>
                      <Stack pl={6} mt={1} spacing={1}>
                        {options.map((child) => {
                          const displayName = (child.display_name ?? "").match(/.+\s(\d+m($|\s.+))/i);
                          return (
                            <Checkbox
                              display={matchesCategory ? "inherit" : displayField(child.display_name as string)}
                              key={child.dimension}
                              isChecked={checkedItems.includes(child.dimension ?? "")}
                              onChange={onChildChange}
                              value={child.dimension ?? undefined}
                            >
                              <Tooltip
                                label={
                                  <SelectionTooltip agenciesSupported={child.agencies} selections={child.selections} />
                                }
                                placement="right-end"
                              >
                                <Text variant="default">
                                  {displayName?.[2] || displayName?.[1] || child.display_name}
                                </Text>
                              </Tooltip>
                            </Checkbox>
                          );
                        })}
                      </Stack>
                    </ListItem>
                  );
                })}
              </List>
            </GridItem>
          ))}
        </Grid>
      </ModalBody>
      <ModalFooter gap={3}>
        <Button size="sm" variant="secondary" onClick={() => toggleIsOpen(false)}>
          Close
        </Button>
        <Button size="sm" variant="primary" onClick={onSubmit}>
          Apply
        </Button>
      </ModalFooter>
    </>
  );
};
