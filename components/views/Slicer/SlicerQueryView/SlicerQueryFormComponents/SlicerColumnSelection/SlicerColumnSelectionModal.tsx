import React from "react";
import { useColorModeValue } from "@chakra-ui/react";
import CAModal from "@/design-system/molecules/CAModal";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute,
  CA_Mastr_Models_v1_0_Models_QueryType,
} from "@/utils/openapi";

import { SlicerSelectionForm } from "./SlicerSelectionForm";

interface SlicerColumnSelectionModalProps {
  isOpen: boolean;
  toggleIsOpen: (isOpen: boolean) => void;
  onModalClose: (columns: string[]) => void;
  selectedColumns?: string[];
  columnList: CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute[];

  queryType: CA_Mastr_Models_v1_0_Models_QueryType;
}

const SlicerColumnSelectionModal: React.FC<SlicerColumnSelectionModalProps> = ({
  isOpen,
  toggleIsOpen,
  onModalClose,
  selectedColumns: defaultSelectedColumns = [],
  columnList = [],
  queryType,
}: SlicerColumnSelectionModalProps) => {
  return (
    <CAModal
      modalBackground={useColorModeValue("celloBlue.50", "celloBlue.900")}
      contentStyle={{ minW: "95%" }}
      modalHeader="Columns"
      headerStyle={{
        color: useColorModeValue("celloBlue.500", "turmericRoot.500"),
        textTransform: "uppercase",
        py: "1rem",
        px: "1.7rem",
      }}
      modalBodyProps={{
        fontSize: "md",
        mx: "1.7rem",
        p: 0,
      }}
      showCloseIcon
      isOpen={isOpen}
      onClose={() => toggleIsOpen(false)}
    >
      <SlicerSelectionForm
        toggleIsOpen={toggleIsOpen}
        onModalClose={onModalClose}
        defaultSelectedColumns={defaultSelectedColumns}
        columnList={columnList}
        queryType={queryType}
      />
    </CAModal>
  );
};

export default SlicerColumnSelectionModal;
