import React from "react";
import { Box, HStack, Icon, Icon<PERSON>utton, useDisclosure } from "@chakra-ui/react";
import { IoTrashOutline } from "react-icons/io5";
import {
  FieldArrayWithId,
  UseFieldArrayAppend,
  UseFieldArrayRemove,
  UseFieldArrayUpdate,
  UseFormReturn,
  useFieldArray,
  useWatch,
} from "react-hook-form";
import { DragEndEvent } from "@dnd-kit/core";
import { ReorderIcon } from "@/design-system/icons";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import CACard from "@/design-system/molecules/CACard";
import { CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";
import {
  findIndexesOfNonSupportedAgencies,
  getDefaultOperatorAndValueForGroups,
  tickerSelectionFilter,
} from "@/utils/helpers/slicer";
import { CADragDrop, CADragDropContainer } from "@/design-system/molecules/CADragDrop";
import { TSlicerQueryForm } from "@/types/slicer";
import CACheckboxInput from "@/design-system/molecules/CACheckboxInput";
import { slicerSecondaryCardCommonProps } from "../../SlicerQueryDetails/helpers/SlicerChartCardPropsGetter";
import SlicerGroupRowInput, { PREPAYS_COLUMNS } from "./SlicerGroupRowInput";
import { SlicerGroupsMenuActions } from "./SlicerGroups/SlicerGroupsMenuActions";
import SlicerGroupSelectionModal from "./SlicerGroupSelectionModal";

export const MAX_GROUPS_ALLOWED = 6;

interface SlicerGroupProps {
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

const SlicerGroup = ({ formMethods }: SlicerGroupProps) => {
  const {
    state: { loan_level_slicer_config, groupMode },
    action: { setGroupMode },
  } = useSlicerModule();

  const { isOpen, onOpen, onClose } = useDisclosure();

  const isBasic = groupMode === "BASIC" || !groupMode;

  const {
    control,
    getValues,
    formState: { isDirty },
  } = formMethods;

  const {
    fields: groups,
    append,
    remove,
    move,
    update,
    replace,
  } = useFieldArray({
    control,
    name: "groups",
  });

  const watchFilterSets = useWatch({ control, name: "filter_sets" });
  const queryType = React.useMemo(() => getValues("query_type"), [getValues]);

  const selectedAgencies = React.useMemo(
    () =>
      (watchFilterSets ?? [])
        .map((x) =>
          x?.conditions?.[0]?.operator === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF
            ? x?.conditions?.[0]?.list_values
            : x?.conditions?.[0]?.values
        )
        .flat()
        .filter((x): x is string => typeof x === "string"),
    [watchFilterSets]
  );

  React.useEffect(() => {
    if (isDirty && selectedAgencies.length) {
      const uniqueAgencies = [...new Set(selectedAgencies)];
      const groupsFromConfig = loan_level_slicer_config?.group_by;
      const groupsWithAgenciesSupported = groupsFromConfig?.length
        ? groups?.map((condition) => groupsFromConfig.find((gr) => gr.dimension === condition?.dimension))
        : [];

      const fieldIndexes = findIndexesOfNonSupportedAgencies(groupsWithAgenciesSupported, uniqueAgencies).filter(
        Boolean
      );

      if (fieldIndexes.length && uniqueAgencies.length) {
        remove(fieldIndexes);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(selectedAgencies)]);

  React.useEffect(() => {
    if (groups.length === 0) {
      append({ dimension: "", step_size: undefined, enabled: true });
    } else if (groups.length < MAX_GROUPS_ALLOWED) {
      const allGroups = getValues("groups") ?? [];
      const lastRow = allGroups?.length === 1 ? allGroups[0] : allGroups[allGroups.length - 1];
      if (lastRow?.dimension) {
        append({ dimension: "", step_size: undefined, enabled: true });
      }
    }
  }, [append, getValues, groups.length]);

  // validators

  const canDrag = (index: number) => {
    const allGroups = getValues("groups") ?? [];
    const lastRow = allGroups.length === 1 ? allGroups[0] : allGroups[allGroups.length - 1];
    const isLastRowEmpty = !lastRow.dimension;
    return !(index === groups.length - 1 && isLastRowEmpty);
  };

  const dragColumnHandler = (event: DragEndEvent) => {
    const source = event.active.data.current?.sortable.index;
    const destination = event.over?.data.current?.sortable.index;
    if (!destination?.toString()) {
      return;
    }

    const allGroups = getValues("groups") ?? [];
    const lastRow = allGroups.length === 1 ? allGroups[0] : allGroups[allGroups.length - 1];
    const isLastRowEmpty = !lastRow.dimension;
    if (!(destination === groups.length - 1 && isLastRowEmpty)) {
      move(source, destination);
    }
  };

  const updateGroupMode = () => {
    setGroupMode(isBasic ? "ADVANCED" : "BASIC");

    const groups = getValues("groups") ?? [];
    // update values, fill in default values where they are missing
    const updatedGroups = groups.map((group) => {
      if (group.step_size && !group.operator) {
        const config = loan_level_slicer_config?.group_by?.find(
          (groupConfig) => groupConfig.dimension === group.dimension
        );
        return {
          ...group,
          operator: CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE,
          values: [
            (config?.default_lower_bound ?? config?.lower_bound)?.toString() ?? "",
            (config?.default_upper_bound ?? config?.upper_bound)?.toString() ?? "",
          ],
        };
      }
      return group;
    });
    replace(updatedGroups);
  };

  const onSelectionModalClose = (
    normalizedSelections: string[] | undefined,
    selections: Record<string, string[]> | undefined
  ) => {
    if (!normalizedSelections || !selections) {
      onClose();
      return;
    }

    const newAddedGroups = normalizedSelections.map((selection) => {
      const existingGroup = groups.find((c) => c.dimension === selection);
      if (existingGroup) {
        return existingGroup;
      }
      return {
        dimension: selection,
        enabled: true,
        ...getDefaultOperatorAndValueForGroups(loan_level_slicer_config?.group_by, selection),
      };
    });

    replace(newAddedGroups);
    onClose();
  };

  return (
    <>
      <CACard
        title="Groups"
        {...slicerSecondaryCardCommonProps}
        cardBodyStyle={{ p: 0, pb: 4 }}
        headingRight={
          <SlicerGroupsMenuActions
            isBasic={isBasic}
            onModeChange={updateGroupMode}
            onDeleteAllClick={remove}
            openGroupModal={onOpen}
          />
        }
      >
        <CADragDropContainer onDragEnd={dragColumnHandler} items={groups.map((el) => el.id)}>
          {groups.map((column, index) => {
            return (
              <HStack key={column.id} role="group" mr="0.5rem">
                <CADragDrop
                  id={column?.id?.toString()}
                  activatorElement={
                    <Box w="0.8rem" cursor="move">
                      <Icon as={ReorderIcon} />
                    </Box>
                  }
                  activatorElemStyle={{
                    visibility: "hidden",
                    _groupHover: { visibility: canDrag(index) ? "visible" : "hidden" },
                  }}
                  boxStyle={{
                    display: "flex",
                    w: "full",
                  }}
                >
                  <GroupRow
                    idx={index}
                    formMethods={formMethods}
                    append={append}
                    update={update}
                    remove={remove}
                    column={column}
                  />
                </CADragDrop>
              </HStack>
            );
          })}
        </CADragDropContainer>
      </CACard>

      {isOpen && (
        <SlicerGroupSelectionModal
          groups={groups}
          onModalClose={onSelectionModalClose}
          selectedAgencies={selectedAgencies}
          queryType={queryType}
        />
      )}
    </>
  );
};

export default SlicerGroup;

const GroupRow = ({
  formMethods,
  idx,
  append,
  update,
  remove,
  column,
}: {
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
  idx: number;
  append: UseFieldArrayAppend<TSlicerQueryForm, "groups">;
  update: UseFieldArrayUpdate<TSlicerQueryForm, "groups">;
  remove: UseFieldArrayRemove;
  column: FieldArrayWithId<TSlicerQueryForm, "groups", "id">;
}) => {
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();

  const { getValues, setValue } = formMethods;

  const canEnableOrDisable = (index: number) => {
    const allGroups = getValues("groups") ?? [];
    const lastRow = allGroups.length === 1 ? allGroups[0] : allGroups[allGroups.length - 1];
    const isLastRowEmpty = !lastRow.dimension;
    return !(index === allGroups.length - 1 && isLastRowEmpty);
  };

  const isDeleteDisabled = (index: number) => {
    const allGroups = getValues("groups") ?? [];
    const lastRow = allGroups.length === 1 ? allGroups[0] : allGroups[allGroups.length - 1];
    const isLastRowEmpty = !lastRow.dimension;
    return allGroups.length <= 1 || (index === allGroups.length - 1 && isLastRowEmpty);
  };

  // Handlers
  const appendNewGroupHandler = React.useCallback(() => {
    const allGroups = getValues("groups") ?? [];
    allGroups.length < MAX_GROUPS_ALLOWED && append({ dimension: "", step_size: undefined, enabled: true });
  }, [append, getValues]);

  const removeHandler = (index: number, column: FieldArrayWithId<TSlicerQueryForm, "groups", "id">) => {
    if (index === MAX_GROUPS_ALLOWED - 1) {
      update(index, { dimension: "", operator: undefined, values: [], enabled: true });
    } else {
      remove(index);
    }

    if (column.dimension === "FACTORDATE") {
      const formValues = getValues();
      const colsWithModifiedValuesOfPrepays = formValues.columns?.map((col) => {
        if (col.dimension && PREPAYS_COLUMNS.includes(col.dimension)) {
          return {
            ...col,
            values: ["1m"],
          };
        }
        return col;
      });
      setValue("columns", colsWithModifiedValuesOfPrepays);
    }
  };

  const dimensionOptions = React.useMemo(() => {
    return (
      loan_level_slicer_config?.group_by?.map((group) => {
        if (group.dimension === "TICKER") {
          return {
            ...group,
            selectionsFilterFn: (selected_agency: string[] = ["FHL"]) => {
              return tickerSelectionFilter(group, selected_agency);
            },
          };
        }
        return group;
      }) ?? []
    );
  }, [loan_level_slicer_config?.group_by]);

  const isEnabled = useWatch({ control: formMethods.control, name: `groups.${idx}.enabled` });
  return (
    <>
      <Box
        w="1rem"
        visibility={isEnabled ? "hidden" : "visible"}
        _groupHover={{ visibility: canEnableOrDisable(idx) ? "visible" : "hidden" }}
      >
        <CACheckboxInput {...formMethods.register(`groups.${idx}.enabled`)} variant="small" hideLabel />
      </Box>
      <Box w="full" opacity={isEnabled ? "1" : "0.5"} pointerEvents={isEnabled ? "auto" : "none"}>
        <SlicerGroupRowInput
          inputIndex={idx}
          dimensionOptions={dimensionOptions}
          appendNewRow={appendNewGroupHandler}
          formMethods={formMethods}
        />
      </Box>
      <IconButton
        visibility="hidden"
        _groupHover={{ visibility: "visible" }}
        isDisabled={isDeleteDisabled(idx)}
        aria-label="remove-group"
        size="xs"
        variant="unstyled"
        icon={<CAIcon as={IoTrashOutline} variant="secondary" boxSize={4} />}
        onClick={() => removeHandler(idx, column)}
      />
    </>
  );
};
