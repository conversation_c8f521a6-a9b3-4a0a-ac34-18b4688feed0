import { <PERSON><PERSON><PERSON><PERSON>on, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, <PERSON>u<PERSON>ist } from "@chakra-ui/react";
import React from "react";
import { IoEllipsisVerticalOutline, IoSettingsOutline, IoTrashOutline, IoTrendingUpOutline } from "react-icons/io5";
import { MdOutlineEditNote } from "react-icons/md";
import CAIcon from "@/design-system/atoms/CAIcon";
import { menuItemIconProps } from "../FilterSeries/FilterCardPrimaryMenu";

interface SlicerGroupsMenuActionsProps {
  isBasic: boolean;
  onModeChange: () => void;
  onDeleteAllClick: () => void;
  openGroupModal: () => void;
}

export const SlicerGroupsMenuActions = ({
  isBasic,
  onModeChange,
  onDeleteAllClick,
  openGroupModal,
}: SlicerGroupsMenuActionsProps) => {
  return (
    <Menu placement="right-start" isLazy closeOnSelect>
      <MenuButton
        as={IconButton}
        aria-label="query-options-menu"
        size="xs"
        icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
        variant="ghost"
        onClick={(e) => e.stopPropagation()}
      />
      <MenuList zIndex="popover" minW="10rem">
        <MenuItem
          icon={<CAIcon as={MdOutlineEditNote} {...menuItemIconProps} />}
          onClick={(e) => {
            e.stopPropagation();
            openGroupModal();
          }}
        >
          Modify
        </MenuItem>
        <MenuItem
          icon={<CAIcon as={isBasic ? IoTrendingUpOutline : IoSettingsOutline} {...menuItemIconProps} />}
          onClick={(e) => {
            e.stopPropagation();
            onModeChange();
          }}
        >
          {isBasic ? "Advanced Mode" : "Basic Mode"}
        </MenuItem>
        <MenuItem
          icon={<CAIcon as={IoTrashOutline} {...menuItemIconProps} />}
          onClick={(e) => {
            e.stopPropagation();
            onDeleteAllClick();
          }}
        >
          Delete All
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
