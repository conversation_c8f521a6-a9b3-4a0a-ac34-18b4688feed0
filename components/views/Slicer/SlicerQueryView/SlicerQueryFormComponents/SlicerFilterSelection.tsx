import React from "react";
import {
  Box,
  Checkbox,
  CheckboxGroup,
  Grid,
  GridItem,
  List,
  ListItem,
  Skeleton,
  Text,
  Tooltip,
  VStack,
  chakra,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import CAModal from "@/design-system/molecules/CAModal";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter,
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_LoanLevelSlicerConfigDef,
  CA_Mastr_Models_v1_0_Models_Operator,
  CA_Mastr_Models_v1_0_Models_QueryType,
} from "@/utils/openapi";
import CASearch from "@/design-system/molecules/CASearch";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { getCategoryLevelUniqueName, normalizeFiltersForSelectionModal } from "@/utils/helpers/slicer";
import { TSlicerQueryForm } from "@/types/slicer";

export type FilterSelectionOptionType = {
  label: string;
  value?: string;
} & CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter;

interface SlicerFilterSelectionModalProps {
  onModalClose: (normalizedSelection?: string[] | undefined, selection?: Record<string, string[]>) => void;
  filterSelectionActiveIndex: number | null;
  filterSets: TSlicerQueryForm["filter_sets"];
  queryType: CA_Mastr_Models_v1_0_Models_QueryType;
}

const getDefaultSelections = (
  filterSelectionActiveIndex: number | null,
  filterSets: TSlicerQueryForm["filter_sets"],
  loan_level_slicer_config:
    | CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_LoanLevelSlicerConfigDef
    | undefined
) => {
  if (filterSelectionActiveIndex === null) return {};

  const selections = filterSets[filterSelectionActiveIndex]?.conditions?.map((condition) => condition.dimension) ?? [];

  // bind categories
  const selectionsWithCategory = selections
    .map((selection) => {
      const config = loan_level_slicer_config?.filters?.find((filter) => filter.dimension === selection);
      return {
        value: selection,
        category: config?.category && config?.level ? getCategoryLevelUniqueName(config.category, config?.level) : "",
      };
    })
    .filter((config) => config.value || config.value !== "AGENCY");

  return selectionsWithCategory.reduce<Record<string, string[]>>((acc, item) => {
    if (item.category) {
      if (acc[item.category]) {
        acc[item.category] = [...acc[item.category as string], item.value as string];
      } else {
        acc[item.category] = [item.value as string];
      }
    }

    return acc;
  }, {});
  // eslint-disable-next-line react-hooks/exhaustive-deps
};

export const SlicerFilterSelectionModal = ({
  onModalClose,
  filterSelectionActiveIndex,
  filterSets,
  queryType,
}: SlicerFilterSelectionModalProps) => {
  const columnCount = useBreakpointValue({
    sm: 1,
    md: 1,
    lg: 1,
    xl: 2,
  });

  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();

  const [searchText, setSearchText] = React.useState("");
  const [checkboxSelections, updateSelection] = React.useState<Record<string, string[]>>(
    () => getDefaultSelections(filterSelectionActiveIndex, filterSets, loan_level_slicer_config) ?? {}
  );

  const getSelectedAgenciesAndOperator = (
    index: number
  ): { operator: CA_Mastr_Models_v1_0_Models_Operator; selectedAgencies: string[] } => {
    const selectedAgencyObj = filterSets[index]?.conditions?.find((c) => c.dimension === "AGENCY");

    if (!selectedAgencyObj) return { operator: CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF, selectedAgencies: [] };

    if (selectedAgencyObj.operator === CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF) {
      return { operator: selectedAgencyObj.operator, selectedAgencies: selectedAgencyObj.list_values ?? [] };
    } else {
      return {
        operator: selectedAgencyObj.operator ?? CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO,
        selectedAgencies: selectedAgencyObj.values as string[],
      };
    }
  };

  const filterOptions = React.useMemo(() => {
    if (filterSelectionActiveIndex === null) return {};

    const { operator, selectedAgencies } = getSelectedAgenciesAndOperator(filterSelectionActiveIndex);
    return normalizeFiltersForSelectionModal(
      loan_level_slicer_config?.filters ?? [],
      operator,
      selectedAgencies,
      queryType
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterSelectionActiveIndex, loan_level_slicer_config?.filters]);

  const selectGroupHandler = (groupName: string, level: string) => {
    const allChildren = filterOptions[level].find((d) => d.category === groupName)?.options ?? [];
    const allChecked = checkboxSelections?.[groupName]?.length === allChildren.length;
    if (allChecked) {
      updateSelection({ ...checkboxSelections, [groupName]: [] });
    } else {
      const values = allChildren.map((c) => c.value).filter(Boolean);
      updateSelection({ ...checkboxSelections, [groupName]: values as string[] });
    }
  };

  const submitSelection = () => {
    const normalizedSelection = Object.values(checkboxSelections).flat();
    onModalClose(normalizedSelection, checkboxSelections);
  };

  const displayField = (filterLabel: string) => {
    if (!searchText) return "inherit";
    return filterLabel.toLowerCase().includes(searchText.toLowerCase()) ? "inherit" : "none";
  };

  const displayCategory = (category: string, level: string) => {
    const matchesAnyChild = filterOptions[level]
      ?.find((c) => c.category === category)
      ?.options?.find((option) => option?.display_name?.toLowerCase().includes(searchText.toLowerCase()));
    return matchesAnyChild ? "block" : "none";
  };

  return (
    <CAModal
      modalHeader="Filters"
      showCloseIcon
      isOpen={true}
      onClose={onModalClose}
      headerStyle={{
        color: useColorModeValue("celloBlue.500", "turmericRoot.500"),
        textTransform: "uppercase",
        py: "1rem",
        px: "1.7rem",
      }}
      contentStyle={{ minW: "80%" }}
      modalBackground={useColorModeValue("celloBlue.50", "celloBlue.900")}
      modalFooter={
        !Object.keys(filterOptions).length
          ? undefined
          : [
              {
                title: "Cancel",
                variant: "secondary",
                onClick: onModalClose,
              },
              {
                title: "Apply",
                variant: "primary",
                onClick: submitSelection,
              },
            ]
      }
    >
      <Box p="1.7rem" background={useColorModeValue("white", "celloBlue.1000")}>
        {!Object.keys(filterOptions).length ? (
          <Box>
            <Text fontWeight="medium" fontSize="lg">
              Please select agency first.
            </Text>
          </Box>
        ) : (
          <>
            <Box maxW={{ base: "13rem", md: "20rem" }}>
              <CASearch
                name="search-columns"
                placeholder="Quick Filter"
                onChange={({ target: { value } }) => setSearchText(value.trim())}
              />
            </Box>
            {!columnCount ? (
              <Skeleton height="25px" />
            ) : (
              <Grid templateColumns={{ base: "repeat(1, 1fr)", md: "repeat(2, 1fr)" }} mt={2} gridGap={12}>
                {Object.keys(filterOptions).map((level) => (
                  <GridItem key={level} mt={6}>
                    <Box p={2}>
                      <Text variant="tableLeft" fontSize="xl" fontWeight="bold">
                        {level}
                      </Text>
                    </Box>
                    <List
                      css={{
                        columnCount,
                      }}
                    >
                      {filterOptions[level].map(({ category, options }) => {
                        const matchesCategory = category.toLowerCase().includes(searchText.toLowerCase());

                        return (
                          <ListItem
                            key={category}
                            css={{
                              breakInside: "avoid",
                            }}
                            p={1}
                            display={matchesCategory ? "block" : displayCategory(category as string, level)}
                          >
                            <Checkbox
                              isChecked={checkboxSelections?.[category]?.length === options?.length}
                              isIndeterminate={
                                checkboxSelections?.[category] &&
                                checkboxSelections?.[category]?.length !== options.length &&
                                checkboxSelections?.[category]?.length !== 0
                              }
                              onChange={() => selectGroupHandler(category, level)}
                            >
                              <Text variant="tableHead" whiteSpace="nowrap">
                                {category.replace(/(--([\w ])*)/g, "").trim()}
                              </Text>
                            </Checkbox>
                            <Box pl={6} mt={2.5}>
                              <CheckboxGroup
                                value={checkboxSelections?.[category]}
                                onChange={(e) => updateSelection({ ...checkboxSelections, [category]: e.map(String) })}
                              >
                                <VStack alignItems="flex-start">
                                  {options.map((c) => (
                                    <Checkbox
                                      key={c.value}
                                      value={c.value}
                                      display={matchesCategory ? "inherit" : displayField(c.display_name as string)}
                                    >
                                      <Tooltip
                                        label={
                                          <chakra.pre maxW="max-content" whiteSpace={"pre-wrap"}>
                                            <strong>Supported by</strong>
                                            <br />
                                            {c?.agencies?.join(", ")}
                                            {c?.selections && c.selections.length > 0 && (
                                              <>
                                                <br />
                                                <br />
                                                <strong>Options</strong>
                                                <br />
                                                {c.selections?.join(", ")}
                                              </>
                                            )}
                                          </chakra.pre>
                                        }
                                      >
                                        <Text display="inline" variant="default">
                                          {c.label}
                                        </Text>
                                      </Tooltip>
                                    </Checkbox>
                                  ))}
                                </VStack>
                              </CheckboxGroup>
                            </Box>
                          </ListItem>
                        );
                      })}
                    </List>
                  </GridItem>
                ))}
              </Grid>
            )}
          </>
        )}
      </Box>
    </CAModal>
  );
};
