import React from "react";
import { Controller, UseFormReturn, useWatch } from "react-hook-form";
import { Box, Center, Menu, MenuButton, MenuItem, MenuList, Text, useDisclosure } from "@chakra-ui/react";
import { useOutsideClick } from "@chakra-ui/react";
import { MultiSelectOptions } from "@/design-system/molecules/CAMultiSelectDropdown";
import {
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute,
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter,
  CA_Mastr_Models_v1_0_Models_ConfigurationType,
} from "@/utils/openapi";
import CAInput from "@/design-system/molecules/CAInput";
import { OperatorsType, SlicerConfigFormat, SlicerDataSetOptions, TSlicerQueryForm } from "@/types/slicer";
import { getOperators } from "@/constants/slicer";

export type SelectedDimensionObjectType = CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Filter &
  CA_Mastr_Api_v1_0_Models_Slicer_LoanLevelSlicerConfigInfo_Attribute &
  MultiSelectOptions;

interface SlicerOperatorInputProps {
  registerFieldPath: `groups.${number}` | `filter_sets.${number}.conditions.${number}`;
  selectedDimension: SelectedDimensionObjectType;
  selectedOperator: OperatorsType | null;
  setSelectedOperator: (selectedOperator: OperatorsType) => void;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
  excludeInclusiveOperators?: boolean;
}

const SlicerOperatorInput: React.FC<SlicerOperatorInputProps> = ({
  registerFieldPath,
  selectedOperator,
  setSelectedOperator,
  selectedDimension,
  formMethods,
  excludeInclusiveOperators = false,
}: SlicerOperatorInputProps) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const { isOpen, onClose, onOpen } = useDisclosure();
  const { control, setValue } = formMethods;
  const watch_dataset_type = useWatch({ control, name: "dataset_type" });

  const onOperatorChange = (operator: OperatorsType) => {
    setSelectedOperator(operator);

    if (operator.value === selectedOperator?.value) {
      return;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setValue(`${registerFieldPath}.operator` as any, operator.value, { shouldDirty: true });
  };

  const allowedOperators: OperatorsType[] = React.useMemo(() => {
    return getOperators(selectedDimension?.type ?? "default", {
      isVector: !!selectedDimension.default_vector_values,
      excludeInclusiveOperators: excludeInclusiveOperators,
      includeLookBack:
        selectedDimension.format === SlicerConfigFormat.YEAR ||
        selectedDimension.format === SlicerConfigFormat.YEAR_MONTH,
    });
  }, [
    excludeInclusiveOperators,
    selectedDimension.default_vector_values,
    selectedDimension?.type,
    selectedDimension.format,
  ]);

  const isDisabled =
    selectedDimension.type === CA_Mastr_Models_v1_0_Models_ConfigurationType.AGENCY &&
    watch_dataset_type === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL;

  useOutsideClick({
    ref,
    handler: () => {
      onClose();
    },
  });

  const closeOnBlur: React.KeyboardEventHandler<HTMLElement> = (e) => {
    if (e.key === "Tab" || e.key === "Escape") {
      onClose();
    }
  };

  return (
    <Menu id="slicer-operator" isOpen={isOpen} closeOnSelect>
      <MenuButton ref={ref} onFocus={onOpen} as="div" disabled={isDisabled} onKeyDown={closeOnBlur}>
        <Controller
          control={control}
          name={`${registerFieldPath}.operator`}
          render={({ field: { name } }) => (
            <Box cursor={isDisabled ? "not-allowed" : "pointer"}>
              <CAInput
                pointerEvents={isDisabled ? "none" : "inherit"}
                width="2.188rem"
                height="30px"
                textAlign="center"
                value={selectedOperator?.symbol ?? ""}
                name={name}
                readOnly
              />
            </Box>
          )}
        />
      </MenuButton>
      <MenuList onKeyDown={closeOnBlur}>
        {allowedOperators?.map((operator, index) => (
          <MenuItem
            key={index}
            onClick={() => {
              onClose();
              onOperatorChange(operator);
            }}
          >
            <Center w="30px">
              <Text variant="tableLeft" fontSize="lg">
                {operator.symbol}
              </Text>
            </Center>
            <Text>{operator.displayName}</Text>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default SlicerOperatorInput;
