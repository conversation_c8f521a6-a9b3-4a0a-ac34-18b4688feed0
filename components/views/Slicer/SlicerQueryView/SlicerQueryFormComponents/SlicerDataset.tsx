import React from "react";
import { <PERSON>, HStack, VStack } from "@chakra-ui/react";
import { UseFormReturn } from "react-hook-form";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { SlicerDataSetDropdownOptions } from "@/constants/slicer";
import { SlicerDataSetOptions, TSlicerQueryForm } from "@/types/slicer";
import { CA_Mastr_Models_v1_0_Models_Operator, CA_Mastr_Models_v1_0_Models_QueryType } from "@/utils/openapi";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { getDefaultSlicerFormValues } from "@/utils/helpers/slicer";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useFormChanges } from "../SlicerQueryForm";

interface SlicerDatasetProps {
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

export const SlicerDataset = ({ formMethods }: SlicerDatasetProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { loan_level_slicer_config },
  } = useSlicerModule();
  const setHasChanges = useFormChanges((state) => state.setHasChanges);

  const handleDataSetTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { value } = e.target;
    formMethods.setValue("dataset_type", value);
    formMethods.setValue("filter_sets", [
      {
        filter_set_name: "Filters 1",
        enabled: true,
        conditions: [
          {
            dimension: "AGENCY",
            operator:
              value === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL
                ? CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO
                : CA_Mastr_Models_v1_0_Models_Operator.ELEMENT_OF,
            values: value === SlicerDataSetOptions.MULTI_FAMILY_LOAN_LEVEL ? ["GPL"] : [],
            list_values: [],
          },
        ],
      },
    ]);
  };

  const handleQueryTypeChange = (type: CA_Mastr_Models_v1_0_Models_QueryType) => {
    const defaultFormData = getDefaultSlicerFormValues({
      loan_level_slicer_config: loan_level_slicer_config,
      defaultModelVersion: metadata?.slicer_settings?.version?.find((v) => v.is_default)?.value,
      queryType: type,
    });
    formMethods.reset({
      ...defaultFormData,
      user_slicer_name: formMethods.getValues("user_slicer_name"),
      dataset_type: SlicerDataSetOptions.RESIDENTIAL_LOAN_LEVEL,
    });
    setHasChanges(true);
  };
  return (
    <VStack px={10} pb={4} gap={0}>
      <HStack w="full">
        <Box w="50%">
          <CASelectDropdown
            name="type"
            value="type"
            options={[
              {
                id: "type",
                value: "type",
                displayValue: "Type",
              },
            ]}
            readOnly
          />
        </Box>

        <ToggleButtonGroup
          selectedButton={formMethods.watch("query_type")}
          onChange={(value) => handleQueryTypeChange(value as CA_Mastr_Models_v1_0_Models_QueryType)}
          buttons={Object.values(CA_Mastr_Models_v1_0_Models_QueryType).map((type) => ({ label: type, value: type }))}
          fullWidth
        />
      </HStack>
      <HStack w="full">
        <Box w="50%">
          <CASelectDropdown
            name="source"
            value="source"
            options={[
              {
                id: "source",
                value: "source",
                displayValue: "Source",
              },
            ]}
            readOnly
          />
        </Box>

        <CASelectDropdown
          {...formMethods.register("dataset_type")}
          options={SlicerDataSetDropdownOptions}
          onChange={handleDataSetTypeChange}
          onBlur={undefined}
        />
      </HStack>
    </VStack>
  );
};
