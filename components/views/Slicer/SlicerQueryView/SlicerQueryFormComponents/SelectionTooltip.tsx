import React from "react";
import { chakra } from "@chakra-ui/react";

export const SelectionTooltip = ({
  agenciesSupported,
  selections,
}: {
  agenciesSupported: string[] | null | undefined;
  selections: string[] | null | undefined;
}) => {
  return (
    <chakra.pre maxW="max-content" whiteSpace={"pre-wrap"}>
      <chakra.strong>Supported by</chakra.strong>
      <br />
      {agenciesSupported?.join(", ")}
      {selections && selections.length > 0 && (
        <>
          <br />
          <br />
          <strong>Options</strong>
          <br />
          {selections?.join(", ")}
        </>
      )}
    </chakra.pre>
  );
};
