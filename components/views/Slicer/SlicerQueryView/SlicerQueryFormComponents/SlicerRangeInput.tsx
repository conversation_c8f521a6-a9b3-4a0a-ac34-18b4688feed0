import React from "react";
import { Box, HStack } from "@chakra-ui/layout";
import { Controller, UseFormReturn } from "react-hook-form";
import { get } from "lodash";
import { CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";
import CAInput from "@/design-system/molecules/CAInput";
import { SelectedDimensionObjectType, SlicerConfigFormat, TSlicerQueryForm } from "@/types/slicer";
import CADateInput from "@/design-system/molecules/CADateInput";
import { getDateFromNumericYYYYMMDD } from "@/utils/helpers";
import { DimensionsSupportingLatest, LATEST_DATE, WITHIN_RANGE_INPUTS } from "@/constants/slicer";
import SlicerLatestDateHeader from "../../SlicerQueryDetails/helpers/SlicerLatestDateHeader";

interface SlicerRangeInputProps {
  selectedDimension: SelectedDimensionObjectType;
  operator: CA_Mastr_Models_v1_0_Models_Operator | undefined;
  registerFieldPath: `groups.${number}` | `columns.${number}` | `filter_sets.${number}.conditions.${number}`;
  validateRowHandler: () => void;
  formMethods: UseFormReturn<TSlicerQueryForm, object>;
}

const SlicerRangeInput: React.FC<SlicerRangeInputProps> = ({
  selectedDimension,
  operator,
  registerFieldPath,
  validateRowHandler,
  formMethods,
}: SlicerRangeInputProps) => {
  const {
    control,
    getValues,
    register,
    setValue,
    formState: { errors },
  } = formMethods;

  const formatsWithDatePicker: string[] = [SlicerConfigFormat.YEAR, SlicerConfigFormat.YEAR_MONTH];

  // Validators
  const validateRangeMinValue = (rangeValue: string, minRange: number | undefined | null) => {
    if (!minRange?.toString()) return;
    if (+rangeValue < minRange) {
      return false;
    }
    return true;
  };

  const validateRangeMaxValue = (rangeValue: string, maxRange: number | undefined | null) => {
    //If rangeValue is not a number, do not validate
    if (isNaN(parseFloat(rangeValue))) return true;

    if (!maxRange?.toString()) return;
    if (+rangeValue > maxRange) {
      return false;
    }
    return true;
  };

  const validateRange = (rangeValue: string, isMin: boolean) => {
    if (isMin) {
      const maxValue = getValues(`${registerFieldPath}.values.1`);
      if (!maxValue) return true;
      if (+rangeValue > +maxValue) {
        return false;
      }
      return true;
    } else {
      const minValue = getValues(`${registerFieldPath}.values.0`);
      if (!minValue) return true;
      if (+rangeValue < +minValue) {
        return false;
      }
      return true;
    }
  };

  const getAgencyBasedMinDate = (selectedDimension: SelectedDimensionObjectType) => {
    const filterSets = getValues(`filter_sets`);

    const agenciesOnFilterSets = new Set(filterSets.flatMap((filter) => filter.conditions[0].list_values));

    const agenciesSelected = selectedDimension?.agency_lower_bounds?.filter((agency) =>
      agenciesOnFilterSets.has(agency.name ?? "")
    );

    if (!agenciesSelected) return;

    const highestAgencyLowerBound = Math.max(
      ...agenciesSelected.map((item) => item.value ?? selectedDimension.default_lower_bound ?? 0)
    );

    return getDateFromNumericYYYYMMDD(highestAgencyLowerBound);
  };

  const getAgencyBasedMaxDate = (selectedDimension: SelectedDimensionObjectType) => {
    const filterSets = getValues(`filter_sets`);

    const agenciesOnFilterSets = new Set(filterSets.flatMap((filter) => filter.conditions[0].list_values));

    const agenciesSelected = selectedDimension?.agency_upper_bounds?.filter((agency) =>
      agenciesOnFilterSets.has(agency.name ?? "")
    );

    if (!agenciesSelected) return;

    const lowestAgencyUpperBound = Math.min(
      ...agenciesSelected.map((item) => item.value ?? selectedDimension.default_upper_bound ?? 0)
    );

    return getDateFromNumericYYYYMMDD(lowestAgencyUpperBound);
  };

  const getMinDate = (selectedDimension: SelectedDimensionObjectType, isMaxRangePicker = false) => {
    const currentSelectedMinDate = getValues(`${registerFieldPath}.values.0`);
    if (currentSelectedMinDate && isMaxRangePicker) return new Date(currentSelectedMinDate);
    let minDate;
    if (
      selectedDimension?.format === SlicerConfigFormat.YEAR_MONTH ||
      selectedDimension?.format === SlicerConfigFormat.YEAR
    ) {
      minDate = selectedDimension?.lower_bound ? getDateFromNumericYYYYMMDD(selectedDimension.lower_bound) : undefined;
      if ("agency_lower_bounds" in selectedDimension) {
        minDate = getAgencyBasedMinDate(selectedDimension);
      }
    }
    if (!minDate) return;
    return new Date(minDate?.toString());
  };

  const getMaxDate = (selectedDimension: SelectedDimensionObjectType) => {
    const currentSelectedMaxDate = getValues(`${registerFieldPath}.values.1`);
    if (currentSelectedMaxDate) return new Date(currentSelectedMaxDate);
    let maxDate;
    if (
      selectedDimension?.format === SlicerConfigFormat.YEAR_MONTH ||
      selectedDimension?.format === SlicerConfigFormat.YEAR
    ) {
      maxDate = selectedDimension?.upper_bound ? getDateFromNumericYYYYMMDD(selectedDimension.upper_bound) : undefined;

      if ("agency_upper_bounds" in selectedDimension) {
        maxDate = getAgencyBasedMaxDate(selectedDimension);
      }
    }
    if (!maxDate) return;
    return new Date(maxDate?.toString());
  };

  const onInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.length === 1) {
      validateRowHandler();
    }
  };
  const renderDatePicker = (
    name:
      | `groups.${number}.values.${number}`
      | `columns.${number}.values.${number}`
      | `filter_sets.${number}.conditions.${number}.values.${number}`,
    selectedDimension: SelectedDimensionObjectType,
    isMaxRangePicker?: boolean
  ) => {
    const showLatestDateBtn =
      DimensionsSupportingLatest.includes(selectedDimension.dimension as string) && isMaxRangePicker !== false;
    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: true,
        }}
        render={({ field: { name, value, onChange, onBlur } }) => {
          return (
            <CADateInput
              name={name}
              className="slicer"
              showYearPicker={selectedDimension?.format === SlicerConfigFormat.YEAR}
              showMonthYearPicker={selectedDimension?.format === SlicerConfigFormat.YEAR_MONTH}
              selectedDate={value && value !== LATEST_DATE ? new Date(value) : undefined}
              value={value && value === LATEST_DATE ? value : undefined}
              placeholderText={value === LATEST_DATE ? LATEST_DATE : ""}
              minDate={getMinDate(selectedDimension, isMaxRangePicker)}
              maxDate={getMaxDate(selectedDimension)}
              wrapperClassName="SlicerRangeInput"
              onChange={(value) => {
                onChange(value);
                onBlur();
                validateRowHandler();
                if (!value) return;
                // Clear End Date if the selected Start date is greater than End Date
                const dateValue = getValues(`${registerFieldPath}.values.1`);
                const dateValueTime = new Date(dateValue).getTime();
                if (!isMaxRangePicker && dateValueTime < value.getTime()) {
                  setValue(`${registerFieldPath}.values.1`, "");
                }
              }}
              onChangeRaw={(e) => {
                const value = e.target.value;
                if (showLatestDateBtn && typeof value === "string" && value.toUpperCase() === LATEST_DATE) {
                  onChange(value.toUpperCase());
                } else {
                  onChange("");
                }
              }}
              onBlur={onBlur}
              info={{
                show: !!get(errors, `${registerFieldPath}.values.${isMaxRangePicker ? 1 : 0}.type`),
                msg1: `Error - ${selectedDimension?.display_name}`,
                msg2:
                  get(errors, `${registerFieldPath}.values.${isMaxRangePicker ? 1 : 0}.message`) ||
                  "Please select a date. Field is required",
              }}
              headerEl={
                showLatestDateBtn
                  ? ({ closeDatePicker }) => (
                      <SlicerLatestDateHeader
                        isSelected={value === LATEST_DATE}
                        onChange={(value) => {
                          onChange(value);
                          onBlur();
                        }}
                        closeDatePicker={closeDatePicker}
                      />
                    )
                  : undefined
              }
            />
          );
        }}
      />
    );
  };

  if (!operator) return null;
  return (
    <Box w="full">
      {!WITHIN_RANGE_INPUTS.includes(operator) &&
        (formatsWithDatePicker.includes(selectedDimension.format ?? "") ? (
          renderDatePicker(`${registerFieldPath}.values.0`, selectedDimension)
        ) : (
          <CAInput
            type="number"
            step="any"
            inputType={selectedDimension.format === SlicerConfigFormat.INTEGER ? "digit" : undefined}
            {...register(`${registerFieldPath}.values.0`, {
              max: {
                value: selectedDimension.upper_bound ?? "",
                message: `Value should be less than ${selectedDimension.upper_bound}`,
              },
              min: {
                value: selectedDimension.lower_bound ?? "",
                message: `Value should be greater than ${selectedDimension.lower_bound}`,
              },
              onBlur: onInputBlur,
            })}
            info={{
              show: !!get(errors, `${registerFieldPath}.values.0.type`),
              msg1: `Error - ${selectedDimension?.display_name}`,
              msg2: get(errors, `${registerFieldPath}.values.0.message`),
            }}
            height="30px"
          />
        ))}

      {WITHIN_RANGE_INPUTS.includes(operator) &&
        (formatsWithDatePicker.includes(selectedDimension.format ?? "") ? (
          <HStack spacing={1}>
            {renderDatePicker(`${registerFieldPath}.values.0`, selectedDimension, false)}
            {renderDatePicker(`${registerFieldPath}.values.1`, selectedDimension, true)}
          </HStack>
        ) : (
          <HStack spacing={1}>
            <CAInput
              type="number"
              step="any"
              inputType={selectedDimension.format === SlicerConfigFormat.INTEGER ? "digit" : undefined}
              {...register(`${registerFieldPath}.values.0`, {
                validate: {
                  minValue: (value) =>
                    validateRangeMinValue(value as string, selectedDimension.lower_bound) ||
                    `Min range must be greater than ${selectedDimension.lower_bound}`,
                  input: (value) =>
                    validateRange(value as string, true) || "Min range must not be greater than max range.",
                },
                onBlur: onInputBlur,
              })}
              info={{
                show: !!get(errors, `${registerFieldPath}.values.0.type`),
                msg1: `Error - ${selectedDimension?.display_name}`,
                msg2: get(errors, `${registerFieldPath}.values.0.message`),
              }}
              height="30px"
            />
            <CAInput
              type="number"
              step="any"
              inputType={selectedDimension.format === SlicerConfigFormat.INTEGER ? "digit" : undefined}
              {...register(`${registerFieldPath}.values.1`, {
                validate: {
                  maxValue: (value) =>
                    validateRangeMaxValue(value as string, selectedDimension.upper_bound) ||
                    `Max range must be less than ${selectedDimension.upper_bound}`,
                  input: (value) =>
                    validateRange(value as string, false) || "Max range must not be less than min range.",
                },
                onBlur: onInputBlur,
              })}
              info={{
                show: !!get(errors, `${registerFieldPath}.values.1.type`),
                msg1: `Error - ${selectedDimension?.display_name}`,
                msg2: get(errors, `${registerFieldPath}.values.1.message`),
              }}
              height="30px"
            />
          </HStack>
        ))}
    </Box>
  );
};

export default SlicerRangeInput;
