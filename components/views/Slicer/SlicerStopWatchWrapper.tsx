import React from "react";
import StopWatch from "@/design-system/molecules/StopWatch";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";

type SlicerQueryStopWatchProps = {
  lastRun: string | undefined;
};

export const SlicerQueryStopWatchWrapper: React.FC<SlicerQueryStopWatchProps> = ({
  lastRun,
}: SlicerQueryStopWatchProps) => {
  const {
    state: { isTimerRunning },
  } = useSlicerModule();

  return <StopWatch startDate={lastRun} isTimerRunning={isTimerRunning} />;
};
