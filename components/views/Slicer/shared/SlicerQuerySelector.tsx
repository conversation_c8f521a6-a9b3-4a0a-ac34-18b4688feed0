import * as React from "react";
import { <PERSON>, Center, Flex, HStack, Text, Tooltip, VStack, useColorModeValue } from "@chakra-ui/react";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { MdOpenInNew, MdPeople } from "react-icons/md";
import { mutate } from "swr";
import CAIcon from "@/design-system/atoms/CAIcon";
import CATable from "@/design-system/molecules/CATable";
import { Tags } from "@/design-system/organisms/Tags/Tags";
import { getFormattedLocaleDate, getFormattedTime } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer,
  CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicersResponse,
  CA_Mastr_Models_v1_0_Models_EntityPermissionRole,
  UserSlicerService,
} from "@/utils/openapi";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import { useSortTable } from "@/hooks/useSortTable";
import { ColumnHeader } from "@/design-system/molecules/CATable/CATableHeader";
import { useTagFilter } from "@/hooks/useTagFilter";
import { TagFilter } from "@/design-system/organisms/TagFilter/TagFilter";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { userSlicerServiceKey } from "@/utils/swr-hooks/Slicer";

type SlicerQuerySelectorProps = {
  onSlicerQueryChange: (selectedSlicerQuery: CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer) => void;
  additionalTextSearch?: string;
  data: CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicersResponse | null | undefined;
};

export const SlicerQuerySelector: React.FC<SlicerQuerySelectorProps> = ({
  data,
  onSlicerQueryChange,
  additionalTextSearch,
}: SlicerQuerySelectorProps) => {
  // Search Queries
  const { filteredList, resetSearch } = useKeywordsSearch({
    getSearchableText: (item) =>
      `${item.user_slicer_name} ${item.user?.firstName} ${item.user?.lastName} ${item.tags?.join(" ")}`,
    list: data?.user_slicers,
    additionalTextSearch,
  });
  // Sort Table
  const { sortedData, getTableProps } = useSortTable({ data: filteredList });
  // Filter data by tags
  const { data: filteredDataByTags, getTagFilterProps } = useTagFilter<CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer>({
    data: sortedData,
  });

  const [showTags, setShowTags] = React.useState(false);

  const deleteSlicerQuery = async (id: number | undefined, queryName: string, lastUpdated: string | undefined) => {
    if (!id) return;
    try {
      await UserSlicerService.deleteUserSlicer(id, lastUpdated);
      showSuccessToast("Deleted", `"${queryName}" deleted successfully.`);
      mutate(userSlicerServiceKey.getUserSlicer);
    } catch (err) {
      showErrorToast("Error", `Error while deleting "${queryName}"`);
    }
  };

  const tableItemClick = (
    _selectedItem: { name: string | React.JSX.Element; values: (string | number | React.JSX.Element)[] },
    index: number
  ) => {
    if (filteredDataByTags?.length) {
      const selectedSlicerQuery = filteredDataByTags[index];
      onSlicerQueryChange(selectedSlicerQuery);
    }
    resetSearch();
  };

  const tableData = React.useMemo(() => {
    return filteredDataByTags?.map((item, index) => {
      const queryTags = [...new Set(item.tags ?? [])];
      const lastRunDate = item.user_slicer_result?.run_date;
      return {
        name: (
          <HStack maxWidth={"30.4rem"}>
            <HStack>
              <Center
                title="Open in a new tab"
                p={0.5}
                onClick={(e) => {
                  e.stopPropagation();
                  window.open("/slicer?id=" + item?.user_slicer_id);
                }}
              >
                <CAIcon as={MdOpenInNew} variant="secondary" />
              </Center>
            </HStack>
            <VStack alignItems="flex-start" py="0.4rem">
              <Tooltip isDisabled={showTags || !queryTags?.length} label={`Tags: ${queryTags?.join(", ")}`}>
                <Flex gap={3}>
                  <Text variant="tableLeft" noOfLines={1} maxWidth={"25rem"}>
                    {item.user_slicer_name}
                  </Text>
                  {item.is_shared && <CAIcon ml={-1} as={MdPeople} variant="secondary" />}
                </Flex>
              </Tooltip>
              {showTags && queryTags.length ? (
                <Tags key="tags" tags={queryTags} rowIndex={index} wrapperStyle={{ flexWrap: "wrap", maxW: "28rem" }} />
              ) : null}
            </VStack>
          </HStack>
        ),
        values: [
          `${item.user?.firstName} ${item.user?.lastName}`,
          <Tooltip
            key={`${getFormattedLocaleDate(item.inserted)}`}
            label={`${getFormattedLocaleDate(item.inserted)} ${getFormattedTime(item.inserted)}`}
          >
            <Text>{getFormattedLocaleDate(item.inserted)}</Text>
          </Tooltip>,
          <Tooltip
            key={`${getFormattedLocaleDate(item.updated)}`}
            label={`${getFormattedLocaleDate(item.updated)} ${getFormattedTime(item.updated)}`}
          >
            <Text>{getFormattedLocaleDate(item.updated)}</Text>
          </Tooltip>,

          lastRunDate ? (
            <Tooltip
              key={`${getFormattedLocaleDate(lastRunDate)}`}
              label={`${getFormattedLocaleDate(lastRunDate)} ${getFormattedTime(lastRunDate)}`}
            >
              <Text>{getFormattedLocaleDate(lastRunDate)}</Text>
            </Tooltip>
          ) : (
            "-"
          ),
          item.role ?? "",
          item.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER ? (
            <Center
              p={0.5}
              onClick={(e) => {
                e.stopPropagation();
                deleteSlicerQuery(item.user_slicer_id, item.user_slicer_name ?? "", item.updated ?? undefined);
              }}
            >
              <CAIcon as={RiDeleteBin6Fill} variant="secondary" />
            </Center>
          ) : (
            <Box />
          ),
        ],
      };
    });
  }, [filteredDataByTags, showTags]);

  const headers: ColumnHeader[] = React.useMemo(
    () => [
      {
        header: "Query",
        accessor: "user_slicer_name",
        rightEl: (
          <Box
            w="5rem"
            pt="4px"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            <CASwitchInput
              size="sm"
              isChecked={showTags}
              onChange={(e) => {
                e.stopPropagation();
                const checked = e.currentTarget.checked;
                setShowTags(checked);
              }}
              label="Show Tags"
              labelPosition="left"
              labelStyle={{
                fontWeight: "normal",
                fontSize: "2xs",
              }}
            />
          </Box>
        ),
      },
      { header: "Created by", accessor: "user.firstName" },
      {
        header: "Created",
        accessor: "inserted",
        style: {
          paddingLeft: 0,
        },
      },
      {
        header: "Updated",
        accessor: "updated",
        style: {
          paddingLeft: 0,
        },
      },
      {
        header: "Run",
        accessor: "user_slicer_result.run_date",
        style: {
          paddingLeft: 0,
        },
      },
      { header: "Role", accessor: "role" },
      { header: "" },
    ],
    [showTags]
  );

  const tags = data?.user_slicers?.flatMap((userSlicer) => userSlicer.tags);
  const uniqueTags = [...new Set(tags)].filter(Boolean) as string[];

  const bg = useColorModeValue("white", "celloBlue.1100");

  return (
    <Flex position="relative" w="full" onClick={(e) => e.stopPropagation()}>
      <TagFilter tags={uniqueTags} {...getTagFilterProps} />
      <Box
        pb="2"
        backgroundColor={bg}
        maxH={{
          base: "calc(100vh - 10rem)",
          lg: "calc(100vh - 110px)",
        }}
        minH="15rem"
        overflowX="auto"
      >
        <CATable
          w="63rem"
          headers={headers}
          data={tableData ?? []}
          variant="caFullCentered"
          onItemClick={tableItemClick}
          headerStyles={{
            backgroundColor: bg,
            textAlign: "left",
          }}
          columnsToLeftAlign={[0, 1, 2, 3, 4, 5]}
          {...getTableProps()}
        />
      </Box>
    </Flex>
  );
};
