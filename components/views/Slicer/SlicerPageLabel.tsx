import {
  <PERSON>,
  Button,
  <PERSON><PERSON><PERSON><PERSON>,
  Icon<PERSON>utton,
  <PERSON>u,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  Tooltip,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoEllipsisVerticalOutline, IoRefresh, IoSaveOutline, IoTrashOutline } from "react-icons/io5";
import { VscSaveAs } from "react-icons/vsc";
import React from "react";
import { State, useSWRConfig } from "swr";
import { IoMdShare } from "react-icons/io";
import { BiPencil } from "react-icons/bi";
import { MdPeople } from "react-icons/md";
import CAIcon, { CAIconVariant } from "@/design-system/atoms/CAIcon";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { useGetUserSlicerSWR } from "@/utils/swr-hooks";
import { showErrorToast, showSuccessToast, showWarningToast } from "@/design-system/theme/toast";
import { TSlicerQueryForm } from "@/types/slicer";
import { getQueryGridState, prepareSlicerRequestPayload } from "@/utils/helpers/slicer";
import {
  useAddUserSlicerSWR,
  useDeleteUserSlicerSWR,
  usePatchUserSlicerSWR,
  useUpdateUserSlicerSWR,
} from "@/utils/swr-hooks/UserSlicer";
import {
  ApiError,
  CA_Mastr_Api_v1_0_Models_Status,
  CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView,
  CA_Mastr_Models_v1_0_Models_Entity,
} from "@/utils/openapi";
import { IN_MEMORY_OBJECT_STORAGE_KEY, LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { inMemoryObjectStorage, safeLocalStorage } from "@/utils/local-storage";
import { userGridViewServiceKey } from "@/utils/swr-hooks/UserGridView";
import CAInput from "@/design-system/molecules/CAInput";
import S from "@/constants/strings";
import ConfirmationModalPopup from "@/design-system/organisms/ConfirmationModalPopup";
import { insertGridView, updateGridView } from "@/components/helpers/ViewSelector/gridViewApi";
import { DEFAULT_VIEW_NAME } from "@/design-system/molecules/CAGrid/constants";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { canDelete, canEdit, canShare, getAPIErrorMessage, getErrorMessageFromStatus } from "@/utils/helpers";
import { getUserSlicer } from "@/utils/swr-hooks/Slicer";
import SharePopup from "@/components/helpers/SharePopup";
import { useGridStoreContext } from "@/design-system/molecules/CAGrid/GridContext";
import { useMemoryStorageState } from "@/hooks/ca-grid/useMemoryStorageState";
import { slicerQueryFormRefManager } from "./SlicerQueryDetails/slicerFormRefManager";
import { useFormChanges } from "./SlicerQueryView/SlicerQueryForm";
import { slicerQueryGridRefManager } from "./SlicerQueryDetails/slicerGridRefManager";
import { useSlicerStore } from "./SlicerQueryDetails/useSlicerStore";

enum ModalStatesType {
  SAVE_AS = "save_as",
  DELETE = "delete",
  RENAME = "rename",
}

const SlicerPageLabel = () => {
  const { cache, mutate: globalMutate } = useSWRConfig();
  const { resetGridState, selectedGridView, setHasUnsavedChanges, setSelectedGridView } = useGridStoreContext(
    (state) => state
  );
  const { gridViewsChanges, resetAllGridViewsChanges } = useSlicerStore();

  const { getChartStateOnMemory } = useMemoryStorageState();

  const { replaceWithoutRendering } = useQueryParameters();
  const [showShareModal, setShowShareModal] = React.useState(false);

  const {
    state: { query, groupMode, loan_level_slicer_config },
    action: { resetSlicer, updateQueryDetails },
  } = useSlicerModule();

  const { hasFormChanges, setHasChanges } = useFormChanges((state) => state);

  const resetHasChangesFlag = () => {
    setTimeout(() => {
      setHasChanges(false);
      resetAllGridViewsChanges();
      setHasUnsavedChanges(false);
    }, 200);
  };

  const { data, mutate } = useGetUserSlicerSWR(query?.server_id);
  const userSlicer = data?.user_slicer;

  const hasChanges = hasFormChanges || Object.values(gridViewsChanges).some(Boolean);

  const { trigger: updateUserSlicer } = useUpdateUserSlicerSWR();
  const { trigger: patchUserSlicer } = usePatchUserSlicerSWR();
  const { trigger: deleteUserSlicer } = useDeleteUserSlicerSWR();
  const { trigger: addUserSlicer } = useAddUserSlicerSWR();

  const menuItemColor = useColorModeValue("charcoal.400", "white");
  const menuItemIconStyle = {
    color: menuItemColor,
    boxSize: 4,
    variant: "default" as CAIconVariant,
  };

  const queryNameInput = React.useRef<HTMLInputElement>(null);
  const modalConfig: {
    [key in ModalStatesType]: {
      headerText: string;
      description: string | React.JSX.Element;
      confirmHandler?: () => void;
    };
  } = React.useMemo(
    () => ({
      save_as: {
        headerText: S.COMMON.SAVE_AS,
        description: (
          <CAInput name="query_name" defaultValue={`Copy of ${query?.name}`} ref={queryNameInput} autoFocus />
        ),
      },
      rename: {
        headerText: query.server_id ? "Rename" : "Save",
        description: (
          <CAInput
            name="rename_query_name"
            placeholder="Query name"
            ref={queryNameInput}
            defaultValue={query.name ?? ""}
            autoFocus
          />
        ),
      },
      remove: {
        headerText: "Close Query?",
        description: canEdit(userSlicer?.role) ? (
          <Text fontSize="md">
            There are unsaved changes. Click on <b>Confirm</b> to discard or <b>{S.COMMON.SAVE_AND_CLOSE}</b>.
          </Text>
        ) : (
          <Text fontSize="md">
            There are unsaved changes. Click on <b>Confirm</b> to discard or <b>{S.COMMON.SAVE_AND_CLOSE}</b> to save
            your work and close.
          </Text>
        ),
      },
      delete: {
        headerText: S.COMMON.DELETE_CONFIRMATION.HEADER,
        description: S.COMMON.DELETE_CONFIRMATION.DESCRIPTION,
      },
    }),
    [query?.name, query?.server_id, userSlicer]
  );

  const [confirmationModalState, setConfirmationModalState] = React.useState<ModalStatesType | null>(null);

  //   Handlers
  const renameQueryName = async (slicerName: string) => {
    const formRef = slicerQueryFormRefManager.getFormRef();
    const prevSlicerName = query.name;

    await patchUserSlicer(
      {
        user_slicer_id: query.server_id,
        user_slicer_name: slicerName,
        last_updated: data?.user_slicer?.updated ?? undefined,
      },
      {
        onError: (e) => {
          formRef?.setValue?.("user_slicer_name", prevSlicerName ?? null);
          showErrorToast("Error", getAPIErrorMessage(e));
        },
        onSuccess: (res) => {
          formRef?.setValue?.("user_slicer_name", slicerName ?? null);
          updateQueryDetails({
            name: res.user_slicer?.user_slicer_name ?? prevSlicerName ?? "",
          });
          mutate();
        },
      }
    );
  };

  // const clearPersistedViewsFromMemory = () => {
  //   // remove persisted state
  //   const currentGridViews:
  //     | State<{ user_grid_views: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView[] }>
  //     | undefined = cache.get(
  //     userGridViewServiceKey.getUserGridView(userGridViewServiceKey.getSlicerGridKey(query.server_id))
  //   );
  //   currentGridViews?.data?.user_grid_views?.forEach((view) => {
  //     inMemoryStorage.removeItem(`${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${view.user_grid_view_id}`);
  //   });
  // };

  const resetQueryForm = () => {
    const formRef = slicerQueryFormRefManager.getFormRef();

    // clearPersistedViewsFromMemory();

    // Reset grid
    resetGridState();
    setHasChanges(false);
    formRef?.resetForm?.(userSlicer);
  };

  const getRequestData = (data: TSlicerQueryForm) => {
    const { error, payload } = prepareSlicerRequestPayload(data, loan_level_slicer_config, groupMode);

    if (error) {
      showErrorToast("Error", error);
      return undefined;
    }

    return payload;
  };

  const saveLatestGridViewsState = async (queryId: number) => {
    const currentGridViews:
      | State<{ user_grid_views: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView[] }>
      | undefined = cache.get(
      userGridViewServiceKey.getUserGridView(userGridViewServiceKey.getSlicerGridKey(queryId)) ?? ""
    );

    const viewsToUpdate: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView[] = [];
    currentGridViews?.data?.user_grid_views?.forEach((gridView) => {
      if (
        gridView?.user_grid_view_id &&
        selectedGridView?.user_grid_view_id === gridView?.user_grid_view_id &&
        gridViewsChanges[`${selectedGridView?.grid_view_name}-${selectedGridView?.user_grid_view_id}`]
      ) {
        const gridRef = slicerQueryGridRefManager.getGridRef();
        const gridState = gridRef?.api?.getState();
        const chartState = getChartStateOnMemory();
        const combinedState = {
          ...gridState,
          chartState,
        };
        if (!gridRef) return;
        viewsToUpdate.push({
          grid_key: selectedGridView?.grid_key,
          user_grid_view_id: selectedGridView?.user_grid_view_id,
          grid_view_name: selectedGridView?.grid_view_name,
          grid_view_state: JSON.stringify(combinedState),
          is_query_default_view: true,
        });
      } else {
        const persistedState = inMemoryObjectStorage.getItem(
          `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${gridView.user_grid_view_id}`
        );

        if (persistedState) {
          const chartState = getChartStateOnMemory();
          const combinedState = JSON.stringify({
            ...persistedState,
            chartState,
          });
          viewsToUpdate.push({
            grid_key: gridView?.grid_key,
            user_grid_view_id: gridView?.user_grid_view_id,
            grid_view_name: gridView?.grid_view_name,
            grid_view_state: combinedState,
            is_query_default_view: false,
          });
        }
      }
    });
    const requests = viewsToUpdate.map((view) => updateGridView(view));
    const res = await Promise.all(requests);
    res.forEach((r) => {
      inMemoryObjectStorage.removeItem(
        `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${r?.user_grid_view?.user_grid_view_id}`
      );
      r?.user_grid_view?.grid_key && globalMutate(userGridViewServiceKey.getUserGridView(r?.user_grid_view?.grid_key));
    });

    const gridView = res?.find((r) => r?.user_grid_view?.user_grid_view_id === selectedGridView?.user_grid_view_id);
    if (gridView?.user_grid_view) {
      setSelectedGridView(gridView.user_grid_view);
    }
  };

  const updateSlicerQuery = async (formData: TSlicerQueryForm) => {
    const requestData = getRequestData(formData);

    if (!requestData) return;

    await updateUserSlicer(
      {
        user_slicer_input: requestData,
        user_slicer_id: query.server_id,
        user_slicer_name: userSlicer?.user_slicer_name,
        is_report: userSlicer?.is_report,
        last_updated: userSlicer?.updated ?? undefined,
        tags: [
          ...(data?.user_slicer?.tags ?? []).filter((tag) => tag !== "#issuance" && tag !== "#prepay"),
          `#${formData.query_type.toLowerCase()}`,
        ],
      },
      {
        onSuccess: (data) => {
          if (data) {
            showSuccessToast("Saved", `User slicer "${userSlicer?.user_slicer_name}" has been updated.`);
            const formRef = slicerQueryFormRefManager.getFormRef();
            formRef?.reset?.(
              {},
              {
                keepValues: true,
              }
            );
            mutate();
            saveLatestGridViewsState(query.server_id as number);
            resetHasChangesFlag();
          }
        },
        onError: (err: unknown) => {
          if (err instanceof ApiError) {
            const errMsg = getErrorMessageFromStatus(err?.body?.status as CA_Mastr_Api_v1_0_Models_Status);
            showErrorToast("Error", errMsg);
          } else {
            showErrorToast("Error", `Failed to update user slicer "${formData.user_slicer_name}".`);
          }
        },
      }
    );
  };

  const saveNewSlicerQuery = async (formData: TSlicerQueryForm, shouldUpdateOpenQueries = true) => {
    if (!formData.user_slicer_name) {
      showWarningToast("Warning", "Please enter a name before saving.");
      return;
    }

    const requestData = getRequestData(formData);

    if (!requestData) return;

    await addUserSlicer(
      {
        user_slicer_name: formData.user_slicer_name,
        user_slicer_input: requestData,
        tags: [formData.query_type.toLowerCase()],
      },
      {
        onSuccess: (res) => {
          if (res && shouldUpdateOpenQueries) {
            showSuccessToast("Saved", `User slicer "${formData.user_slicer_name}" has been added.`);

            const newSlicerId = res?.user_slicer?.user_slicer_id;
            const newSlicerName = res?.user_slicer?.user_slicer_name;

            if (newSlicerId) {
              updateQueryDetails({ name: newSlicerName ?? "", server_id: newSlicerId });
              const formRef = slicerQueryFormRefManager.getFormRef();
              formRef?.reset?.(
                {},
                {
                  keepValues: true,
                }
              );
              resetHasChangesFlag();
              const gridState = getQueryGridState();
              const chartState = getChartStateOnMemory();
              if (!gridState) return;
              insertGridView(
                {
                  grid_key: userGridViewServiceKey.getSlicerGridKey(newSlicerId),
                  grid_view_name: DEFAULT_VIEW_NAME,
                  grid_view_state: JSON.stringify({ ...gridState, chartState }),
                  is_query_default_view: true,
                },
                false
              );
            }
          }
        },
        onError: (err) => {
          console.error(err);
          showErrorToast("Error", `Failed to update user slicer "${formData.user_slicer_name}".`);
        },
      }
    );
  };

  const saveQuery = async () => {
    const formRef = slicerQueryFormRefManager.getFormRef();
    await formRef?.handleSubmit?.(async (formData) => {
      if (!query.server_id) {
        setConfirmationModalState(ModalStatesType.RENAME);
        modalConfig.rename.confirmHandler = async () => {
          await saveNewSlicerQuery({ ...formData, user_slicer_name: queryNameInput.current?.value ?? "" });
        };
      } else {
        await updateSlicerQuery(formData);
      }
    })();
  };

  const closeConfirmationModalPopup = () => {
    setConfirmationModalState(null);
  };

  const deleteQuery = async () => {
    await deleteUserSlicer(
      { userSlicerId: query.server_id, lastUpdated: userSlicer?.updated ?? undefined },
      {
        onSuccess: () => {
          showSuccessToast("Deleted", `User Slicer "${userSlicer?.user_slicer_name}" has been deleted.`);
          replaceWithoutRendering({ id: "" });
          resetSlicer();
        },
        onError: (err: unknown) => {
          if (err instanceof ApiError) {
            const errMsg = getErrorMessageFromStatus(err?.body?.status as CA_Mastr_Api_v1_0_Models_Status);
            showErrorToast("Error", errMsg);
          } else {
            showErrorToast("Error", `Failed to delete user slicer "${userSlicer?.user_slicer_name}".`);
          }
        },
      }
    );
  };

  const copyGridViewsWithNewId = async (oldId: number | string | undefined, newId: number | undefined) => {
    if (!newId || !oldId) return;

    // Copy grid views
    const data: State<{ user_grid_views: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView[] }> | undefined =
      cache.get(userGridViewServiceKey.getUserGridView(userGridViewServiceKey.getSlicerGridKey(oldId)) ?? "");
    const gridViews = data?.data?.user_grid_views ?? [];

    gridViews.forEach(async (view) => {
      let gridState = view.grid_view_state;

      // get latest unsaved state
      const persistedStateJSON = inMemoryObjectStorage.getItem(
        `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${view.user_grid_view_id}`
      );
      if (persistedStateJSON) {
        gridState = persistedStateJSON;
      }

      const isActive = view.user_grid_view_id === selectedGridView?.user_grid_view_id;
      if (isActive) {
        gridState = JSON.stringify(getQueryGridState());
      }
      const newCopyOfGridView = await insertGridView(
        {
          ...view,
          grid_view_state: gridState,
          grid_key: userGridViewServiceKey.getSlicerGridKey(newId),
        },
        false
      );

      if (
        newCopyOfGridView?.user_grid_view?.grid_view_name === selectedGridView?.grid_view_name &&
        newCopyOfGridView?.user_grid_view?.grid_view_state === gridState
      ) {
        const gridKey = newCopyOfGridView?.user_grid_view?.grid_key;
        const gridViewId = newCopyOfGridView?.user_grid_view?.user_grid_view_id;
        safeLocalStorage.setItem(`${LOCAL_STORAGE_KEY.LAST_LOADED_VIEW}_${gridKey}`, JSON.stringify(gridViewId));
      }
    });
  };

  const duplicateQuery = async () => {
    const formRef = slicerQueryFormRefManager.getFormRef();
    const queryName = queryNameInput?.current?.value;

    const slicerData = await getUserSlicer(query.server_id);

    formRef?.handleSubmit?.(async (formData) => {
      const requestData = getRequestData(formData);

      await addUserSlicer(
        {
          user_slicer_name: queryName ?? "",
          user_slicer_input: requestData,
          tags: slicerData?.user_slicer?.tags,
        },
        {
          onSuccess: (res) => {
            showSuccessToast("Saved", `User slicer "${queryName}" has been added.`);
            const newSlicerId = res?.user_slicer?.user_slicer_id as number;
            copyGridViewsWithNewId(query.server_id, newSlicerId);
            const formRef = slicerQueryFormRefManager.getFormRef();
            formRef?.reset?.(
              {},
              {
                keepValues: true,
              }
            );
            resetHasChangesFlag();
            window.open("/slicer?id=" + newSlicerId.toString());
          },
          onError: (err) => {
            console.error(err);
            showErrorToast("Error", `Failed to duplicate user slicer "${formData.user_slicer_name}".`);
          },
        }
      );
    })();
  };

  const popupOnConfirmHandler = React.useCallback(() => {
    switch (confirmationModalState) {
      case ModalStatesType.SAVE_AS: {
        const queryName = queryNameInput?.current?.value;
        if (!queryName) {
          queryNameInput?.current?.focus();
          return;
        }

        duplicateQuery();
        break;
      }
      case ModalStatesType.DELETE:
        deleteQuery();
        break;

      case ModalStatesType.RENAME: {
        const newSlicerQueryName = queryNameInput?.current?.value;
        if (!newSlicerQueryName) {
          queryNameInput?.current?.focus();
          return;
        } else if (newSlicerQueryName === query.name) {
          return;
        }

        if (modalConfig.rename.confirmHandler) {
          modalConfig.rename.confirmHandler();
        } else {
          renameQueryName(newSlicerQueryName);
        }
        break;
      }
    }
    setConfirmationModalState(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [confirmationModalState]);

  const handleSaveIcon = () => {
    if (canEdit(userSlicer?.role) || !userSlicer) {
      saveQuery();
      return;
    }
    setConfirmationModalState(ModalStatesType.SAVE_AS);
  };

  return (
    <>
      <HStack minW="56">
        {hasChanges && (
          <Tooltip label={canEdit(userSlicer?.role) || !userSlicer ? "Save Changes" : "Duplicate As A New Query"}>
            <Button
              onClick={handleSaveIcon}
              paddingStart={0}
              paddingEnd={0}
              minWidth={4}
              height={4}
              backgroundColor={"transparent"}
              textColor={"safetyOrange.500"}
            >
              <CAIcon as={IoSaveOutline} boxSize={4} variant="default" cursor={"pointer"} />
            </Button>
          </Tooltip>
        )}
        <Box w="full" wordBreak="break-all" noOfLines={1}>
          <Text variant="tableLeft" lineHeight="6" fontSize="14px" px="1" borderRadius="md">
            {query.name}
          </Text>
        </Box>
        {data?.user_slicer?.is_shared && <CAIcon ml={-1} as={MdPeople} variant="secondary" />}
        <Menu placement="right-start" isLazy>
          <MenuButton
            as={IconButton}
            aria-label="header-menu"
            size="xs"
            ml={2}
            icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
            variant="ghost"
          />
          <MenuList zIndex="popover" minW="10rem">
            <MenuItem
              isDisabled={!hasChanges}
              icon={<CAIcon as={IoRefresh} {...menuItemIconStyle} transform={"rotateY(180deg)"} />}
              onClick={resetQueryForm}
            >
              Reset
            </MenuItem>
            {canEdit(userSlicer?.role) && (
              <MenuItem
                icon={<CAIcon as={BiPencil} {...menuItemIconStyle} />}
                onClick={() => {
                  modalConfig.rename.confirmHandler = undefined;
                  setConfirmationModalState(ModalStatesType.RENAME);
                }}
              >
                Rename
              </MenuItem>
            )}
            {(canEdit(userSlicer?.role) || !userSlicer) && (
              <MenuItem
                isDisabled={!hasChanges}
                icon={<CAIcon as={IoSaveOutline} {...menuItemIconStyle} />}
                onClick={saveQuery}
              >
                Save
              </MenuItem>
            )}
            {query.server_id && (
              <MenuItem
                icon={<CAIcon as={VscSaveAs} {...menuItemIconStyle} />}
                onClick={() => {
                  setConfirmationModalState(ModalStatesType.SAVE_AS);
                }}
              >
                {S.COMMON.SAVE_AS}
              </MenuItem>
            )}
            {canDelete(userSlicer?.role) && (
              <MenuItem
                icon={<CAIcon as={IoTrashOutline} {...menuItemIconStyle} />}
                onClick={() => setConfirmationModalState(ModalStatesType.DELETE)}
              >
                Delete
              </MenuItem>
            )}
            {canShare(userSlicer?.role) && (
              <MenuItem
                icon={<CAIcon as={IoMdShare} {...menuItemIconStyle} />}
                onClick={() => {
                  // [NOTE] For some weird reason toggling modal on click of menu-item results in input of the modal being focused, not sure why
                  setTimeout(() => {
                    setShowShareModal(true);
                  }, 100);
                }}
              >
                Share
              </MenuItem>
            )}
          </MenuList>
        </Menu>
      </HStack>

      <SharePopup
        open={showShareModal}
        toggleModal={(state) => setShowShareModal(state)}
        recordId={query.server_id as number}
        entity={CA_Mastr_Models_v1_0_Models_Entity.USER_SLICER}
        recordName={query.name}
        triggerElement={() => <></>}
      />

      <ConfirmationModalPopup
        isOpen={!!confirmationModalState}
        onModalClose={closeConfirmationModalPopup}
        onCancel={closeConfirmationModalPopup}
        onConfirm={popupOnConfirmHandler}
        headerText={confirmationModalState ? modalConfig[confirmationModalState]?.headerText : ""}
        description={confirmationModalState ? modalConfig[confirmationModalState]?.description : ""}
        showCloseIcon
        showFooter={
          confirmationModalState === ModalStatesType.DELETE ||
          confirmationModalState === ModalStatesType.SAVE_AS ||
          confirmationModalState === ModalStatesType.RENAME
        }
      />
    </>
  );
};

export default SlicerPageLabel;
