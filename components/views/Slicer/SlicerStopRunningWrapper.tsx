import React from "react";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { TLoanLevelLastRunDetails } from "@/types/slicer";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { showErrorToast } from "@/design-system/theme/toast";
import { getAPIErrorMessage } from "@/utils/helpers";
import { CancelService } from "@/utils/openapi";

interface Props {
  lastRunInfo: TLoanLevelLastRunDetails | undefined;
  setLastRunInfo: (state: TLoanLevelLastRunDetails) => void;
}

export const SlicerStopRunningWrapper: React.FC<Props> = ({ lastRunInfo, setLastRunInfo }) => {
  const {
    action: { setIsTimerRunning, resetRun },
  } = useSlicerModule();

  const { replaceWithoutRendering, query } = useQueryParameters();

  const triggerCancel = React.useCallback(async () => {
    if (lastRunInfo?.loanLevelRequestData.run_id) {
      try {
        await CancelService.postCancel(undefined, lastRunInfo?.loanLevelRequestData.run_id);
      } catch (error) {
        showErrorToast("Error", getAPIErrorMessage(error));
      }
    }
    setIsTimerRunning(false);
    resetRun();
    setLastRunInfo({
      loanLevelRequestData: {
        run_id: undefined,
      },
    });
    replaceWithoutRendering(
      {
        id: query?.server_id?.toString(),
      },
      true
    );
  }, [
    lastRunInfo?.loanLevelRequestData.run_id,
    query?.server_id,
    replaceWithoutRendering,
    resetRun,
    setIsTimerRunning,
    setLastRunInfo,
  ]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};
