import React from "react";
import { CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer, CA_Mastr_Models_v1_0_Models_TagType } from "@/utils/openapi";
import { TagSelector } from "@/design-system/organisms/TagSelector/TagSelector";
import { usePatchUserSlicerSWR } from "@/utils/swr-hooks/UserSlicer";
import { useGetUserSlicerSWR } from "@/utils/swr-hooks";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";

export const SlicerTagSelector: React.FC<Pick<CA_Mastr_Api_v1_0_Models_UserSlicer_UserSlicer, "user_slicer_id">> = ({
  user_slicer_id,
}) => {
  const {
    state: { query },
  } = useSlicerModule();
  const { data, isValidating, isLoading } = useGetUserSlicerSWR(query.server_id);
  const { trigger: patchUserView, isMutating } = usePatchUserSlicerSWR();

  const updated = data?.user_slicer?.updated;

  const handleSubmit = (selections: string[]) => {
    patchUserView({
      user_slicer_id: user_slicer_id,
      tags: selections,
      last_updated: updated ?? undefined,
    });
  };

  const handleRemoveClick = (tags: string[]) => {
    patchUserView({
      user_slicer_id: user_slicer_id,
      tags: tags,
      last_updated: updated ?? undefined,
    });
  };

  const role = data?.user_slicer?.role;
  const tags = data?.user_slicer?.tags;

  if (!role || !tags || isValidating || isLoading) return null;
  return (
    <TagSelector
      role={role}
      tags={data?.user_slicer?.tags ?? []}
      tagType={CA_Mastr_Models_v1_0_Models_TagType.USER_SLICER}
      onSubmit={handleSubmit}
      onRemoveClick={handleRemoveClick}
      isDeleting={isMutating}
    />
  );
};
