import * as React from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Flex, Grid, GridItem } from "@chakra-ui/react";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { blurActiveElement, isSuperAdmin } from "@/utils/helpers";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { RunInfo, RunTag, RunTagButtonGroup } from "@/design-system/molecules/RunTag/RunTag";
import { runToastManager } from "@/utils/run-toast-manager";
import RunAlertButton from "@/components/helpers/RunAlertButton";
import { SQLQueriesModal } from "@/components/helpers/SQLQueriesModal/SQLQueriesModal";
import { Tokens } from "@/hooks/useSWRIncremental";
import { useLoanLevelSlicerSqlQuerySWR } from "@/utils/swr-hooks/Slicer";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import SlicerPageLabel from "./SlicerPageLabel";

export type RunClickOptParams = { no_cache: boolean; runId?: string; isOldRun?: boolean; lastRunDate?: string };
interface Props {
  onRunClick?: (opts: RunClickOptParams) => void;
  stopWatch?: React.JSX.Element;
  hasTimer?: boolean;
  runInfo?: RunInfo;
  tokens: Tokens[] | undefined;
  stopRunning?: React.JSX.Element;
  tagSelectorEl: React.JSX.Element;
}

const SlicerHeader: React.FC<Props> = ({
  onRunClick,
  stopWatch,
  hasTimer = true,
  runInfo,
  tokens,
  stopRunning,
  tagSelectorEl,
}) => {
  const {
    state: { userSettings, isTimerRunning, isPending },
  } = useSlicerModule();

  const {
    state: { userData },
  } = useAuthentication();

  const onRun = React.useCallback(() => {
    runInfo?.id && runToastManager.removeToasts(runInfo.id);
    blurActiveElement();
    onRunClick?.({ no_cache: !userSettings.useCache });
  }, [onRunClick, runInfo?.id, userSettings.useCache]);

  useHotkeys("Ctrl+Enter, Command+Enter", onRun, {
    enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
  });

  useHotkeys(
    "Ctrl+Shift+Enter, Command+Shift+Enter",
    () => {
      runInfo?.id && runToastManager.removeToasts(runInfo.id);
      blurActiveElement();
      onRunClick?.({ no_cache: true });
    },
    {
      enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
    }
  );

  const { data: sqlQueryData } = useLoanLevelSlicerSqlQuerySWR({
    tokens,
    skip: tokens?.length === 0 || !isSuperAdmin(userData) || isTimerRunning,
  });

  const hasQuery = sqlQueryData?.slicer_queries?.some((item) => item.query);

  return (
    <Grid
      templateColumns={{
        base: "repeat(1, 1fr)",
        md: "repeat(3, 1fr)",
      }}
      gap={runInfo?.id ? 6 : 0}
      px={2}
      alignItems="center"
    >
      <GridItem display={{ base: "none", md: "block" }} />
      <GridItem display="grid" placeItems={{ base: "center", md: "flex-start", xl: "center" }}>
        <SlicerPageLabel />
      </GridItem>

      <GridItem display="grid" placeItems="flex-end">
        <Grid
          gap={2}
          placeItems="flex-end"
          alignItems="center"
          templateColumns={{
            base: "repeat(1, 1fr)",
            "2xl": "repeat(2, auto)",
          }}
        >
          <GridItem order={{ sm: 2, "2xl": -1 }}>{tagSelectorEl}</GridItem>
          <GridItem>
            <Flex alignItems="center" gap={runInfo?.id ? 2 : { base: 0, md: 4 }}>
              {sqlQueryData?.slicer_queries && tokens && hasQuery && (
                <SQLQueriesModal
                  key={tokens.toString()}
                  data={sqlQueryData?.slicer_queries?.filter((item) => item.query)}
                />
              )}

              {runInfo?.id && (
                <RunTagButtonGroup>
                  <RunAlertButton runId={runInfo?.id} />
                  <RunTag runInfo={runInfo} />
                </RunTagButtonGroup>
              )}
              {stopWatch}
              {isPending ? (
                <CARun disabled={!hasTimer || isPending} title={S.MODULES.SLICER.RUN} />
              ) : isTimerRunning && stopRunning ? (
                stopRunning
              ) : (
                <CARun onClick={onRun} disabled={!hasTimer || isTimerRunning} title={S.MODULES.SLICER.RUN} />
              )}
            </Flex>
          </GridItem>
        </Grid>
      </GridItem>
    </Grid>
  );
};

export default SlicerHeader;
