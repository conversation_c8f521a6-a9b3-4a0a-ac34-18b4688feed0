import { Button, HStack, useColorModeValue } from "@chakra-ui/react";
import { LATEST_DATE } from "@/constants/slicer";

interface SlicerLatestDateHeaderProps {
  onChange: (value: string) => void;
  isSelected: boolean;
  closeDatePicker: () => void;
}
const SlicerLatestDateHeader = ({ onChange, isSelected, closeDatePicker }: SlicerLatestDateHeaderProps) => {
  const onClickHandler = () => {
    onChange(LATEST_DATE);
    closeDatePicker();
  };

  return (
    <HStack
      w="full"
      px={"0.6rem"}
      py="0.25rem"
      bg={useColorModeValue("white", "celloBlue.1000")}
      justifyContent="center"
    >
      <Button
        letterSpacing={0.5}
        fontSize="xs"
        fontWeight="none"
        size="xs"
        borderRadius="none"
        bg={isSelected ? "celloBlue.500" : "inherit"}
        color={isSelected ? "white" : "inherit"}
        onClick={onClickHandler}
        _hover={{ bg: "celloBlue.400", color: "white" }}
      >
        {LATEST_DATE}
      </Button>
    </HStack>
  );
};

export default SlicerLatestDateHeader;
