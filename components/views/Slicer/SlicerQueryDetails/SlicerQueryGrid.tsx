import React from "react";
import { State, useSWRConfig } from "swr";
import { useRouter } from "next/router";
import { CellClickedEvent, ColDef, ModelUpdatedEvent } from "ag-grid-community";
import { HStack, Text } from "@chakra-ui/react";
import { AgGridReact } from "ag-grid-react";
import {
  ApiError,
  CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables,
  CA_Mastr_Api_v1_0_Models_Slicer_LoanQueryResponse,
  CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView,
  UserGridViewService,
} from "@/utils/openapi";
import {
  defaultCsvExportParams,
  defaultExcelExportParams,
  diff,
  getCurrentGridState,
  wavgFunction,
} from "@/design-system/molecules/CAGrid/helpers";
import { insertGridView } from "@/components/helpers/ViewSelector/gridViewApi";
import { showWarningToast } from "@/design-system/theme/toast";
import { DEFAULT_VIEW_NAME, defaultColDef } from "@/design-system/molecules/CAGrid/constants";
import { getSlicerGridColumns, resetGridRowsColor, setGridRowColorBasedOnGroupLevel } from "@/utils/helpers/slicer";
import { useSlicerModule } from "@/contexts/ModuleContexts/SlicerModuleContext";
import { SLICER_ROW_HEIGHT } from "@/constants/slicer";
import { getDisplayNameFromVariable, getMonthYearFromNumericYYYYMM } from "@/utils/helpers";
import { userGridViewServiceKey } from "@/utils/swr-hooks/UserGridView";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { CAGridWithoutStore } from "@/design-system/molecules/CAGrid/CAGrid";
import { useGridStoreContext } from "@/design-system/molecules/CAGrid/GridContext";
import { slicerQueryGridRefManager } from "./slicerGridRefManager";
import { useSlicerStore } from "./useSlicerStore";

type SlicerQueryGridProps = {
  columns: ColDef[];
  allData:
    | {
        name: string;
        data: CA_Mastr_Api_v1_0_Models_Slicer_LoanQueryResponse;
      }[]
    | null
    | undefined;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  results: any[];
  lastRun?: string;
  error?: ApiError | undefined;
  oldRunQueryId?: number | undefined;
  usePreFactorDateBalance?: boolean | null | undefined;
  apiRequestVariables: (CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables | null | undefined)[];
  isPartiallyLoaded?: boolean;
  queryType?: string;
};

const defaultColDefs = {
  ...defaultColDef,
  suppressHeaderMenuButton: true,
};

const aggFuncs = {
  wavg: wavgFunction,
  diff: diff,
};
const autoGroupColumnDef = {
  pinned: "left" as const,
  initialWidth: 200,
  cellRendererParams: {
    suppressCount: true, // turn off the row count
  },
};

const SlicerQueryGrid: React.FC<SlicerQueryGridProps> = ({
  columns,
  allData,
  results,
  lastRun,
  error,
  usePreFactorDateBalance,
  apiRequestVariables,
  isPartiallyLoaded,
  queryType,
}: SlicerQueryGridProps) => {
  const setSelectedGridView = useGridStoreContext((state) => state.setSelectedGridView);
  const groupedColumns = React.useMemo(() => getSlicerGridColumns(columns), [columns]);
  const setHasQueryChanges = useSlicerStore((state) => state.setHasQueryChanges);

  const {
    state: { query },
  } = useSlicerModule();

  const { cache, mutate: globalMutator } = useSWRConfig();

  const router = useRouter();

  const gridRef = React.useRef<AgGridReact | null>(null);

  React.useEffect(() => {
    const params = router.query as { id: string; user_grid_view_id: string };

    const loadGridView = async () => {
      if (params.user_grid_view_id && Number(params.id) === query?.server_id) {
        const data = await UserGridViewService.getUserGridView(Number(params.user_grid_view_id));
        if (data.user_grid_view?.grid_key?.includes(`${query?.server_id}`)) {
          setSelectedGridView(data.user_grid_view);
        } else {
          showWarningToast("Failed to load grid view", "Grid view is no longer available.");
        }
      }
    };

    loadGridView();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getQueryGridState = () => {
    if (!gridRef.current) return;

    const gridApi = gridRef.current?.api;

    return getCurrentGridState(gridApi, undefined);
  };

  const onFirstDataRendered = React.useCallback(() => {
    // After first data is rendered, if there are no views added, then add one
    const slicerId = query?.server_id;
    const data: State<{ user_grid_views: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView[] }> | undefined =
      cache.get(userGridViewServiceKey.getUserGridView(userGridViewServiceKey.getSlicerGridKey(slicerId)) ?? "");

    if (!data?.data) return;

    const gridViews = data.data?.user_grid_views ?? [];
    if ((!gridViews || gridViews.length === 0) && query?.isQueryOwner) {
      const gridState = getQueryGridState();
      if (gridState) return;
      insertGridView(
        {
          grid_view_state: JSON.stringify(gridState),
          grid_view_name: DEFAULT_VIEW_NAME,
          grid_key: userGridViewServiceKey.getSlicerGridKey(slicerId),
        },
        false
      ).then(() => {
        globalMutator(userGridViewServiceKey.getUserGridView(userGridViewServiceKey.getSlicerGridKey(slicerId)));
      });
    }
  }, [cache, globalMutator, query?.isQueryOwner, query?.server_id]);

  const onModelUpdated = React.useCallback((e: ModelUpdatedEvent) => {
    const groups = e.api?.getRowGroupColumns();
    if (groups.length === 0) {
      resetGridRowsColor();
    } else {
      setGridRowColorBasedOnGroupLevel(groups);
    }
  }, []);

  const displayAPIRequestVariables = () => {
    return (
      <HStack spacing={4} wrap="wrap">
        {apiRequestVariables.map((variable) => (
          <HStack key={variable?.field}>
            <Text variant="primary" whiteSpace="nowrap">
              {variable?.name} ({getDisplayNameFromVariable(variable?.field ?? "-")}) :
            </Text>
            <Text variant="secondary" whiteSpace="nowrap">
              {variable?.field === "model_version"
                ? variable.value
                : (variable?.value?.length || 0) >= 6
                ? getMonthYearFromNumericYYYYMM(variable?.value)
                : variable?.value}
            </Text>
          </HStack>
        ))}
      </HStack>
    );
  };

  const defaultExcelExportParam = React.useMemo(
    () => ({ ...defaultExcelExportParams, fileName: query?.name }),
    [query?.name]
  );
  const defaultCsvExportParam = React.useMemo(
    () => ({ ...defaultCsvExportParams, fileName: query?.name }),
    [query?.name]
  );

  const onCellClicked = React.useCallback((event: CellClickedEvent) => {
    if (event.column.getColId() === "groupbycolumn_poolnumber" && !event.rowPinned) {
      safeSessionStorage.removeItem(SESSION_STORAGE_KEY.DASHBOARD_LAST_RUN_ID);
      window.open(
        `/pricer/pricing/dashboard?bond_name=${encodeURIComponent(event.data["groupbycolumn_poolnumber"])}`,
        "_blank"
      );
    }
  }, []);

  const context = React.useMemo(
    () => ({
      usePreFactorDateBalance,
      queryType,
    }),
    [usePreFactorDateBalance, queryType]
  );

  const handleChanges = (hasChanges: boolean, gridType?: string) => {
    if (gridType) {
      setHasQueryChanges(hasChanges, gridType);
    }
  };

  return (
    <CAGridWithoutStore
      stackStyles={{
        minH: "35rem",
        h: "calc(100vh - 16rem)",
      }}
      gridType={`${userGridViewServiceKey.getSlicerGridKey(query?.server_id ?? query?.key)}`}
      ref={(ref) => {
        gridRef.current = ref;
        slicerQueryGridRefManager.storeGridRef(gridRef.current);
      }}
      hasRun={!!lastRun || allData?.length !== 0}
      className="Slicer"
      gridProps={{
        columnDefs: groupedColumns,
        rowData: error ? [] : !isPartiallyLoaded ? undefined : results,
        aggFuncs,
        groupDefaultExpanded: -1,
        onFirstDataRendered,
        context,
        defaultColDef: defaultColDefs,
        defaultExcelExportParams: defaultExcelExportParam,
        defaultCsvExportParams: defaultCsvExportParam,
        rowHeight: SLICER_ROW_HEIGHT,
        onModelUpdated,
        autoGroupColumnDef,
        onCellClicked,
      }}
      initialMessage={`Click on ▷ to load Slicer Query.`}
      showExpand
      hideSearch
      cardProps={{
        title: " ",
        cardKey: "matrix-grid",
        cardStyleOnOpen: {
          height: "full",
        },
        allowCollapse: true,
      }}
      shouldDisableSave={() => {
        if (query?.server_id) return false;
        else {
          showWarningToast("Unsaved Query", "Please save the query first in order to save grid view");
          return true;
        }
      }}
      getSharableLink={(activeGridId) => {
        return `${location.href}&user_grid_view_id=${activeGridId}`;
      }}
      customEl={apiRequestVariables?.length === 0 || allData?.length === 0 ? undefined : displayAPIRequestVariables()}
      canCreateOrModifyViews={query?.isQueryOwner}
      allowViewSave={false}
      onHasChangesCallback={handleChanges}
    />
  );
};

export default React.memo(SlicerQueryGrid);
