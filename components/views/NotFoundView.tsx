import * as React from "react";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import BlankLayout from "../layouts/BlankLayout";
import MainLayout from "../layouts/MainLayout";

const NotFoundView: React.FC = () => {
  const {
    state: { userData },
  } = useAuthentication();

  const Layout = userData ? MainLayout : BlankLayout;

  return (
    <Layout title="404 Not Found">
      <p>404 Not Found</p>
    </Layout>
  );
};

export default NotFoundView;
