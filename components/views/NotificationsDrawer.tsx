import * as React from "react";
import { <PERSON>, Drawer, <PERSON>er<PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>, IconButton, useColorModeValue } from "@chakra-ui/react";
import { IoClose } from "react-icons/io5";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import CAIcon from "@/design-system/atoms/CAIcon";
import { CA_Mastr_Api_v1_0_Models_Notification_Notification } from "@/utils/openapi";

export type NotificationsDrawerProps = {
  onClose: () => void;
  isOpen: boolean;
  notificationsList: CA_Mastr_Api_v1_0_Models_Notification_Notification[];
  dismissNotification: (id?: number) => void;
};

const NotificationsDrawer: React.FC<NotificationsDrawerProps> = ({
  onClose,
  isOpen,
  notificationsList,
  dismissNotification,
}: NotificationsDrawerProps) => {
  const drawerBackgroundColor = useColorModeValue("celloBlue.75", "celloBlue.900");
  const contentBackgroundColor = useColorModeValue("celloBlue.25", "celloBlue.900");
  const iconColor = useColorModeValue("celloBlue.400", "white");
  const isDrawerEmpty = notificationsList.length ? false : true;

  const renderNotifications = () => {
    return notificationsList
      .sort((n1, n2) => (n2.inserted ?? "").localeCompare(n1.inserted ?? ""))
      .map((n) => {
        return (
          <CAAlertCard
            key={n.notification_id}
            title={n.title ?? undefined}
            description={n.description ?? undefined}
            status={n.notification_type ?? "info"}
            dateTime={n.inserted ? new Date(n.inserted) : undefined}
            onCloseHandler={() => dismissNotification(n.notification_id as number)}
            fullWidth
            applyBorder
            actionButtonLabel={n.url ? "Open" : undefined}
            onActionClick={n.url ? () => window.open(n.url as string, "_blank") : undefined}
          />
        );
      });
  };

  return (
    <Drawer placement="right" onClose={() => onClose()} isOpen={isOpen} size={"md"}>
      <DrawerOverlay>
        <DrawerContent overflowY="auto" background={drawerBackgroundColor}>
          <Box background={contentBackgroundColor} width="100%" minHeight="3rem" position="sticky" top="0" zIndex="1">
            <IconButton
              position="absolute"
              left="2"
              top="2"
              aria-label="clear"
              size="sm"
              icon={<CAIcon as={IoClose} boxSize={5} variant="secondary" />}
              onClick={() => onClose()}
              background={"transparent"}
            />
            {!isDrawerEmpty && (
              <Box
                p={5}
                pb={0}
                position="absolute"
                right="0"
                _hover={{ cursor: "pointer" }}
                onClick={() => dismissNotification()}
                color={iconColor}
                width="max-content"
              >
                Clear all
              </Box>
            )}
          </Box>
          <Box height="100%" width="100%" flexDirection="column">
            {isDrawerEmpty ? (
              <Box display="flex" justifyContent="center" alignItems="center" position="relative" top="50%">
                No Notifications
              </Box>
            ) : (
              renderNotifications()
            )}
          </Box>
        </DrawerContent>
      </DrawerOverlay>
    </Drawer>
  );
};

export default NotificationsDrawer;
