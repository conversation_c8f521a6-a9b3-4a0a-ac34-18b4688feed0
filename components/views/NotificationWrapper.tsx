import * as React from "react";
import { IoMdNotifications } from "react-icons/io";
import { Box, IconButton, Text } from "@chakra-ui/react";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useGetNotificationsSWR } from "@/utils/swr-hooks";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { useDeleteNotificationSWR, useDeleteNotificationsSWR } from "@/utils/swr-hooks/Notification";
import {
  CA_Mastr_Api_v1_0_Models_Notification_Notification,
  CA_Mastr_Models_v1_0_Models_NotificationType,
} from "@/utils/openapi";
import { showErrorToast, showInfoToast, showSuccessToast, showWarningToast } from "@/design-system/theme/toast";
import NotificationsDrawer from "./NotificationsDrawer";

const NotificationsWrapper: React.FC = () => {
  const {
    state: { userData },
  } = useAuthentication();
  const {
    state: { notificationsDrawerOpen },
    action: { toggleNotificationsDrawer },
  } = useGlobalContext();

  const { data: getNotificationRes, mutate } = useGetNotificationsSWR(userData?.username ?? undefined, 5000);
  const { trigger: deleteNotification } = useDeleteNotificationSWR();
  const { trigger: deleteNotifications } = useDeleteNotificationsSWR();

  const notificationsList = React.useMemo(
    () => getNotificationRes?.notifications ?? [],
    [getNotificationRes?.notifications]
  );

  const dismissNotification = async (notificationId?: number) => {
    if (notificationId) {
      await deleteNotification({ notificationId });
      mutate({
        ...getNotificationRes,
        notifications: notificationsList.filter((n) => n.notification_id !== notificationId),
      });
    } else {
      await deleteNotifications();
      mutate({ ...getNotificationRes, notifications: [] });
    }
  };

  useOnNewNotifications({ notificationsList, dismissNotification });

  return (
    <>
      <Box position="relative">
        {(notificationsList ?? []).length > 0 && (
          <Box
            bg="red"
            w="1rem"
            h="1rem"
            position="absolute"
            borderRadius="full"
            right="3px"
            top="3px"
            zIndex="docked"
            display="flex"
            justifyContent="center"
            alignItems="center"
            pointerEvents="none"
          >
            <Text color="celloBlue.25">{notificationsList?.length > 99 ? "" : notificationsList?.length}</Text>
          </Box>
        )}
        <IconButton
          aria-label="notifications"
          variant="ghost"
          icon={<CAIcon as={IoMdNotifications} variant="secondary" boxSize={5} />}
          onClick={() => toggleNotificationsDrawer()}
        />
      </Box>
      <NotificationsDrawer
        onClose={toggleNotificationsDrawer}
        isOpen={notificationsDrawerOpen}
        notificationsList={notificationsList}
        dismissNotification={dismissNotification}
      />
    </>
  );
};

export default NotificationsWrapper;

const getToastByNotificationType = (notification_type?: "error" | "warning" | "success" | "share" | "info") => {
  switch (notification_type) {
    case "error":
      return showErrorToast;
    case "warning":
      return showWarningToast;
    case "success":
      return showSuccessToast;
    case "info":
    default:
      return showInfoToast;
  }
};

// supporting hooks
const useOnNewNotifications = ({
  notificationsList,
  dismissNotification,
}: {
  notificationsList: CA_Mastr_Api_v1_0_Models_Notification_Notification[];
  dismissNotification: (notification_id?: number) => void;
}) => {
  React.useEffect(() => {
    notificationsList.forEach((n) => {
      const toastHandler = getToastByNotificationType(n.notification_type);
      // [UI-1873] Do not show toast for "share" type notifications
      if (!n.last_fetched && n.notification_type !== CA_Mastr_Models_v1_0_Models_NotificationType.SHARE) {
        toastHandler(n.title ?? "Notification", n?.description ?? "", {
          onCloseHandler: dismissNotification.bind(null, n.notification_id),
        });
      }
    });
    // Only reload when notifications are fetched
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [notificationsList]);
};
