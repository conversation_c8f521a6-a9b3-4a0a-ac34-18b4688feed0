import { CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigInfo_Slice } from "@/utils/openapi";

export const transformColToOptionType = (
  col: [string | null | undefined, CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigInfo_Slice[]]
) => ({
  label: col[0] ?? "",
  options: col[1].map((c) => ({
    ...c,
    label: c.display_name,
    value: c.dimension,
  })),
});

export type OptionType = ReturnType<typeof transformColToOptionType>;

export const transformStringToOptionType = (el: string) => ({
  label: el,
  value: el,
});
