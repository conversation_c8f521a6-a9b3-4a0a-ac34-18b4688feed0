import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u<PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, useColorModeValue } from "@chakra-ui/react";
import React from "react";
import { IoEllipsisVerticalOutline, IoTrashOutline } from "react-icons/io5";
import { MdOutlineEditNote } from "react-icons/md";
import CAIcon from "@/design-system/atoms/CAIcon";
import CAModal from "@/design-system/molecules/CAModal";
import { EmpiricalTrackingColumnsSelectionForm } from "./EmpiricalTrackingSimplifiedColumnsSelectionForm";

interface EmpiricalTrackingColumnsMenuProps {
  defaultSelectedColumns: string[];
  onSubmit: (cols: string[]) => void;
  onDeleteAllClick: () => void;
}

export const EmpiricalTrackingColumnsMenu = ({
  defaultSelectedColumns,
  onSubmit,
  onDeleteAllClick,
}: EmpiricalTrackingColumnsMenuProps) => {
  const [isOpen, setIsOpen] = React.useState(false);
  return (
    <>
      <Menu placement="right-start" isLazy closeOnSelect>
        <MenuButton
          as={IconButton}
          aria-label="filter-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
          variant="ghost"
        />
        <MenuList zIndex="popover" minW="10rem">
          <MenuItem icon={<CAIcon as={MdOutlineEditNote} boxSize={4} />} onClick={() => setIsOpen(true)}>
            Modify
          </MenuItem>
          <MenuItem icon={<CAIcon as={IoTrashOutline} boxSize={4} />} onClick={onDeleteAllClick}>
            Delete All
          </MenuItem>
        </MenuList>
      </Menu>
      <CAModal
        modalBackground={useColorModeValue("celloBlue.50", "celloBlue.900")}
        contentStyle={{ minW: "45%" }}
        modalHeader="Columns"
        headerStyle={{
          color: useColorModeValue("celloBlue.500", "turmericRoot.500"),
          textTransform: "uppercase",
          py: "1rem",
          px: "1.7rem",
        }}
        modalBodyProps={{
          fontSize: "md",
          mx: "1.7rem",
          p: 0,
        }}
        showCloseIcon
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      >
        <EmpiricalTrackingColumnsSelectionForm
          toggleIsOpen={setIsOpen}
          onModalClose={(columns) => {
            onSubmit(columns);
            setIsOpen(false);
          }}
          defaultSelectedColumns={defaultSelectedColumns}
        />
      </CAModal>
    </>
  );
};
