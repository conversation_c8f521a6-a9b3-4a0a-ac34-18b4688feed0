import React from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { DragEndEvent } from "@dnd-kit/core";
import { Box } from "@chakra-ui/react";
import { groupByKey } from "@/design-system/molecules/CAGrid/helpers";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import CACard from "@/design-system/molecules/CACard";
import { CADragDropContainer } from "@/design-system/molecules/CADragDrop";
import { removeDuplicatesFromArrays } from "@/utils/helpers";
import {
  ColumnType,
  EmpiricalTrackingFormType,
  getDefaultOperator,
  getDefaultValuesForOptions,
} from "../../useEmpiricalTrackingSimplifiedForm";
import { OptionType, transformColToOptionType } from "../../utils";
import { EmpiricalTrackingColumnRow } from "./EmpiricalTrackingSimplifiedColumnRow";
import { EmpiricalTrackingColumnsMenu } from "./EmpiricalTrackingSimplifiedColumnsMenu";

interface EmpiricalTrackingColumnsProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
}

export const EmpiricalTrackingColumns = ({ form }: EmpiricalTrackingColumnsProps) => {
  const { data } = useEmpiricalTrackingConfiguration();

  const {
    fields: columns,
    append,
    update,
    move,
    remove,
  } = useFieldArray({
    control: form.control,
    name: "columns",
  });

  const nonSelectedColumns =
    data?.empirical_tracking_config?.columns &&
    removeDuplicatesFromArrays(data?.empirical_tracking_config?.columns, columns, (col) => col.dimension);

  const columnsByCategory: OptionType[] = groupByKey(nonSelectedColumns ?? [], (item) => item.category).map(
    transformColToOptionType
  );

  const addColumn = (value: ColumnType) => {
    append({
      ...value,
      dimension: value.value,
      enabled: true,
      display_name: value.display_name,
    });
  };

  const updateColumn = (i: number, value: ColumnType) => {
    update(i, {
      ...value,
      dimension: value.value,
      enabled: true,
      display_name: value.display_name,
    });
  };

  const removeColumn = (i: number) => remove(i);

  const dragColumnHandler = (event: DragEndEvent) => {
    const source = event.active.data.current?.sortable.index;
    const destination = event.over?.data.current?.sortable.index;
    if (destination == null) return;
    move(source, destination);
  };

  const addColumnsSelections = (selectedColsDimension: string[]) => {
    // Get config for the selected columns
    const selectedColsOnConfig = data?.empirical_tracking_config?.columns?.filter(
      (el) => el.dimension && selectedColsDimension.includes(el.dimension)
    );
    const currentColumns = form.getValues("columns");

    const selectedCols = selectedColsOnConfig?.map((column) => {
      const alreadySelectedCol = currentColumns?.find((col) => col.dimension === column.dimension);
      // If the column is already selected then return that column
      if (alreadySelectedCol) {
        return alreadySelectedCol;
      } else {
        // Otherwise get default values and operator and add them to the columns
        const operator = getDefaultOperator(column);
        const values = getDefaultValuesForOptions(column);
        return {
          ...column,
          operator,
          values,
          enabled: true,
        };
      }
    });

    form.setValue("columns", selectedCols);
  };

  const resetColumnsToDefaultValues = () => {
    form.setValue("columns", []);
  };

  const dragItems = columns.map((el) => el.id);
  const defaultSelectedColumns = columns.filter(Boolean).map((col) => col.dimension) as string[];

  return (
    <CACard
      title="Columns"
      variant="square"
      cardBodyStyle={{
        p: "0.3rem",
      }}
      headingRight={
        <EmpiricalTrackingColumnsMenu
          defaultSelectedColumns={defaultSelectedColumns}
          onSubmit={addColumnsSelections}
          onDeleteAllClick={resetColumnsToDefaultValues}
        />
      }
    >
      <CADragDropContainer onDragEnd={dragColumnHandler} items={dragItems}>
        {columns.map((col, i) => (
          <EmpiricalTrackingColumnRow
            key={col.id}
            col={col}
            index={i}
            onColumnChange={updateColumn}
            onDeleteClick={removeColumn}
            options={columnsByCategory}
            form={form}
          />
        ))}
      </CADragDropContainer>
      <Box mr="2.7rem" ml="2.8rem">
        <CAMultiSelectDropdown
          name="columns"
          options={columnsByCategory ?? []}
          shouldOnlyReturnValue={false}
          isMultiSelect={false}
          onChange={(value) => addColumn(value as ColumnType)}
        />
      </Box>
    </CACard>
  );
};
