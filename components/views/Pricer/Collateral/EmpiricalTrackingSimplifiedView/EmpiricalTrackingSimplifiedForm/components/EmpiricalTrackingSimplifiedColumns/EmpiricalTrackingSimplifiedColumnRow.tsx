import { Box, Flex, Icon, Icon<PERSON>utton } from "@chakra-ui/react";
import React from "react";
import { IoTrashOutline } from "react-icons/io5";
import { FieldArrayWithId, UseFormReturn, useWatch } from "react-hook-form";
import CACheckboxInput from "@/design-system/molecules/CACheckboxInput";
import { CADragDrop } from "@/design-system/molecules/CADragDrop";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { ReorderIcon } from "@/design-system/icons";
import CAIcon from "@/design-system/atoms/CAIcon";
import { ColumnType, EmpiricalTrackingFormType } from "../../useEmpiricalTrackingSimplifiedForm";
import { OptionType } from "../../utils";
import { EmpiricalTrackingColumnValue } from "./EmpiricalTrackingSimplifiedColumnValue";

interface EmpiricalTrackingColumnRowProps {
  col: FieldArrayWithId<EmpiricalTrackingFormType, "columns", "id">;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  index: number;
  options: OptionType[];
  onColumnChange: (index: number, value: ColumnType) => void;
  onDeleteClick: (index: number) => void;
}

export const EmpiricalTrackingColumnRow = ({
  col,
  form,
  index,
  options,
  onColumnChange,
  onDeleteClick,
}: EmpiricalTrackingColumnRowProps) => {
  const isEnabled = useWatch({
    name: `columns.${index}.enabled`,
    control: form.control,
  });
  return (
    <Box role="group" mr="0.7rem">
      <CADragDrop
        key={col.id}
        id={col.id}
        activatorElement={
          <Box
            w="0.8rem"
            cursor="move"
            visibility="hidden"
            _groupHover={{
              visibility: "visible",
            }}
          >
            <Icon as={ReorderIcon} />
          </Box>
        }
      >
        <Flex w="full" gap={2} alignItems="flex-start">
          <Flex w="full" gap={2} alignItems="center">
            <CACheckboxInput
              formProps={{
                w: "1rem",
              }}
              {...form.register(`columns.${index}.enabled`)}
              variant="small"
              hideLabel
              visibility={isEnabled ? "hidden" : "visible"}
              _groupHover={{
                visibility: "visible",
              }}
            />
            <CAMultiSelectDropdown
              tooltipText={col.display_name ?? undefined}
              name="columns"
              options={options ?? []}
              value={{
                label: col.display_name ?? "",
                value: col.dimension ?? "",
              }}
              isMultiSelect={false}
              shouldOnlyReturnValue={false}
              onChange={(value) => onColumnChange(index, value as ColumnType)}
              isDisabled={!isEnabled}
            />
          </Flex>
          <EmpiricalTrackingColumnValue
            isDisabled={!isEnabled}
            index={index}
            options={col.selections}
            type={col.type}
            form={form}
          />

          <IconButton
            visibility="hidden"
            _groupHover={{ visibility: "visible" }}
            aria-label="remove-column"
            size="xs"
            variant="unstyled"
            icon={<CAIcon as={IoTrashOutline} variant="secondary" boxSize={4} />}
            onClick={() => onDeleteClick(index)}
          />
        </Flex>
      </CADragDrop>
    </Box>
  );
};
