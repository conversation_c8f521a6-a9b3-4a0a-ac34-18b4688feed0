import React from "react";
import { Controller, UseFormReturn } from "react-hook-form";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { CA_Mastr_Models_v1_0_Models_ConfigurationType } from "@/utils/openapi";
import { EmpiricalTrackingFormType } from "../../useEmpiricalTrackingSimplifiedForm";

interface EmpiricalTrackingColumnValueProps {
  options: string[] | undefined | null;
  index: number;
  type: string | undefined;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  isDisabled: boolean;
}

export const EmpiricalTrackingColumnValue = ({
  index,
  options,
  type,
  form,
  isDisabled,
}: EmpiricalTrackingColumnValueProps) => {
  if (type === CA_Mastr_Models_v1_0_Models_ConfigurationType.LIST) {
    const opts = options?.map((opt) => ({
      value: opt,
      label: opt,
    }));
    return (
      <Controller
        control={form.control}
        name={`columns.${index}.values`}
        render={({ field: { onChange, value } }) => (
          <CAMultiSelectDropdown
            width="200px"
            name={`columns.${index}.values`}
            id="list"
            isMultiSelect
            shouldOnlyReturnValue
            options={opts ?? []}
            value={value ?? undefined}
            onChange={onChange}
            isDisabled={isDisabled}
          />
        )}
      />
    );
  }
  return null;
};
