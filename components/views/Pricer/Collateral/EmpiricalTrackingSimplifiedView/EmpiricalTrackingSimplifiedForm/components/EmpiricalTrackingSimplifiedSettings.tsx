import { Box } from "@chakra-ui/react";
import React from "react";
import { UseFormReturn } from "react-hook-form";
import ValueDisplay from "@/components/helpers/ValueDisplay";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import CACard from "@/design-system/molecules/CACard";
import { EmpiricalTrackingFormType } from "../useEmpiricalTrackingSimplifiedForm";

interface EmpiricalTrackingSettingsProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
}
export const EmpiricalTrackingSettings = ({ form }: EmpiricalTrackingSettingsProps) => {
  return (
    <CACard title="Settings" variant="square" borderBottomRadius="2xl">
      <ValueDisplay
        name="Pre-Factor Date Balance"
        value={
          <Box w="1.6rem">
            <CASwitchInput hideLabel {...form.register("settings.use_pre_factor_date_balance")} />
          </Box>
        }
      />
    </CACard>
  );
};
