import React from "react";
import { Center, <PERSON>u, <PERSON>u<PERSON>utton, <PERSON>uI<PERSON>, MenuList, Text, useDisclosure, useOutsideClick } from "@chakra-ui/react";
import { UseFormReturn } from "react-hook-form";
import { format } from "date-fns";
import { getOperators } from "@/constants/slicer";
import CAInput from "@/design-system/molecules/CAInput";
import { CA_Mastr_Models_v1_0_Models_Operator } from "@/utils/openapi";
import { getDateFromNumericYYYYMMDD } from "@/utils/helpers";
import { EmpiricalTrackingFormType, OperatorType } from "../useEmpiricalTrackingSimplifiedForm";

interface EmpiricalTrackingOperatorProps {
  dimensionType: string | null | undefined;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  index: number;
  dimensionIndex: number;
  isDisabled: boolean;
  includeLookback?: boolean;
}
export const EmpiricalTrackingOperator = ({
  dimensionType,
  form,
  dimensionIndex,
  index: sliceIndex,
  isDisabled,
  includeLookback,
}: EmpiricalTrackingOperatorProps) => {
  const ref = React.useRef<HTMLDivElement>(null);
  const { isOpen, onClose, onToggle, onOpen } = useDisclosure();

  const operatorsPerType = getOperators(dimensionType ?? "default", {
    excludeNotElementOf: true,
    includeLookBack: includeLookback,
  });

  useOutsideClick({
    ref,
    handler: () => {
      onClose();
    },
  });

  const closeOnBlur: React.KeyboardEventHandler<HTMLElement> = (e) => {
    if (e.key === "Tab" || e.key === "Escape") {
      onClose();
    }
  };

  const handleSelect = (operator: OperatorType) => {
    form.unregister(`algo_series.${sliceIndex}.dimensions.${dimensionIndex}.values`);
    if (operator.value === CA_Mastr_Models_v1_0_Models_Operator.LOOK_BACK) {
      const dimension = form.getValues(`algo_series.${sliceIndex}.dimensions.${dimensionIndex}`);
      form.setValue(
        `algo_series.${sliceIndex}.dimensions.${dimensionIndex}.values`,
        [dimension?.default_look_back?.toString() ?? "1"],
        {
          shouldDirty: true,
        }
      );
    } else {
      const dimension = form.getValues(`algo_series.${sliceIndex}.dimensions.${dimensionIndex}`);
      const lowerBoundDate = getDateFromNumericYYYYMMDD(dimension.default_lower_bound);
      const upperBoundDate = getDateFromNumericYYYYMMDD(dimension.default_upper_bound);

      if (lowerBoundDate && upperBoundDate) {
        switch (operator.value) {
          case CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE:
          case CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_LEFT_INCLUSIVE:
          case CA_Mastr_Models_v1_0_Models_Operator.WITHIN_RANGE_RIGHT_INCLUSIVE:
            form.setValue(
              `algo_series.${sliceIndex}.dimensions.${dimensionIndex}.values`,
              [format(lowerBoundDate, "yyyy-MM-dd"), format(upperBoundDate, "yyyy-MM-dd")],
              {
                shouldDirty: true,
              }
            );
            break;

          case CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN:
          case CA_Mastr_Models_v1_0_Models_Operator.LESS_THAN_OR_EQUAL_TO:
            form.setValue(
              `algo_series.${sliceIndex}.dimensions.${dimensionIndex}.values`,
              [format(lowerBoundDate, "yyyy-MM-dd")],
              {
                shouldDirty: true,
              }
            );
            break;

          case CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN:
          case CA_Mastr_Models_v1_0_Models_Operator.GREATER_THAN_OR_EQUAL_TO:
          case CA_Mastr_Models_v1_0_Models_Operator.EQUAL_TO:
            form.setValue(
              `algo_series.${sliceIndex}.dimensions.${dimensionIndex}.values`,
              [format(upperBoundDate, "yyyy-MM-dd")],
              {
                shouldDirty: true,
              }
            );
            break;
        }
      }
    }

    form.setValue(`algo_series.${sliceIndex}.dimensions.${dimensionIndex}.operator`, operator, {
      shouldDirty: true,
    });

    onClose();
  };

  return (
    <Menu isOpen={isOpen} closeOnSelect>
      <MenuButton as="div" ref={ref} onFocus={onOpen} onClick={onToggle} disabled={isDisabled} onKeyDown={closeOnBlur}>
        <CAInput
          width="2.188rem"
          height="30px"
          textAlign="center"
          readOnly
          {...form.register(`algo_series.${sliceIndex}.dimensions.${dimensionIndex}.operator.symbol`)}
          isDisabled={isDisabled}
          pointerEvents={isDisabled ? "none" : "inherit"}
        />
      </MenuButton>
      <MenuList onKeyDown={closeOnBlur}>
        {operatorsPerType?.map((operator, index) => (
          <MenuItem key={index} onClick={() => handleSelect(operator)}>
            <Center w="30px">
              <Text variant="tableLeft" fontSize="lg">
                {operator.symbol}
              </Text>
            </Center>
            <Text>{operator.displayName}</Text>
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};
