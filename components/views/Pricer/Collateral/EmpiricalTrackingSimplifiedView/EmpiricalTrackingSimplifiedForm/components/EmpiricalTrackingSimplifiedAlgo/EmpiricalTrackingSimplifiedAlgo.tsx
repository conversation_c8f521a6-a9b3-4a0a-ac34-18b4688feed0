import React from "react";
import { Box, Flex, useColorModeValue } from "@chakra-ui/react";
import { FieldArrayWithId, UseFormReturn, useFieldArray, useWatch } from "react-hook-form";
import { HiOutlineChevronLeft } from "react-icons/hi";
import CACard from "@/design-system/molecules/CACard";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import {
  ColumnType,
  DimensionType,
  EmpiricalTrackingFormType,
  algoSeriesDefaultValues,
  getDefaultOperator,
  getDefaultValuesForOptions,
} from "../../useEmpiricalTrackingSimplifiedForm";
import { EmpiricalTrackingDimensions } from "./EmpiricalTrackingSimplifiedDimension/EmpiricalTrackingSimplifiedDimensions";
import { EmpiricalTrackingAlgoMenu } from "./EmpiricalTrackingSimplifiedAlgoMenu";

export interface EmpiricalTrackingAlgoProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
  onCollapseClick: () => void;
}

export const EmpiricalTrackingAlgo = ({ form, onCollapseClick }: EmpiricalTrackingAlgoProps) => {
  const { data } = useEmpiricalTrackingConfiguration();
  const { fields: algos, remove } = useFieldArray({
    control: form.control,
    name: "algo_series",
  });

  const removeColumnsAfterAlgoSeriesChange = (i: number) => {
    const columns = form.getValues("columns");
    const filteredAlgos = algos.filter((_, index) => index !== i);

    const newColumns: ColumnType[] = [];

    columns?.forEach((column) => {
      const columnAgency = column.agencies;
      const isAgencySupported = filteredAlgos.some((algo) => columnAgency?.includes(algo?.agency ?? ""));

      if (isAgencySupported) {
        newColumns.push(column);
      }
    });

    form.setValue("columns", newColumns);
  };

  const removeAlgoSerie = (i: number) => {
    if (algos.length === 1) {
      form.setValue("algo_series", algoSeriesDefaultValues);
      return;
    }
    removeColumnsAfterAlgoSeriesChange(i);
    remove(i);
    if (i === 0) {
      form.setValue("columns", []);
    }
  };

  const autoFillDimensions = (index: number) => {
    const agency = form.getValues(`algo_series.${index}.agency`);
    const slicesBySupportedAgency = agency
      ? data?.empirical_tracking_config?.slices?.filter((slice) => slice.agencies?.includes(agency))
      : [];

    const dimensions: DimensionType[] | undefined = slicesBySupportedAgency?.map((slice) => {
      const operator = getDefaultOperator(slice);
      const values = getDefaultValuesForOptions(slice);
      return {
        ...slice,
        name: slice.dimension,
        values: values,
        enabled: true,
        display_name: slice.display_name,
        operator: operator,
      };
    });

    form.setValue(`algo_series.${index}.dimensions`, dimensions);
  };

  return (
    <CACard
      title="MBS Universe Matching Algorithm"
      cardBodyStyle={{ p: 0 }}
      borderBottomRadius={0}
      headerStyle={{ height: "30px" }}
      headingRight={
        <>
          <Box
            mr="1"
            cursor="pointer"
            borderRadius="full"
            bg={useColorModeValue("celloBlue.50", "celloBlue.800")}
            p={1}
            onClick={onCollapseClick}
          >
            <HiOutlineChevronLeft />
          </Box>
          <EmpiricalTrackingAlgoMenu
            onDeleteClick={() => removeAlgoSerie(0)}
            onResetClick={() => autoFillDimensions(0)}
          />
        </>
      }
    >
      <Flex flexDirection="column" ml={0.8} flex={1} pl="0.8rem" pr="1rem" pb="1rem" gap="1rem">
        {algos.map((algo, i) => {
          return <AlgoRow key={algo.id} form={form} algo={algo} index={i} />;
        })}
      </Flex>
    </CACard>
  );
};

interface AlgoRowProps {
  algo: FieldArrayWithId<EmpiricalTrackingFormType, "algo_series", "id">;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  index: number;
}

const AlgoRow = ({ algo, form, index }: AlgoRowProps) => {
  const isEnabled = useWatch({ name: `algo_series.${index}.enabled`, control: form.control });
  return (
    <Flex key={algo.id} w="full">
      <Box flex={1} ml="1rem" opacity={!isEnabled ? 0.5 : 1} pointerEvents={!isEnabled ? "none" : "auto"}>
        <EmpiricalTrackingDimensions
          key={algo.id}
          isAlgoSerieEnabled={isEnabled}
          defaultSupportedAgency={algo.agency}
          index={index}
          form={form}
        />
      </Box>
    </Flex>
  );
};
