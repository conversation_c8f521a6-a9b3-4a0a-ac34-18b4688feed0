import { useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { useRouter } from "next/router";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import { useValidateBondSWR } from "@/utils/swr-hooks/Util";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { useEffectOnce } from "@/hooks/useEffectOnce";
import { showWarningToast } from "@/design-system/theme/toast";
import { OperatorsType } from "@/types/slicer";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import {
  ColumnType,
  DimensionType,
  EmpiricalTrackingFormType,
  dimensionDefaultValue,
  getDefaultOperator,
  getDefaultValuesForOptions,
} from "../../useEmpiricalTrackingSimplifiedForm";

type FormColumnsType = Array<
  ColumnType & {
    name?: string | null | undefined;
    operator?: OperatorsType | undefined;
  }
>;

interface BondSelectionProps {
  index: number;
  form: UseFormReturn<EmpiricalTrackingFormType>;
  selectAgency: (agency: string | null | undefined) => void;
  supportedAgency: string | null | undefined;
}

const DEFAULT_DISABLED_COLUMNS = ["CRR", "CDR", "DTI", "ISSUER"];

export const useBondSelection = ({ index, form, selectAgency, supportedAgency }: BondSelectionProps) => {
  const { trigger: validateBond } = useValidateBondSWR();
  const { replaceWithoutRendering } = useQueryParameters();
  const {
    state: { bond_name },
  } = usePricerModule();
  const { data } = useEmpiricalTrackingConfiguration();
  const router = useRouter();

  const runId = router.query.run_id as string | undefined;
  const bondName = router.query.bond_name as string | undefined;
  const userEmpiricalTrackingId = router.query.id as string | undefined;

  const autoFillDimensions = (agency: string | undefined | null) => {
    const slicesBySupportedAgency = agency
      ? data?.empirical_tracking_config?.slices?.filter((slice) => slice.agencies?.includes(agency))
      : [];

    const dimensions: DimensionType[] | undefined = slicesBySupportedAgency?.map((slice) => {
      const operator = getDefaultOperator(slice);
      const values = getDefaultValuesForOptions(slice, agency ?? undefined);
      return {
        ...slice,
        name: slice.dimension,
        values: values,
        enabled: true,
        display_name: slice.display_name,
        operator: operator,
      };
    });

    form.setValue(`algo_series.${index}.dimensions`, dimensions);
  };

  const mergeOrAddColumnsOfMultipleAlgoSeries = () => {
    const algos = form.getValues("algo_series");
    const formColumns = form.getValues("columns");
    const configColumns = data?.empirical_tracking_config?.columns;
    const newColumns: FormColumnsType | undefined = [];

    algos?.forEach((algo) => {
      const agency = algo.agency;

      if (!agency) {
        return;
      }

      const supportedColumns = configColumns?.filter((col) => col.agencies?.includes(agency));

      supportedColumns?.forEach((col) => {
        const existingCol = newColumns?.find((c) => c.dimension === col.dimension);
        const existingColPrev = formColumns?.find((c) => c.dimension === col.dimension);

        if (existingColPrev && !existingCol) {
          newColumns.push(existingColPrev);
          return;
        }

        if (!existingCol) {
          newColumns.push({
            ...col,
            name: col.dimension,
            enabled: col.dimension && DEFAULT_DISABLED_COLUMNS.includes(col.dimension) ? false : true,
            values: getDefaultValuesForOptions(col),
            operator: getDefaultOperator(col),
          });
          return;
        }
      });
    });

    return newColumns;
  };

  const autoFillColumns = () => {
    const columns = mergeOrAddColumnsOfMultipleAlgoSeries();
    form.setValue("columns", columns);
  };

  const addBondAndAgencyToSerie = async (value: string) => {
    const res = await validateBond({
      bond_name: value,
    });
    let agency = res?.security_info?.sub_type;
    if (!["FHL", "FNM", "GNM", "GPL"].includes(agency ?? "")) {
      // Use security_info?.agency instead of sub_type when it's not one of them
      agency = res?.security_info?.agency;

      if (!["FHL", "FNM", "GNM", "GPL"].includes(agency ?? "")) {
        showWarningToast("Not Supported", "This Bond is currently not supported.");
        return;
      }
    }

    form.setValue(`algo_series.${index}.bond_name`, value, {
      shouldDirty: true,
    });
    const prevSelectedAgency = form.getValues(`algo_series.${index}.agency`);
    if (agency === prevSelectedAgency) return;
    form.setValue(`algo_series.${index}.agency`, agency);
    form.setValue(`algo_series.${index}.dimensions`, dimensionDefaultValue);
    replaceWithoutRendering(
      {
        run_id: runId,
        bond_name: value,
        id: userEmpiricalTrackingId,
      },
      true
    );
    selectAgency(agency);
    autoFillDimensions(agency);
    autoFillColumns();
  };

  useEffectOnce(() => {
    if (!userEmpiricalTrackingId && !runId && bondName && !supportedAgency) {
      addBondAndAgencyToSerie(bondName);
    }
  });

  useEffect(() => {
    if (bond_name) {
      addBondAndAgencyToSerie(bond_name);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bond_name]);
};
