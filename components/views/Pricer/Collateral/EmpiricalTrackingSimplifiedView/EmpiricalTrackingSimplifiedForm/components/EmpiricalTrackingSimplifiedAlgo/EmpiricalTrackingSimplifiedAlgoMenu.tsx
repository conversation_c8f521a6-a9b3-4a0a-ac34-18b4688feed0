import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON> } from "@chakra-ui/react";
import React from "react";
import { IoEllipsisVerticalOutline, IoSyncCircleOutline, IoTrashOutline } from "react-icons/io5";
import CAIcon from "@/design-system/atoms/CAIcon";

interface FilterSeriesMenuActionsProps {
  onDeleteClick: React.MouseEventHandler<HTMLButtonElement>;
  onResetClick: React.MouseEventHandler<HTMLButtonElement>;
}

export const EmpiricalTrackingAlgoMenu = ({ onDeleteClick, onResetClick }: FilterSeriesMenuActionsProps) => {
  return (
    <Menu placement="right-start" isLazy closeOnSelect>
      <MenuButton
        as={IconButton}
        aria-label="filter-menu"
        size="xs"
        icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
        variant="ghost"
      />
      <MenuList zIndex="popover" minW="10rem">
        <MenuItem icon={<CAIcon as={IoSyncCircleOutline} boxSize={4} />} onClick={onResetClick}>
          Reset
        </MenuItem>
        <MenuItem icon={<CAIcon as={IoTrashOutline} boxSize={4} />} onClick={onDeleteClick}>
          Delete
        </MenuItem>
      </MenuList>
    </Menu>
  );
};
