import {
  Box,
  Button,
  <PERSON>S<PERSON>ck,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  Tooltip,
  useColorModeValue,
  useDisclosure,
} from "@chakra-ui/react";
import { IoEllipsisVerticalOutline, IoRefresh, IoSaveOutline, IoTrashOutline } from "react-icons/io5";
import { VscSaveAs } from "react-icons/vsc";
import React, { useState } from "react";
import { IoMdShare } from "react-icons/io";
import { BiPencil } from "react-icons/bi";
import { UseFormReturn } from "react-hook-form";
import { useRouter } from "next/router";
import { MdPeople } from "react-icons/md";
import CAIcon, { CAIconVariant } from "@/design-system/atoms/CAIcon";
import S from "@/constants/strings";
import {
  getEmpiricalTracking,
  useDeleteUserEmpiricalTrackingMutation,
  usePatchUserEmpiricalTrackingMutation,
  usePutUserEmpiricalTrackingMutation,
  useSaveUserEmpiricalTrackingMutation,
} from "@/utils/swr-hooks/UserEmpiricalTracking";
import CAModal from "@/design-system/molecules/CAModal";
import CAInput from "@/design-system/molecules/CAInput";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import {
  ApiError,
  CA_Mastr_Api_v1_0_Models_Status,
  CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTrackingResponse,
  CA_Mastr_Models_v1_0_Models_Entity,
  CA_Mastr_Models_v1_0_Models_Page,
} from "@/utils/openapi";
import { canDelete, canEdit, canShare, getErrorMessageFromStatus, getParam } from "@/utils/helpers";
import { APP_ROUTES_V2, VALID_APPLICATIONS } from "@/constants/enums";
import SharePopup from "@/components/helpers/SharePopup";
import { usePricerEmpiricalTrackingSimplifiedPage } from "@/contexts/PageContexts/PricerEmpiricalTrackingSimplifiedPageContext/PricerEmpiricalTrackingSimplified";
import {
  useGetUserGridViews,
  useInsertGridViewMutation,
  useUpdateGridViewMutation,
} from "@/utils/swr-hooks/UserGridView";
import { DEFAULT_VIEW_NAME } from "@/design-system/molecules/CAGrid/constants";
import { IN_MEMORY_OBJECT_STORAGE_KEY, LOCAL_STORAGE_KEY, SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { inMemoryObjectStorage, safeLocalStorage, safeSessionStorage } from "@/utils/local-storage";
import { useEmpiricalTrackingConfiguration } from "@/utils/swr-hooks/EmpiricalTracking";
import { useGridStoreContext } from "@/design-system/molecules/CAGrid/GridContext";
import { useMemoryStorageState } from "@/hooks/ca-grid/useMemoryStorageState";
import {
  EmpiricalTrackingFormType,
  defaultValues,
  getDefaultValuesFromRequest,
} from "./EmpiricalTrackingSimplifiedForm/useEmpiricalTrackingSimplifiedForm";
import { transformFormDataForPayload, useEmpiricalTrackingData } from "./EmpiricalTrackingSimplifiedView";
import { getEmpiricalTrackingGridKey } from "./EmpiricalTrackingSimplifiedDisplay/EmpiricalTrackingSimplifiedGrid";
import { useEmpiricalTrackingStore } from "./useEmpiricalTrackingSimplifiedStore";

interface EmpiricalTrackingActionsMenuProps {
  form: UseFormReturn<EmpiricalTrackingFormType>;
  selectedUserEmpiricalTrackingData:
    | CA_Mastr_Api_v1_0_Models_UserEmpiricalTracking_UserEmpiricalTrackingResponse
    | undefined;
}

enum ModalActions {
  SAVE = "Save",
  SAVE_AS = "Save As",
  RENAME = "Rename",
}
function getLatestDateFromStringDates(dateStrings: (string | undefined | null)[]): string {
  const validDates: Date[] = dateStrings
    .filter((dateString) => dateString !== undefined && dateString !== null)
    .map((dateString) => new Date(dateString as string));

  if (validDates.length === 0) {
    throw new Error("No valid dates found in the array.");
  }

  const latestDate: Date = new Date(Math.max(...validDates.map((date) => date.getTime())));
  const latestDateString: string = latestDate.toISOString(); // Get string representation

  return latestDateString;
}

export const EmpiricalTrackingSimplifiedActionsMenu = ({
  form,
  selectedUserEmpiricalTrackingData: data,
}: EmpiricalTrackingActionsMenuProps) => {
  const router = useRouter();
  const {
    refs: { detailGridRef },
  } = usePricerEmpiricalTrackingSimplifiedPage();
  const { gridViewsChanges, resetAllGridViewsChanges } = useEmpiricalTrackingStore();
  const { hasUnsavedChanges, setHasUnsavedChanges, resetGridState, selectedGridView, setSelectedGridView } =
    useGridStoreContext((state) => state);
  const { getChartStateOnMemory } = useMemoryStorageState();

  // Config request to apply Reset
  const { data: config } = useEmpiricalTrackingConfiguration();
  // Empirical Tracking Mutations
  const { trigger: updateUserEmpiricalTracking, data: put } = usePutUserEmpiricalTrackingMutation();
  const { trigger: updatePartiallyUserEmpiricalTracking, data: patch } =
    usePatchUserEmpiricalTrackingMutation("actions-menu");
  const { trigger: postUserEmpiricalTracking, data: add } = useSaveUserEmpiricalTrackingMutation();
  const { trigger: deleteUserEmpiricalTracking } = useDeleteUserEmpiricalTrackingMutation();

  const setToken = useEmpiricalTrackingData((state) => state.setToken);
  // constants
  const userEmpiricalTracking = data?.user_empirical_tracking;
  const userEmpiricalTrackingId = userEmpiricalTracking?.user_empirical_tracking_id;
  const lastUpdated = userEmpiricalTracking?.updated
    ? getLatestDateFromStringDates([
        userEmpiricalTracking?.updated,
        put?.user_empirical_tracking?.updated,
        patch?.user_empirical_tracking?.updated,
        add?.user_empirical_tracking?.updated,
      ])
    : null;
  const isShared = userEmpiricalTracking?.is_shared;

  // Grid Views
  const { data: gridViews, mutate } = useGetUserGridViews(
    getEmpiricalTrackingGridKey(userEmpiricalTrackingId?.toString() ?? null)
  );
  const { trigger: insertUserGridView } = useInsertGridViewMutation();
  const { trigger: updateUserGridView } = useUpdateGridViewMutation();

  const userEmpiricalTrackingName =
    patch?.user_empirical_tracking?.user_empirical_tracking_name ??
    userEmpiricalTracking?.user_empirical_tracking_name ??
    "Untitled Query";
  let viewName = userEmpiricalTrackingName ?? "Untitled Query";

  const { isOpen: isShareModalOpen, onToggle: onToggleShareModal } = useDisclosure();
  const [modalAction, setModalAction] = useState<ModalActions>();

  const updateCurrentUserGridView = async (selectedViewId: string) => {
    const requests = gridViews?.user_grid_views?.map((gridView) => {
      const persistedState = inMemoryObjectStorage.getItem(
        `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${gridView.user_grid_view_id}`
      );

      if (persistedState) {
        const chartState = getChartStateOnMemory();
        const combinedState = JSON.stringify({
          ...persistedState,
          chartState,
        });
        return updateUserGridView({
          grid_key: gridView.grid_key,
          grid_view_name: gridView.grid_view_name,
          user_grid_view_id: gridView.user_grid_view_id,
          grid_view_state: combinedState,
        });
      }
    });

    if (!requests) return;

    const res = await Promise.all(requests);
    const gridView = res?.find((r) => r?.user_grid_view?.user_grid_view_id === Number(selectedViewId));
    if (gridView?.user_grid_view) {
      setSelectedGridView(gridView.user_grid_view);
    }
    mutate();
  };

  const resetChangeFlag = () => {
    resetAllGridViewsChanges();
    setHasUnsavedChanges(false);
  };

  const resetFormDirty = () => form.reset({}, { keepValues: true });

  const saveCurrentEmpiricalTracking = async () => {
    const userEmpiricalTrackingIdParam = getParam("id");
    const formInputs = form.getValues();
    const userEmpiricalTrackingInputs = transformFormDataForPayload(formInputs);

    if (userEmpiricalTrackingIdParam) {
      updateCurrentUserGridView(userEmpiricalTrackingIdParam);
    }

    await updateUserEmpiricalTracking({
      user_empirical_tracking_input: userEmpiricalTrackingInputs,
      user_empirical_tracking_name: userEmpiricalTrackingName,
      user_empirical_tracking_id: Number(userEmpiricalTrackingIdParam),
      last_updated: lastUpdated ?? undefined,
    });

    form.reset({}, { keepValues: true });
    resetChangeFlag();
  };

  const addNewUserEmpiricalTracking = async (tags?: string[] | null) => {
    const formInputs = form.getValues();
    const userEmpiricalTrackingInputs = transformFormDataForPayload(formInputs);

    const res = await postUserEmpiricalTracking({
      user_empirical_tracking_input: userEmpiricalTrackingInputs,
      user_empirical_tracking_name: viewName,
      tags,
    });
    return res;
  };

  const handleSave = () => {
    const userEmpiricalTrackingIdParam = getParam("id");
    if (userEmpiricalTrackingIdParam) {
      saveCurrentEmpiricalTracking();
    } else {
      setModalAction(ModalActions.SAVE);
    }
  };

  const handleDuplicate = () => {
    setModalAction(ModalActions.SAVE_AS);
  };

  const handleDelete = async () => {
    form.reset(defaultValues);
    await deleteUserEmpiricalTracking(
      {
        userEmpiricalTrackingId: userEmpiricalTrackingId,
        lastUpdated: lastUpdated ?? data?.user_empirical_tracking?.updated ?? undefined,
      },
      {
        onError: (err) => {
          if (err instanceof ApiError) {
            const errMsg = getErrorMessageFromStatus(err?.body?.status as CA_Mastr_Api_v1_0_Models_Status);
            showErrorToast("Error", errMsg);
          } else {
            showErrorToast(
              "Error",
              `Failed to delete User Query "${userEmpiricalTracking?.user_empirical_tracking_name}".`
            );
          }
        },
      }
    );
    router.push({
      pathname:
        APP_ROUTES_V2[VALID_APPLICATIONS.EMPIRICAL_TRACKING]?.[CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING],
    });
    setToken(undefined);
    safeSessionStorage.removeItem(SESSION_STORAGE_KEY.EMPIRICAL_TRACKING_LAST_RUN_ID);
    showSuccessToast("Deleted", `User Query ${userEmpiricalTracking?.user_empirical_tracking_name} has been deleted.`);
  };

  const handleReset = () => {
    if (data?.user_empirical_tracking?.user_empirical_tracking_input) {
      const defaultValues = getDefaultValuesFromRequest(
        data?.user_empirical_tracking?.user_empirical_tracking_input,
        config
      );
      form.reset(defaultValues);
    } else {
      form.reset(defaultValues);
    }
    resetGridState();
    resetChangeFlag();
  };

  const insertNewUserGridView = async (viewId: string) => {
    const gridApi = detailGridRef.current?.api;
    if (gridApi) {
      const gridState = gridApi?.getState();
      const chartState = getChartStateOnMemory();
      const gridViewState = {
        ...gridState,
        chartState,
      };
      const gridKey = getEmpiricalTrackingGridKey(viewId ?? null);
      const res = await insertUserGridView({
        grid_key: gridKey,
        grid_view_name: DEFAULT_VIEW_NAME,
        grid_view_state: JSON.stringify(gridViewState),
      });
      if (res?.user_grid_view) {
        mutate();
        setSelectedGridView(res?.user_grid_view);
      }
    }
  };

  const createUserEmpiricalTracking = async () => {
    const res = await addNewUserEmpiricalTracking();
    resetFormDirty();
    const userEmpiricalTrackingId = res?.user_empirical_tracking?.user_empirical_tracking_id?.toString() ?? undefined;

    if (userEmpiricalTrackingId) {
      const runId = getParam("run_id") ?? undefined;
      router.push({
        query: runId ? { id: userEmpiricalTrackingId, run_id: runId } : { id: userEmpiricalTrackingId },
      });
      insertNewUserGridView(userEmpiricalTrackingId);
    }
  };

  const duplicateHandler = async () => {
    const userEmpiricalTrackingId = data?.user_empirical_tracking?.user_empirical_tracking_id;
    const userViewData = await getEmpiricalTracking(userEmpiricalTrackingId);
    const tags = userViewData?.user_empirical_tracking?.tags;
    const newlyAddedUserEmpiricalTracking = await addNewUserEmpiricalTracking(tags);
    const newlyAddedUserEmpiricalTrackingId =
      newlyAddedUserEmpiricalTracking?.user_empirical_tracking?.user_empirical_tracking_id?.toString();
    const gridKey = getEmpiricalTrackingGridKey(newlyAddedUserEmpiricalTrackingId ?? null);

    if (gridViews?.user_grid_views?.length) {
      gridViews?.user_grid_views?.forEach(async (view) => {
        const newCopyOfGridView = await insertUserGridView({
          ...view,
          grid_key: gridKey,
        });

        if (newCopyOfGridView?.user_grid_view?.user_grid_view_id === selectedGridView?.user_grid_view_id) {
          const gridKey = newCopyOfGridView?.user_grid_view?.grid_key;
          const gridViewId = newCopyOfGridView?.user_grid_view?.user_grid_view_id;
          safeLocalStorage.setItem(`${LOCAL_STORAGE_KEY.LAST_LOADED_VIEW}_${gridKey}`, JSON.stringify(gridViewId));
        }
      });
    }
    showSuccessToast("Saved", `User Query "${viewName}" has been added.`);
    window.open(
      `${
        APP_ROUTES_V2[VALID_APPLICATIONS.EMPIRICAL_TRACKING]?.[CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING]
      }?id=${newlyAddedUserEmpiricalTracking?.user_empirical_tracking?.user_empirical_tracking_id}`
    );
  };

  const handleSubmitAfterRename = async () => {
    setModalAction(undefined);
    if (modalAction === ModalActions.SAVE) {
      await createUserEmpiricalTracking();
      resetChangeFlag();
    } else if (modalAction === ModalActions.SAVE_AS) {
      await duplicateHandler();
    } else if (modalAction === ModalActions.RENAME) {
      await updatePartiallyUserEmpiricalTracking({
        user_empirical_tracking_id: userEmpiricalTrackingId,
        user_empirical_tracking_name: viewName,
        last_updated: lastUpdated ?? undefined,
      });
    }
  };

  const menuItemIconStyle = {
    color: useColorModeValue("charcoal.400", "white"),
    boxSize: 4,
    variant: "default" as CAIconVariant,
  };

  const hasChanges = form.formState.isDirty || Object.values(gridViewsChanges).some(Boolean);
  return (
    <>
      <HStack>
        {hasChanges && (canEdit(userEmpiricalTracking?.role) || !userEmpiricalTracking) && (
          <Tooltip label="Unsaved Changes">
            <Button
              onClick={handleSave}
              paddingStart={0}
              paddingEnd={0}
              minWidth={4}
              height={4}
              backgroundColor={"transparent"}
              textColor={"safetyOrange.500"}
            >
              <CAIcon as={IoSaveOutline} boxSize={4} variant="default" cursor={"pointer"} />
            </Button>
          </Tooltip>
        )}
        <Box w="full" wordBreak="break-all" noOfLines={1}>
          <Text variant="tableLeft" lineHeight="6" fontSize="14px" px="1" borderRadius="md">
            {userEmpiricalTrackingName}
          </Text>
        </Box>
        {isShared && <CAIcon ml={-1} as={MdPeople} variant="secondary" />}
        <Menu placement="right-start" isLazy>
          <MenuButton
            as={IconButton}
            aria-label="header-menu"
            size="xs"
            ml={2}
            icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={4} variant="secondary" />}
            variant="ghost"
          />
          <MenuList zIndex="popover" minW="10rem">
            <MenuItem
              icon={<CAIcon as={IoRefresh} {...menuItemIconStyle} transform={"rotateY(180deg)"} />}
              onClick={handleReset}
            >
              {S.COMMON.RESET}
            </MenuItem>
            {canEdit(userEmpiricalTracking?.role) && (
              <MenuItem
                icon={<CAIcon as={BiPencil} {...menuItemIconStyle} />}
                onClick={() => setModalAction(ModalActions.RENAME)}
              >
                {S.COMMON.RENAME}
              </MenuItem>
            )}
            {(canEdit(userEmpiricalTracking?.role) || !userEmpiricalTracking) && (
              <MenuItem
                isDisabled={!form.formState.isDirty && !hasUnsavedChanges}
                icon={<CAIcon as={IoSaveOutline} {...menuItemIconStyle} />}
                onClick={handleSave}
              >
                {S.COMMON.SAVE}
              </MenuItem>
            )}
            {!!userEmpiricalTrackingId && (
              <MenuItem icon={<CAIcon as={VscSaveAs} {...menuItemIconStyle} />} onClick={handleDuplicate}>
                {S.COMMON.SAVE_AS}
              </MenuItem>
            )}
            {canDelete(userEmpiricalTracking?.role) && (
              <MenuItem icon={<CAIcon as={IoTrashOutline} {...menuItemIconStyle} />} onClick={handleDelete}>
                {S.COMMON.DELETE}
              </MenuItem>
            )}
            {canShare(userEmpiricalTracking?.role) && (
              <MenuItem icon={<CAIcon as={IoMdShare} {...menuItemIconStyle} />} onClick={onToggleShareModal}>
                {S.COMMON.SHARE}
              </MenuItem>
            )}
          </MenuList>
        </Menu>
      </HStack>

      {userEmpiricalTrackingId && (
        <SharePopup
          open={isShareModalOpen}
          toggleModal={() => onToggleShareModal()}
          recordId={userEmpiricalTrackingId}
          entity={CA_Mastr_Models_v1_0_Models_Entity.USER_EMPIRICAL_TRACKING}
          recordName={userEmpiricalTrackingName}
          triggerElement={() => <></>}
        />
      )}

      <CAModal
        modalHeader={modalAction}
        isOpen={!!modalAction}
        onClose={() => setModalAction(undefined)}
        modalFooter={[
          {
            title: "Cancel",
            onClick: () => setModalAction(undefined),
            variant: "secondary",
          },
          {
            title: "Submit",
            onClick: handleSubmitAfterRename,
            variant: "primary",
          },
        ]}
      >
        <CAInput
          defaultValue={viewName}
          onChange={(e) => {
            const val = e.currentTarget.value;
            viewName = val;
          }}
        />
      </CAModal>
    </>
  );
};
