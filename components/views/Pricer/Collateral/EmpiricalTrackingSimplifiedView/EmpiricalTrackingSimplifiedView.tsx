import React, { useEffect } from "react";
import { Flex, Grid, GridItem, Skeleton } from "@chakra-ui/react";
import { useSearchParams } from "next/navigation";
import { create } from "zustand";
import {
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigurationResponse,
  CA_Mastr_Models_v1_0_Models_Application,
  CA_Mastr_Models_v1_0_Models_EmpiricalTracking_EmpiricalTrackingRequest,
  CA_Mastr_Models_v1_0_Models_Page,
} from "@/utils/openapi";
import { usePricerEmpiricalTrackingSimplifiedPage } from "@/contexts/PageContexts/PricerEmpiricalTrackingSimplifiedPageContext/PricerEmpiricalTrackingSimplified";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { SQLQueriesModal } from "@/components/helpers/SQLQueriesModal/SQLQueriesModal";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { isSuperAdmin } from "@/utils/helpers";
import { useActivityStream } from "@/utils/swr-hooks/ActivityStream";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { getParam } from "@/utils/helpers";
import { useUserEmpiricalTrackingQuery } from "@/utils/swr-hooks/UserEmpiricalTracking";
import { GridStoreProvider } from "@/design-system/molecules/CAGrid/GridContext";
import { LoadingOverlay } from "@/components/overlay/LoadingOverlay";
import {
  useEmpiricalTrackingSimplifiedMutation,
  useEmpiricalTrackingSimplifiedSqlQuery,
} from "@/utils/swr-hooks/EmpiricalTrackingSimplified";
import PricerHeader from "../../PricerHeader";
import EmpiricalTrackingTagSelector from "../EmpiricalTrackingSimplifiedTagSelector/EmpiricalTrackingTagSelector";
import { EmpiricalTrackingSimplifiedRequestStopRunningWrapper } from "../../PricerStopRunningWrapper";
import { PricerEmpiricalTrackingSimplifiedStopWatchWrapper } from "../../PricerStopWatchWrapper";
import { EmpiricalTrackingSimplifiedForm } from "./EmpiricalTrackingSimplifiedForm/EmpiricalTrackingSimplifiedForm";
import { EmpiricalTrackingSimplifiedDisplay } from "./EmpiricalTrackingSimplifiedDisplay/EmpiricalTrackingSimplifiedDisplay";
import {
  EmpiricalTrackingFormType,
  useEmpiricalTrackingForm,
} from "./EmpiricalTrackingSimplifiedForm/useEmpiricalTrackingSimplifiedForm";
import { getEmpiricalTrackingGridKey } from "./EmpiricalTrackingSimplifiedDisplay/EmpiricalTrackingSimplifiedGrid";

export const transformFormDataForPayload = (data: EmpiricalTrackingFormType) => {
  return {
    algo_series: data.algo_series?.map((serie) => ({
      ...serie,
      dimensions: serie.dimensions
        ?.filter((el) => !!el.dimension)
        ?.map((dim) => ({
          name: dim.dimension,
          operator: dim.operator?.value,
          values: dim.values,
          enabled: dim.enabled,
        })),
    })),
    columns: data.columns?.map((col) => ({
      dimension: col.dimension,
      enabled: col.enabled,
      values: col.values,
    })),
    settings: data.settings,
  };
};

const transformDataForPayload = (
  data: EmpiricalTrackingFormType,
  run_id: string,
  useCache: boolean | undefined,
  timeoutMinutes: number | undefined
): CA_Mastr_Models_v1_0_Models_EmpiricalTracking_EmpiricalTrackingRequest => {
  return {
    no_cache: !useCache,
    app: CA_Mastr_Models_v1_0_Models_Application.PRICER,
    page: CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING,
    run_id,
    empirical_tracking_payload: transformFormDataForPayload(data),
    timeout_minutes: timeoutMinutes,
  };
};

export const useEmpiricalTrackingData = create<{
  token: string | undefined;
  setToken: (token: string | undefined) => void;
}>((set) => ({
  token: undefined,
  setToken: (token) => set({ token }),
}));

export const EmpiricalTrackingSimplifiedView = ({
  formConfig,
}: {
  formConfig: CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingConfigurationResponse;
}) => {
  const {
    state: {
      userSettings: { useCache, timeoutMinutes },
      isTimerRunning,
      bond_name,
    },
    action: { setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { userData },
  } = useAuthentication();

  const {
    state: { lastRun, oldRunId, lastRunId, isRequestStopped },
    action: { run, setOldRun },
  } = usePricerEmpiricalTrackingSimplifiedPage();

  const { setToken, token: mutationToken } = useEmpiricalTrackingData();
  const params = useSearchParams();
  const activeUserEmpiricalTrackingId = params.get("id");
  const runId = params.get("run_id");

  const { replaceWithoutRendering } = useQueryParameters();
  const { trigger: postEmpiricalTracking } = useEmpiricalTrackingSimplifiedMutation();
  const { form, resetFormWithOldRunData, resetFormWithUserEmpiricalTrackingInputs } = useEmpiricalTrackingForm({
    config: formConfig,
  });

  const {
    data: selectedUserEmpiricalTrackingData,
    isLoading: isLoadingUserEmpiricalTracking,
    isValidating: isValidatingUserEmpiricalTracking,
  } = useUserEmpiricalTrackingQuery(activeUserEmpiricalTrackingId ? Number(activeUserEmpiricalTrackingId) : undefined, {
    onSuccess: resetFormWithUserEmpiricalTrackingInputs,
  });

  const userEmpiricalTracking = selectedUserEmpiricalTrackingData?.user_empirical_tracking;

  const {
    data: oldRunData,
    isLoading,
    isValidating,
  } = useActivityStream(
    // Wait for user empirical tracking request to finish then run Activity Stream
    isLoadingUserEmpiricalTracking ||
      !oldRunId ||
      userEmpiricalTracking?.user_empirical_tracking_result?.run_id === oldRunId
      ? undefined
      : oldRunId,
    {
      onSuccess: resetFormWithOldRunData,
    }
  );
  const apiRequestVariables = oldRunData?.run_activity?.api_activities
    ?.flatMap((activity) => activity.api_request_variables || [])
    ?.filter(Boolean);

  const userEmpiricalTrackingDataToken =
    selectedUserEmpiricalTrackingData?.user_empirical_tracking?.user_empirical_tracking_result?.user_empirical_tracking_outputs
      ?.map((el) => el.token)
      .join("|");

  const oldToken = oldRunData?.run_activity?.api_activities?.map((el) => el.api_token).join("|");
  const oldActicity = oldRunData?.run_activity?.api_activities?.find(
    (activity) => activity.api_function === "EmpiricalTracking"
  );
  const userEmpiricalTrackingRunId = userEmpiricalTracking?.user_empirical_tracking_result?.run_id;

  const getToken = () => {
    if (mutationToken) return mutationToken;
    if (oldRunId && oldRunId !== userEmpiricalTrackingRunId) return oldToken;
    return userEmpiricalTrackingDataToken;
  };

  const token = getToken();

  const { data: sqlQueryData } = useEmpiricalTrackingSimplifiedSqlQuery({
    skip: !token || !isSuperAdmin(userData) || isTimerRunning,
    tokens: token,
  });

  const hasQuery = sqlQueryData?.empirical_tracking_queries?.some((item) => item.query);

  const handleRunClick = form.handleSubmit(async (data) => {
    const runId = run();
    const bondName = getParam("bond_name");
    setIsTimerRunning(true);
    setOldRun(null);
    replaceWithoutRendering(
      {
        run_id: runId,
        bond_name: bondName ?? undefined,
      },
      true
    );
    const res = await postEmpiricalTracking({
      ...transformDataForPayload(data, runId, useCache, timeoutMinutes),
      user_empirical_tracking_id: activeUserEmpiricalTrackingId ? Number(activeUserEmpiricalTrackingId) : undefined,
    });
    if (res?.token) {
      setToken(res?.token);
    }
  });

  const runInfo = isRequestStopped
    ? undefined
    : oldRunId && oldActicity // Is coming from old run
    ? {
        id: oldActicity.run_id,
        date: oldActicity.api_start_time,
        isOldRun: !!oldRunId,
      }
    : !lastRunId // Is coming for selected query old run
    ? {
        id: userEmpiricalTracking?.user_empirical_tracking_result?.run_id ?? lastRunId,
        date: userEmpiricalTracking?.user_empirical_tracking_result?.run_date,
        isOldRun: !!userEmpiricalTracking?.user_empirical_tracking_result?.run_id,
      }
    : {
        // Is coming from new run
        id: lastRunId,
        date: lastRun,
        isOldRun: false,
      };

  const gridType = getEmpiricalTrackingGridKey(activeUserEmpiricalTrackingId ?? null);

  useEffect(() => {
    if (bond_name && !runId) {
      setToken(undefined);
      setOldRun(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bond_name, runId]);

  return (
    <Grid
      templateAreas={{
        base: `"header"
             "form"
             "grid"`,
        lg: `"header header"
                    "form grid"`,
      }}
      templateColumns={{ base: "1fr", sm: "1fr", lg: "auto 1fr" }}
      templateRows={{ base: "auto", sm: "auto", lg: "auto 1fr" }}
      gap="3"
      mb="5"
    >
      <LoadingOverlay
        isLoading={isLoading || isLoadingUserEmpiricalTracking || isValidating || isValidatingUserEmpiricalTracking}
      />
      <GridStoreProvider gridType={gridType}>
        <GridItem area="header">
          <PricerHeader
            title="Empirical Tracking"
            bond_name={bond_name}
            onRunClick={() => handleRunClick()}
            runInfo={runInfo}
            stopWatch={<PricerEmpiricalTrackingSimplifiedStopWatchWrapper lastRun={lastRun} />}
            needsBond
            stopRunning={<EmpiricalTrackingSimplifiedRequestStopRunningWrapper token={token} />}
            rightSideContent={
              <Flex gap={2}>
                {isLoadingUserEmpiricalTracking ? (
                  <Skeleton w={100} h={5} />
                ) : (
                  <EmpiricalTrackingTagSelector
                    role={userEmpiricalTracking?.role}
                    tags={userEmpiricalTracking?.tags}
                    user_empirical_tracking_id={userEmpiricalTracking?.user_empirical_tracking_id}
                    updated={userEmpiricalTracking?.updated}
                  />
                )}
                {sqlQueryData?.empirical_tracking_queries && hasQuery && (
                  <SQLQueriesModal key={mutationToken} data={sqlQueryData?.empirical_tracking_queries} />
                )}
              </Flex>
            }
          />
        </GridItem>
        <GridItem
          ml={{
            base: "5",
            lg: "2",
          }}
          area="grid"
          h={{ base: "100%", lg: "calc(100vh - 13rem)" }}
          overflow={"scroll"}
          mr="5"
        >
          <EmpiricalTrackingSimplifiedDisplay token={token} apiRequestVariables={apiRequestVariables ?? []} />
        </GridItem>
      </GridStoreProvider>

      <EmpiricalTrackingSimplifiedForm form={form} />
    </Grid>
  );
};
