import React from "react";
import { ColDef } from "ag-grid-community";
import { Box } from "@chakra-ui/react";
import { EmpiricalTrackingReturnType, empiricalTrackingColDefs } from "@/utils/grid/EmpiricalTrackingColumnData";
import {
  CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables,
  CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingResponse,
} from "@/utils/openapi";
import { usePricerEmpiricalTrackingSimplifiedPage } from "@/contexts/PageContexts/PricerEmpiricalTrackingSimplifiedPageContext/PricerEmpiricalTrackingSimplified";
import CAGrid from "@/design-system/molecules/CAGrid";
import { EmpiricalTrackingApiVariables } from "./EmpiricalTrackingSimplifiedApiVariables";

interface EmpiricalTrackingGridProps {
  headerActionButtons: React.JSX.Element;
  isLoading: boolean;
  data: CA_Mastr_Api_v1_0_Models_EmpiricalTracking_EmpiricalTrackingResponse[] | undefined;
  apiRequestVariables: (CA_Mastr_Api_v1_0_Models_Activity_ApiRequestVariables | null | undefined)[];
}

const autoGroupColumnDef: ColDef<EmpiricalTrackingReturnType> = {
  initialWidth: 200,
  cellRendererParams: {
    suppressCount: true, // turn off the row count
  },
};

export const getEmpiricalTrackingGridKey = (userEmpiricalTrackingId: string | null) => {
  if (!userEmpiricalTrackingId) return;
  return `empiricalTrackingQuery-${userEmpiricalTrackingId}-v31`;
};

export const EmpiricalTrackingSimplifiedGrid = ({
  data,
  isLoading,
  apiRequestVariables,
  headerActionButtons,
}: EmpiricalTrackingGridProps) => {
  const {
    refs: { detailGridRef },
  } = usePricerEmpiricalTrackingSimplifiedPage();
  const {
    state: { isRequestStopped },
  } = usePricerEmpiricalTrackingSimplifiedPage();

  const parsedData: EmpiricalTrackingReturnType[] | undefined = React.useMemo(() => {
    if (!data) return [];

    return JSON.parse(data?.[0]?.results || "[]");
  }, [data]);

  const colDefs = React.useMemo(() => {
    const dataFields = parsedData?.[0] && Object.keys(parsedData[0]);
    return dataFields ? (empiricalTrackingColDefs.filter((col) => dataFields.includes(col.field)) as ColDef[]) : [];
  }, [parsedData]);

  const hasRun = data?.every((el) => el.status);

  return (
    <Box borderRadius="2xl" overflow="hidden">
      <CAGrid
        ref={detailGridRef}
        gridType={"simplifed-grid-view"}
        cardProps={{
          title: " ",
          cardKey: "empirical-tracking-grid",
          borderTopRadius: 0,
          variant: "square",
          cardBodyStyle: { p: 0 },
          className: "SlicerQueryGrid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
          style: {
            borderRadius: "1rem",
          },
        }}
        hasRun={hasRun && !isRequestStopped}
        wrapperStyles={{
          flexDirection: {
            base: "column",
            "3xl": "row",
          },
        }}
        stackStyles={{
          h: "calc(100vh - 15.2rem)",
        }}
        hideSearch
        initialMessage={"Click on ▷ to load Empirical Tracking."}
        headerActionButtons={headerActionButtons}
        gridProps={{
          columnDefs: colDefs,
          rowData: parsedData,
          groupDefaultExpanded: 1,
          autoGroupColumnDef,
          loading: isLoading,
        }}
        customEl={
          apiRequestVariables?.length === 0 || parsedData?.length === 0 ? undefined : (
            <EmpiricalTrackingApiVariables apiRequestVariables={apiRequestVariables} />
          )
        }
      />
    </Box>
  );
};
