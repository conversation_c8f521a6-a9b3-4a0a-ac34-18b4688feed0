import * as React from "react";
import { Box, VStack } from "@chakra-ui/layout";
import { Skeleton } from "@chakra-ui/skeleton";
import { getAPIErrorMessage, parseDateToYYYYMMDD } from "@/utils/helpers";
import { useCollateralLevelSummarySWR } from "@/utils/swr-hooks";
import CACard from "@/design-system/molecules/CACard";
import CAHeading from "@/design-system/atoms/CAHeading";
import CATable from "@/design-system/molecules/CATable";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest,
  CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel,
  CA_Mastr_Models_v1_0_Models_Stage,
} from "@/utils/openapi";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import HistoricalPrepay from "../shared/HistoricalPrepay";
import { getLoanLevelSummary, getPoolLevelSummary } from "./helpers";

interface LoanPoolDistributionSummaryProps {
  collateral_level: CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel;
  summaryRequest: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest;
  factorDate: Date | undefined;
}

const LoanPoolDistributionSummary: React.FC<LoanPoolDistributionSummaryProps> = ({
  summaryRequest,
  collateral_level,
  factorDate,
}: LoanPoolDistributionSummaryProps) => {
  const { data: response, error } = useCollateralLevelSummarySWR(collateral_level, summaryRequest, {});

  const tableData = React.useMemo(() => {
    if (!response || response?.status?.stage !== CA_Mastr_Models_v1_0_Models_Stage.COMPLETED) {
      return;
    }

    if (!response?.data) {
      return null;
    }

    if (collateral_level === CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel.LOAN_LEVEL) {
      return getLoanLevelSummary(response);
    }

    return getPoolLevelSummary(response);
  }, [collateral_level, response]) as {
    [key: string]: {
      name: string;
      values: (string | number)[];
    }[];
  };

  return (
    <CACard
      allowCollapse
      key="LoanPoolDistributionSummary"
      title=" "
      cardStyleOnOpen={{
        h: "full",
      }}
    >
      {error ? (
        <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
      ) : tableData === null ? (
        <CAInfo status="info" description="No data available" />
      ) : !tableData ? (
        <Skeleton height="25px" />
      ) : (
        <VStack alignItems="stretch" spacing={4}>
          <Box>
            <CAHeading as="h3" mb={3}>
              Basic
            </CAHeading>
            <CATable data={tableData?.basic} />
          </Box>
          <Box>
            <CAHeading as="h3" mb={3}>
              Loan Size
            </CAHeading>
            <CATable data={tableData?.loanSize} />
          </Box>
          {tableData?.others.length > 0 && (
            <Box>
              <CAHeading as="h3" mb={3}>
                Others
              </CAHeading>
              <CATable data={tableData?.others} />
            </Box>
          )}
          {collateral_level === CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel.POOL_LEVEL && (
            <Box>
              <CAHeading as="h3" mb={3}>
                Prepay
              </CAHeading>
              <HistoricalPrepay
                showFactorMonth={false}
                bond_name={summaryRequest.bond_name ?? undefined}
                asOf={parseDateToYYYYMMDD(factorDate)}
              />
            </Box>
          )}
        </VStack>
      )}
    </CACard>
  );
};

export default LoanPoolDistributionSummary;
