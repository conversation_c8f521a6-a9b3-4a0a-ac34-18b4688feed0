import * as React from "react";
import { useBreakpointValue } from "@chakra-ui/media-query";
import { Box, Center, Flex, SimpleGrid, Spacer, Stack, VStack } from "@chakra-ui/layout";
import { DragEndEvent } from "@dnd-kit/core";
import CASearch from "@/design-system/molecules/CASearch";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Distributions_ConventionalPoolLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Distributions_GplPoolLevel,
} from "@/utils/openapi";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import CABar<PERSON>hart, { CABarChartProps } from "@/design-system/molecules/CABarChart";
import { getFormattedNumberFixed } from "@/utils/helpers";
import { safeLocalStorage } from "@/utils/local-storage";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import { CADragDrop, CADragDropContainer } from "@/design-system/molecules/CADragDrop";

type LoanPoolDistributionChartsProps = {
  collateralLevel: "Loan" | "Pool";
  headingElement?: React.JSX.Element;
  headerActionButtons?: React.JSX.Element;
  data?:
    | CA_Mastr_Api_v1_0_Models_Collateral_Distributions_ConventionalPoolLevel[]
    | CA_Mastr_Api_v1_0_Models_Collateral_Distributions_GplPoolLevel[];
  gplView: "balance" | "count";
  headerGPLButtons: React.JSX.Element;
};

type barChartData = {
  label: string;
  curr_value: number;
  original_value?: number;
}[];

const percentageYAxisFormatter = (value: string | number) => {
  return `${value}%`;
};

const getBarChartOptions = (data: barChartData): CABarChartProps["options"] => {
  const dataKeys = [{ key: "curr_value", name: "Current" }];
  if (data && data[0].original_value) {
    dataKeys.push({ key: "original_value", name: "Original" });
  }

  return {
    xkey: "label",
    dataKeys,
    margin: { left: -20, right: 10, bottom: -10 },
    data,
    hideLegend: true,
    yAxisLabelFormatter: percentageYAxisFormatter,
  };
};

const getSortedChartTitleFromLocalStorage = (
  collateralLevel: "Loan" | "Pool",
  sub_type?: string | null | undefined
) => {
  if (sub_type && collateralLevel) {
    const chartTitlesArr = safeLocalStorage.getItem(
      `${LOCAL_STORAGE_KEY.LOAN_POOL_DISTRIBUTION_CARD_ORDERING}_${collateralLevel}_${sub_type}`
    );
    const savedChartTitles: string[] = chartTitlesArr ? JSON.parse(chartTitlesArr)?.filter((ct: string) => !!ct) : [];
    return savedChartTitles;
  }
};

const LoanPoolDistributionCharts: React.FC<LoanPoolDistributionChartsProps> = ({
  collateralLevel,
  headingElement,
  headerActionButtons,
  data,
  gplView,
  headerGPLButtons,
}: LoanPoolDistributionChartsProps) => {
  const {
    state: { security_info },
  } = usePricerModule();
  const isLargerThanMd = useBreakpointValue({ base: false, md: true });
  const [sortedChartTitles, setSortedChartTitles] = React.useState<string[]>(
    getSortedChartTitleFromLocalStorage(collateralLevel, security_info?.sub_type) || []
  );

  const { chartData, hideHeader } = React.useMemo(() => {
    if (!data) return { chartData: {}, hideHeader: true };
    let hideHeader = true;

    const chartData: { [title: string]: { label: string; curr_value: number; original_value?: number }[] } = {};
    for (const row of data) {
      const title = row.dimension_display_value;
      const label = row.slice_display_value;
      if ("orig_balance_ratio" in row || "loan_count_orig_ratio" in row) {
        hideHeader = false;
      }

      let curr_value = "curr_ratio" in row ? row.curr_ratio : undefined;
      if (curr_value) {
        curr_value = Number(getFormattedNumberFixed(1)(curr_value * 100));
      }
      let original_value = "orig_balance_ratio" in row ? row.orig_balance_ratio : undefined;
      if (original_value) {
        original_value = Number(getFormattedNumberFixed(1)(original_value * 100));
      }

      if (gplView == "count" && original_value) {
        curr_value =
          "curr_ratio" in row ? row.curr_ratio : "loan_count_curr_ratio" in row ? row.loan_count_curr_ratio : undefined;
        if (curr_value) {
          curr_value = Number(getFormattedNumberFixed(1)(curr_value * 100));
        }

        original_value = "loan_count_orig_ratio" in row ? row.loan_count_orig_ratio : undefined;
        if (original_value) {
          original_value = Number(getFormattedNumberFixed(1)(original_value * 100));
        }
      }

      if (title && label && !label.match(/total/i) && typeof curr_value === "number") {
        if (!chartData[title]) {
          chartData[title] = [];
        }
        if (original_value && typeof original_value === "number") {
          chartData[title].push({ label, curr_value, original_value });
        } else {
          chartData[title].push({ label, curr_value });
        }
      }
    }

    return {
      chartData,
      hideHeader,
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, gplView]);

  const move = (event: DragEndEvent) => {
    const dragIndex = event.active.data.current?.sortable.index;
    const hoverIndex = event.over?.data.current?.sortable.index;

    const arr = [...sortedChartTitles.filter(Boolean)];
    const element = arr.splice(dragIndex, 1)[0];
    arr.splice(hoverIndex, 0, element);

    setSortedChartTitles(arr);
    const bond_sub_type = security_info?.sub_type;
    if (bond_sub_type) {
      safeLocalStorage.setItem(
        `${LOCAL_STORAGE_KEY.LOAN_POOL_DISTRIBUTION_CARD_ORDERING}_${collateralLevel}_${bond_sub_type}`,
        JSON.stringify([...arr])
      );
    }
  };

  React.useEffect(() => {
    if (!sortedChartTitles.length && chartData) {
      setSortedChartTitles([...Object.keys(chartData)]);
    }
  }, [chartData, sortedChartTitles.length]);

  const { filteredList, getInputProps } = useKeywordsSearch({
    getSearchableText: (item) => `${item[0]}`,
    list: chartData && Object.entries(chartData),
  });

  const formattedData = filteredList?.sort(
    ([c1], [c2]) => sortedChartTitles.indexOf(c1) - sortedChartTitles.indexOf(c2) || c1.localeCompare(c2)
  );

  return (
    <VStack alignItems="stretch" spacing={4}>
      <CACard cardBodyStyle={{ p: 0 }} minH="auto">
        <VStack px={4} py={3} spacing={2} alignContent="flex-start">
          <Flex alignItems="center" w="full">
            <Stack
              direction={{ base: "column", md: "row" }}
              spacing={{ base: 2, md: 6 }}
              alignItems={{ base: "flex-start", md: "center" }}
              w="full"
            >
              <Flex justifyContent={{ base: "flex-start", lg: "center" }} alignItems="center">
                <Box w="full" maxW="320px">
                  <CASearch name="chart-search" placeholder={"Quick Filter"} isDisabled={!data} {...getInputProps()} />
                </Box>
              </Flex>
              {isLargerThanMd && headingElement}
            </Stack>
            <Spacer />
            {hideHeader ? null : headerGPLButtons}
            <Spacer />
            <Box pl="3">{headerActionButtons}</Box>
          </Flex>
          {!isLargerThanMd && <Box w="full">{headingElement}</Box>}
          {!chartData || (chartData && Object.keys(chartData).length === 0) ? (
            <CACard minH="calc(100vh - 16.5rem)">
              <Center minH="25rem" w="full" h="100%" overflow="hidden">
                <CAInfo status="info" description="No data available" />
              </Center>
            </CACard>
          ) : null}
        </VStack>
      </CACard>
      <SimpleGrid columns={{ base: 1, lg: 2, "2xl": 3, "3xl": 4 }} gap={4}>
        <CADragDropContainer onDragEnd={move} items={formattedData?.map((el) => el[0]) ?? []}>
          {formattedData?.map(([title, data], index) => {
            return (
              <CADragDrop key={title} id={title}>
                <DistributionChartCardMemo title={title} index={index} data={data} />
              </CADragDrop>
            );
          })}
        </CADragDropContainer>
      </SimpleGrid>
    </VStack>
  );
};

export default React.memo(LoanPoolDistributionCharts);

type DistributionChartCardProps = {
  title: string;
  index: number;
  data: barChartData;
};

const DistributionChartCard: React.FC<DistributionChartCardProps> = ({
  title,
  index,
  data,
}: DistributionChartCardProps) => {
  return (
    <CACard pt={4}>
      <Flex>
        <CAHeading as="h3">{title}</CAHeading>
        <Spacer />
      </Flex>
      <CABarChart options={{ ...getBarChartOptions(data), colorOffset: index }} />
    </CACard>
  );
};

const DistributionChartCardMemo = React.memo(DistributionChartCard);
