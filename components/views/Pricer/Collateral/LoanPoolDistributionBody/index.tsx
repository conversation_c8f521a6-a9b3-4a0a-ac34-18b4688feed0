import * as React from "react";
import { Box } from "@chakra-ui/layout";
import { ColDef } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import CAGrid from "@/design-system/molecules/CAGrid";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Distributions_ConventionalPoolLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Distributions_GplPoolLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest,
  CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel,
} from "@/utils/openapi";
import { useGetCollateralDistributionSWR } from "@/utils/swr-hooks";
import { getColumnDataProps } from "@/utils/grid/column";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { CollateralDistributionRequest } from "@/types/swr";
import { safeLocalStorage } from "@/utils/local-storage";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import GridChartToggle, { GridView, GridViewValuesType } from "@/components/helpers/GridChartToggle";
import LoanPoolDistributionSummary from "../LoanPoolDistributionSummary";
import LoanPoolDistributionCharts from "./LoanPoolDistributionCharts";
import LoanPoolDistributionTable from "./LoanPoolDistributionTable";

type LoanPoolDistributionBodyProps = {
  collateralDistributionRequest: CollateralDistributionRequest;
  summaryRequest: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest;
  lastRun?: string;
  factorDateInput: React.JSX.Element;
  factorDate?: Date;
};

const columnProps = [
  getColumnDataProps("enum", {
    field: "dimension_display_value",
    headerName: "Dimension",
    rowGroup: true,
    hide: true,
    allowedAggFuncs: ["count"],
  }),
  getColumnDataProps("text", {
    field: "slice_display_value",
    headerName: "Type",
    allowedAggFuncs: ["count"],
  }),
  getColumnDataProps("number%", {
    field: "curr_ratio",
    headerName: "Current",
    allowedAggFuncs: ["count"],
  }),
];

const GPLTypeColumnProps = [
  getColumnDataProps("number%", {
    field: "curr_balance_ratio",
    headerName: "Current",
    allowedAggFuncs: ["count"],
  }),
  getColumnDataProps("number%", {
    field: "loan_count_curr_ratio",
    headerName: "Current #",
    allowedAggFuncs: ["count"],
  }),
  getColumnDataProps("number%", {
    field: "orig_balance_ratio",
    headerName: "Original",
    allowedAggFuncs: ["count"],
  }),
  getColumnDataProps("number%", {
    field: "loan_count_orig_ratio",
    headerName: "Original #",
    allowedAggFuncs: ["count"],
  }),
];

type GPLViewType = "balance" | "count";

const LoanPoolDistributionBody: React.FC<LoanPoolDistributionBodyProps> = ({
  collateralDistributionRequest,
  summaryRequest,
  lastRun,
  factorDateInput,
  factorDate,
}: LoanPoolDistributionBodyProps) => {
  const gridRef = React.useRef<AgGridReact>(null);
  const { data, isLoading } = useGetCollateralDistributionSWR(collateralDistributionRequest, lastRun);
  const viewLocalStorage = (safeLocalStorage.getItem(LOCAL_STORAGE_KEY.LOAN_POOL_DISTRIBUTION_VIEW) ||
    GridView.GRID) as GridViewValuesType;
  const [loanPoolDistributionView, setLoanPoolDistributionView] = React.useState<GridViewValuesType>(viewLocalStorage);

  const gplViewLocalStorage = (safeLocalStorage.getItem(LOCAL_STORAGE_KEY.LOAN_POOL_DISTRIBUTION_GPL_VIEW) ||
    "balance") as "balance" | "count";
  const [gplView, setGplView] = React.useState<"balance" | "count">(gplViewLocalStorage);

  const toggleGplView = React.useCallback((view: GPLViewType) => {
    safeLocalStorage.setItem(LOCAL_STORAGE_KEY.LOAN_POOL_DISTRIBUTION_GPL_VIEW, view);
    setGplView(view);
  }, []);

  const setView = React.useCallback((view: GridViewValuesType) => {
    safeLocalStorage.setItem(LOCAL_STORAGE_KEY.LOAN_POOL_DISTRIBUTION_VIEW, view);
    setLoanPoolDistributionView(view);
  }, []);

  const headerGPLButtons = React.useMemo(() => {
    return (
      <>
        {data?.gpl_pool_level && (
          <ToggleButtonGroup
            buttons={[
              { label: "Balance", value: "balance" },
              { label: "Count", value: "count" },
            ]}
            selectedButton={gplView}
            onChange={toggleGplView}
          />
        )}
      </>
    );
  }, [gplView, toggleGplView, data]);

  const HeaderActionButtons = () => (
    <GridChartToggle hasTableView selectedView={loanPoolDistributionView} setSelectedView={setView} />
  );

  const collateralLevel =
    collateralDistributionRequest.collateral_level === CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel.LOAN_LEVEL
      ? "Loan"
      : "Pool";

  const gridRowData =
    collateralLevel === "Loan"
      ? data?.conventional_loan_level ?? data?.gnm_loan_level ?? undefined
      : data?.conventional_pool_level ?? data?.gnm_pool_level ?? data?.gpl_pool_level ?? undefined;

  const sortedGridRowData = React.useMemo(() => {
    return gridRowData?.sort((a, b) => {
      if (a && b) {
        if (a.dimension_display_value && b.dimension_display_value) {
          const order = a.dimension_display_value.localeCompare(b.dimension_display_value);
          if (order !== 0) return order;
        }
      }
      return 0;
    });
  }, [gridRowData]);

  let gridType = collateralLevel === "Loan" ? "LoanDistribution" : "PoolDistribution";

  if (data?.gpl_pool_level) {
    gridType += "-GPL";
  }

  const columnData: ColDef[] = React.useMemo(() => {
    if (gridType.endsWith("-GPL")) {
      return [...columnProps, ...GPLTypeColumnProps];
    }
    return columnProps;
  }, [gridType]);

  return (
    <MainInputContentTemplate
      inputs={
        <LoanPoolDistributionSummary
          summaryRequest={summaryRequest}
          collateral_level={collateralDistributionRequest.collateral_level}
          factorDate={factorDate}
        />
      }
    >
      <Box>
        <Box
          minH="calc(100vh - 9rem)"
          h="full"
          display={loanPoolDistributionView === GridView.GRID || !sortedGridRowData ? "display" : "none"}
        >
          <CAGrid<
            | CA_Mastr_Api_v1_0_Models_Collateral_Distributions_ConventionalPoolLevel
            | CA_Mastr_Api_v1_0_Models_Collateral_Distributions_GplPoolLevel
          >
            ref={gridRef}
            hasRun={!!lastRun}
            headingElement={factorDateInput}
            headerActionButtons={<HeaderActionButtons />}
            gridProps={{
              columnDefs: columnData,
              rowData: sortedGridRowData,
              pivotMode: false,
              // groupDefaultExpanded: -1,
              sideBar: null,
              enableCharts: false,
              cellSelection: false,
              loading: isLoading,
            }}
            gridType={gridType}
            initialMessage={`Select a bond and click on ▷ to load ${collateralLevel} Distribution.`}
            showExpand
            cardProps={{
              title: " ",
              cardKey: "loan-distribution-grid",
              cardStyleOnOpen: {
                height: "full",
              },
              allowCollapse: true,
            }}
            stackStyles={{
              h: "calc(100vh - 12rem)",
            }}
          />
        </Box>
        {loanPoolDistributionView === GridView.CHART && (
          <LoanPoolDistributionCharts
            collateralLevel={collateralLevel}
            data={sortedGridRowData}
            headingElement={factorDateInput}
            headerActionButtons={<HeaderActionButtons />}
            gplView={gplView}
            headerGPLButtons={headerGPLButtons}
          />
        )}
        {loanPoolDistributionView === GridView.TABLE && (
          <Box>
            <LoanPoolDistributionTable
              collateralLevel={collateralLevel}
              data={sortedGridRowData}
              headingElement={factorDateInput}
              headerActionButtons={<HeaderActionButtons />}
              gplView={gplView}
              headerGPLButtons={headerGPLButtons}
            />
          </Box>
        )}
      </Box>
    </MainInputContentTemplate>
  );
};

export default LoanPoolDistributionBody;
