import * as React from "react";
import { useBreakpointValue } from "@chakra-ui/media-query";
import { Box, Center, Flex, Spacer, Stack, VStack } from "@chakra-ui/layout";
import CASearch from "@/design-system/molecules/CASearch";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Distributions_ConventionalPoolLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Distributions_GplPoolLevel,
} from "@/utils/openapi";
import CACard from "@/design-system/molecules/CACard";
import { percentageGetter } from "@/utils/grid/formatters";
import ColumnWrapper from "@/components/helpers/ColumnWrapper";
import useWindowSize from "@/utils/useWindowResize";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import LoanPoolDistributionTableCard from "./LoanPoolDistributionTableCard";

type LoanPoolDistributionTablesProps = {
  collateralLevel: "Loan" | "Pool";
  headingElement?: React.JSX.Element;
  headerActionButtons?: React.JSX.Element;
  data?:
    | CA_Mastr_Api_v1_0_Models_Collateral_Distributions_ConventionalPoolLevel[]
    | CA_Mastr_Api_v1_0_Models_Collateral_Distributions_GplPoolLevel[];
  gplView: "balance" | "count";
  headerGPLButtons: React.JSX.Element;
};

export type LoanPoolDistributionTableData = {
  label: string;
  curr_value: string;
  original_value?: string;
};

const LoanPoolDistributionTable: React.FC<LoanPoolDistributionTablesProps> = ({
  headingElement,
  headerActionButtons,
  data,
  gplView,
  headerGPLButtons,
}: LoanPoolDistributionTablesProps) => {
  const isLargerThanMd = useBreakpointValue({ base: false, md: true });
  useWindowSize(); // we we don't need value, just needs to rerender and recalculate columnCount
  const columnWrapperContainerRef = React.useRef<HTMLDivElement>(null);

  const { chartData, headers } = React.useMemo(() => {
    const chartData: { [title: string]: LoanPoolDistributionTableData[] } = {};
    let headers = ["Type", "Current"];

    if (data) {
      for (const row of data) {
        const title = row.dimension_display_value;
        const label = row.slice_display_value;

        // When GPL Balance  and non-GPL
        let curr_value = "curr_ratio" in row ? row.curr_ratio : undefined;
        let original_value = "orig_balance_ratio" in row ? row.orig_balance_ratio : undefined;

        // When GPL Count
        if (gplView == "count" && typeof original_value === "number") {
          curr_value = "loan_count_curr_ratio" in row ? row.loan_count_curr_ratio : undefined;
          original_value = "loan_count_orig_ratio" in row ? row.loan_count_orig_ratio : undefined;
        }

        if (typeof original_value === "number") {
          headers = gplView == "count" ? ["Type", "Current #", "Original #"] : ["Type", "Current", "Original"];
        }
        if (title && label && typeof curr_value === "number") {
          if (!chartData[title]) {
            chartData[title] = [];
          }
          if (typeof original_value === "number") {
            chartData[title].push({
              label,
              curr_value: percentageGetter(1)(Number(curr_value)),
              original_value: percentageGetter(1)(Number(original_value)),
            });
          } else {
            chartData[title].push({ label, curr_value: percentageGetter(1)(Number(curr_value)) });
          }
        }
      }
    }
    return { chartData, headers };
  }, [data, gplView]);

  const { filteredList, getInputProps } = useKeywordsSearch({
    getSearchableText: (item) => `${item[0]}`,
    list: Object.entries(chartData),
  });

  const columnCount = useUpdateColumnCount({
    columnWrapperContainerWidth: columnWrapperContainerRef.current?.clientWidth,
    headers,
  });

  const numerOfCardsPerColumn = Math.floor(Object.keys(chartData).length / columnCount);
  let remainingCards = Object.keys(chartData).length % columnCount;
  const arrayOfColumns: Array<number[]> = [];
  let endColumnIndex = 0;
  let startColumnIndex = 0;
  [...Array(columnCount)].map(() => {
    if (remainingCards > 0) {
      endColumnIndex += numerOfCardsPerColumn + 1;
      arrayOfColumns.push([startColumnIndex, endColumnIndex]);
      startColumnIndex += numerOfCardsPerColumn + 1;
      remainingCards--;
    } else {
      endColumnIndex += numerOfCardsPerColumn;
      arrayOfColumns.push([startColumnIndex, endColumnIndex]);
      startColumnIndex += numerOfCardsPerColumn;
    }
  });

  return (
    <Flex direction="column" alignItems="stretch" height="full">
      <CACard cardBodyStyle={{ p: 0 }} minH="auto">
        <VStack px={4} py={3} spacing={2} alignContent="flex-start">
          <Flex alignItems="center" w="full">
            <Stack
              direction={{ base: "column", md: "row" }}
              spacing={{ base: 2, md: 6 }}
              alignItems={{ base: "flex-start", md: "center" }}
              w="full"
            >
              <Flex justifyContent={{ base: "flex-start", lg: "center" }} alignItems="center">
                <CASearch
                  maxW="320px"
                  name="chart-search"
                  placeholder={"Quick Filter"}
                  isDisabled={!data}
                  {...getInputProps()}
                />
              </Flex>
              {isLargerThanMd && headingElement}
            </Stack>
            <Spacer />
            {headers.length === 3 && headerGPLButtons}
            <Spacer />
            <Box pl="3">{headerActionButtons}</Box>
          </Flex>
          {!isLargerThanMd && <Box w="full">{headingElement}</Box>}
          {chartData && Object.keys(chartData).length === 0 ? (
            <CACard minH="calc(100vh - 16.5rem)">
              <Center minH="25rem" w="full" h="100%" overflow="hidden">
                <CAInfo status="info" description="No data available" />
              </Center>
            </CACard>
          ) : null}
        </VStack>
      </CACard>
      <Flex
        ref={columnWrapperContainerRef}
        direction="row"
        position="relative"
        mx={{ base: -1, sm: -2 }}
        mt={{ base: 1, sm: 2 }}
      >
        {arrayOfColumns.map((cardsPerColumn, colIndex) => {
          return (
            <ColumnWrapper
              key={`${colIndex}/${columnCount}`}
              size={headers.length === 3 ? "distribution-table-card-3" : "distribution-table-card-2"}
              p={{ base: 1, sm: 2 }}
            >
              {filteredList
                .filter((_, index) => {
                  return index >= cardsPerColumn[0] && index < cardsPerColumn[1];
                })
                .map(([title, data]) => {
                  return <LoanPoolDistributionTableCard key={title} title={title} data={data} headers={headers} />;
                })}
            </ColumnWrapper>
          );
        })}
      </Flex>
    </Flex>
  );
};

export default React.memo(LoanPoolDistributionTable);

const useUpdateColumnCount = ({
  columnWrapperContainerWidth,
  headers,
}: {
  columnWrapperContainerWidth: number | undefined;
  headers: string[];
}) => {
  const smSize = useBreakpointValue({ base: false, sm: true, md: false });
  const [columnCount, setColumnCount] = React.useState(0);

  React.useEffect(() => {
    let newColumnCount = 0;
    if (!columnWrapperContainerWidth) {
      newColumnCount = 0;
    } else if (headers.length === 3 && !smSize) {
      newColumnCount = Math.floor(columnWrapperContainerWidth / 256);
    } else if (headers.length === 2 && !smSize) {
      newColumnCount = Math.floor(columnWrapperContainerWidth / 180);
    } else {
      newColumnCount = 2;
    }

    if (newColumnCount !== columnCount) {
      setColumnCount(newColumnCount);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columnWrapperContainerWidth, headers.length, smSize]);

  return columnCount;
};
