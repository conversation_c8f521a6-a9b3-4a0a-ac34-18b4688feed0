import React from "react";
import { Text, chakra, useColorModeValue } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import CATable, { CATableData } from "@/design-system/molecules/CATable";
import { LoanPoolDistributionTableData } from "./LoanPoolDistributionTable";

type LoanPoolDistributionTableCardProps = {
  title: string;
  data: LoanPoolDistributionTableData[];
  headers: string[];
};

const Label = ({ label, title }: { label: string; title: string }) => {
  const dimmedTextColor = useColorModeValue("celloBlue.300", "gemBlue.700");
  if (title === "Remaining Pool Penalty Structure") {
    const [before, after] = label.split("|");
    return (
      <Text wordBreak="break-word" variant="tableLeft">
        <chakra.span color={dimmedTextColor}>{before}|</chakra.span>
        {after}
      </Text>
    );
  }

  return (
    <Text wordBreak="break-word" variant="tableLeft">
      {label}
    </Text>
  );
};

const LoanPoolDistributionTableCard: React.FC<LoanPoolDistributionTableCardProps> = ({
  title,
  data,
  headers,
}: LoanPoolDistributionTableCardProps) => {
  const dataValues: CATableData = [];
  for (const element of data) {
    if (element.original_value) {
      dataValues.push({
        name: <Label label={element.label} title={title} />,
        values: [element.curr_value, element.original_value],
      });
    } else {
      dataValues.push({
        name: <Label label={element.label} title={title} />,
        values: [element.curr_value],
      });
    }
  }
  return (
    <CACard title={title} cardKey={title} allowCollapse cardBodyStyle={{ overflowX: "auto" }}>
      <CATable headers={headers} data={dataValues} />
    </CACard>
  );
};

export default React.memo(LoanPoolDistributionTableCard);
