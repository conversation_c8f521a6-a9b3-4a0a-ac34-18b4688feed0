import * as React from "react";
import { CellClickedEvent, ColDef } from "ag-grid-community";
import { Box } from "@chakra-ui/layout";
import CAGrid from "@/design-system/molecules/CAGrid";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Details_ConventionalLoanLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Details_ConventionalPoolLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Details_GnmLoanLevel,
  CA_Mastr_Api_v1_0_Models_Collateral_Details_GplPoolLevel,
  CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel,
} from "@/utils/openapi";
import { useGetCollateralDetailSWR } from "@/utils/swr-hooks";
import loanListColumnData, { getLoanListFooterOptions } from "@/utils/grid/LoanListColumnData";
import poolListColumnData, { getPoolListFooterOptions } from "@/utils/grid/PoolListColumnData";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { CollateralRequest } from "@/types/swr";
import { CAGridFooterOption } from "@/design-system/molecules/CAGrid/types";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { safeSessionStorage } from "@/utils/local-storage";

type LoanPoolListBodyProps = {
  request: CollateralRequest;
  lastRun?: string;
  factorDateInput: React.JSX.Element;
};

export type LoanPoolRowData =
  | CA_Mastr_Api_v1_0_Models_Collateral_Details_ConventionalLoanLevel
  | CA_Mastr_Api_v1_0_Models_Collateral_Details_GnmLoanLevel
  | CA_Mastr_Api_v1_0_Models_Collateral_Details_ConventionalPoolLevel
  | CA_Mastr_Api_v1_0_Models_Collateral_Details_GplPoolLevel;

const LoanPoolListBody: React.FC<LoanPoolListBodyProps> = ({
  request,
  lastRun,
  factorDateInput,
}: LoanPoolListBodyProps) => {
  const { data, isLoading } = useGetCollateralDetailSWR(request, lastRun);
  const collateralLevel =
    request.collateral_level === CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel.LOAN_LEVEL ? "Loan" : "Pool";

  const { loan_pool_data_key, data: gridRowData } = React.useMemo(() => {
    if (collateralLevel === "Loan") {
      if (data?.conventional_loan_level) {
        return {
          loan_pool_data_key: "conventional_loan_level",
          data: data.conventional_loan_level.map((item) => ({
            ...item,
            weight:
              item.contributed_balance &&
              item.current_rpb &&
              item.current_rpb > 0 &&
              item.contributed_balance / item.current_rpb,
          })),
        };
      }
      if (data?.gnm_loan_level) {
        return {
          loan_pool_data_key: "gnm_loan_level",
          data: data.gnm_loan_level.map((item) => ({
            ...item,
            weight:
              item.contributed_balance &&
              item.current_rpb &&
              item.current_rpb > 0 &&
              item.contributed_balance / item.current_rpb,
          })),
        };
      }
      if (data?.gpl_pool_level) {
        // UI-717 GPL do not have loan level data
        return {
          loan_pool_data_key: "",
          data: [],
        };
      }
    }
    if (collateralLevel === "Pool") {
      if (data?.conventional_pool_level) {
        return {
          loan_pool_data_key: "conventional_pool_level",
          data: data.conventional_pool_level.map((item) => ({
            ...item,
            weight:
              item.contributed_current_balance &&
              item.acls &&
              item.contributed_current_balance > 0 &&
              item.contributed_current_balance / item.acls,
          })),
        };
      }
      if (data?.gnm_pool_level) {
        return {
          loan_pool_data_key: "gnm_pool_level",
          data: data.gnm_pool_level.map((item) => ({
            ...item,
            weight:
              item.contributed_current_balance &&
              item.acls &&
              item.contributed_current_balance > 0 &&
              item.contributed_current_balance / item.acls,
          })),
        };
      }
      if (data?.gpl_pool_level) {
        return {
          loan_pool_data_key: "gpl_pool_level",
          data: data.gpl_pool_level,
        };
      }
    }
    return { loan_pool_data_key: "", data: undefined };
  }, [
    collateralLevel,
    data?.conventional_loan_level,
    data?.gnm_loan_level,
    data?.conventional_pool_level,
    data?.gnm_pool_level,
    data?.gpl_pool_level,
  ]);

  const { columnProps, footerOptions } = React.useMemo(() => {
    let columnProps: ColDef[] = [];

    if (collateralLevel) {
      columnProps = (collateralLevel === "Loan" ? loanListColumnData : poolListColumnData).filter((item) => item.field);
    } else {
      columnProps = [];
    }

    let footerOptions: CAGridFooterOption[] = [];
    if (columnProps && columnProps.length) {
      footerOptions = (collateralLevel === "Loan" ? getLoanListFooterOptions : getPoolListFooterOptions)(columnProps);
    }

    return { columnProps, footerOptions };
  }, [collateralLevel]);

  const onCellClicked = (event: CellClickedEvent) => {
    if (event.column.getColId() === "pool_mnemonic" && !event.rowPinned) {
      safeSessionStorage.removeItem(SESSION_STORAGE_KEY.DASHBOARD_LAST_RUN_ID);
      window.open(`/pricer/pricing/dashboard?bond_name=${encodeURIComponent(event.data["pool_mnemonic"])}`, "_blank");
    }
  };
  const gridType = loan_pool_data_key ? `${collateralLevel}List-${loan_pool_data_key}` : "";

  return (
    <MainInputContentTemplate>
      <Box minH="calc(100vh - 12rem)">
        <CAGrid<LoanPoolRowData>
          gridDataType="historical"
          hasRun={!!lastRun}
          footerOptions={footerOptions}
          headingElement={factorDateInput}
          gridProps={{
            columnDefs: columnProps,
            rowData: gridRowData,
            pivotMode: false,
            onCellClicked,
            loading: isLoading,
          }}
          gridType={gridType}
          cardProps={{
            title: " ",
            cardKey: "loan-list-grid",
            cardStyleOnOpen: {
              height: "full",
            },
            allowCollapse: true,
          }}
          stackStyles={{
            h: "calc(100vh - 12rem)",
          }}
          initialMessage={`Select a bond and click on ▷ to load ${collateralLevel} List.`}
        />
      </Box>
    </MainInputContentTemplate>
  );
};

export default LoanPoolListBody;
