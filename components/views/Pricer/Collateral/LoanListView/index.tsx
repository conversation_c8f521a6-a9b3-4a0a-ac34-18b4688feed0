import * as React from "react";
import { Box, HStack, Text } from "@chakra-ui/layout";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel } from "@/utils/openapi";
import CADateInput from "@/design-system/molecules/CADateInput";
import { getFormattedYearMonth, getInfoMsg } from "@/utils/helpers";
import { CollateralRequest } from "@/types/swr";
import { useGetCollateralDetailSWRForDashboard } from "@/utils/swr-hooks";
import { usePricerLoanListPage } from "@/contexts/PageContexts/PricerLoanListPageContext";
import { showWarningToast } from "@/design-system/theme/toast";
import PricerHeader from "../../PricerHeader";
import { CollateralDetailStopWatchWrapper } from "../../PricerStopWatchWrapper";
import LoanPoolListBody from "../LoanPoolListBody";
import { PricerModuleProps } from "../..";

const PricerCollateralLoanListView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, latestFactorDate },
    action: { setIsTimerRunning },
  } = usePricerModule();
  const {
    state: {
      loanListPageSettings: { factorDate },
      loanListPageSettingsCopy: { factorDate: tempFactorDate },
      lastRun,
    },
    action: { updateFactorDate, updateFactorDateCopy, run },
  } = usePricerLoanListPage();

  const setLastRun = (opts: { no_cache: boolean }) => {
    if (!tempFactorDate) {
      showWarningToast("Missing value", "Please select factor date value.");
      return;
    }
    const { no_cache } = opts;
    updateFactorDate(tempFactorDate);
    setIsTimerRunning(true);
    run({ no_cache });
  };

  const FactorDateInput = React.useMemo(() => {
    if (latestFactorDate) {
      return (
        <HStack>
          <Text variant="tableLeft">Factor Month</Text>
          <Box w="5.5rem">
            <CADateInput
              selectedDate={tempFactorDate}
              maxDate={latestFactorDate}
              onChange={(date) => updateFactorDateCopy(date ?? undefined)}
              showMonthYearPicker
              centered
              info={getInfoMsg(getFormattedYearMonth(tempFactorDate), getFormattedYearMonth(factorDate))}
            />
          </Box>
        </HStack>
      );
    } else {
      return <></>;
    }
  }, [tempFactorDate, latestFactorDate, factorDate, updateFactorDateCopy]);

  const collateralRequest: CollateralRequest = React.useMemo((): CollateralRequest => {
    return {
      bond_name,
      collateral_level: CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel.LOAN_LEVEL,
      start_date: factorDate,
    };
  }, [bond_name, factorDate]);

  const { data } = useGetCollateralDetailSWRForDashboard(collateralRequest, lastRun);

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={setLastRun}
        stopWatch={
          bond_name && factorDate ? (
            <CollateralDetailStopWatchWrapper
              collateralRequest={collateralRequest}
              lastRun={data?.status?.start_time ?? lastRun}
            />
          ) : undefined
        }
      />
      <LoanPoolListBody request={collateralRequest} lastRun={lastRun} factorDateInput={FactorDateInput} />
    </Box>
  );
};

export default PricerCollateralLoanListView;
