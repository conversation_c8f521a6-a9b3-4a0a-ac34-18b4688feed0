import * as React from "react";
import { Box, HStack, Text } from "@chakra-ui/layout";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest,
  CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel,
} from "@/utils/openapi";
import CADateInput from "@/design-system/molecules/CADateInput";
import { CollateralDistributionRequest } from "@/types/swr";
import { getFormattedYearMonth, getInfoMsg, parseDateToYYYYMMDD } from "@/utils/helpers";
import { usePricerPoolDistributionPage } from "@/contexts/PageContexts/PricerPoolDistributionPageContext";
import { showWarningToast } from "@/design-system/theme/toast";
import { useGetCollateralDistributionSWR } from "@/utils/swr-hooks";
import PricerHeader from "../../PricerHeader";
import { CollateralDistributionsStopWatchWrapper } from "../../PricerStopWatchWrapper";
import LoanPoolDistributionBody from "../LoanPoolDistributionBody";
import { PricerModuleProps } from "../..";

const PricerCollateralPoolDistributionView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, latestFactorDate, userSettings },
    action: { setIsTimerRunning },
  } = usePricerModule();
  const {
    state: {
      poolDistributionPageSettings: { factorDate },
      poolDistributionPageSettingsCopy: { factorDate: tempFactorDate },
      lastRun,
      app,
      page,
    },
    action: { updateFactorDate, updateFactorDateCopy, run },
  } = usePricerPoolDistributionPage();

  const setLastRun = (opts: { no_cache: boolean }) => {
    if (!tempFactorDate) {
      showWarningToast("Missing value", "Please select factor date value.");
      return;
    }
    const { no_cache } = opts;
    updateFactorDate(tempFactorDate);
    setIsTimerRunning(true);
    run({ no_cache });
  };

  const FactorDateInput = React.useMemo(() => {
    if (latestFactorDate) {
      return (
        <HStack>
          <Text variant="tableLeft">Factor Month</Text>
          <Box w="5.5rem">
            <CADateInput
              selectedDate={tempFactorDate}
              maxDate={latestFactorDate}
              onChange={(date) => updateFactorDateCopy(date ?? undefined)}
              showMonthYearPicker
              centered
              info={getInfoMsg(getFormattedYearMonth(tempFactorDate), getFormattedYearMonth(factorDate))}
            />
          </Box>
        </HStack>
      );
    } else {
      return <></>;
    }
  }, [tempFactorDate, latestFactorDate, factorDate, updateFactorDateCopy]);

  const {
    collateralDistributionRequest,
    collateralSummaryRequest,
  }: {
    collateralDistributionRequest: CollateralDistributionRequest;
    collateralSummaryRequest: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest;
  } = React.useMemo((): {
    collateralDistributionRequest: CollateralDistributionRequest;
    collateralSummaryRequest: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest;
  } => {
    const collateralDistributionRequest: CollateralDistributionRequest = {
      bond_name,
      collateral_level: CA_Mastr_Models_v1_0_Models_Collateral_CollateralLevel.POOL_LEVEL,
      start_date: factorDate,
    };

    const collateralSummaryRequest: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest = {
      app,
      page,
      bond_name,
      start_date: parseDateToYYYYMMDD(factorDate),
      market_data_source: userSettings.market_data_source,
    };
    return { collateralDistributionRequest, collateralSummaryRequest };
  }, [app, bond_name, factorDate, page, userSettings.market_data_source]);

  const { data } = useGetCollateralDistributionSWR(collateralDistributionRequest, lastRun);

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={setLastRun}
        stopWatch={
          bond_name && factorDate ? (
            <CollateralDistributionsStopWatchWrapper
              collateralDistributionRequest={collateralDistributionRequest}
              lastRun={data?.status?.start_time ?? lastRun}
            />
          ) : undefined
        }
      />
      <LoanPoolDistributionBody
        collateralDistributionRequest={collateralDistributionRequest}
        summaryRequest={collateralSummaryRequest}
        lastRun={lastRun}
        factorDateInput={FactorDateInput}
        factorDate={factorDate}
      />
    </Box>
  );
};

export default PricerCollateralPoolDistributionView;
