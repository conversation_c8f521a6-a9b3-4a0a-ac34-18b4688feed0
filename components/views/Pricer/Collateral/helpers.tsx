import { Box, Text } from "@chakra-ui/react";
import { formatCurrency, getFormattedNumberFixed } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralLoanLevelSummaryResponseExt,
  CA_Mastr_Api_v1_0_Models_Collateral_Summary_PoolLevel,
} from "@/utils/openapi";

export const getLoanLevelSummary = (
  data: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralLoanLevelSummaryResponseExt
) => {
  const { agency, loan_level } = data;

  if (!loan_level) {
    return {
      basic: [],
      loanSize: [],
      others: [],
    };
  }

  return {
    basic: [
      { name: "Agency", values: [agency ?? "-"] },
      { name: "Current Balance ($)", values: [formatCurrency(loan_level?.curr_balance, true)] },
      { name: "Loan Cnt", values: [getFormattedNumberFixed(0)(loan_level?.loan_count) ?? "-"] },
      { name: "Coupon (%)", values: [getFormattedNumberFixed(2)(loan_level?.net_coupon) ?? "-"] },
      { name: "WAC (%)", values: [getFormattedNumberFixed(2)(loan_level?.wac) ?? "-"] },
      { name: "WAM (months)", values: [getFormattedNumberFixed(0)(loan_level?.wam) ?? "-"] },
      { name: "WALA (months)", values: [getFormattedNumberFixed(0)(loan_level?.age) ?? "-"] },
    ].filter((r) => !!r),
    loanSize: [
      { name: "WAOLS ($)", values: [formatCurrency(loan_level?.waols, true)] },
      { name: "WACLS ($)", values: [formatCurrency(loan_level?.wacls, true) ?? "-"] },
      { name: "AOLS ($)", values: [formatCurrency(loan_level?.aols, true) ?? "-"] },
      { name: "ACLS ($)", values: [formatCurrency(loan_level?.acls, true) ?? "-"] },
    ],
    others: [
      {
        name: "Credit Score",
        values: [getFormattedNumberFixed(0)(loan_level?.credit_score) ?? "-"],
      },
      {
        name: "SATO (bps)",
        values: [getFormattedNumberFixed(0)(loan_level?.sato) ?? "-"],
      },
      {
        name: "Original LTV (%)",
        values: [getFormattedNumberFixed(1)(loan_level?.oltv) ?? "-"],
      },
      {
        name: "Current LTV (%)",
        values: [getFormattedNumberFixed(1)(loan_level?.cltv) ?? "-"],
      },
      {
        name: "Combined LTV (%)",
        values: [getFormattedNumberFixed(1)(loan_level?.orig_combined_ltv) ?? "-"],
      },
      {
        name: "DTI (%)",
        values: [getFormattedNumberFixed(1)(loan_level?.dti) ?? "-"],
      },
    ]
      .filter(Boolean)
      .map((item) => ({
        name: item && (
          <Text w="70%" variant="tableLeft">
            {item?.name}
          </Text>
        ),
        values:
          item &&
          item?.values.map((v, i) => (
            <Box key={i} whiteSpace="nowrap">
              {v}
            </Box>
          )),
      })),
  };
};

export const getPoolLevelSummary = (response: {
  data: CA_Mastr_Api_v1_0_Models_Collateral_Summary_PoolLevel;
  agency: string | null;
}) => {
  const { agency, data } = response;

  return {
    basic: [
      { name: "Agency", values: [agency ?? "-"] },
      { name: "Current Balance ($)", values: [formatCurrency(data.curr_balance, true)] },
      { name: "Factor", values: [getFormattedNumberFixed(8)(data.factor) ?? "-"] },
      "pool_count" in data && {
        name: "Pool Cnt",
        values: [data.pool_count ? getFormattedNumberFixed(0)(data.pool_count) : "-"],
      },
      "std_dev_cpr" in data && {
        name: "StD CPR (%)",
        values: [data.std_dev_cpr ? getFormattedNumberFixed(1)(data.std_dev_cpr) : "-"],
      },
      { name: "Coupon (%)", values: [getFormattedNumberFixed(2)(data?.net_coupon) ?? "-"] },
      "std_dev_wac" in data && {
        name: "StD WAC (%)",
        values: [data.std_dev_wac ? getFormattedNumberFixed(2)(data.std_dev_wac) : "-"],
      },
      { name: "WAC (%)", values: [getFormattedNumberFixed(2)(data.wac) ?? "-"] },
      { name: "WAM (months)", values: [getFormattedNumberFixed(0)(data.wam) ?? "-"] },
      { name: "WALA (months)", values: [getFormattedNumberFixed(0)(data.age) ?? "-"] },
    ].filter((r) => !!r),
    loanSize: [
      { name: "WAOLS ($)", values: [formatCurrency(data.waols, true)] },
      "wacls" in data && {
        name: "WACLS ($)",
        values: [formatCurrency(data.wacls, true)],
      },
      { name: "AOLS ($)", values: [formatCurrency(data.aols, true)] },
      { name: "ACLS ($)", values: [formatCurrency(data.acls, true)] },
      "mols" in data && {
        name: "MOLS ($)",
        values: [formatCurrency(data.mols, true)],
      },
    ].filter((r) => !!r),
    others: [
      "credit_score" in data && {
        name: "Credit Score",
        values: [getFormattedNumberFixed(0)(data.credit_score) ?? "-"],
      },
      "sato" in data && {
        name: "SATO (bps)",
        values: [getFormattedNumberFixed(0)(data.sato) ?? "-"],
      },
      "oltv" in data && {
        name: "Original LTV (%)",
        values: [getFormattedNumberFixed(1)(data.oltv) ?? "-"],
      },
      "cltv" in data && {
        name: "Current LTV (%)",
        values: [getFormattedNumberFixed(1)(data.cltv) ?? "-"],
      },
      "orig_combined_ltv" in data && {
        name: "Combined LTV (%)",
        values: [getFormattedNumberFixed(1)(data.orig_combined_ltv) ?? "-"],
      },
      "dti" in data && {
        name: "DTI (%)",
        values: [getFormattedNumberFixed(1)(data.dti) ?? "-"],
      },
    ].filter((r) => !!r),
  };
};
