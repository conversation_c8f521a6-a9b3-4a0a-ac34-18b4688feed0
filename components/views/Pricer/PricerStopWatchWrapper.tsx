import * as React from "react";
import {
  CustomRequestOptions,
  useGetBondCashFlowProjectionsSWR,
  useGetBondIndicativesSWRForReplinePage,
  useGetBondPrepayProjectionsSWR,
  useGetBondPrepayTrackingSWR,
  useGetBondPricingSWR,
  useGetCollateralDetailSWR,
  useGetCollateralDistributionSWR,
  usePostPrepayTracking,
} from "@/utils/swr-hooks";
import {
  BondCashFlowProjectionsRequest,
  BondIndicativesRequest,
  BondPrepayProjectionsRequest,
  BondPrepayTrackingRequest,
  BondPricingRequest,
  CollateralDistributionRequest,
  CollateralRequest,
} from "@/types/swr";
import StopWatch from "@/design-system/molecules/StopWatch";
import {
  CA_Mastr_Api_v1_0_Models_Batch_BatchRequest,
  CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest,
  CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest,
} from "@/utils/openapi";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";

type BondPricingStopWatchProps = {
  requestOpts: CustomRequestOptions;
  bondPricingRequest: BondPricingRequest;
  endDate?: string;
};

const StopWatchWrapper = ({ startDate, endDate }: { startDate: string | undefined; endDate?: string }) => {
  const {
    state: { isTimerRunning },
  } = usePricerModule();
  return <StopWatch startDate={startDate} endDate={endDate} isTimerRunning={isTimerRunning} />;
};

export const BondPricingStopWatchWrapper: React.FC<BondPricingStopWatchProps> = ({
  bondPricingRequest,
  requestOpts,
  endDate,
}: BondPricingStopWatchProps) => {
  useGetBondPricingSWR(bondPricingRequest, requestOpts);
  return <StopWatchWrapper startDate={requestOpts.lastRun} endDate={endDate} />;
};

type BondIndicativesStopWatchProps = {
  opts: CustomRequestOptions;
  bondIndicativesRequest: BondIndicativesRequest;
  endDate?: string;
};

export const BondIndicativesStopWatchWrapper: React.FC<BondIndicativesStopWatchProps> = ({
  bondIndicativesRequest,
  opts,
  endDate,
}: BondIndicativesStopWatchProps) => {
  useGetBondIndicativesSWRForReplinePage(bondIndicativesRequest, opts);

  return <StopWatchWrapper startDate={opts.lastRun} endDate={endDate} />;
};

type CollateralDetailStopWatchProps = {
  collateralRequest: CollateralRequest;
  lastRun: string | undefined;
};

export const CollateralDetailStopWatchWrapper: React.FC<CollateralDetailStopWatchProps> = ({
  collateralRequest,
  lastRun,
}: CollateralDetailStopWatchProps) => {
  useGetCollateralDetailSWR(collateralRequest, lastRun);

  return <StopWatchWrapper startDate={lastRun} />;
};

type CollateralDistributionsStopWatchProps = {
  collateralDistributionRequest: CollateralDistributionRequest;
  lastRun: string | undefined;
};

export const CollateralDistributionsStopWatchWrapper: React.FC<CollateralDistributionsStopWatchProps> = ({
  collateralDistributionRequest,
  lastRun,
}: CollateralDistributionsStopWatchProps) => {
  useGetCollateralDistributionSWR(collateralDistributionRequest, lastRun);

  return <StopWatchWrapper startDate={lastRun} />;
};

type BondCashFlowProjectionsStopWatchProps = {
  bondCashFlowProjectionsRequest: BondCashFlowProjectionsRequest;
  opts: CustomRequestOptions;
  endDate?: string;
};

export const BondCashFlowProjectionsStopWatchWrapper: React.FC<BondCashFlowProjectionsStopWatchProps> = ({
  bondCashFlowProjectionsRequest,
  opts,
  endDate,
}: BondCashFlowProjectionsStopWatchProps) => {
  useGetBondCashFlowProjectionsSWR(bondCashFlowProjectionsRequest, opts);

  return <StopWatchWrapper startDate={opts?.lastRun} endDate={endDate} />;
};

type BondPrepayProjectionsStopWatchProps = {
  bondPrepayProjectionsRequest: BondPrepayProjectionsRequest;
  requestOpts: CustomRequestOptions;
  endDate?: string;
};

export const BondPrepayProjectionsStopWatchWrapper: React.FC<BondPrepayProjectionsStopWatchProps> = ({
  bondPrepayProjectionsRequest,
  requestOpts,
  endDate,
}: BondPrepayProjectionsStopWatchProps) => {
  useGetBondPrepayProjectionsSWR(bondPrepayProjectionsRequest, requestOpts);

  return <StopWatchWrapper startDate={requestOpts?.lastRun} endDate={endDate} />;
};

type BondPrepayTrackingStopWatchProps = {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
  endDate?: string;
};

export const BondPrepayTrackingStopWatchWrapper: React.FC<BondPrepayTrackingStopWatchProps> = ({
  bondPrepayTrackingRequest,
  requestOpts,
  endDate,
}: BondPrepayTrackingStopWatchProps) => {
  useGetBondPrepayTrackingSWR(bondPrepayTrackingRequest, requestOpts);

  return <StopWatchWrapper startDate={requestOpts?.lastRun} endDate={endDate} />;
};

type PrepayTrackingStopWatchProps = {
  lastRun: string | undefined;
  prepayTrackingRequest: CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest;
};

export const PrepayTrackingStopWatchWrapper: React.FC<PrepayTrackingStopWatchProps> = ({
  prepayTrackingRequest,
  lastRun,
}: PrepayTrackingStopWatchProps) => {
  usePostPrepayTracking(prepayTrackingRequest, lastRun);

  return <StopWatchWrapper startDate={lastRun} />;
};

type BondCompareStopWatchProps = {
  requestOpts: CustomRequestOptions;
  bondCompareRequest: CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest;
  endDate: string | undefined;
};

export const BondCompareStopWatchWrapper: React.FC<BondCompareStopWatchProps> = ({
  requestOpts,
  endDate,
}: BondCompareStopWatchProps) => {
  return <StopWatchWrapper startDate={requestOpts?.lastRun} endDate={endDate} />;
};

type BatchRequestStopWatchProps = {
  request: CA_Mastr_Api_v1_0_Models_Batch_BatchRequest;
  requestOpts: CustomRequestOptions;
  endDate?: string;
};

export const BatchRequestStopWatch: React.FC<BatchRequestStopWatchProps> = ({
  requestOpts,
  endDate,
}: BatchRequestStopWatchProps) => {
  return <StopWatchWrapper startDate={requestOpts?.lastRun} endDate={endDate} />;
};

type PricerEmpiricalTrackingSimplifiedStopWatchProps = {
  lastRun: string | undefined;
  endDate?: string;
};

export const PricerEmpiricalTrackingSimplifiedStopWatchWrapper: React.FC<
  PricerEmpiricalTrackingSimplifiedStopWatchProps
> = ({ lastRun, endDate }: PricerEmpiricalTrackingSimplifiedStopWatchProps) => {
  return <StopWatchWrapper startDate={lastRun} endDate={endDate} />;
};
