import * as React from "react";
import { useForm } from "react-hook-form";
import { Box, Text } from "@chakra-ui/react";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import { CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse } from "@/utils/openapi";
import CAInput from "@/design-system/molecules/CAInput";
import CATable from "@/design-system/molecules/CATable";
import { getFormattedNumberFixed } from "@/utils/helpers";
import CAStepFillInput from "@/design-system/molecules/CAStepFillInput";

type ProjectionAverageTableProps = {
  allData: (CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse | null)[] | null | undefined;
};

type rateShiftProps = {
  rateShift0: number;
  rateShift1: number;
  rateShift2: number;
  rateShift3: number;
  rateShift4: number;
  rateShift5: number;
  rateShift6: number;
  rateShift7: number;
  rateShift8: number;
};

const ProjectionAverageTable: React.FC<ProjectionAverageTableProps> = ({ allData }: ProjectionAverageTableProps) => {
  const {
    state: { rate_type, curve_shifts, curve_shifts_copy },
    action: { setCurveShifts },
  } = usePricerProjectionPage();

  const { register, getValues, watch, setValue } = useForm<rateShiftProps>({
    defaultValues: {
      rateShift0: curve_shifts[0],
      rateShift1: curve_shifts[1],
      rateShift2: curve_shifts[2],
      rateShift3: curve_shifts[3],
      rateShift4: curve_shifts[4],
      rateShift5: curve_shifts[5],
      rateShift6: curve_shifts[6],
      rateShift7: curve_shifts[7],
      rateShift8: curve_shifts[8],
    },
  });

  const [
    watch_rateShift0,
    watch_rateShift1,
    watch_rateShift2,
    watch_rateShift3,
    watch_rateShift4,
    watch_rateShift5,
    watch_rateShift6,
    watch_rateShift7,
    watch_rateShift8,
  ] = watch([
    "rateShift0",
    "rateShift1",
    "rateShift2",
    "rateShift3",
    "rateShift4",
    "rateShift5",
    "rateShift6",
    "rateShift7",
    "rateShift8",
  ]);
  const msg2 = "Click run to update your calculation";

  const getRateShiftResult = (rateShift: number) => {
    return allData?.find((d) => Number(d?.ui_request_id?.split(":")?.at(-1)) === rateShift)?.prepay_projection_speeds;
  };

  const d0 = getRateShiftResult(curve_shifts[0]) as { [key: string]: number };
  const d1 = getRateShiftResult(curve_shifts[1]) as { [key: string]: number };
  const d2 = getRateShiftResult(curve_shifts[2]) as { [key: string]: number };
  const d3 = getRateShiftResult(curve_shifts[3]) as { [key: string]: number };
  const d4 = getRateShiftResult(curve_shifts[4]) as { [key: string]: number };
  const d5 = getRateShiftResult(curve_shifts[5]) as { [key: string]: number };
  const d6 = getRateShiftResult(curve_shifts[6]) as { [key: string]: number };
  const d7 = getRateShiftResult(curve_shifts[7]) as { [key: string]: number };
  const d8 = getRateShiftResult(curve_shifts[8]) as { [key: string]: number };

  const durationArr = ["1m", "3m", "6m", "1y", "3y", "5y", "lt"];

  const tableData: { name: string; values: (string | number)[] }[] = [];
  for (let i = 0; i < durationArr.length; i++) {
    tableData.push({
      name: durationArr[i] === "lt" ? "LT" : durationArr[i],
      values: [
        getFormattedNumberFixed(1)(d0?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d1?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d2?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d3?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d4?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d5?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d6?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d7?.[`${rate_type}_${durationArr[i]}`]),
        getFormattedNumberFixed(1)(d8?.[`${rate_type}_${durationArr[i]}`]),
      ],
    });
  }

  const getRateShiftWatchValue = React.useCallback(
    (index: number) => {
      switch (index) {
        case 0: {
          return watch_rateShift0;
        }
        case 1: {
          return watch_rateShift1;
        }
        case 2: {
          return watch_rateShift2;
        }
        case 3: {
          return watch_rateShift3;
        }
        case 4: {
          return watch_rateShift4;
        }
        case 5: {
          return watch_rateShift5;
        }
        case 6: {
          return watch_rateShift6;
        }
        case 7: {
          return watch_rateShift7;
        }
        case 8: {
          return watch_rateShift8;
        }
      }
    },
    [
      watch_rateShift0,
      watch_rateShift1,
      watch_rateShift2,
      watch_rateShift3,
      watch_rateShift4,
      watch_rateShift5,
      watch_rateShift6,
      watch_rateShift7,
      watch_rateShift8,
    ]
  );

  const updateActualRateShifts = React.useCallback(() => {
    const {
      rateShift0,
      rateShift1,
      rateShift2,
      rateShift3,
      rateShift4,
      rateShift5,
      rateShift6,
      rateShift7,
      rateShift8,
    } = getValues();

    const newRateShifts = [
      rateShift0,
      rateShift1,
      rateShift2,
      rateShift3,
      rateShift4,
      rateShift5,
      rateShift6,
      rateShift7,
      rateShift8,
    ];

    if (JSON.stringify(newRateShifts) !== JSON.stringify(curve_shifts)) {
      setCurveShifts(newRateShifts);
    }
  }, [getValues, curve_shifts, setCurveShifts]);

  const onStepFill = React.useCallback(
    (step: number) => {
      const centerRateShift = curve_shifts[4];
      const centeredIndex = 4;
      const updatedRateShifts: number[] = curve_shifts.map((item, index) => {
        if (index < centeredIndex) {
          const difference = centeredIndex - index;
          return centerRateShift - difference * step;
        }
        if (index > centeredIndex) {
          const difference = index - centeredIndex;
          return centerRateShift + difference * step;
        }
        return item;
      });
      setValue("rateShift0", updatedRateShifts[0]);
      setValue("rateShift1", updatedRateShifts[1]);
      setValue("rateShift2", updatedRateShifts[2]);
      setValue("rateShift3", updatedRateShifts[3]);
      setValue("rateShift4", updatedRateShifts[4]);
      setValue("rateShift5", updatedRateShifts[5]);
      setValue("rateShift6", updatedRateShifts[6]);
      setValue("rateShift7", updatedRateShifts[7]);
      setValue("rateShift8", updatedRateShifts[8]);
      updateActualRateShifts();
    },
    [curve_shifts, setValue, updateActualRateShifts]
  );

  const tableHeaders = React.useMemo(() => {
    const rateShiftInputs = curve_shifts.map((_, index) => {
      if (index === 4) {
        return (
          <CAStepFillInput
            key={index}
            type="number"
            inputType="digit"
            defaultStepperValue={25}
            info={{
              show: curve_shifts_copy !== undefined && getRateShiftWatchValue(index) != curve_shifts_copy[index],
              msg1: `Value changed from ${
                curve_shifts_copy ? curve_shifts_copy[index] : ""
              } to ${getRateShiftWatchValue(index)}`,
              msg2: msg2,
            }}
            textAlign="center"
            {...register("rateShift4", {
              valueAsNumber: true,
              required: true,
            })}
            onBlur={updateActualRateShifts}
            onFill={onStepFill}
          />
        );
      }
      return (
        <CAInput
          key={index}
          type="number"
          inputType="digit"
          textAlign="center"
          info={{
            show: curve_shifts_copy !== undefined && getRateShiftWatchValue(index) != curve_shifts_copy[index],
            msg1: `Value changed from ${curve_shifts_copy ? curve_shifts_copy[index] : ""} to ${getRateShiftWatchValue(
              index
            )}`,
            msg2: msg2,
          }}
          {...register(
            `rateShift${index}` as
              | "rateShift0"
              | "rateShift1"
              | "rateShift2"
              | "rateShift3"
              | "rateShift4"
              | "rateShift5"
              | "rateShift6"
              | "rateShift7"
              | "rateShift8",
            {
              valueAsNumber: true,
              required: true,
            }
          )}
          onBlur={updateActualRateShifts}
        />
      );
    });
    return [
      "Tenor",
      ...rateShiftInputs.map((input, index) => (
        <Box minW="4.5rem" key={index}>
          <Box maxW="6.25rem" mx="auto" my={0}>
            {input}
          </Box>
        </Box>
      )),
    ];
  }, [getRateShiftWatchValue, onStepFill, curve_shifts, curve_shifts_copy, register, updateActualRateShifts]);

  return (
    <Box minH="300px" overflowY="auto" px={4}>
      <Text variant="tableHead" textAlign="center" position="sticky" left="0">
        Curve shift (bps)
      </Text>
      <CATable variant="caFullCentered" minW="lg" headers={tableHeaders} data={tableData} condensed={false} />
    </Box>
  );
};

export default React.memo(ProjectionAverageTable);
