import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";

const ProjectionViewRateTypeToggle: React.FC = () => {
  const {
    state: { rate_type },
    action: { setRateType },
  } = usePricerProjectionPage();

  return (
    <ToggleButtonGroup
      buttons={[
        { label: "CPR", value: "cpr" },
        { label: "CRR", value: "crr" },
        { label: "CDR", value: "cdr" },
      ]}
      selectedButton={rate_type}
      onChange={setRateType}
    />
  );
};

export default ProjectionViewRateTypeToggle;
