import * as React from "react";
import { Box } from "@chakra-ui/react";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { getRequests } from "@/utils/helpers/pricer/projection";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import PricerHeader from "../PricerHeader";
import { PricerModuleProps } from "..";
import { BatchRequestStopWatch } from "../PricerStopWatchWrapper";
import { BatchRequestProgressIndicator } from "../PricerProgressIndicatorWrapper";
import ProjectionInput, { ProjectionInputHandler } from "./ProjectionInput";
import ProjectionViewBody from "./ProjectionViewBody";

const PricerProjectionView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, curve_shifts, lastRun, isOldRun, api_start_time, api_end_time, ...projectionState },
    action: { updateProjectionPageSettings, run },
  } = usePricerProjectionPage();
  const { pushWithoutRendering } = useQueryParameters();

  const projectionInputRef = React.useRef<ProjectionInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    projectionInputRef.current?.handleSubmit((data) => {
      updateProjectionPageSettings(data);
      updatePricerUserSettingsCopy(userSettings);
      setIsTimerRunning(true);
      const run_id = run({ no_cache });
      pushWithoutRendering({ run_id });
    })();
  };

  const { batchRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRunId,
    lastRun,
    isOldRun,
    bond_name,
    curve_shifts,
    ...projectionState,
  });

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <BatchRequestStopWatch
            request={batchRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={<BatchRequestProgressIndicator request={batchRequest} requestOpts={requestOpts} />}
      />
      <MainInputContentTemplate inputs={<ProjectionInput ref={projectionInputRef} />}>
        {batchRequest && <ProjectionViewBody bondPrepayProjectionsRequest={batchRequest} requestOpts={requestOpts} />}
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerProjectionView;
