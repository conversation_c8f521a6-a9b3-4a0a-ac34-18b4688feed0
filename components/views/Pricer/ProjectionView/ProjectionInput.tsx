import { Box, VStack } from "@chakra-ui/react";
import * as React from "react";
import { UseFormHandleSubmit, useForm } from "react-hook-form";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import { projectionPageSettingsType } from "@/contexts/PageContexts/PricerProjectionPageContext/PricerProjectionPageContextTypes";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import {
  getCDUDateKeyFromSubType,
  getDisplayValue<PERSON>yKey,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getFormattedYearMonth,
  getInfoMsg,
} from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "@/utils/openapi";
import useResetFormData from "@/hooks/useResetFormData";
import useOnPrepayModelChange from "@/hooks/useOnPrepayModelChange";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import ModelDialSelectorWrapper from "../shared/ModelDialSelectorWrapper";
import PrepayVectorSelector from "../shared/PrepayVectorSelector";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import ReplineDialsSwitch from "../shared/ReplineDialsSwitch";

export interface ProjectionInputHandler {
  handleSubmit: UseFormHandleSubmit<projectionPageSettingsType>;
}

const ProjectionInput: React.ForwardRefRenderFunction<ProjectionInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, security_info },
  } = usePricerModule();
  const {
    state: { projectionPageSettingsCopy, projectionPageSettings, isOldRun, lastRun },
  } = usePricerProjectionPage();

  const {
    handleSubmit,
    reset,
    register,
    watch,
    setValue,
    formState: { touchedFields },
  } = useForm<projectionPageSettingsType>();
  const [watch_prepay_model_type, watch_prepay_percentage, watch_prepay_model_scenario, watch_periods] = watch([
    "prepay_model_type",
    "prepay_percentage",
    "prepay_model_scenario",
    "periods",
  ]);
  useResetFormData({ reset, formData: projectionPageSettings });

  useOnPrepayModelChange({
    prepayModelType: watch_prepay_model_type,
    prepayPercentage: watch_prepay_percentage,
    setValue,
    isTouched: !!touchedFields.prepay_model_type,
    isOldRun,
  });

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));
  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);
  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="projection-summary-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
      overflow="visible"
    >
      <VStack alignItems="stretch" spacing={4} height={"100%"}>
        <Box>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            value={getFormattedLocaleDate(userSettings.curve_date)}
            _key="curve_date"
            dateFormatter={getFormattedLocaleDate}
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
        </Box>
        <Box>
          <CASelectDropdown
            label={"Prepay Model"}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_model_type, projectionPageSettingsCopy.prepay_model_type)}
            {...register("prepay_model_type", {
              required: true,
            })}
            options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_models)}
          />
          <CAInput
            type="number"
            label={"Multiplier (%)"}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_percentage, projectionPageSettingsCopy.prepay_percentage)}
            {...register("prepay_percentage", {
              valueAsNumber: true,
              required: true,
            })}
          />
          <CASelectDropdown
            label={"Scenario"}
            width={"6rem"}
            info={getInfoMsg(
              getDisplayValueByKey(watch_prepay_model_scenario),
              getDisplayValueByKey(projectionPageSettingsCopy.prepay_model_scenario)
            )}
            {...register("prepay_model_scenario")}
            options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_model_scenario)}
          />
          {watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR && (
            <PrepayVectorSelector lastRun={lastRun} />
          )}
          {(watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO ||
            watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR) && (
            <ModelDialSelectorWrapper lastRun={lastRun} />
          )}
          <ReplineDialsSwitch />
          <PricerValueDisplayWrapper
            name="Prepay Model Version"
            value={userSettings.model_version ?? ""}
            _key="model_version"
            link
          />
          <PricerValueDisplayWrapper
            name="Current Coupon Model"
            value={
              metadata?.pricer_settings?.current_coupon_model?.find(
                (l) => l.value === userSettings.current_coupon_model
              )?.display_value ?? ""
            }
            _key="current_coupon_model"
            link
          />
          <PricerValueDisplayWrapper
            name="Calibration"
            value={
              metadata?.pricer_settings?.calibration?.find((l) => l.value === userSettings.calibration)
                ?.display_value ?? ""
            }
            _key="calibration"
            link
          />
          <PricerValueDisplayWrapper name="Interest Rate Paths" value="1" _key="interestRatePaths" />
          <PricerValueDisplayWrapper
            name="Cello CDU Date"
            link
            value={getFormattedYearMonth(userSettings[cduDateKey])}
            _key={`cello_cdu_dates.${cduDateKey}`}
            dateFormatter={getFormattedYearMonth}
            drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
          />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
        </Box>
        <Box>
          <CAHeading as="h3" mb={4}>
            Projection
          </CAHeading>
          <CAInput
            type="number"
            inputType="positive-integer"
            label={"Periods"}
            width={"6rem"}
            info={getInfoMsg(watch_periods, projectionPageSettingsCopy.periods)}
            {...register("periods", {
              valueAsNumber: true,
              required: true,
            })}
          />
        </Box>
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(ProjectionInput);
