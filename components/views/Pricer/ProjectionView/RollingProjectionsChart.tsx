/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { Box } from "@chakra-ui/layout";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import CAAdvancedChart from "@/design-system/molecules/CAAdvancedChart";
import { CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse } from "@/utils/openapi";

type RollingProjectionsChartProps = {
  allData: (CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse | null)[] | null | undefined;
};

const RollingProjectionsChart: React.FC<RollingProjectionsChartProps> = ({ allData }: RollingProjectionsChartProps) => {
  const {
    state: { rate_type, period_duration, curve_shifts },
  } = usePricerProjectionPage();

  const d0 = allData?.[0]?.prepay_projection_series as { [key: string]: any };
  const d1 = allData?.[1]?.prepay_projection_series as { [key: string]: any };
  const d2 = allData?.[2]?.prepay_projection_series as { [key: string]: any };
  const d3 = allData?.[3]?.prepay_projection_series as { [key: string]: any };
  const d4 = allData?.[4]?.prepay_projection_series as { [key: string]: any };
  const d5 = allData?.[5]?.prepay_projection_series as { [key: string]: any };
  const d6 = allData?.[6]?.prepay_projection_series as { [key: string]: any };
  const d7 = allData?.[7]?.prepay_projection_series as { [key: string]: any };
  const d8 = allData?.[8]?.prepay_projection_series as { [key: string]: any };

  const chartData = [];
  for (let i = 0; i < d4?.length; i++) {
    chartData.push({
      period: `${d4[i].period}`,
      [curve_shifts[0]]: d0?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[1]]: d1?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[2]]: d2?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[3]]: d3?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[4]]: d4?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[5]]: d5?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[6]]: d6?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[7]]: d7?.[i][`${rate_type}_${period_duration}`],
      [curve_shifts[8]]: d8?.[i][`${rate_type}_${period_duration}`],
    });
  }

  return (
    <Box maxW="7xl" mx="auto">
      <CAAdvancedChart
        options={{
          xkey: "period",
          dataKeys: curve_shifts.map((rs) => ({
            type: "line",
            key: `${rs}`,
            name: `${rs}`,
            hideInitially: rs !== curve_shifts[4] ? true : false,
          })),
          margin: { left: -30, right: -50 },
          data: chartData,
          primaryAxisToFixed: 0,
          secondaryAxisToFixed: 0,
        }}
      />
    </Box>
  );
};

export default RollingProjectionsChart;
