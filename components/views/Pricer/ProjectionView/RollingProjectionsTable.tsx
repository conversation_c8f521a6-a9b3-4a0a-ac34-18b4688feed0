/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { Box, Text } from "@chakra-ui/layout";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import { CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse } from "@/utils/openapi";
import CATable from "@/design-system/molecules/CATable";
import { getFormattedNumberFixed, getFormattedYearMonth } from "@/utils/helpers";

type RollingProjectionsTableProps = {
  allData: (CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse | null)[] | null | undefined;
};

const RollingProjectionsTable: React.FC<RollingProjectionsTableProps> = ({ allData }: RollingProjectionsTableProps) => {
  const {
    state: { rate_type, period_duration, curve_shifts_copy, curve_shifts },
  } = usePricerProjectionPage();

  const getRateShiftResult = (rateShift: number) => {
    return allData?.find((d) => Number(d?.ui_request_id?.split(":")?.at(-1)) === rateShift)?.prepay_projection_series;
  };

  const d0 = getRateShiftResult(curve_shifts[0]) as { [key: string]: any };
  const d1 = getRateShiftResult(curve_shifts[1]) as { [key: string]: any };
  const d2 = getRateShiftResult(curve_shifts[2]) as { [key: string]: any };
  const d3 = getRateShiftResult(curve_shifts[3]) as { [key: string]: any };
  const d4 = getRateShiftResult(curve_shifts[4]) as { [key: string]: any };
  const d5 = getRateShiftResult(curve_shifts[5]) as { [key: string]: any };
  const d6 = getRateShiftResult(curve_shifts[6]) as { [key: string]: any };
  const d7 = getRateShiftResult(curve_shifts[7]) as { [key: string]: any };
  const d8 = getRateShiftResult(curve_shifts[8]) as { [key: string]: any };

  const tableData = React.useMemo(() => {
    if (!d4?.length) return null;

    const tableData: { name: string; values: (string | number)[] }[] = [];
    for (let i = 0; i < d4.length; i++) {
      tableData.push({
        name: `${d4[i].period}`,
        values: [
          getFormattedYearMonth(d4[i].prepay_month),
          getFormattedYearMonth(d4[i].factor_month),
          d0 ? getFormattedNumberFixed(1)(d0?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d1 ? getFormattedNumberFixed(1)(d1?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d2 ? getFormattedNumberFixed(1)(d2?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d3 ? getFormattedNumberFixed(1)(d3?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d4 ? getFormattedNumberFixed(1)(d4?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d5 ? getFormattedNumberFixed(1)(d5?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d6 ? getFormattedNumberFixed(1)(d6?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d7 ? getFormattedNumberFixed(1)(d7?.[i][`${rate_type}_${period_duration}`]) : undefined,
          d8 ? getFormattedNumberFixed(1)(d8?.[i][`${rate_type}_${period_duration}`]) : undefined,
        ].filter((v) => v ?? false) as (string | number)[],
      });
    }

    return tableData;
  }, [d0, d1, d2, d3, d4, d5, d6, d7, d8, period_duration, rate_type]);

  if (!tableData || !curve_shifts_copy) return null;

  const tableHeaders: (string | React.JSX.Element)[] = [
    "Period",
    "Prepay Month",
    "Factor Month",
    ...curve_shifts_copy
      .filter((rs) => !isNaN(rs))
      .map((curve_shift, index) => (
        <Box minW="3rem" key={index}>
          <Box maxW="6.25rem" mx="auto" my={0}>
            <Text variant="tableHead">{curve_shift}</Text>
          </Box>
        </Box>
      )),
  ];

  return (
    <Box h="31.25rem" maxH="85vh" overflow="scroll" px={4}>
      <Text variant="tableHead" textAlign="center">
        Curve shift (bps)
      </Text>
      <CATable variant="caFullCentered" minW="lg" headers={tableHeaders} data={tableData} condensed={false} />
    </Box>
  );
};

export default React.memo(RollingProjectionsTable);
