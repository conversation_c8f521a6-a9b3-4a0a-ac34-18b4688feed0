import * as React from "react";
import { Box, HStack, SimpleGrid, Text } from "@chakra-ui/layout";
import CACard, { CACardWithExpand } from "@/design-system/molecules/CACard";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import { CustomRequestOptions, useRunIdBatchRequestSWR } from "@/utils/swr-hooks";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { PricerProjectionPageContextState } from "@/contexts/PageContexts/PricerProjectionPageContext/PricerProjectionPageContextTypes";
import { CA_Mastr_Api_v1_0_Models_Batch_BatchRequest } from "@/utils/openapi";
import GridChartToggle, { GridView, GridViewValuesType } from "@/components/helpers/GridChartToggle";
import ProjectionViewRateTypeToggle from "./ProjectionViewRateTypeToggle";
import ProjectionAverageTable from "./ProjectionAverageTable";
import ProjectionAverageChart from "./ProjectionAverageChart";
import ProjectionMonthlyTable from "./RollingProjectionsTable";
import ProjectionMonthlyChart from "./RollingProjectionsChart";

interface ProjectionViewBodyProps {
  bondPrepayProjectionsRequest: CA_Mastr_Api_v1_0_Models_Batch_BatchRequest;
  requestOpts: CustomRequestOptions;
}

const ProjectionViewBody: React.FC<ProjectionViewBodyProps> = ({
  bondPrepayProjectionsRequest,
  requestOpts: { lastRun, isOldRun },
}: ProjectionViewBodyProps) => {
  const {
    state: { period_duration },
    action: { updatePeriodDuration },
  } = usePricerProjectionPage();

  const { data } = useRunIdBatchRequestSWR(bondPrepayProjectionsRequest, { lastRun, isOldRun });

  const [gridChartToggle, setGridChartToggle] = React.useState<GridViewValuesType>(GridView.GRID);

  const headerActionButtons = React.useMemo(() => {
    return (
      <>
        <ProjectionViewRateTypeToggle />
        <GridChartToggle selectedView={gridChartToggle} setSelectedView={setGridChartToggle} />
      </>
    );
  }, [gridChartToggle]);

  return (
    <CACardWithExpand title=" " allowCollapse cardKey="projection-view-body" overflow="visible">
      <Box display="flex" justifyContent={"flex-end"} gap={3}>
        {headerActionButtons}
      </Box>
      {
        <SimpleGrid columns={gridChartToggle === GridView.GRID ? 1 : { base: 1, lg: 2 }} gap={0} mx={-4}>
          <CACard title="Average" headingRight={<Box h="2.125rem"></Box>} headerStyle={{ bg: "inherit" }}>
            {gridChartToggle === GridView.GRID ? (
              <Box mx={-4}>
                <ProjectionAverageTable allData={data?.prepay_projection_responses} key={lastRun} />
              </Box>
            ) : (
              <ProjectionAverageChart allData={data?.prepay_projection_responses} />
            )}
          </CACard>
          {(data?.prepay_projection_responses || gridChartToggle === GridView.CHART) && (
            <CACard
              title="Rolling Projections"
              headerStyle={{ bg: "inherit" }}
              headingRight={
                <HStack spacing={3}>
                  <Text variant="primary" whiteSpace="nowrap">
                    Rolling Average
                  </Text>
                  <CASelectDropdown
                    name="period_duration"
                    defaultValue={period_duration}
                    options={[
                      { id: 0, value: "1m", displayValue: "1 Month" },
                      { id: 1, value: "3m", displayValue: "3 Months" },
                      { id: 2, value: "6m", displayValue: "6 Months" },
                      { id: 3, value: "1y", displayValue: "12 Months" },
                      { id: 4, value: "3y", displayValue: "3 Years" },
                      { id: 5, value: "5y", displayValue: "5 years" },
                    ]}
                    onChange={(e) =>
                      updatePeriodDuration(
                        (e.target as HTMLSelectElement).value as PricerProjectionPageContextState["period_duration"]
                      )
                    }
                  />
                </HStack>
              }
            >
              {gridChartToggle === GridView.GRID ? (
                <Box mx={-4}>
                  <ProjectionMonthlyTable allData={data?.prepay_projection_responses} />
                </Box>
              ) : (
                <ProjectionMonthlyChart allData={data?.prepay_projection_responses} />
              )}
            </CACard>
          )}
        </SimpleGrid>
      }
    </CACardWithExpand>
  );
};

export default ProjectionViewBody;
