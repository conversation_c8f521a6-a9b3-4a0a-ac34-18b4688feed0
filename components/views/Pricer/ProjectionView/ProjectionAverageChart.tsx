import * as React from "react";
import { Box } from "@chakra-ui/layout";
import { usePricerProjectionPage } from "@/contexts/PageContexts/PricerProjectionPageContext";
import { CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse } from "@/utils/openapi";
import CABarChart from "@/design-system/molecules/CABarChart";

type ProjectionAverageChartProps = {
  allData: (CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse | null)[] | null | undefined;
};

const ProjectionAverageChart: React.FC<ProjectionAverageChartProps> = ({ allData }: ProjectionAverageChartProps) => {
  const {
    state: { rate_type, curve_shifts },
  } = usePricerProjectionPage();

  const periods = ["1m", "3m", "6m", "1y", "3y", "5y", "lt"];

  const chartData = [];
  for (let i = 0; i < curve_shifts.length; i++) {
    const rateShift = curve_shifts[i];
    const d = allData?.[i]?.prepay_projection_speeds as { [key: string]: number };

    chartData.push({
      rateShift,
      ...periods.reduce((acc, period) => {
        return { ...acc, [period]: d?.[`${rate_type}_${period}`] };
      }, {}),
    });
  }

  return (
    <Box maxW="7xl" mx="auto">
      <CABarChart
        options={{
          xkey: "rateShift",
          dataKeys: periods.map((p) => ({ key: p, name: p === "lt" ? "LT" : p })),
          margin: { left: -30, right: 10 },
          data: chartData,
        }}
      />
    </Box>
  );
};

export default ProjectionAverageChart;
