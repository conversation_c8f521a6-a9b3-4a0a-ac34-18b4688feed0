import * as React from "react";
import { VStack } from "@chakra-ui/layout";
import { Button } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import { BondPrepayProjectionsRequest } from "@/types/swr";
import { CustomRequestOptions, useGetBondPrepayProjectionsSWR } from "@/utils/swr-hooks";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import ProjectionDetailGrid from "./ProjectionDetailGrid";
import ProjectionDetailReplineSummaryTable from "./ProjectionDetailReplineSummaryTable";

interface ProjectionDetailViewBodyProps {
  bondPrepayProjectionsRequest: BondPrepayProjectionsRequest;
  requestOpts: CustomRequestOptions;
}

const ProjectionDetailViewBody: React.FC<ProjectionDetailViewBodyProps> = ({
  bondPrepayProjectionsRequest,
  requestOpts: { lastRun, isOldRun },
}: ProjectionDetailViewBodyProps) => {
  const { data } = useGetBondPrepayProjectionsSWR(bondPrepayProjectionsRequest, { lastRun, isOldRun });

  const [showReplineSummary, setShowReplineSummary] = React.useState(false);

  const toggleReplineSummary = React.useCallback(() => {
    setShowReplineSummary(!showReplineSummary);
  }, [showReplineSummary]);

  const headingElement = React.useMemo(() => {
    if (data?.prepay_projection_speeds_replines) {
      return (
        <Button variant="primary" size="sm" type="submit" onClick={toggleReplineSummary}>
          {`${!showReplineSummary ? "Show" : "Hide"} Repline Summary`}
        </Button>
      );
    }
  }, [data?.prepay_projection_speeds_replines, showReplineSummary, toggleReplineSummary]);

  return (
    <VStack alignItems="stretch" spacing={4} h="full">
      {!lastRun ? (
        <CACard flexGrow={1} display="flex" justifyContent="center" alignItems="center">
          <CAInfo status="info" description={"Click on ▷ to load Projections."} />
        </CACard>
      ) : showReplineSummary ? (
        <ProjectionDetailReplineSummaryTable data={data} headingElement={headingElement} />
      ) : (
        <ProjectionDetailGrid data={data} headingElement={headingElement} />
      )}
    </VStack>
  );
};

export default ProjectionDetailViewBody;
