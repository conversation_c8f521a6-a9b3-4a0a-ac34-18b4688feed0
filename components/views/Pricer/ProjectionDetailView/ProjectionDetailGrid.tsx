import * as React from "react";
import { Box } from "@chakra-ui/layout";
import { projectionDetailColumnData } from "@/utils/grid/ProjectionDetailColumnData";
import { projectionDetailNonDetailViewColumnData } from "@/utils/grid/ProjectionDetailNonDetailViewColumnData";
import {
  CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse,
  CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionDetail,
} from "@/utils/openapi";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerProjectionDetailPage } from "@/contexts/PageContexts/PricerProjectionDetailPageContext";
import { getRecordsWithBondReplineUniqueIdInfo } from "@/design-system/molecules/CAGrid/helpers";
import { getGridKeySubType } from "@/utils/helpers";
import CAGrid from "@/design-system/molecules/CAGrid";

type ProjectionDetailGridProps = {
  data?: CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse | null;
  headingElement?: React.JSX.Element;
};

const ProjectionDetailGrid: React.FC<ProjectionDetailGridProps> = ({
  data,
  headingElement,
}: ProjectionDetailGridProps) => {
  const {
    state: { security_info, isTimerRunning },
  } = usePricerModule();

  const {
    state: {
      projectionDetailPageSettings: { calculate_sections },
    },
  } = usePricerProjectionDetailPage();

  let showDetailModelVariables = false;
  if (calculate_sections.includes("detail")) {
    showDetailModelVariables = true;
  }

  const columnProps = showDetailModelVariables ? projectionDetailColumnData : projectionDetailNonDetailViewColumnData;

  const gridData = React.useMemo(() => {
    const { prepay_projection_details } = data || {};

    return getRecordsWithBondReplineUniqueIdInfo(prepay_projection_details);
  }, [data]);

  const gridKey = `projection-detail-${getGridKeySubType(security_info?.sub_type ?? "")}${
    showDetailModelVariables ? "-detailed-view" : ""
  }`;

  return (
    <Box h="full" minH="37.5rem">
      <CAGrid<CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionDetail>
        gridDataType="futuristic"
        hasRun={!isTimerRunning}
        headingElement={headingElement}
        gridProps={{
          columnDefs: columnProps,
          rowData: gridData,
          pivotMode: false,
          suppressAggFuncInHeader: false,
          suppressColumnVirtualisation: false,
          loading: isTimerRunning,
        }}
        gridType={gridKey}
        showExpand
        hideSearch
        cardProps={{
          title: " ",
          cardKey: "projection-detail-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
        }}
        stackStyles={{
          h: "calc(100vh - 12rem)",
        }}
      />
    </Box>
  );
};

export default React.memo(ProjectionDetailGrid);
