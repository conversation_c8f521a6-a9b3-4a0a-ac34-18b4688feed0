import { Box } from "@chakra-ui/layout";
import * as React from "react";
import { AgGridReact } from "ag-grid-react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { projectionDetailReplineSummaryColumnData } from "@/utils/grid/ProjectionDetailReplineSummaryColumnData";
import {
  CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse,
  CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionSummary,
} from "@/utils/openapi";
import { getRecordsWithBondReplineUniqueIdInfo } from "@/design-system/molecules/CAGrid/helpers";
import BondReplineUniqueIdInfoBreakoutColumnData from "@/utils/grid/BondReplineUniqueIdInfoBreakoutColumnData";
import { getAvailableKeys } from "@/utils/helpers";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";

type ProjectionDetailReplineSummaryTableProps = {
  data?: CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse | null;
  headingElement: React.JSX.Element | undefined;
};

type RateType = "cpr" | "crr" | "cdr";

const ProjectionDetailReplineSummaryTable: React.FC<ProjectionDetailReplineSummaryTableProps> = ({
  data,
  headingElement,
}: ProjectionDetailReplineSummaryTableProps) => {
  const [rateType, setRateType] = React.useState<RateType>("cpr");
  const gridRef = React.useRef<AgGridReact>(null);

  const {
    rowData,
  }: {
    rowData?: CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionSummary[];
  } = React.useMemo(() => {
    let rowData: CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionSummary[] | undefined = undefined;

    if (data?.prepay_projection_speeds_replines) {
      rowData = getRecordsWithBondReplineUniqueIdInfo(data.prepay_projection_speeds_replines);

      if (rowData && data.prepay_projection_speeds) {
        const summarySpeedsRow: CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionSummary = {
          ...data.prepay_projection_speeds,
        };
        rowData.unshift(summarySpeedsRow);
      }
    }

    return { rowData: !data ? undefined : rowData };
  }, [data]);

  const getColumns = (rateType: RateType) => {
    let replineSummaryColumns: string[] = BondReplineUniqueIdInfoBreakoutColumnData.map((c) => c.field as string);
    if (rateType === "cpr") {
      replineSummaryColumns = [
        ...replineSummaryColumns,
        "cpr_1m",
        "cpr_3m",
        "cpr_6m",
        "cpr_1y",
        "cpr_3y",
        "cpr_5y",
        "cpr_lt",
      ];
    } else if (rateType === "cdr") {
      replineSummaryColumns = [
        ...replineSummaryColumns,
        "cdr_1m",
        "cdr_3m",
        "cdr_6m",
        "cdr_1y",
        "cdr_3y",
        "cdr_5y",
        "cdr_lt",
      ];
    } else if (rateType === "crr") {
      replineSummaryColumns = [
        ...replineSummaryColumns,
        "crr_1m",
        "crr_3m",
        "crr_6m",
        "crr_1y",
        "crr_3y",
        "crr_5y",
        "crr_lt",
      ];
    }

    const availableKeys = getAvailableKeys(rowData);

    const columnData = projectionDetailReplineSummaryColumnData
      .filter((c) => {
        return c.field && availableKeys.has(c.field);
      })
      .map((c) => {
        if (c.field) {
          c.hide = !replineSummaryColumns.includes(c.field);
        }
        return c;
      });

    return columnData;
  };

  const onRateTypeChange = (rateType: RateType) => {
    setRateType(rateType);
    const columnData = getColumns(rateType);
    gridRef.current?.api?.setGridOption("columnDefs", columnData);
  };

  const headerActionButtons = React.useMemo(() => {
    return (
      <ToggleButtonGroup
        buttons={[
          { label: "CPR", value: "cpr" },
          { label: "CRR", value: "crr" },
          { label: "CDR", value: "cdr" },
        ]}
        selectedButton={rateType}
        onChange={onRateTypeChange}
      />
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rateType]);

  const gridType = `projetion-detail-repline-summary-${rateType}`;

  return (
    <Box minH="calc(100vh - 11rem)">
      <CAGrid<CA_Mastr_Api_v1_0_Models_BondPrepayProjections_PrepayProjectionSummary>
        ref={gridRef}
        hasRun={!!data?.status}
        headingElement={headingElement}
        headerActionButtons={headerActionButtons}
        gridProps={{
          columnDefs: getColumns(rateType),
          rowData,
        }}
        gridType={gridType}
        showExpand
        hideSearch
        enableUserGridViews={false}
        cardProps={{
          title: " ",
          cardKey: "projection-detail-repline-summary-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
        }}
      />
    </Box>
  );
};

export default ProjectionDetailReplineSummaryTable;
