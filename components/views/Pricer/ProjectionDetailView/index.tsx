import * as React from "react";
import { Box } from "@chakra-ui/react";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { getRequests } from "@/utils/helpers/pricer/projectionDetail/projectionDetail";
import { usePricerProjectionDetailPage } from "@/contexts/PageContexts/PricerProjectionDetailPageContext";
import { showWarningToast } from "@/design-system/theme/toast";
import { BondPrepayProjectionsStopWatchWrapper } from "../PricerStopWatchWrapper";
import PricerHeader from "../PricerHeader";
import { PricerModuleProps } from "..";
import { BondPrepayProjectionsProgressIndicatorWrapper } from "../PricerProgressIndicatorWrapper";
import { BondPrepayProjectionsStopRunningWrapper } from "../PricerStopRunningWrapper";
import ProjectionDetailViewBody from "./ProjectionDetailViewBody";
import ProjectionDetailInput, { ProjectionDetailInputHandler } from "./ProjectionDetailInput";

const PricerProjectionDetailView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, lastRun, isOldRun, api_start_time, api_end_time, ...projectionDetailPageState },
    action: { updateProjectionDetailPageSettings, run },
  } = usePricerProjectionDetailPage();
  const { pushWithoutRendering } = useQueryParameters();

  const projectionDetailInputRef = React.useRef<ProjectionDetailInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;

    projectionDetailInputRef.current?.handleSubmit((data) => {
      if (data.calculate_sections.includes("repline") && data.periods > 60) {
        showWarningToast("Warning", "Projection period is capped at 60 months when repline info is requested.");
      }
      updateProjectionDetailPageSettings(data);
      updatePricerUserSettingsCopy(userSettings);
      setIsTimerRunning(true);
      const run_id = run({ no_cache });
      pushWithoutRendering({ run_id });
    })();
  };

  const { bondPrepayProjectionsRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRunId,
    lastRun,
    isOldRun,
    bond_name,
    ...projectionDetailPageState,
  });

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <BondPrepayProjectionsStopWatchWrapper
            bondPrepayProjectionsRequest={bondPrepayProjectionsRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BondPrepayProjectionsProgressIndicatorWrapper
            bondPrepayProjectionsRequest={bondPrepayProjectionsRequest}
            requestOpts={requestOpts}
          />
        }
        stopRunning={
          <BondPrepayProjectionsStopRunningWrapper
            bondPrepayProjectionsRequest={bondPrepayProjectionsRequest}
            requestOpts={requestOpts}
          />
        }
      />
      <MainInputContentTemplate inputs={<ProjectionDetailInput ref={projectionDetailInputRef} />}>
        {bondPrepayProjectionsRequest && (
          <ProjectionDetailViewBody
            bondPrepayProjectionsRequest={bondPrepayProjectionsRequest}
            requestOpts={requestOpts}
          />
        )}
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerProjectionDetailView;
