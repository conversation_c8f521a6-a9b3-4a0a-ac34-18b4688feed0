import { Box, VStack } from "@chakra-ui/react";
import * as React from "react";
import { Controller, UseFormHandleSubmit, useForm } from "react-hook-form";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerProjectionDetailPage } from "@/contexts/PageContexts/PricerProjectionDetailPageContext";
import { projectionDetailPageSettingsType } from "@/contexts/PageContexts/PricerProjectionDetailPageContext/PricerProjectionDetailPageContextTypes";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import {
  getCDUDateKeyFromSubType,
  getDisplayValueByKey,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getFormattedYearMonth,
  getInfoMsg,
} from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "@/utils/openapi";
import useResetFormData from "@/hooks/useResetFormData";
import useOnPrepayModelChange from "@/hooks/useOnPrepayModelChange";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import { DateSettingsOverrides } from "@/components/helpers/DateSettingsOverrides/DateSettingsOverrides";
import ModelDialSelectorWrapper from "../shared/ModelDialSelectorWrapper";
import PrepayVectorSelector from "../shared/PrepayVectorSelector";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import ReplineDialsSwitch from "../shared/ReplineDialsSwitch";

export interface ProjectionDetailInputHandler {
  handleSubmit: UseFormHandleSubmit<projectionDetailPageSettingsType>;
}

const ProjectionDetailInput: React.ForwardRefRenderFunction<ProjectionDetailInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, security_info },
  } = usePricerModule();
  const {
    state: { projectionDetailPageSettingsCopy, projectionDetailPageSettings, isOldRun, lastRun },
  } = usePricerProjectionDetailPage();

  const {
    handleSubmit,
    reset,
    register,
    watch,
    setValue,
    control,
    formState: { touchedFields },
  } = useForm<projectionDetailPageSettingsType>();
  const [
    watch_prepay_model_type,
    watch_prepay_percentage,
    watch_prepay_model_scenario,
    watch_periods,
    watch_curve_shift,
  ] = watch(["prepay_model_type", "prepay_percentage", "prepay_model_scenario", "periods", "curve_shift"]);

  useResetFormData({ reset, formData: projectionDetailPageSettings });

  useOnPrepayModelChange({
    prepayModelType: watch_prepay_model_type,
    prepayPercentage: watch_prepay_percentage,
    setValue,
    isTouched: !!touchedFields.prepay_model_type,
    isOldRun,
  });

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  const toggleSwitch = React.useCallback(
    (name: "calculate_sections", values: string[], value: string) => {
      const newValues = [...(values || [])];
      if (newValues.includes(value)) {
        newValues.splice(values.indexOf(value), 1);
      } else {
        newValues.push(value);
      }
      setValue(name, newValues);
    },
    [setValue]
  );
  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);
  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="projection-detail-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
      overflow="visible"
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            value={getFormattedLocaleDate(userSettings.curve_date)}
            _key="curve_date"
            dateFormatter={getFormattedLocaleDate}
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
        </Box>
        <Box>
          <CASelectDropdown
            label={"Prepay Model"}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_model_type, projectionDetailPageSettingsCopy.prepay_model_type)}
            {...register("prepay_model_type", {
              required: true,
            })}
            options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_models)}
          />
          <CAInput
            type="number"
            label={"Multiplier (%)"}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_percentage, projectionDetailPageSettingsCopy.prepay_percentage)}
            {...register("prepay_percentage", {
              valueAsNumber: true,
              required: true,
            })}
          />
          <CASelectDropdown
            label={"Scenario"}
            width={"6rem"}
            info={getInfoMsg(
              getDisplayValueByKey(watch_prepay_model_scenario),
              getDisplayValueByKey(projectionDetailPageSettingsCopy.prepay_model_scenario)
            )}
            {...register("prepay_model_scenario")}
            options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_model_scenario)}
          />
          {watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR && (
            <PrepayVectorSelector lastRun={lastRun} />
          )}
          {(watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO ||
            watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR) && (
            <ModelDialSelectorWrapper lastRun={lastRun} />
          )}
          <ReplineDialsSwitch />
          <PricerValueDisplayWrapper
            name="Prepay Model Version"
            link
            value={userSettings.model_version ?? ""}
            _key="model_version"
          />
          <PricerValueDisplayWrapper
            name="Current Coupon Model"
            link
            value={
              metadata?.pricer_settings?.current_coupon_model?.find(
                (l) => l.value === userSettings.current_coupon_model
              )?.display_value ?? ""
            }
            _key="current_coupon_model"
          />
          <PricerValueDisplayWrapper name="Interest Rate Paths" value="1" _key="interestRatePaths" />
          <PricerValueDisplayWrapper
            name="Calibration"
            link
            value={
              metadata?.pricer_settings?.calibration?.find((l) => l.value === userSettings.calibration)
                ?.display_value ?? ""
            }
            _key="calibration"
          />
          <PricerValueDisplayWrapper
            name="Cello CDU Date"
            link
            value={getFormattedYearMonth(userSettings[cduDateKey])}
            _key={`cello_cdu_dates.${cduDateKey}`}
            dateFormatter={getFormattedYearMonth}
            drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
          />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
          <DateSettingsOverrides />
        </Box>
        <Box>
          <CAHeading as="h3" mb={4}>
            Projection
          </CAHeading>
          <CAInput
            type="number"
            label={"Periods"}
            inputType="positive-integer"
            width={"6rem"}
            info={getInfoMsg(watch_periods, projectionDetailPageSettingsCopy.periods)}
            {...register("periods", {
              valueAsNumber: true,
              required: true,
            })}
          />
          <CAInput
            type="number"
            inputType="digit"
            label={"Curve shift (bps)"}
            width={"6rem"}
            info={getInfoMsg(watch_curve_shift, projectionDetailPageSettingsCopy.curve_shift)}
            {...register("curve_shift", {
              valueAsNumber: true,
              required: true,
            })}
          />
        </Box>
        <Box>
          <CAHeading as="h3" mb={4}>
            Additional Parameters
          </CAHeading>
          <Controller
            name={"calculate_sections"}
            control={control}
            render={({ field: { name, value: values } }) => {
              const label = "Detail Model Variables";
              const value = "detail";
              return (
                <CASwitchInput
                  label={label}
                  name={name}
                  hideLabel={true}
                  value={value}
                  isChecked={(values || []).includes(value)}
                  onChange={() => toggleSwitch(name, values, value)}
                />
              );
            }}
          />
          <Controller
            name={"calculate_sections"}
            control={control}
            render={({ field: { name, value: values } }) => {
              const label = "Replines";
              const value = "repline";
              return (
                <CASwitchInput
                  label={label}
                  name={name}
                  hideLabel={true}
                  value={value}
                  isChecked={(values || []).includes(value)}
                  onChange={() => toggleSwitch(name, values, value)}
                />
              );
            }}
          />
        </Box>
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(ProjectionDetailInput);
