import { Box, VStack } from "@chakra-ui/react";
import * as React from "react";
import { UseFormHandleSubmit, useForm } from "react-hook-form";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerReplinePage } from "@/contexts/PageContexts/PricerReplinePageContext";
import { replinePageSettingsType } from "@/contexts/PageContexts/PricerReplinePageContext/PricerReplinePageContextTypes";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import {
  getCDUDateKeyFromSubType,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedYearMonth,
} from "@/utils/helpers";
import useResetFormData from "@/hooks/useResetFormData";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";

export interface ReplineInputHandler {
  handleSubmit: UseFormHandleSubmit<replinePageSettingsType>;
}

const ReplineInput: React.ForwardRefRenderFunction<ReplineInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, security_info },
  } = usePricerModule();
  const {
    state: { replinePageSettings },
  } = usePricerReplinePage();

  const { handleSubmit, reset } = useForm<replinePageSettingsType>();

  useResetFormData({ reset, formData: replinePageSettings });

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);
  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="repline-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
      overflow="visible"
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            value={getFormattedLocaleDate(userSettings.curve_date)}
            dateFormatter={getFormattedLocaleDate}
            _key={"curve_date"}
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
        </Box>
        <Box>
          <PricerValueDisplayWrapper
            name="Cello CDU Date"
            link
            value={getFormattedYearMonth(userSettings[cduDateKey])}
            _key={`cello_cdu_dates.${cduDateKey}`}
            dateFormatter={getFormattedYearMonth}
            drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
          />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
        </Box>
        <Box>
          <CAHeading as="h3" mb={4}>
            Additional Parameters
          </CAHeading>
          <PricerValueDisplayWrapper
            name="Prepay Model Version"
            value={userSettings.model_version ?? ""}
            link
            _key="model_version"
          />
        </Box>
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(ReplineInput);
