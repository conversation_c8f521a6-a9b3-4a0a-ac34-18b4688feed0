import { Box } from "@chakra-ui/react";
import { ICellRendererParams } from "ag-grid-community";
import ReplineGridModelDialSelectorWrapper from "../ReplineGridModelDialSelectorWrapper";

export const ModelDialSelectorCellRenderer = ({ value }: ICellRendererParams) => {
  if (!value) {
    return null;
  }

  return (
    <Box position="relative" top="-5.5px" height="32px">
      <ReplineGridModelDialSelectorWrapper unique_id={value /* props?.data?.unique_id */} />
    </Box>
  );
};
