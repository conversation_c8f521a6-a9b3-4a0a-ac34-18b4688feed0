import { Box, Icon, Text } from "@chakra-ui/react";
import { ICellRendererParams } from "ag-grid-community";
import { HiPencil } from "react-icons/hi";

export const EditableCellRendrer = (params: ICellRendererParams) => {
  const value = params.valueFormatted || (typeof params.value === "object" ? params.value?.value : params.value);

  const align = typeof params.value == "number" ? "flex-end" : "flex-start";

  const startEditing = () => {
    params.api.setFocusedCell(params.node.rowIndex ?? 0, params.column?.getColId() ?? "", undefined);
    params.api.startEditingCell({
      rowIndex: params.node.rowIndex ?? 0,
      colKey: params.column?.getColId() ?? "",
    });
  };

  // Prevent editing when grouping, pinning, aggregating, and in pivot mode for now
  return !params.node?.group && !params.node?.rowPinned && !params.api.isPivotMode() && params?.colDef?.editable ? (
    <Box position="relative" bottom="0" display="flex" justifyContent={align} alignItems="center" h="2rem" role="group">
      <Box display="inline-flex" onClick={startEditing}>
        <Icon
          as={HiPencil}
          color={"currentColor"}
          fontSize="16"
          mr="1"
          opacity={0.2}
          _groupHover={{ opacity: 1, cursor: "pointer" }}
        />
      </Box>
      <Text>{value}</Text>
    </Box>
  ) : (
    <Box position="relative" bottom="0" display="flex" justifyContent={align} alignItems="center" h="2rem">
      <Text>{value}</Text>
    </Box>
  );
};
