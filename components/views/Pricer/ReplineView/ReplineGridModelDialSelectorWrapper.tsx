import * as React from "react";
import { HStack } from "@chakra-ui/react";
import { CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial } from "@/utils/openapi";
import { useGetUserModelDialsSWR } from "@/utils/swr-hooks";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { safeLocalStorage } from "@/utils/local-storage";
import { usePricerReplinePage } from "@/contexts/PageContexts/PricerReplinePageContext";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import ModelDialSelector from "../shared/ModelDialSelector";

type ReplineGridModelDialSelectorWrapperProps = {
  unique_id?: string;
};

const ReplineGridModelDialSelectorWrapper: React.FC<ReplineGridModelDialSelectorWrapperProps> = ({
  unique_id,
}: ReplineGridModelDialSelectorWrapperProps) => {
  const {
    state: { bond_name, userSettings },
  } = usePricerModule();
  const {
    state: { savedModelDials },
    action: { updateSavedModelDials },
  } = usePricerReplinePage();

  const REPLINE_MODEL_DIALS_KEY: `ca.PRICER_REPLINE_MODEL_DIALS_${string}_${string}_${string}` = `${LOCAL_STORAGE_KEY.PRICER_REPLINE_MODEL_DIALS}_${bond_name}_${userSettings.model_version}_${userSettings.repline_level}`;

  const savedModelDialsList = safeLocalStorage.getItem(REPLINE_MODEL_DIALS_KEY);
  const modelDialList: { unique_id?: string; user_model_dial_id?: number }[] = savedModelDialsList
    ? JSON.parse(savedModelDialsList)
    : [];

  const [selectedModelDialId, setSelectedModelDialId] = React.useState(
    modelDialList.find((m) => m.unique_id === unique_id)?.user_model_dial_id
  );

  React.useEffect(() => {
    const savedModelDialId = savedModelDials.find((m) => m.unique_id === unique_id)?.user_model_dial_id;
    setSelectedModelDialId(savedModelDialId);
  }, [savedModelDials, unique_id]);

  const { data, mutate } = useGetUserModelDialsSWR();
  const userModelDials = data?.user_model_dials;

  const getModelDialById = React.useCallback(
    (id?: number) => {
      if (userModelDials && id) {
        for (let i = 0; i < userModelDials?.length; i++) {
          if (id === userModelDials[i].user_model_dial_id) {
            return userModelDials[i];
          }
        }
      }
      return undefined;
    },
    [userModelDials]
  );

  const [selectedDealDial, setSelectedDealDial] =
    React.useState<CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial>();

  React.useEffect(() => {
    if (selectedModelDialId) {
      const modelDial = getModelDialById(selectedModelDialId);
      setSelectedDealDial(modelDial);
    } else {
      setSelectedDealDial(undefined);
    }
    // getModelDialById was getting redefined because userModelDials gets updated on page focus
    // we don't want to call setSelectedDealDial when that happens
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedModelDialId]);

  const onUserModelChange = (updatedUserModelDial?: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial) => {
    setSelectedModelDialId(updatedUserModelDial?.user_model_dial_id);

    const modelDialList: { unique_id?: string; user_model_dial_id?: number }[] = savedModelDials;

    // Remove unique_id from modelDialList
    const existingIndex = modelDialList.findIndex((m) => m.unique_id === unique_id);
    if (existingIndex > -1) {
      modelDialList.splice(existingIndex, 1);
    }

    // Add back
    if (updatedUserModelDial) {
      modelDialList.push({ unique_id, user_model_dial_id: updatedUserModelDial.user_model_dial_id });
    }

    updateSavedModelDials(modelDialList);
    safeLocalStorage.setItem(REPLINE_MODEL_DIALS_KEY, JSON.stringify(modelDialList));
  };

  return (
    <HStack>
      <ModelDialSelector
        onUserModelDialChange={onUserModelChange}
        isSearchDisabled={!data}
        selectedDealDial={selectedDealDial}
        userModelDials={userModelDials}
        mutate={mutate}
        fullWidth
        hasAdHoc={false}
      />
    </HStack>
  );
};

export default ReplineGridModelDialSelectorWrapper;
