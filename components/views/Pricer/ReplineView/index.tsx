import * as React from "react";
import { Box } from "@chakra-ui/react";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerReplinePage } from "@/contexts/PageContexts/PricerReplinePageContext";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { getRequests } from "@/utils/helpers/pricer/repline";
import { BondIndicativesStopWatchWrapper } from "../PricerStopWatchWrapper";
import PricerHeader from "../PricerHeader";
import { PricerModuleProps } from "..";
import { BondIndicativesProgressIndicatorWrapper } from "../PricerProgressIndicatorWrapper";
import { BondIndicativesStopRunningWrapper } from "../PricerStopRunningWrapper";
import ReplineGrid from "./ReplineGrid";
import ReplineInput, { ReplineInputHandler } from "./ReplineInput";

const PricerReplineView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, lastRun, isOldRun, api_start_time, api_end_time, ...replineState },
    action: { updateReplinePageSettings, run, updateCheckedModelDials },
  } = usePricerReplinePage();
  const { pushWithoutRendering } = useQueryParameters();

  const replineInputRef = React.useRef<ReplineInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    replineInputRef.current?.handleSubmit((data) => {
      updateReplinePageSettings(data);
      updatePricerUserSettingsCopy(userSettings);
      updateCheckedModelDials([]);
      setIsTimerRunning(true);
      const run_id = run({ no_cache });
      pushWithoutRendering({ run_id });
    })();
  };

  const { bondIndicativesRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRunId,
    lastRun,
    isOldRun,
    bond_name,
    ...replineState,
  });

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <BondIndicativesStopWatchWrapper
            bondIndicativesRequest={bondIndicativesRequest}
            opts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BondIndicativesProgressIndicatorWrapper bondIndicativesRequest={bondIndicativesRequest} opts={requestOpts} />
        }
        stopRunning={
          <BondIndicativesStopRunningWrapper bondIndicativesRequest={bondIndicativesRequest} opts={requestOpts} />
        }
      />
      <MainInputContentTemplate inputs={<ReplineInput ref={replineInputRef} />}>
        <ReplineGrid bondIndicativesRequest={bondIndicativesRequest} opts={requestOpts} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerReplineView;
