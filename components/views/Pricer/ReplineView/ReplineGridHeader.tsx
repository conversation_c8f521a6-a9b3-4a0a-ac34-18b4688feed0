import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Text } from "@chakra-ui/react";
import React from "react";
import { FaRedo, FaUndo } from "react-icons/fa";
import { IoCheckmarkCircleSharp, IoRemoveCircleSharp } from "react-icons/io5";
import { AgGridReact } from "ag-grid-react";
import { LOCAL_STORAGE_KEY_TYPE } from "@/constants/storage-keys";
import { usePricerReplinePage } from "@/contexts/PageContexts/PricerReplinePageContext";
import { safeLocalStorage } from "@/utils/local-storage";
import { CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial } from "@/utils/openapi";
import { useGetUserModelDialsSWR } from "@/utils/swr-hooks";
import ModelDialSelector from "../shared/ModelDialSelector";

interface ReplineGridheaderStyles {
  gridRef: React.RefObject<AgGridReact>;
  replineModelDialsKey: LOCAL_STORAGE_KEY_TYPE;
  undoSize: number;
  redoSize: number;
}

export const ReplineGridHeader = ({ gridRef, replineModelDialsKey, undoSize, redoSize }: ReplineGridheaderStyles) => {
  const { data, mutate: mutateModelDials } = useGetUserModelDialsSWR();

  const undoChanged = React.useCallback(() => {
    const gridApi = gridRef.current?.api;
    gridApi?.undoCellEditing();
  }, [gridRef]);

  const redoChanged = React.useCallback(() => {
    const gridApi = gridRef.current?.api;
    gridApi?.redoCellEditing();
  }, [gridRef]);

  const {
    state: { savedModelDials, checkedModelDials },
    action: { updateSavedModelDials, updateCheckedModelDials },
  } = usePricerReplinePage();
  const showApplyModelDial = checkedModelDials.length ? true : false;

  const clearAllDials = React.useCallback(() => {
    safeLocalStorage.removeItem(replineModelDialsKey);
    updateSavedModelDials([]);
    updateCheckedModelDials([]);
    const gridApi = gridRef.current?.api;
    gridApi?.deselectAll();
  }, [gridRef, updateCheckedModelDials, updateSavedModelDials, replineModelDialsKey]);

  const onUserModelChange = React.useCallback(
    (updatedUserModelDial?: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial) => {
      const modelDialsList = [...savedModelDials];

      checkedModelDials.forEach((unique_id) => {
        let isModelDialPresent = false;
        modelDialsList.forEach((m) => {
          if (m.unique_id === unique_id) {
            m.user_model_dial_id = updatedUserModelDial?.user_model_dial_id;
            isModelDialPresent = true;
          }
        });
        if (!isModelDialPresent) {
          modelDialsList.push({ unique_id, user_model_dial_id: updatedUserModelDial?.user_model_dial_id });
        }
      });
      updateSavedModelDials([...modelDialsList]);
      safeLocalStorage.setItem(replineModelDialsKey, JSON.stringify(modelDialsList));
    },
    [checkedModelDials, savedModelDials, updateSavedModelDials, replineModelDialsKey]
  );

  return (
    <Flex
      flexDirection={{ base: "column", sm: "row" }}
      w="full"
      justifyContent={{ base: "flex-start", "2xl": "flex-end" }}
      alignItems={{ base: "flex-start", "2xl": "flex-end" }}
      pr={{ base: 0, lg: "0.8rem" }}
    >
      <HStack mr={{ base: "0", sm: "1.6rem" }}>
        <Button
          height={8}
          isDisabled={undoSize === 0}
          leftIcon={<Icon as={FaUndo} fontSize="14" />}
          variant="iconButton"
          onClick={undoChanged}
        >
          <Text color="inherit">Undo</Text>
        </Button>
        <Button
          height={8}
          isDisabled={redoSize === 0}
          leftIcon={<Icon as={FaRedo} fontSize="14" />}
          variant="iconButton"
          onClick={redoChanged}
        >
          <Text color="inherit">Redo</Text>
        </Button>
      </HStack>
      <HStack pt={{ base: "2", sm: "0" }}>
        <ModelDialSelector
          isDisabled={!showApplyModelDial}
          triggerElement={
            <Button
              height={8}
              isDisabled={!showApplyModelDial}
              leftIcon={<Icon as={IoCheckmarkCircleSharp} fontSize="20" />}
              variant="iconButton"
            >
              <Text color="inherit">Apply Model Dial</Text>
            </Button>
          }
          onUserModelDialChange={onUserModelChange}
          isSearchDisabled={!data}
          userModelDials={data?.user_model_dials}
          fullWidth
          mutate={mutateModelDials}
          hasAdHoc={false}
        />
        <Button
          height={8}
          isDisabled={!savedModelDials.length}
          leftIcon={<Icon as={IoRemoveCircleSharp} fontSize="20" />}
          variant="iconButton"
          onClick={clearAllDials}
        >
          <Text color="inherit">Clear All</Text>
        </Button>
      </HStack>
    </Flex>
  );
};
