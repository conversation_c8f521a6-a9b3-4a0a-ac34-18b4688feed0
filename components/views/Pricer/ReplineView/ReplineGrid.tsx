import * as React from "react";
import { Box } from "@chakra-ui/react";
import { CellValueChangedEvent, RowSelectionOptions, SelectionChangedEvent } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import { CustomRequestOptions, useGetBondIndicativesSWRForReplinePage } from "@/utils/swr-hooks";
import { BondIndicativesRequest } from "@/types/swr";
import CAGrid from "@/design-system/molecules/CAGrid";
import { getRecordsWithBondReplineUniqueIdInfo, wavgFunction } from "@/design-system/molecules/CAGrid/helpers";
import replineColumnData from "@/utils/grid/ReplineColumnData";
import { getGridKeySubType } from "@/utils/helpers";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { safeLocalStorage } from "@/utils/local-storage";
import { CA_Mastr_Api_v1_0_Models_BondRepline, CA_Mastr_Models_v1_0_Models_BondReplineInOut } from "@/utils/openapi";
import { usePricerReplinePage } from "@/contexts/PageContexts/PricerReplinePageContext";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { ModelDialSelectorCellRenderer } from "./cellRenderers/ModelDialSelectorCellRenderer";
import { EditableCellRendrer } from "./cellRenderers/EditableCellRenderer";
import { ReplineGridHeader } from "./ReplineGridHeader";

const rowSelection: RowSelectionOptions | "single" | "multiple" = {
  mode: "multiRow",
  checkboxes: true,
  groupSelects: "descendants",
  copySelectedRows: true,
};
const aggFuncs = {
  wavg: wavgFunction,
};
const components = {
  modelDialSelectorCellRenderer: ModelDialSelectorCellRenderer,
  editableCellRendrer: EditableCellRendrer,
};
const ReplinesGrid: React.FC<{
  bondIndicativesRequest: BondIndicativesRequest;
  opts: CustomRequestOptions;
}> = ({ bondIndicativesRequest, opts: { isOldRun, lastRun } }) => {
  const {
    state: { bond_name, security_info, userSettings, isTimerRunning },
  } = usePricerModule();
  const {
    action: { updateSavedModelDials, updateCheckedModelDials },
  } = usePricerReplinePage();
  const { data: responseData } = useGetBondIndicativesSWRForReplinePage(bondIndicativesRequest, {
    lastRun,
    isOldRun,
  });
  const { bond_replines } = responseData || {};
  const [updatedCellList, setUpdatedCellList] = React.useState<CA_Mastr_Models_v1_0_Models_BondReplineInOut[]>([]);
  const [undoSize, setUndoSize] = React.useState<number>(0);
  const [redoSize, setRedoSize] = React.useState<number>(0);

  const gridRef = React.createRef<AgGridReact>();

  const REPLINE_MODEL_DIALS_KEY: `ca.PRICER_REPLINE_MODEL_DIALS_${string}_${string}_${string}` = `${LOCAL_STORAGE_KEY.PRICER_REPLINE_MODEL_DIALS}_${bond_name}_${userSettings.model_version}_${userSettings.repline_level}`;
  const REPLINE_EDITED_CELL_KEY: `ca.PRICER_REPLINE_EDITED_CELL_${string}_${string}_${string}` = `${LOCAL_STORAGE_KEY.PRICER_REPLINE_EDITED_CELL}_${bond_name}_${userSettings.model_version}_${userSettings.repline_level}`;

  const updateCellList = React.useCallback(() => {
    const updatedCellList = safeLocalStorage.getItem(REPLINE_EDITED_CELL_KEY);
    if (updatedCellList) {
      setUpdatedCellList(JSON.parse(updatedCellList));
    }
  }, [REPLINE_EDITED_CELL_KEY]);

  React.useEffect(() => {
    const savedModelDialsList = safeLocalStorage.getItem(REPLINE_MODEL_DIALS_KEY);
    if (savedModelDialsList) {
      updateSavedModelDials(JSON.parse(savedModelDialsList));
    }
    updateCellList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [REPLINE_MODEL_DIALS_KEY]);

  const gridData = React.useMemo(() => {
    return getRecordsWithBondReplineUniqueIdInfo(bond_replines)?.map((item, index) => {
      const indexer = updatedCellList.findIndex(
        (m) => m.unique_id && m.unique_id === (item as CA_Mastr_Api_v1_0_Models_BondRepline).unique_id
      );
      if (index > -1 && updatedCellList[indexer]) {
        return {
          ...item,
          ...updatedCellList[indexer],
          editedKeys: Object.keys(updatedCellList[indexer]),
        };
      }

      return item;
    });
  }, [bond_replines, updatedCellList]);

  React.useEffect(() => {
    const checkEditStackInterval = window.setInterval(() => {
      const gridApi = gridRef.current?.api;
      if (gridApi) {
        const currentUndoSize = gridApi?.getCurrentUndoSize();
        const currentRedoSize = gridApi?.getCurrentRedoSize();
        if (currentUndoSize === 0 && currentUndoSize !== undoSize) setUndoSize(0);
        if (currentRedoSize === 0 && currentRedoSize !== redoSize) setRedoSize(0);
      }
    }, 1000);

    return () => clearInterval(checkEditStackInterval);
  }, [gridRef, undoSize, redoSize]);

  const onResetColumnState = () => {
    safeLocalStorage.removeItem(REPLINE_MODEL_DIALS_KEY);
    safeLocalStorage.removeItem(REPLINE_EDITED_CELL_KEY);
    setUpdatedCellList([]);
    setRedoSize(0);
    setUndoSize(0);
    updateSavedModelDials([]);
    updateCheckedModelDials([]);
  };

  const updateCellValue = React.useCallback(
    (params: CellValueChangedEvent, colId: keyof CA_Mastr_Models_v1_0_Models_BondReplineInOut) => {
      const rowData: CA_Mastr_Models_v1_0_Models_BondReplineInOut = params.node.data;
      const uniqueId: string = rowData.unique_id || "";
      const cellList: CA_Mastr_Models_v1_0_Models_BondReplineInOut[] = updatedCellList;
      const index = cellList?.findIndex((m) => m.unique_id === uniqueId);
      let colIdOriginalVal: string | number | boolean | undefined | null = undefined;
      bond_replines?.map((m) => {
        if (m.unique_id === uniqueId) {
          colIdOriginalVal = m[colId];
        }
      });
      const isMatched: boolean = colIdOriginalVal === rowData[colId];
      params.api.flashCells({
        rowNodes: [params.node],
        flashDuration: isMatched ? 0 : 2000000,
        columns: [colId],
      });
      if (index > -1) {
        if (isMatched) {
          delete cellList[index][colId];
          if (Object.keys(cellList[index]).length == 1) {
            cellList.splice(index, 1);
          }
        } else {
          cellList[index][colId] = rowData[colId] as undefined;
        }
      } else {
        const object: Partial<CA_Mastr_Models_v1_0_Models_BondReplineInOut> = {};
        object["unique_id"] = rowData.unique_id as string;
        object[colId] = rowData[colId] as undefined;
        cellList.push(object);
      }
      if (cellList.length) {
        setUpdatedCellList(cellList);
        safeLocalStorage.setItem(REPLINE_EDITED_CELL_KEY, JSON.stringify(cellList));
      } else {
        safeLocalStorage.removeItem(REPLINE_EDITED_CELL_KEY);
      }
    },
    [updatedCellList, bond_replines, REPLINE_EDITED_CELL_KEY]
  );

  const onSelectionChanged = (e: SelectionChangedEvent) => {
    const selectedNodes = e.api.getSelectedNodes();

    updateCheckedModelDials(selectedNodes.map((n) => n.data.unique_id));
  };

  const onCellValueChanged = React.useCallback(
    (params: CellValueChangedEvent) => {
      const undoSize = params.api.getCurrentUndoSize();
      const redoSize = params.api.getCurrentRedoSize();
      setRedoSize(redoSize);
      setUndoSize(undoSize);
      const colId = params.column.getId() as keyof CA_Mastr_Models_v1_0_Models_BondReplineInOut;
      let newValue = params.newValue;
      if (typeof params.oldValue === "number") {
        newValue = !isNaN(Number(newValue))
          ? Number(newValue)
          : params.newValue === undefined
          ? null
          : Number(params.oldValue);
      }

      params.node.setDataValue(colId, newValue);
      updateCellValue(params, colId);
    },
    [updateCellValue]
  );

  return (
    <Box h="600px" minH="calc(100vh - 12rem)">
      <CAGrid
        ref={gridRef}
        gridType={`repline-${getGridKeySubType(security_info?.sub_type ?? "")}`}
        gridDataType="historical"
        hasRun={!!lastRun}
        gridProps={{
          onSelectionChanged: (e) => onSelectionChanged(e),
          columnDefs: replineColumnData,
          rowData: gridData,
          pivotMode: false,
          components,
          undoRedoCellEditing: true,
          onCellValueChanged: onCellValueChanged,
          onColumnRowGroupChanged: updateCellList,
          aggFuncs,
          groupDefaultExpanded: -1,
          groupTotalRow: undefined,
          stopEditingWhenCellsLoseFocus: true,
          enableCharts: false,
          rowSelection,
          loading: isTimerRunning,
        }}
        cardProps={{
          title: " ",
          cardKey: "repline-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
        }}
        stackStyles={{
          h: "calc(100vh - 15rem)",
        }}
        onResetColumnState={onResetColumnState}
        initialMessage={`Click on ▷ to load Repline.`}
        showExpand
        headingElement={
          <ReplineGridHeader
            gridRef={gridRef}
            replineModelDialsKey={REPLINE_MODEL_DIALS_KEY}
            undoSize={undoSize}
            redoSize={redoSize}
          />
        }
      />
    </Box>
  );
};

export default ReplinesGrid;
