import React from "react";
import { Skeleton, useDisclosure } from "@chakra-ui/react";
import { AiOutlineInsertRowBelow } from "react-icons/ai";
import { AgGridReact } from "ag-grid-react";
import { AgAxisCaptionFormatterParams, AgChartThemeOverrides } from "ag-charts-types";
import {
  CA_Mastr_Api_v1_0_Models_Batch_BatchRequest,
  CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView,
} from "@/utils/openapi";
import { CustomRequestOptions, useGetUserVectorsSWR } from "@/utils/swr-hooks";
import { getColumnDef, getRowColumnDef } from "@/utils/grid/matrixColumnData";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import { MatrixPrepayModelType } from "@/contexts/PageContexts/PricerMatrixPageContext/PricerMatrixPageContextTypes";
import CAGrid from "@/design-system/molecules/CAGrid";
import { inMemoryObjectStorage } from "@/utils/local-storage";
import { IN_MEMORY_OBJECT_STORAGE_KEY } from "@/constants/storage-keys";
import CACard from "@/design-system/molecules/CACard";

import {
  PRICING_LEVEL_DECIMAL_PLACES,
  PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES,
  useMatrixRequest,
} from "./hooks/useMatrixRequest";
import {
  MatrixByValuesStepFormType,
  MatrixDisplayDropdown,
  MatrixGridHeaderInput,
  MatrixGridRowInput,
} from "./components";
import {
  MatrixGridDataType,
  getColumnValueType,
  getDefaultBottomPinnedRowsSelection,
  getPinnedBottomRowData,
  tabToNextCell,
  transformDataForGrid,
} from "./utils";
import { MatrixMenu } from "./components/MatrixMenu";
import { ColumnsType, RowsType, useDynamicRowsColumns } from "./hooks/useDynamicRowsColumns";

export interface MatrixGridContextType {
  fillMatrixByValues: (data: MatrixByValuesStepFormType) => void;
}

export interface MatrixGridProps {
  bondPricingRequest: CA_Mastr_Api_v1_0_Models_Batch_BatchRequest;
  requestOpts: CustomRequestOptions;
}

export interface MatrixFieldValues {
  columns: ColumnsType;
  rows: RowsType;
}

interface MatrixGridViewState {
  customState: {
    columns: ColumnsType;
    displayProperties: Record<string, string[]>;
  };
}

const chartThemeOverrides: AgChartThemeOverrides = {
  common: {
    padding: {
      top: 16,
      right: 35,
      bottom: 16,
      left: 16,
    },
    axes: {
      number: {
        title: {
          enabled: true,
          formatter: (params: AgAxisCaptionFormatterParams) => {
            return params.boundSeries.map((s) => s.name).join(" / ");
          },
          fontWeight: "bold",
        },
      },
    },
  },
};

const voidFunc = () => null;

const components = {
  agColumnHeader: MatrixGridHeaderInput,
  agRowInput: MatrixGridRowInput,
};

const MatrixGrid: React.FC<MatrixGridProps> = ({ bondPricingRequest, requestOpts }) => {
  /* CONTEXT */
  const {
    state: {
      matrixPageSettings: { matrix_display_value, pricing_type, matrix_by },
      matrixPageSettingsCopy: { pricing_type: pricing_type_copy },
    },
    action: { resetPrepayModels, setMatrixByValues },
  } = usePricerMatrixPage();
  const { data: userVectors, mutate } = useGetUserVectorsSWR();
  const { isOpen, onOpen, onClose } = useDisclosure();

  /* REFS */
  const matrixGridRef = React.useRef<AgGridReact>(null);

  /* REQUEST */
  const { data, isLoading } = useMatrixRequest({ bondPricingRequest, requestOpts });

  /* ROWS AND COLUMNS */
  const { rows, columns, getRowInputProps, getColumnInputProps } = useDynamicRowsColumns();

  /* FOOTER ROWS */
  const [footerRowsSelection, setFooterRowsSelection] = React.useState<Record<string, string[]>>(
    getDefaultBottomPinnedRowsSelection
  );

  const gridData = React.useMemo(
    () =>
      transformDataForGrid({
        displayValue: matrix_display_value,
        groupByKey: pricing_type_copy as "price" | "yield" | "oas",
        rows,
        data: pricing_type !== pricing_type_copy ? [] : data,
        matrixBy: matrix_by,
      }),
    [data, matrix_display_value, pricing_type, pricing_type_copy, rows, matrix_by]
  );

  const onUpdateGridView = (selectedView?: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView) => {
    if (!selectedView?.grid_view_state) return;

    const persistedState = inMemoryObjectStorage.getItem(
      `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${selectedView?.user_grid_view_id}`
    );
    const parsedGridState: MatrixGridViewState = persistedState ?? JSON.parse(selectedView.grid_view_state);

    if (parsedGridState.customState?.displayProperties) {
      setFooterRowsSelection(parsedGridState.customState.displayProperties);
    }

    if (parsedGridState.customState?.columns) {
      const prepayModels = Object.values(parsedGridState.customState.columns) as MatrixPrepayModelType[];
      resetPrepayModels(prepayModels);
    }
  };

  const fillMatrixByValuesHandler = React.useCallback(
    ({ numberOfRows, price, stepSize }: MatrixByValuesStepFormType) => {
      const centeredIndex = Math.floor(numberOfRows / 2);
      const matrixByValues: (number | string | undefined)[] = Array(numberOfRows)
        .fill(undefined)
        .map((_, index) => {
          if (index < centeredIndex) {
            const difference = centeredIndex - index;
            return price - difference * stepSize;
          }
          if (index > centeredIndex) {
            const difference = index - centeredIndex;
            return price + difference * stepSize;
          }
          return price;
        })
        .map((p) => {
          if (matrix_by === "index") {
            return p.toFixed(PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES);
          }
          return p.toFixed(PRICING_LEVEL_DECIMAL_PLACES);
        });

      setMatrixByValues([...matrixByValues, ""].map(String));
    },
    [setMatrixByValues, matrix_by]
  );

  const matrixGridContext: MatrixGridContextType = React.useMemo(
    () => ({
      fillMatrixByValues: fillMatrixByValuesHandler,
    }),
    [fillMatrixByValuesHandler]
  );

  const pinnedBottomRowData = React.useMemo(() => {
    // Avoid memoization of pinned bottom row data
    if (isLoading) return [];
    return getPinnedBottomRowData({
      selectedRows: footerRowsSelection,
      data: gridData,
    });
  }, [footerRowsSelection, isLoading, gridData]);

  const columnDefs = React.useMemo(
    () => [
      getRowColumnDef(getRowInputProps(), matrix_by === "index" ? "index_rate" : "price"),
      ...Object.values(columns).map((el, i) =>
        getColumnDef(!data?.length ? "text" : getColumnValueType(matrix_display_value), {
          key: `${el.value}-${i}`,
          index: i,
          field: el.value,
          defaultValue: el.value,
          userVectors: userVectors?.user_vectors ?? [],
          mutateVectors: mutate,
          ...getColumnInputProps(),
        })
      ),
    ],
    [
      columns,
      data?.length,
      getColumnInputProps,
      getRowInputProps,
      matrix_display_value,
      mutate,
      userVectors?.user_vectors,
      matrix_by,
    ]
  );

  const handleCreateNewView = () => {
    resetPrepayModels();
    setFooterRowsSelection(getDefaultBottomPinnedRowsSelection);
  };

  if (isLoading) {
    return (
      <CACard pt="6" px="2" h="fit-content">
        <Skeleton height="30px" w="full" p="0" />
      </CACard>
    );
  }

  return (
    <>
      <CAGrid<Record<string, number | MatrixGridDataType | null | undefined> | undefined>
        ref={matrixGridRef}
        hasRun={true}
        hideSearch
        showExpand
        headerActionButtons={<MatrixDisplayDropdown />}
        defaultPinRows={false}
        gridType="matrix"
        onUpdateGridView={onUpdateGridView}
        onDeleteGridView={resetPrepayModels}
        onCreateNewView={handleCreateNewView}
        customState={{
          columns,
          displayProperties: footerRowsSelection,
        }}
        menuItems={[
          {
            label: "Display Properties",
            icon: AiOutlineInsertRowBelow,
            onClick: onOpen,
          },
        ]}
        gridProps={{
          chartThemeOverrides,
          context: matrixGridContext,
          domLayout: "autoHeight",
          className: "Matrix",
          columnDefs: columnDefs,
          rowData: gridData,
          components,
          pinnedBottomRowData,
          tabToNextCell,
          navigateToNextCell: voidFunc,
          navigateToNextHeader: voidFunc,
          cellSelection: !!data.length && !isLoading,
          enableCharts: !!data.length && !isLoading,
          loading: isLoading,
        }}
        cardProps={{
          title: " ",
          cardKey: "matrix-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
          marginTop: "2px",
        }}
      />
      <MatrixMenu
        isOpen={isOpen}
        onClose={onClose}
        defaultSelections={footerRowsSelection}
        onModalClose={setFooterRowsSelection}
      />
    </>
  );
};

export default MatrixGrid;
