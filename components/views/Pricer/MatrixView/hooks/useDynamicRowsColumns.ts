import React from "react";
import { <PERSON>rid<PERSON><PERSON> } from "ag-grid-community";
import { getPrepayDisplayValue } from "@/components/views/Pricer/MatrixView/utils";
import { MatrixPrepayModelType } from "@/contexts/PageContexts/PricerMatrixPageContext/PricerMatrixPageContextTypes";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "@/utils/openapi";
import { DEC_REGEX, POSITIVE_DEC_REGEX, TICK_REGEX, tickToDecimal } from "@/utils/helpers";
import { MatrixGridDataType, decodeValue } from "./../utils/index";
import { PRICING_LEVEL_DECIMAL_PLACES, PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES } from "./useMatrixRequest";

export type RowsType = Record<number, string | undefined>;
export type ColumnsType = Record<number, MatrixPrepayModelType>;

export interface GetRowInputProps {
  rowsLength: number;
  rows: Record<number, string | null | undefined>;
  addRow: (rowIndex: number) => (e: React.FocusEvent<HTMLInputElement, Element>) => void;
  removeRow: (api: GridApi | undefined, rowIndex: number) => void;
}

export interface GetColumnInputProps {
  columnsLength: number;
  removeColumn: (colIndex: number) => void;
  addColumn: (colIndex: number) => (value: string, isVector?: boolean) => void;
}

export interface UseDynamicRowsColumnsReturnType {
  rows: RowsType;
  columns: ColumnsType;
  getRowInputProps: () => GetRowInputProps;
  getColumnInputProps: () => GetColumnInputProps;
}

export const useDynamicRowsColumns = (): UseDynamicRowsColumnsReturnType => {
  const {
    state: {
      matrix_by_values,
      matrix_by_values_copy,
      prepay_models,
      prepay_models_copy,
      matrixPageSettings: { prepay_model_type, matrix_by },
    },
    action: { setPrepayModels, setMatrixByValues, setPrepayModelsCopy, setMatrixByValuesCopy },
  } = usePricerMatrixPage();

  const addRow = (rowIndex: number) => (e: React.FocusEvent<HTMLInputElement, Element>) => {
    const rowsLength = Object.keys(matrix_by_values).length;
    const isLastItem = rowsLength - 1 === rowIndex;
    const val = e.currentTarget.value;
    const matchesDecRegex = e.currentTarget.value?.match(DEC_REGEX);
    const matchesNegativeRegex = !e.currentTarget.value?.match(POSITIVE_DEC_REGEX);
    const matchesTickRegex = e.currentTarget.value?.match(TICK_REGEX);

    const value = matchesDecRegex ? Number(e.currentTarget.value) : tickToDecimal(e.currentTarget.value);

    const modifiedRowValue =
      isNaN(value) || (matchesNegativeRegex && !matchesTickRegex)
        ? ""
        : matrix_by === "index"
        ? value.toFixed(PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES)
        : value.toFixed(PRICING_LEVEL_DECIMAL_PLACES);

    if (!isLastItem || !e.currentTarget.value) {
      setMatrixByValues(
        Object.values({
          ...matrix_by_values,
          [rowIndex]: val.startsWith("C") ? val : modifiedRowValue,
        }) as (string | undefined)[]
      );
      return;
    }

    setMatrixByValues(
      Object.values({
        ...matrix_by_values,
        [rowIndex]: val.startsWith("C") ? val : modifiedRowValue,
        [rowsLength]: "",
      }) as (string | undefined)[]
    );
  };

  const removeRow = (api: GridApi | undefined, rowIndex: number) => {
    const rowData: Record<string, MatrixGridDataType | null>[] = [];
    api?.forEachNodeAfterFilterAndSort((rowNode) => {
      rowData.push(rowNode.data);
    });
    const dataWithoutRemovedRow = rowData.filter((_, i) => i !== rowIndex);

    const filteredRows = Object.values(matrix_by_values).filter((_, i) => i !== rowIndex);

    matrix_by_values_copy && setMatrixByValuesCopy(matrix_by_values_copy?.filter((_, i) => i !== rowIndex));
    setMatrixByValues(Object.values({ ...filteredRows }) as (string | undefined)[]);
    setTimeout(() => {
      // Set new grid data
      api?.setGridOption("rowData", dataWithoutRemovedRow);
    }, 0);
  };

  const addColumn =
    (colIndex: number) =>
    (value: string, isVector = false) => {
      const colsLength = Object.keys(prepay_models).length;
      const isLastInput = colsLength - 1 === colIndex;
      const { prepayPercentage, prepayModel, vectorId } = decodeValue(value, isVector);

      if (!isLastInput || !value) {
        const prepay_percentage =
          !prepayPercentage && vectorId?.toString() ? prepay_models[colIndex].prepay_percentage : prepayPercentage;
        return setPrepayModels(
          Object.values({
            ...prepay_models,
            [colIndex]: {
              ...prepay_models[colIndex],
              prepay_percentage,
              prepay_method: isVector
                ? CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR
                : prepayModel ?? prepay_model_type,
              value: getPrepayDisplayValue({
                user_vector_id: vectorId,
                prepay_method: prepayModel ?? prepay_model_type,
                prepay_percentage,
              }),
              user_vector_id: vectorId,
            },
          }) as MatrixPrepayModelType[]
        );
      }

      setPrepayModels(
        Object.values({
          ...prepay_models,
          [colIndex]: {
            ...prepay_models[colIndex],
            prepay_percentage: prepayPercentage,
            prepay_method: isVector
              ? CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR
              : prepayModel ?? prepay_model_type,
            value: getPrepayDisplayValue({
              user_vector_id: vectorId,
              prepay_method: prepayModel ?? prepay_model_type,
              prepay_percentage: prepayPercentage,
            }),
            user_vector_id: vectorId,
          },
          [colsLength]: {
            ...prepay_models[colIndex],
            prepay_percentage: "",
            prepay_method: prepay_model_type,
            user_vector_id: null,
            value: "",
          },
        }) as MatrixPrepayModelType[]
      );
    };

  const removeColumn = (colIndex: number) => {
    setPrepayModels(Object.values(prepay_models).filter((_, i) => i !== colIndex));
    prepay_models_copy && setPrepayModelsCopy(prepay_models_copy?.filter((_, i) => i !== colIndex));
  };

  const rows = React.useMemo(() => ({ ...matrix_by_values }), [matrix_by_values]);
  const columns = React.useMemo(() => ({ ...prepay_models }), [prepay_models]);
  return {
    rows,
    columns,
    getRowInputProps: () => ({
      rowsLength: matrix_by_values.length,
      rows,
      addRow,
      removeRow,
    }),
    getColumnInputProps: () => ({
      columnsLength: prepay_models.length,
      removeColumn,
      addColumn,
    }),
  };
};
