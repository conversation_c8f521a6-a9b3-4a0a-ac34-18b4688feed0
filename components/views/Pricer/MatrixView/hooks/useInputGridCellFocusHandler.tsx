import React from "react";

const useInputGridCellFocusHandler = (parentRef: HTMLElement | undefined, inputRef: HTMLInputElement | null) => {
  React.useEffect(() => {
    let TIMEOUT: NodeJS.Timeout;
    const focusCellHandler = () => {
      TIMEOUT = setTimeout(() => {
        inputRef?.focus();
        inputRef?.select();
      });
    };
    parentRef?.addEventListener("focus", focusCellHandler);

    return () => {
      parentRef?.removeEventListener("focus", focusCellHandler);
      clearTimeout(TIMEOUT);
    };
  }, [inputRef, parentRef]);
};

export default useInputGridCellFocusHandler;
