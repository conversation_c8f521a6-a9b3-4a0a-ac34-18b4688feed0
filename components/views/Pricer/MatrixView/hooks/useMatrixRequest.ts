import { CA_Mastr_Api_v1_0_Models_BondPricing_Result } from "@/utils/openapi";
import { useRunIdBatchRequestSWR } from "@/utils/swr-hooks";
import { MatrixGridProps } from "../MatrixGrid";

export interface MatrixRequestResult extends CA_Mastr_Api_v1_0_Models_BondPricing_Result {
  prepay_percentage: number;
  prepayPercentageValue: string;
  request: string;
}

export const MATRIX_ITEMS_PER_ROW = 7;
export const PRICING_LEVEL_DECIMAL_PLACES = 5;
export const PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES = 3;

export const useMatrixRequest = ({ bondPricingRequest, requestOpts }: MatrixGridProps) => {
  const { data, ...swr } = useRunIdBatchRequestSWR(bondPricingRequest, requestOpts);

  const datas =
    data?.pricing_responses?.map((el) => {
      const uniqueId = el?.ui_request_id?.match(/:([\d.]+):([\d\sA-Za-z.]+)/) ?? [];
      return {
        ...el.results?.[0],
        request: el?.request,
        prepay_percentage: +uniqueId?.[2]?.split(" ")?.[0],
        prepayPercentageValue: uniqueId?.[2],
      };
    }) ?? [];

  return { data: datas as MatrixRequestResult[], ...swr };
};
