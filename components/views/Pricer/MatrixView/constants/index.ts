export interface MATRIX_FOOTER_DATA {
  label: string;
  value?: string;
  isDefault?: boolean;
  children?: { label: string; value?: string; isDefault?: boolean }[];
}

export const MATRIX_ALL_BOTTOM_ROW: MATRIX_FOOTER_DATA[] = [
  // General
  {
    label: "General",
    children: [
      {
        label: "Price",
        value: "price",
      },
      {
        label: "WAL",
        value: "weighted_average_life",
        isDefault: true,
      },
      {
        label: "Settle Date Factor",
        value: "settle_date_factor",
      },
      {
        label: "Accrual Interest",
        value: "accrued_interest",
      },
      {
        label: "Accrual Start Date",
        value: "accrual_start_date",
      },
      {
        label: "Next Payment Date",
        value: "next_payment_date",
      },
      {
        label: "1st Payment Date",
        value: "payment_window_begin",
      },
      {
        label: "Last Payment Date",
        value: "payment_window_end",
      },
      {
        label: "1st Principal Date",
        value: "principal_window_begin",
        isDefault: true,
      },
      {
        label: "Last Principal Date",
        value: "principal_window_end",
        isDefault: true,
      },
      {
        label: "Forward WAL",
        value: "forward_weighted_average_life",
      },
      {
        label: "Forward Accrual Start Date",
        value: "forward_accrual_start_date",
      },
      {
        label: "Forward Next Payment Date",
        value: "forward_next_payment_date",
      },
      {
        label: "Forward 1st Principal Date",
        value: "forward_principal_window_begin",
      },
      {
        label: "Forward Last Principal Date",
        value: "forward_principal_window_end",
      },
      {
        label: "Forward 1st Payment Date",
        value: "forward_payment_window_begin",
      },
      {
        label: "Forward Last Payment Date",
        value: "forward_payment_window_end",
      },
    ],
  },

  // CPR
  {
    label: "CPR (%)",
    children: [
      {
        label: "1m",
        value: "one_month_cpr",
      },
      {
        label: "3m",
        value: "three_month_cpr",
      },
      {
        label: "6m",
        value: "six_month_cpr",
      },
      {
        label: "1yr",
        value: "one_year_cpr",
      },
      {
        label: "3yr",
        value: "three_year_cpr",
      },
      {
        label: "5yr",
        value: "five_year_cpr",
      },
      {
        label: "LT",
        value: "long_term_cpr",
      },
    ],
  },

  // CRR
  {
    label: "CRR (%)",
    children: [
      {
        label: "1m",
        value: "one_month_crr",
      },
      {
        label: "1yr",
        value: "one_year_crr",
      },
      {
        label: "3yr",
        value: "three_year_crr",
      },
      {
        label: "5yr",
        value: "five_year_crr",
      },
      {
        label: "LT",
        value: "long_term_crr",
      },
    ],
  },

  // CDR
  {
    label: "CDR (%)",
    children: [
      {
        label: "1m",
        value: "one_month_cdr",
      },
      {
        label: "1yr",
        value: "one_year_cdr",
      },
      {
        label: "3yr",
        value: "three_year_cdr",
      },
      {
        label: "5yr",
        value: "five_year_cdr",
      },
      {
        label: "LT",
        value: "long_term_cdr",
      },
    ],
  },

  // Forward CPR
  {
    label: "Forward CPR (%)",
    children: [
      {
        label: "1m",
        value: "one_month_forward_cpr",
      },
      {
        label: "1yr",
        value: "one_year_forward_cpr",
      },
      {
        label: "3yr",
        value: "three_year_forward_cpr",
      },
      {
        label: "5yr",
        value: "five_year_forward_cpr",
      },
      {
        label: "LT",
        value: "forward_long_term_cpr",
      },
    ],
  },

  // Forward CRR
  {
    label: "Forward CRR (%)",
    children: [
      {
        label: "1m",
        value: "one_month_forward_crr",
      },
      {
        label: "1yr",
        value: "one_year_forward_crr",
      },
      {
        label: "3yr",
        value: "three_year_forward_crr",
      },
      {
        label: "LT",
        value: "forward_long_term_crr",
      },
    ],
  },

  // Forward CDR
  {
    label: "Forward CDR (%)",
    children: [
      {
        label: "1m",
        value: "one_month_forward_cdr",
      },
      {
        label: "1yr",
        value: "one_year_forward_cdr",
      },
      {
        label: "3yr",
        value: "three_year_forward_cdr",
      },
      {
        label: "5yr",
        value: "five_year_forward_cdr",
      },
      {
        label: "LT",
        value: "forward_long_term_cdr",
      },
    ],
  },

  // Forward Cumulative CRR
  {
    label: "Forward Cumulative CRR (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_forward_cum_prepay_pct",
      },
      {
        label: "3yr",
        value: "three_year_forward_cum_prepay_pct",
      },
      {
        label: "5yr",
        value: "five_year_forward_cum_prepay_pct",
      },
      {
        label: "LT",
        value: "life_forward_cum_prepay_pct",
      },
    ],
  },

  // Forward Cumulative CDR
  {
    label: "Forward Cumulative CDR (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_forward_cum_default_pct",
      },
      {
        label: "3yr",
        value: "three_year_forward_cum_default_pct",
      },
      {
        label: "5yr",
        value: "five_year_forward_cum_default_pct",
      },
      {
        label: "LT",
        value: "life_forward_cum_default_pct",
      },
    ],
  },

  // Cumulative CDR
  {
    label: "Cumulative CDR (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_cum_default_pct",
      },
      {
        label: "3yr",
        value: "three_year_cum_default_pct",
      },
      {
        label: "5yr",
        value: "five_year_cum_default_pct",
      },
      {
        label: "LT",
        value: "life_cum_default_pct",
      },
    ],
  },

  // Cumulatice CRR
  {
    label: "Cumulatice CRR (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_cum_prepay_pct",
      },
      {
        label: "3yr",
        value: "three_year_cum_prepay_pct",
      },
      {
        label: "5yr",
        value: "five_year_cum_prepay_pct",
      },
      {
        label: "LT",
        value: "life_cum_prepay_pct",
      },
    ],
  },

  // Amortization
  {
    label: "Cumulative Amortization (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_cum_amort_pct",
      },
      {
        label: "3yr",
        value: "three_year_cum_amort_pct",
      },
      {
        label: "5yr",
        value: "five_year_cum_amort_pct",
      },
      {
        label: "LT",
        value: "life_cum_amort_pct",
      },
    ],
  },

  // Forward Amortization
  {
    label: "Forward Cumulative Amortization (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_forward_cum_amort_pct",
      },
      {
        label: "3yr",
        value: "three_year_forward_cum_amort_pct",
      },
      {
        label: "5yr",
        value: "five_year_forward_cum_amort_pct",
      },
      {
        label: "LT",
        value: "life_forward_cum_amort_pct",
      },
    ],
  },

  // Remaining Balance
  {
    label: "Remaining Balance (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_rem_bal_pct",
      },
      {
        label: "3yr",
        value: "three_year_rem_bal_pct",
      },
      {
        label: "5yr",
        value: "five_year_rem_bal_pct",
      },
      {
        label: "LT",
        value: "life_rem_bal_pct",
      },
    ],
  },

  // Forward Remaining Balance
  {
    label: "Forward Remaining Balance (%)",
    children: [
      {
        label: "1yr",
        value: "one_year_forward_rem_bal_pct",
      },
      {
        label: "3yr",
        value: "three_year_forward_rem_bal_pct",
      },
      {
        label: "5yr",
        value: "five_year_forward_rem_bal_pct",
      },
      {
        label: "LT",
        value: "life_forward_rem_bal_pct",
      },
    ],
  },
];
