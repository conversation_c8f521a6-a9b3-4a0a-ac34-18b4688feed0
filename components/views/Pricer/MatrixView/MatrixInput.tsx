import * as React from "react";
import { Box, VStack } from "@chakra-ui/react";
import { Controller, UseFormHandleSubmit, useForm } from "react-hook-form";
import CACard from "@/design-system/molecules/CACard";
import CAHeading from "@/design-system/atoms/CAHeading";
import CADateInput from "@/design-system/molecules/CADateInput";
import {
  DEC_REGEX,
  TICK_REGEX,
  capitalizeFirstLetter,
  getDisplayValueByKey,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getInfoMsg,
  tickToDecimal,
} from "@/utils/helpers";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { MatrixPageSettingsType } from "@/contexts/PageContexts/PricerMatrixPageContext/PricerMatrixPageContextTypes";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import {
  CA_Mastr_Models_v1_0_Models_BondType,
  CA_Mastr_Models_v1_0_Models_PrepayMethod,
  CA_Mastr_Models_v1_0_Models_PricingType,
} from "@/utils/openapi";
import useResetFormData from "@/hooks/useResetFormData";
import useUpdateSettleDate from "@/hooks/useUpdateSettleDate";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import CAInput from "@/design-system/molecules/CAInput";
import { DateSettingsOverrides } from "@/components/helpers/DateSettingsOverrides/DateSettingsOverrides";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import { PRICING_LEVEL_DECIMAL_PLACES, PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES } from "./hooks/useMatrixRequest";
import { MatrixByValuesStepFormType } from "./components";

export const prepayModelTypeOptions = [
  { id: 0, value: CA_Mastr_Models_v1_0_Models_PrepayMethod.CPR, displayValue: "CPR" },
  { id: 1, value: CA_Mastr_Models_v1_0_Models_PrepayMethod.CPJ, displayValue: "CPJ" },
  { id: 2, value: CA_Mastr_Models_v1_0_Models_PrepayMethod.PSA, displayValue: "PSA" },
];
const pricingTypeOptions = [
  { id: 0, value: "price", displayValue: "Price" },
  { id: 1, value: "yield", displayValue: "Yield" },
  { id: 3, value: "index", displayValue: "Index" },
];

export interface MatrixInputHandler {
  handleSubmit: UseFormHandleSubmit<MatrixPageSettingsType>;
}

const MatrixInput: React.ForwardRefRenderFunction<MatrixInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, userSettingsCopy, bond_name, security_info },
  } = usePricerModule();
  const {
    state: { app, page, matrixPageSettingsCopy, matrixPageSettings, isOldRun },
    action: { setSettleDate, setPricingType, setMatrixBy, setMatrixByValues, setPricingLevel },
  } = usePricerMatrixPage();

  const { register, handleSubmit, watch, control, setValue, reset, getValues } = useForm<MatrixPageSettingsType>();
  const [watch_settle_date, watch_modify_class, pricing_type, pricing_level, matrix_by] = watch([
    "settle_date",
    "modify_class",
    "pricing_type",
    "pricing_level",
    "matrix_by",
  ]);

  useResetFormData({ reset, formData: matrixPageSettings });

  useUpdateSettleDate<typeof matrixPageSettings>({
    pageSettings: matrixPageSettings,
    setSettleDate: (settleDate) => {
      setValue("settle_date", settleDate);
      setSettleDate(settleDate);
    },
    isOldRun,
    security_info,
    userSettings,
    userSettingsCopy,
    bond_name,
    app,
    page,
  });

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  const fillPricingLevelsHandler = ({ numberOfRows, price, stepSize }: MatrixByValuesStepFormType) => {
    const centeredIndex = Math.floor(numberOfRows / 2);
    const matrixTest = getValues("matrix_by");

    const pricingLevels: (number | string | undefined)[] = Array(numberOfRows)
      .fill(undefined)
      .map((_, index) => {
        if (index < centeredIndex) {
          const difference = centeredIndex - index;
          return price - difference * stepSize;
        }
        if (index > centeredIndex) {
          const difference = index - centeredIndex;
          return price + difference * stepSize;
        }
        return price;
      })
      .map((p) => {
        if (matrixTest === "index") {
          return p.toFixed(PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES);
        }
        return p.toFixed(PRICING_LEVEL_DECIMAL_PLACES);
      });

    setMatrixByValues([...pricingLevels, ""].map(String));
  };

  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardStyleOnOpen={{
        height: "100%",
      }}
      cardKey="matrix-inputs"
      overflow="visible"
      marginTop={"2px"}
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <CAHeading as="h3" mb={4}>
            Dates
          </CAHeading>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.curve_date)}
            _key="curve_date"
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
          <Controller
            name={"settle_date"}
            control={control}
            rules={{ required: true }}
            render={({ field: { name, value, onChange, ref } }) => (
              <CADateInput
                ref={ref}
                width={"6rem"}
                selectedDate={value}
                minDate={userSettings.curve_date}
                onChange={onChange}
                name={name}
                label={"Settle Date"}
                info={getInfoMsg(watch_settle_date, matrixPageSettingsCopy.settle_date)}
                includeNonBusinessDays={true}
              />
            )}
          />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
          {security_info?.bond_type !== CA_Mastr_Models_v1_0_Models_BondType.CMO && (
            <CASelectDropdown
              label="Cash Flow"
              width="6rem"
              info={getInfoMsg(watch_modify_class, matrixPageSettingsCopy.modify_class)}
              {...register("modify_class", {
                required: true,
              })}
              options={getFormattedStringOptions(metadata?.pricer_settings?.bond_class)}
            />
          )}
          <CASelectDropdown
            {...register("matrix_by", {
              onChange(event) {
                setMatrixBy(event.target.value as "index" | "price" | "yield");
                if (event.target.value === "index") {
                  fillPricingLevelsHandler({
                    numberOfRows: 7,
                    price: 5.33,
                    stepSize: 0.5,
                  });
                  return;
                }
                setValue("pricing_type", event.target.value);
                setPricingType(event.target.value as CA_Mastr_Models_v1_0_Models_PricingType);
                fillPricingLevelsHandler({
                  numberOfRows: 6,
                  price: 18,
                  stepSize: 2,
                });
              },
            })}
            width="6rem"
            label="Matrix By"
            options={pricingTypeOptions}
            info={
              matrixPageSettingsCopy.matrix_by &&
              getInfoMsg(
                capitalizeFirstLetter(matrixPageSettings.matrix_by),
                capitalizeFirstLetter(matrixPageSettingsCopy.matrix_by)
              )
            }
          />
        </Box>
        {matrix_by === "index" && (
          <Box>
            <CAHeading as="h3" mb={4}>
              Price
            </CAHeading>
            <CASelectDropdown
              data-testid="pricing-type"
              label={"Type"}
              width={"6rem"}
              info={getInfoMsg(
                getDisplayValueByKey(pricing_type),
                getDisplayValueByKey(matrixPageSettingsCopy.pricing_type)
              )}
              {...register("pricing_type", {
                required: true,
                onChange(event) {
                  setPricingType(event.target.value as CA_Mastr_Models_v1_0_Models_PricingType);
                  if (`${pricing_level}`.match(TICK_REGEX) && event.target.value !== "price") {
                    setValue("pricing_level", undefined);
                    setPricingLevel(undefined);
                  }
                },
              })}
              options={[
                {
                  id: 0,
                  value: CA_Mastr_Models_v1_0_Models_PricingType.PRICE,
                  displayValue: getDisplayValueByKey(CA_Mastr_Models_v1_0_Models_PricingType.PRICE),
                },
                {
                  id: 1,
                  value: CA_Mastr_Models_v1_0_Models_PricingType.OAS,
                  displayValue: getDisplayValueByKey(CA_Mastr_Models_v1_0_Models_PricingType.OAS),
                },
                {
                  id: 3,
                  value: CA_Mastr_Models_v1_0_Models_PricingType.YIELD,
                  displayValue: getDisplayValueByKey(CA_Mastr_Models_v1_0_Models_PricingType.YIELD),
                },
              ]}
            />
            <CAInput
              data-test="pricing-level"
              label={"Value"}
              width={"6rem"}
              info={getInfoMsg(pricing_level, matrixPageSettingsCopy.pricing_level)}
              {...register("pricing_level", {
                required: true,
                onBlur: (e) => {
                  if (pricing_type === "price" && `${e.target.value}`.match(TICK_REGEX)) {
                    setValue("pricing_level", e.target.value);
                    setPricingLevel(e.target.value);
                    return;
                  }

                  const isDecimalValue = e.target.value?.match(DEC_REGEX);
                  const convertedValue = !isDecimalValue
                    ? Number(Number(tickToDecimal(e.target.value)).toFixed(PRICING_LEVEL_DECIMAL_PLACES))
                    : +e.target.value;

                  if (!isNaN(convertedValue)) {
                    setValue("pricing_level", convertedValue);
                    setPricingLevel(convertedValue);
                  }
                },
              })}
            />
          </Box>
        )}

        <DateSettingsOverrides />
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(MatrixInput);
