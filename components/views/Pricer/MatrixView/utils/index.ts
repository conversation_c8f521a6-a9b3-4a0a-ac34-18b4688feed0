import { CellPosition, TabToNextCellParams } from "ag-grid-community";
import { MATRIX_FOOTER_DATA } from "@/components/views/Pricer/MatrixView/constants";
import {
  PRICING_LEVEL_DECIMAL_PLACES,
  PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES,
} from "@/components/views/Pricer/MatrixView/hooks/useMatrixRequest";
import {
  MatrixDisplayValueTypes,
  MatrixPrepayModelType,
} from "@/contexts/PageContexts/PricerMatrixPageContext/PricerMatrixPageContextTypes";
import { groupBy } from "@/design-system/molecules/CAGrid/helpers";
import { showErrorToast } from "@/design-system/theme/toast";
import { getFormattedLocaleDate } from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "@/utils/openapi";
import { MatrixRequestResult } from "../hooks/useMatrixRequest";
import { MATRIX_ALL_BOTTOM_ROW } from "./../constants/index";

export interface MatrixGridDataType
  extends Pick<MatrixRequestResult, "weighted_average_life" | "accrual_start_date" | "next_payment_date" | "price"> {
  displayValue: number | null | undefined | "-";
}

const sortDataInRowsOrder = (
  data: (Record<string, number | MatrixGridDataType | null | undefined> | undefined)[],
  rowsArr: (string | null | undefined)[],
  groupByKey: keyof MatrixRequestResult,
  matrixBy: "index" | "price" | "yield"
) => {
  return data?.sort((a, b) => {
    const valA = a?.[groupByKey];
    const valB = b?.[groupByKey];
    if (!valA || !valB) return 0;
    if (typeof valA === "number" && typeof valB === "number") {
      return (
        rowsArr.indexOf(
          valA?.toFixed(matrixBy === "index" ? PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES : PRICING_LEVEL_DECIMAL_PLACES)
        ) -
        rowsArr.indexOf(
          valB?.toFixed(matrixBy === "index" ? PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES : PRICING_LEVEL_DECIMAL_PLACES)
        )
      );
    }
    return 0;
  });
};

const groupDataByColumns = (
  rowsArr: (string | null | undefined)[],
  data: MatrixRequestResult[],
  groupByKey: keyof MatrixRequestResult,
  displayValue: MatrixDisplayValueTypes,
  matrixBy: "index" | "price" | "yield"
) => {
  const objByKeys = groupBy(data, (i) => {
    if (matrixBy !== "index") {
      return i[groupByKey] ?? groupByKey;
    }
    const request = i.request ? JSON.parse(i.request) : undefined;

    if (!request) return "";
    if ("index_rate" in request) {
      return request?.index_rate?.values[0];
    } else if ("index_rate_string" in request) {
      return request?.index_rate_string;
    }
  });

  return rowsArr?.map((row, i) => {
    if (!objByKeys[i]) return {}; // Enables adding new rows on grid

    const recordIndex = objByKeys.findIndex((obj) => {
      const request = obj[0].request ? JSON.parse(obj[0].request) : undefined;
      if (matrixBy === "index") {
        if (!request) return false;
        if ("index_rate" in request) {
          return request?.index_rate?.values[0]?.toFixed(PRICING_LEVEL_ON_INDEX_DECIMAL_PLACES) === row;
        } else if ("index_rate_string" in request) {
          return request?.index_rate_string === row;
        }
      }

      return request?.pricing_level?.toFixed(PRICING_LEVEL_DECIMAL_PLACES) === row;
    });
    if (recordIndex === -1) return {};

    const result = objByKeys?.[recordIndex]?.reduce(
      (acc: Record<string, MatrixGridDataType | null | number | undefined> | undefined, nextVal) => {
        if (nextVal.prepay_percentage == undefined) return undefined;
        const indexRate = nextVal.request ? JSON.parse(nextVal.request) : undefined;
        if (acc) {
          if (indexRate?.index_rate_string) {
            const indexRateValue = indexRate?.index_rate_string;
            acc["index_rate"] = indexRateValue;
          }
          acc[`${nextVal.prepayPercentageValue}`] = {
            displayValue: nextVal[displayValue] ?? "-",
            ...nextVal,
          };
          acc[groupByKey] = nextVal[groupByKey] as number;
          return acc;
        }
      },
      {}
    );
    return result;
  });
};

export const transformDataForGrid = ({
  displayValue,
  groupByKey,
  data,
  rows,
  matrixBy,
}: {
  displayValue: MatrixDisplayValueTypes;
  groupByKey: keyof MatrixRequestResult;
  data: MatrixRequestResult[];
  rows: Record<number, string | null | undefined>;
  matrixBy: "index" | "price" | "yield";
}) => {
  if (!rows) return;

  const rowsArr = Object.values(rows);

  const transformedData = groupDataByColumns(rowsArr, data, groupByKey, displayValue, matrixBy);

  const sortedData = sortDataInRowsOrder(transformedData, rowsArr, groupByKey, matrixBy);

  return sortedData;
};

export const generatePinnedBottomRowData = (
  data: Record<string, number | MatrixGridDataType | null | undefined> | undefined,
  key: keyof MatrixGridDataType
) => {
  if (!data) return;
  return Object.entries(data).reduce((acc: Record<string, string | number | undefined | null>, nextVal) => {
    if (!nextVal[0]) return acc;
    if (typeof nextVal[1] === "number") return acc;

    acc[nextVal[0]] =
      typeof nextVal[1]?.[key] === "number"
        ? nextVal[1]?.[key]
        : getFormattedLocaleDate(nextVal[1]?.[key] as string | Date | null | undefined);
    return acc;
  }, {});
};

export const getColumnValueType = (displayValue: string) => {
  if (displayValue === "zvoas" || displayValue === "market_value") return "number";
  if (displayValue === "dv01") return "number4";
  return "number3";
};

const PrepayMethods = Object.values(CA_Mastr_Models_v1_0_Models_PrepayMethod);
export const PrepayInputRegex = new RegExp(`([\\^0-9.]+)\\s{0,}(${PrepayMethods.join("|")})`, "i");
export const VectorValueRegex = /([\d]*)\s*V([\d]*)/i;

export const decodeValue = (value: string, isVector: boolean) => {
  // [INFO] Vector values such as 12v45, 12V45, 12 v45...
  if (isVector) {
    const [, prepayPercentage, vectorId] = value.match(VectorValueRegex) ?? [];
    return {
      prepayPercentage: +prepayPercentage.trim(),
      vectorId: Number(vectorId) ?? null,
    };
  }

  // [INFO] Constant values such as 1, 2, 35...
  const isConstantValue = !isNaN(+value);
  if (isConstantValue) {
    return { prepayPercentage: Number(value), vectorId: null };
  }

  // [INFO] Rest inputs such as 1 PSA, 1psa, 12 psa etc...
  const isValidInput = value.match(PrepayInputRegex);

  if (!isValidInput) {
    showErrorToast("Invalid Input", "Entered value is invalid");
    return { prepayPercentage: "", vectorId: null };
  }

  const [, prepayPercentage, prepayModel] = isValidInput;
  return {
    prepayPercentage: Number(prepayPercentage),
    prepayModel: prepayModel.toUpperCase() as CA_Mastr_Models_v1_0_Models_PrepayMethod,
    vectorId: null,
  };
};

export const getPrepayDisplayValue = ({
  prepay_method,
  user_vector_id,
  prepay_percentage,
}: Partial<Pick<MatrixPrepayModelType, "prepay_method" | "prepay_percentage" | "user_vector_id">>) => {
  if (prepay_percentage == undefined || prepay_percentage === "") return "";
  if (user_vector_id || prepay_method === "Vector") return `${prepay_percentage} V${user_vector_id}`;
  return `${prepay_percentage} ${prepay_method}`;
};

export const tabToNextCell = (params: TabToNextCellParams): CellPosition => {
  const previousCell = params.previousCellPosition;
  const lastRowIndex = previousCell.rowIndex;
  let nextRowIndex = params.backwards ? lastRowIndex - 1 : lastRowIndex + 1;
  const renderedRowCount = params.api?.getLastDisplayedRowIndex() ?? 0;
  if (nextRowIndex < 0) {
    nextRowIndex = -1;
  }
  if (nextRowIndex >= renderedRowCount) {
    nextRowIndex = renderedRowCount - 1;
  }
  const result = {
    rowIndex: nextRowIndex,
    column: previousCell.column,
    rowPinned: previousCell.rowPinned,
  };
  return result;
};

export interface MatrixFooterRowOption {
  label: string;
  value: string;
  isDefault: boolean;
}
const flattenedMatrixFooterData = (data: MATRIX_FOOTER_DATA[]) => {
  return data.reduce<MatrixFooterRowOption[]>((acc, item) => {
    if (!item.children) {
      acc = [...acc, item as MatrixFooterRowOption];
    } else {
      acc = [...acc, ...(item.children as MatrixFooterRowOption[])];
    }
    return acc;
  }, []);
};

export const getPinnedBottomRowData = ({
  selectedRows,
  data,
}: {
  selectedRows: Record<string, string[]>;
  data: (Record<string, number | MatrixGridDataType | null | undefined> | undefined)[] | undefined;
}) =>
  Object.entries(selectedRows)
    .map((el) => {
      if (el[1].length)
        return [
          el[0],
          ...el[1].map((child) => ({
            parentLabel: el[0],
            value: child,
          })),
        ];
      return [];
    })
    .flat()
    .slice(1)
    .map((el) => {
      if (typeof el === "string") return;

      if (el.parentLabel === "General")
        return {
          price: flattenedMatrixFooterData(MATRIX_ALL_BOTTOM_ROW).find((row) => row.value === el.value)?.label,
          index_rate: flattenedMatrixFooterData(MATRIX_ALL_BOTTOM_ROW).find((row) => row.value === el.value)?.label,
          ...generatePinnedBottomRowData(data?.[0], el.value as keyof MatrixGridDataType),
        };
      return {
        price: `${el.parentLabel} ${
          flattenedMatrixFooterData(MATRIX_ALL_BOTTOM_ROW).find((row) => row.value === el.value)?.label
        }`,
        index_rate: flattenedMatrixFooterData(MATRIX_ALL_BOTTOM_ROW).find((row) => row.value === el.value)?.label,
        ...generatePinnedBottomRowData(data?.[0], el.value as keyof MatrixGridDataType),
      };
    })
    .filter(Boolean);

export const getDefaultBottomPinnedRowsSelection = () => {
  return MATRIX_ALL_BOTTOM_ROW.reduce<Record<string, string[]>>(
    (acc, current) => {
      if (!current.children) {
        if (current.isDefault) {
          acc["general"] = [...(acc["general"] ?? []), current.value as string];
        }
      } else {
        const allDefaultValues = current.children
          .filter((c) => c.isDefault)
          .map((c) => c.value)
          .map(String);
        acc[current.label] = allDefaultValues;
      }

      return acc;
    },
    { general: [] }
  );
};
