import * as React from "react";
import { Box } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { getRequests } from "@/utils/helpers/pricer/matrix";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { PricerModuleProps } from "../";
import PricerHeader from "../PricerHeader";
import { BatchRequestStopWatch } from "../PricerStopWatchWrapper";
import { BatchRequestStopRunningWrapper } from "../PricerStopRunningWrapper";
import { BatchRequestProgressIndicator } from "../PricerProgressIndicatorWrapper";
import MatrixInput, { MatrixInputHandler } from "./MatrixInput";
import MatrixGrid from "./MatrixGrid";

const PricerMatrixView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: {
      lastRunId,
      isOldRun,
      lastRun,
      api_start_time,
      api_end_time,
      matrix_by_values,
      prepay_models,
      ...restMatrixState
    },
    action: { run, updateMatrixPageSettings, resetLastRun },
  } = usePricerMatrixPage();
  const { pushWithoutRendering } = useQueryParameters();

  const matrixInputRef = React.useRef<MatrixInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;

    matrixInputRef.current?.handleSubmit((data) => {
      if (!matrix_by_values.length || !prepay_models.length) return;
      updateMatrixPageSettings(data);
      updatePricerUserSettingsCopy(userSettings);

      const pricingLevels = [...matrix_by_values];

      if (data?.matrix_by === "index") {
        pricingLevels.map(() => data.pricing_level);
      }

      const run_id = run({
        no_cache,
        inputs: {
          rows: { ...pricingLevels },
          columns: { ...prepay_models },
        },
      });
      setIsTimerRunning(true);
      if (run_id) {
        pushWithoutRendering({ run_id });
      }
    })();
  };

  const { batchRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRun,
    lastRunId,
    isOldRun,
    bond_name,
    matrix_by_values,
    prepay_models,
    ...restMatrixState,
  });

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <BatchRequestStopWatch
            request={batchRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={<BatchRequestProgressIndicator request={batchRequest} requestOpts={requestOpts} />}
        stopRunning={
          <BatchRequestStopRunningWrapper
            request={batchRequest}
            requestOpts={requestOpts}
            resetLastRun={resetLastRun}
          />
        }
      />
      <MainInputContentTemplate inputs={<MatrixInput ref={matrixInputRef} />}>
        <MatrixGrid bondPricingRequest={batchRequest} requestOpts={requestOpts} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerMatrixView;
