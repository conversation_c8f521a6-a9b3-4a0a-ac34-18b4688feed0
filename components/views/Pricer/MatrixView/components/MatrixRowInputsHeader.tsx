import React, { useEffect } from "react";
import {
  Box,
  Button,
  ButtonGroup,
  Flex,
  HStack,
  IconButton,
  Popover,
  PopoverArrow,
  PopoverCloseButton,
  PopoverContent,
  PopoverTrigger,
  Portal,
  Text,
  Tooltip,
  chakra,
  useColorMode,
  useDisclosure,
} from "@chakra-ui/react";
import { CiCircleInfo } from "react-icons/ci";
import { useForm } from "react-hook-form";
import { IHeaderParams } from "ag-grid-community";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import { DEC_REGEX, POSITIVE_DEC_REGEX, capitalizeFirstLetter, tickToDecimal } from "@/utils/helpers";
import { StepperIcon } from "@/design-system/icons";
import CAInput from "@/design-system/molecules/CAInput";
import { showErrorToast } from "@/design-system/theme/toast";
import colors from "@/design-system/theme/colors";
import { MatrixGridContextType } from "../MatrixGrid";
import { PRICING_LEVEL_DECIMAL_PLACES } from "../hooks/useMatrixRequest";

export interface MatrixByValuesStepFormType {
  numberOfRows: number;
  price: number;
  stepSize: number;
}

interface MatrixRowInputsheaderStyles extends IHeaderParams {
  context: MatrixGridContextType;
}

export const MatrixRowInputsHeader = ({ context }: MatrixRowInputsheaderStyles) => {
  const { onOpen, onClose, isOpen } = useDisclosure();
  const {
    state: {
      matrixPageSettings: { matrix_by },
    },
  } = usePricerMatrixPage();
  const { colorMode } = useColorMode();

  const { register, handleSubmit, setFocus, setValue, reset } = useForm<MatrixByValuesStepFormType>({
    defaultValues: {
      numberOfRows: 7,
      price: 12,
      stepSize: 0.5,
    },
  });

  useEffect(() => {
    if (!matrix_by) return;
    if (matrix_by === "index") {
      reset({
        numberOfRows: 7,
        price: 5.33,
        stepSize: 0.5,
      });
      return;
    }

    reset({
      numberOfRows: 7,
      price: 12,
      stepSize: 0.5,
    });
  }, [matrix_by, reset]);

  const fillMatrixByValuesHandler = (data: MatrixByValuesStepFormType) => {
    if (!`${data.price}`.match(POSITIVE_DEC_REGEX)) {
      showErrorToast("Validation", "Please enter a valid positive pricing value.");
      return;
    }
    context.fillMatrixByValues({ ...data, price: +data.price });
    onClose();
  };

  const convertToTick = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const isDecimalValue = e.target.value?.match(DEC_REGEX);
      const convertedValue = !isDecimalValue
        ? Number(Number(tickToDecimal(e.target.value)).toFixed(PRICING_LEVEL_DECIMAL_PLACES))
        : +e.target.value;

      if (!`${convertedValue}`.match(POSITIVE_DEC_REGEX)) {
        showErrorToast("Validation", "Please enter a valid positive pricing value.");
        return;
      }

      !isNaN(convertedValue) && setValue("price", convertedValue);
    } catch (err) {
      setValue("price", Number(e.target.value));
    }
  };

  return (
    <Flex alignItems="center" justifyContent="flex-end" w="7rem">
      <HStack alignItems="center" h="30px" position={"relative"}>
        <Flex gap={"5px"} alignItems="center">
          <Text>{capitalizeFirstLetter(matrix_by)}</Text>
          {matrix_by === "index" && (
            <Tooltip
              label={
                <>
                  <Box>Enter one of:</Box>
                  <Box>・Index value (e.g. 4.35);</Box>
                  <Box>・C for constant rate;</Box>
                  <Box>・C+/-{`<bps>`} for basis-point adjustment (e.g. C-25, C+10).</Box>
                </>
              }
              placement="top"
            >
              <Box>
                <CiCircleInfo size={16} />
              </Box>
            </Tooltip>
          )}
        </Flex>
        <Popover
          isOpen={isOpen}
          onOpen={() => {
            onOpen();
            setTimeout(() => {
              setFocus("price");
            }, 100);
          }}
          onClose={onClose}
          placement="bottom-start"
        >
          <PopoverTrigger>
            <IconButton
              size="xs"
              variant="primary"
              aria-label="stepper-icon"
              icon={<StepperIcon />}
              onClick={() => {
                onOpen();
                setTimeout(() => {
                  setFocus("numberOfRows");
                });
              }}
            />
          </PopoverTrigger>
          <Portal>
            <PopoverContent maxW="12rem">
              <PopoverArrow
                backgroundColor={colorMode === "light" ? colors.celloBlue["25"] : colors.celloBlue["900"]}
              />
              <PopoverCloseButton />
              <chakra.form onSubmit={handleSubmit(fillMatrixByValuesHandler)}>
                <Box maxW="12rem" mt={4} px={6} py={3}>
                  <CAInput
                    label={capitalizeFirstLetter(matrix_by)}
                    width="4rem"
                    step="any"
                    {...register("price", { required: true, onBlur: convertToTick })}
                  />
                  <CAInput
                    label="Step Size"
                    width="4rem"
                    type="number"
                    step="any"
                    {...register("stepSize", { required: true, valueAsNumber: true })}
                  />
                  <CAInput
                    label="Rows"
                    width="4rem"
                    type="number"
                    step="any"
                    {...register("numberOfRows", { required: true, valueAsNumber: true })}
                  />
                  <ButtonGroup display="flex" justifyContent="flex-end" mt={4}>
                    <Button variant="primary" size="sm" type="submit">
                      Fill
                    </Button>
                  </ButtonGroup>
                </Box>
              </chakra.form>
            </PopoverContent>
          </Portal>
        </Popover>
      </HStack>
    </Flex>
  );
};
