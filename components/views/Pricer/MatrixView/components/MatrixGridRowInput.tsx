import React from "react";
import { BiTrash } from "react-icons/bi";
import { HStack, Icon<PERSON>utton, useColorModeValue } from "@chakra-ui/react";
import { ICellRendererParams } from "ag-grid-community";
import { getInfoMsg } from "@/utils/helpers";
import CAInput from "@/design-system/molecules/CAInput";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import { GetRowInputProps } from "../hooks/useDynamicRowsColumns";
import useInputGridCellFocusHandler from "../hooks/useInputGridCellFocusHandler";

export interface MatrixGridRowInputProps extends Partial<ICellRendererParams>, GetRowInputProps {}

export const MatrixGridRowInput = ({
  node,
  rowsLength,
  rows,
  api,
  removeRow,
  addRow,
  eGridCell,
}: MatrixGridRowInputProps) => {
  const {
    state: { matrix_by_values_copy },
  } = usePricerMatrixPage();
  const refInput = React.useRef<HTMLInputElement>(null);

  useInputGridCellFocusHandler(eGridCell, refInput.current);

  const deleteIconColor = useColorModeValue("black", "white");
  const rowIndex = node?.rowIndex ?? 0;

  const isLastItem = rowsLength - 1 === rowIndex;

  if (rowIndex === undefined) return null;

  const infoMsg = getInfoMsg(rows[rowIndex] ?? undefined, matrix_by_values_copy?.[rowIndex] ?? undefined);
  return (
    <HStack position="relative" h="1.7rem" role="group" maxW="6rem">
      <CAInput
        key={rows[rowIndex]}
        ref={refInput}
        name={`rows.${rowIndex}`}
        onBlur={addRow(rowIndex)}
        defaultValue={rows[rowIndex] ?? undefined}
        info={infoMsg}
      />
      {!isLastItem && (
        <IconButton
          _groupHover={{
            visibility: "visible",
          }}
          visibility="hidden"
          aria-label="Delete column"
          position="absolute"
          right="1"
          variant="transparent"
          size="xs"
          icon={<BiTrash color={infoMsg.show ? "black" : deleteIconColor} />}
          onClick={() => removeRow(api, rowIndex)}
        />
      )}
    </HStack>
  );
};
