import React from "react";
import {
  Box,
  Checkbox,
  CheckboxGroup,
  Flex,
  Text,
  VStack,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import CAModal from "@/design-system/molecules/CAModal";
import { MATRIX_ALL_BOTTOM_ROW } from "../constants";

interface MatrixMenuProps {
  defaultSelections: Record<string, string[]>;
  onModalClose: (selection: Record<string, string[]>) => void;
  isOpen: boolean;
  onClose: () => void;
}

export const MatrixMenu = ({ onModalClose, defaultSelections, isOpen, onClose }: MatrixMenuProps) => {
  const modalSize = useBreakpointValue({ base: "lg", md: "2xl", lg: "4xl", xl: "6xl" });
  const [checkboxSelections, updateSelection] = React.useState<Record<string, string[]>>({});

  React.useEffect(() => {
    if (isOpen) {
      updateSelection(defaultSelections);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const selectGroupHandler = (groupName: string) => {
    const allChildren = MATRIX_ALL_BOTTOM_ROW.find((d) => d.label === groupName)?.children ?? [];
    const allChecked = checkboxSelections?.[groupName]?.length === allChildren.length;
    if (allChecked) {
      updateSelection({ ...checkboxSelections, [groupName]: [] });
    } else {
      const values = allChildren.map((c) => c.value).filter(Boolean) as string[];
      updateSelection({ ...checkboxSelections, [groupName]: values });
    }
  };

  // will be used for checkbox which are not grouped/or does not have a parent
  const handleCheckboxSelection = (value: string | undefined) => {
    if (!value) return;

    // checkbox without group will be stored under "general"
    const generalSelections = checkboxSelections?.["general"] ?? [];
    if (generalSelections.includes(value)) {
      generalSelections.splice(generalSelections.indexOf(value), 1);
    } else {
      generalSelections.push(value);
    }
    updateSelection({ ...checkboxSelections, general: generalSelections });
  };

  const submitSelection = () => {
    onModalClose(checkboxSelections);
    onClose();
  };

  return (
    <CAModal
      modalBackground={useColorModeValue("celloBlue.50", "celloBlue.900")}
      size={modalSize}
      modalHeader="Display Properties"
      showCloseIcon
      isOpen={isOpen}
      onClose={onClose}
      modalBodyProps={{
        overflowX: "auto",
      }}
      headerStyle={{
        color: useColorModeValue("celloBlue.500", "turmericRoot.500"),
        textTransform: "uppercase",
        py: "1rem",
        px: "1.7rem",
      }}
      modalFooter={[
        {
          title: "Cancel",
          variant: "secondary",
          onClick: onClose,
        },
        {
          title: "Apply",
          variant: "primary",
          onClick: submitSelection,
        },
      ]}
    >
      <Box fontSize="md" p="1.7rem" mb={4} background={useColorModeValue("white", "celloBlue.1000")}>
        <Flex
          flexDirection="column"
          flexWrap="wrap"
          alignContent={{ base: "flex-start", md: "space-around" }}
          maxH={{
            base: "auto",
            md: "100rem",
            lg: "75rem",
            xl: "55rem",
          }}
          pt={3}
        >
          {MATRIX_ALL_BOTTOM_ROW?.map(({ value, label, children, isDefault }) => {
            if (!children) {
              return (
                <Box key={value} mt={1.5} mb={"2rem"}>
                  <Checkbox
                    value={value}
                    isChecked={checkboxSelections?.["general"]?.includes(value as string)}
                    defaultChecked={isDefault}
                    onChange={() => handleCheckboxSelection(value)}
                  >
                    <Text variant="tableHead" whiteSpace="nowrap">
                      {label}
                    </Text>
                  </Checkbox>
                </Box>
              );
            }
            return (
              <Box key={label} mb="2rem">
                <Checkbox
                  value={value}
                  isChecked={checkboxSelections?.[label]?.length === children?.length}
                  isIndeterminate={
                    checkboxSelections?.[label] &&
                    checkboxSelections?.[label]?.length !== children.length &&
                    checkboxSelections?.[label]?.length !== 0
                  }
                  onChange={() => selectGroupHandler(label)}
                >
                  <Text variant="tableHead" whiteSpace="nowrap">
                    {label}
                  </Text>
                </Checkbox>
                <Box pl={6} mt={2.5}>
                  <CheckboxGroup
                    defaultValue={
                      children
                        .filter((c) => c.isDefault)
                        .map((c) => c.value)
                        .filter(Boolean) as string[]
                    }
                    value={checkboxSelections?.[label]}
                    onChange={(e) => updateSelection({ ...checkboxSelections, [label]: e.map(String) })}
                  >
                    <VStack alignItems="flex-start">
                      {children.map((c) => (
                        <Checkbox key={c.value} value={c.value}>
                          <Text display="inline" variant="default" whiteSpace="nowrap">
                            {c.label}
                          </Text>
                        </Checkbox>
                      ))}
                    </VStack>
                  </CheckboxGroup>
                </Box>
              </Box>
            );
          })}
        </Flex>
      </Box>
    </CAModal>
  );
};
