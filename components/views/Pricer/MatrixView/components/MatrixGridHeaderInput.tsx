import React from "react";
import { IHeaderParams } from "ag-grid-community";
import {
  HStack,
  IconButton,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Portal,
  useBoolean,
  useColorModeValue,
  useOutsideClick,
} from "@chakra-ui/react";
import { BiTrash } from "react-icons/bi";
import { KeyedMutator } from "swr";
import CAInput, { CAInputProps } from "@/design-system/molecules/CAInput";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";
import { getInfoMsg } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_UserVector_UserVector,
  CA_Mastr_Api_v1_0_Models_UserVector_UserVectorsResponse,
} from "@/utils/openapi";
import { GetColumnInputProps } from "../hooks/useDynamicRowsColumns";
import useInputGridCellFocusHandler from "../hooks/useInputGridCellFocusHandler";
import PrepayVectorSelectorTable from "../../shared/PrepayVectorSelectorTable";
import { PrepayInputRegex, VectorValueRegex } from "../utils";

export interface MatrixGridheaderStyles
  extends Omit<CAInputProps, "name">,
    Partial<IHeaderParams>,
    GetColumnInputProps {
  index: number;
  userVectors?: CA_Mastr_Api_v1_0_Models_UserVector_UserVector[];
  mutateVectors: KeyedMutator<CA_Mastr_Api_v1_0_Models_UserVector_UserVectorsResponse | null>;
}

export const MatrixGridHeaderInput = ({
  index,
  columnsLength,
  defaultValue,
  addColumn,
  removeColumn,
  userVectors,
  eGridHeader,
  mutateVectors,
}: MatrixGridheaderStyles) => {
  const {
    state: {
      prepay_models_copy,
      matrixPageSettings: { prepay_model_type },
    },
  } = usePricerMatrixPage();
  const prevValue = React.useRef<string>("");

  // The ref of the element that should receive focus when the popover opens.
  const ref = React.useRef<HTMLInputElement>(null);
  const popoverRef = React.useRef<HTMLElement>(null);

  const [isSelectingVector, setIsSelectingVector] = useBoolean();

  const deleteIconColor = useColorModeValue("black", "white");

  const isLastInput = columnsLength - 1 === index;

  useInputGridCellFocusHandler(eGridHeader, ref.current);

  useOutsideClick({
    ref: popoverRef,
    handler: () => setIsSelectingVector.off(),
  });

  const onVKeyPress: React.KeyboardEventHandler<HTMLInputElement> = (e) => {
    if (e.key === "v" || e.key === "V") {
      setIsSelectingVector.on();
    }
  };

  const handleVectorChange = (vector: CA_Mastr_Api_v1_0_Models_UserVector_UserVector) => {
    const vectorId = "V" + vector.user_vector_id;
    const inputVal = ref.current?.value;

    addColumn(index)(`${inputVal?.replace(/v/i, "").split(" ")[0]} ${vectorId}`, true);

    setIsSelectingVector.off();
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement, Element>) => {
    const value = e.currentTarget.value;
    // Check if it's invalid value
    const isInputValid = !!value.match(PrepayInputRegex) || !isNaN(+value);
    const isSameAsPrevious = prevValue.current.replace(" ", "").toLowerCase() === value.replace(" ", "").toLowerCase();
    if (!isInputValid) {
      if (ref.current) {
        // Reset to previous value on invalid input
        ref.current.value = prevValue.current;
      }
      return;
    } else if (value && !isNaN(Number(value))) {
      if (ref.current) {
        // [INFO] - https://app.clickup.com/t/55934/UI-2238?comment=1207418274
        ref.current.value = value + " " + prepay_model_type;
      }
    } else if (isSameAsPrevious) {
      if (ref.current) {
        ref.current.value = prevValue.current;
      }
    }

    addColumn(index)(value);
  };

  const handlePopoverBlur = () => {
    const value = ref.current?.value ?? "";
    const isVector = value.match(VectorValueRegex);
    const vectorId = isVector && isVector[2];

    if (isVector && !vectorId) {
      if (ref.current) {
        ref.current.value = prevValue.current;
      }
      setIsSelectingVector.off();
      return;
    }

    if (vectorId) {
      addColumn(index)(value, true);
      setIsSelectingVector.off();
      return;
    }
  };

  const onInputFocusOrClickHandler = (e: { currentTarget: { value: string } }) => {
    // [INFO] Store the current value, so it can be used to reset the input when receives invalid value
    prevValue.current = e.currentTarget.value;

    if (e.currentTarget.value.match(VectorValueRegex)) {
      setTimeout(() => {
        setIsSelectingVector.on();
      }, 10);
    }
  };

  if (isNaN(index)) return null;
  const infoMsg = getInfoMsg(defaultValue ?? undefined, prepay_models_copy?.[index]?.value);

  return (
    <Popover initialFocusRef={ref} isOpen={isSelectingVector} onClose={setIsSelectingVector.off}>
      {({ isOpen }) => (
        <>
          <PopoverTrigger>
            <HStack w="fit-content" role="group">
              <CAInput
                ref={ref}
                type="text"
                maxInputWidth="5.4rem"
                name={`columns.${index}`}
                defaultValue={defaultValue}
                info={infoMsg}
                onKeyDown={onVKeyPress}
                autoComplete="off"
                onFocus={onInputFocusOrClickHandler}
                onClick={onInputFocusOrClickHandler}
                // We need settimeout here as we want the blur to be called after vector selection is done
                onBlur={isOpen ? () => setTimeout(() => handlePopoverBlur(), 200) : handleInputBlur}
              />
              {!isLastInput && (
                <IconButton
                  _groupHover={{
                    visibility: "visible",
                  }}
                  visibility="hidden"
                  aria-label="Delete column"
                  position="absolute"
                  right="2"
                  variant="transparent"
                  size="xs"
                  icon={<BiTrash color={infoMsg.show ? "black" : deleteIconColor} />}
                  onClick={() => removeColumn(index)}
                />
              )}
            </HStack>
          </PopoverTrigger>

          <Portal>
            <PopoverContent ref={popoverRef}>
              <PopoverBody w="max-content">
                {isOpen && (
                  <PrepayVectorSelectorTable
                    mutate={mutateVectors}
                    userVectors={userVectors ?? []}
                    onTableItemSelect={handleVectorChange}
                  />
                )}
              </PopoverBody>
            </PopoverContent>
          </Portal>
        </>
      )}
    </Popover>
  );
};
