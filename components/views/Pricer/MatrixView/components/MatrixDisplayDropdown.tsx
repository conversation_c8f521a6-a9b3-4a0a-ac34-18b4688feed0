import React from "react";
import { HStack } from "@chakra-ui/react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { MatrixDisplayValueTypes } from "@/contexts/PageContexts/PricerMatrixPageContext/PricerMatrixPageContextTypes";
import { usePricerMatrixPage } from "@/contexts/PageContexts/PricerMatrixPageContext";

type DisplayDropdownOptionsType = {
  id: MatrixDisplayValueTypes;
  value: MatrixDisplayValueTypes;
  displayValue: string;
};

export const displayDropdownOptions: DisplayDropdownOptionsType[] = [
  {
    id: "price",
    value: "price",
    displayValue: "Price",
  },
  {
    id: "yield",
    value: "yield",
    displayValue: "Yield",
  },
  {
    id: "modified_duration",
    value: "modified_duration",
    displayValue: "Modified Duration",
  },
  {
    id: "dv01",
    value: "dv01",
    displayValue: "Modified DV01",
  },
  {
    id: "forward_yield",
    value: "forward_yield",
    displayValue: "Forward Yield",
  },
  {
    id: "forward_modified_duration",
    value: "forward_modified_duration",
    displayValue: "Forward Modified Duration",
  },
];

export const MatrixDisplayDropdown = () => {
  const {
    state: {
      matrixPageSettings: { matrix_display_value },
    },
    action: { setDisplayValue },
  } = usePricerMatrixPage();

  return (
    <HStack w="11.5rem" m="auto">
      <CASelectDropdown
        label="Display"
        value={matrix_display_value}
        name="matrix_display_value"
        options={displayDropdownOptions}
        onChange={(e) => setDisplayValue(e.target.value as MatrixDisplayValueTypes)}
        inputWidth="12rem"
      />
    </HStack>
  );
};
