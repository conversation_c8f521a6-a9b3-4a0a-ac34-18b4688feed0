import * as React from "react";
import { Box, Text } from "@chakra-ui/layout";
import { chakra } from "@chakra-ui/react";
import {
  ColDef,
  GetDetailRowDataParams,
  GridReadyEvent,
  ICellRendererParams,
  IDetailCellRendererParams,
  ITooltipParams,
} from "ag-grid-community";
import { getDiff, lightenColor } from "@/utils/helpers";
import colors from "@/design-system/theme/colors";
import { CA_Mastr_Api_v1_0_Models_BondPricing_Result, CA_Mastr_Api_v1_0_Models_BondRepline } from "@/utils/openapi";
import {
  DEFAULT_CATEGORIES,
  ReplineAttributeConfigDataType,
  comparisonResultType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { getCollateralAttributeDetailCols } from "@/utils/grid/PricingColumnData";
import { getColumnTypes } from "@/utils/grid/column";
import { CollateralAttrFields, DetailRowDataType, SliceIds } from "./types";

export const diffCollateralFlatPriceBgColor = colors.safetyOrange[500];
export const diffStructureFlatPriceBgColor = colors.turmericRoot[600];
export const diffTotalFlatPriceBgColor = colors.celloBlue[300];
export const diffReplineAlgoBgColor = colors.gemBlue[600];
export const totalBgColor = colors.magenta[500];

export const rowWithoutMasterDetail = [DEFAULT_CATEGORIES.ALL, DEFAULT_CATEGORIES.NONE, "total"];

export const getHighlightedTableCell = (value: string | number, bgColor: string) => (
  <Box>
    <Text transform={"translateX(4px)"}>
      <chakra.span px={"8px"} py={"2px"} minW={"50px"} display="inline" bgColor={bgColor} color={"white"}>
        {value}
      </chakra.span>
    </Text>
  </Box>
);

export const getColors = (value: string | number, colorPercentage: number) => {
  const highlightedBgColor =
    colorPercentage != null && Math.abs(Number(value)) > 0.09
      ? colorPercentage < 0
        ? lightenColor(colors.magenta["500"], 100 - -1 * colorPercentage)
        : lightenColor(colors.gemBlue["500"], 100 - colorPercentage)
      : undefined;
  const highlightedColor = highlightedBgColor
    ? Math.abs(colorPercentage ?? 0) < 75
      ? colors.celloBlue["1200"]
      : "white"
    : undefined;
  return {
    highlightedBgColor,
    highlightedColor,
  };
};

export const getDiffValues = (
  firstObj: CA_Mastr_Api_v1_0_Models_BondPricing_Result,
  secondObj: CA_Mastr_Api_v1_0_Models_BondPricing_Result
) => {
  const keys: Array<keyof CA_Mastr_Api_v1_0_Models_BondPricing_Result> = Object.keys(firstObj ?? {}) as Array<
    keyof CA_Mastr_Api_v1_0_Models_BondPricing_Result
  >;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const diff: Partial<Record<keyof CA_Mastr_Api_v1_0_Models_BondPricing_Result, any>> =
    {} as CA_Mastr_Api_v1_0_Models_BondPricing_Result;

  keys.forEach((key) => {
    if (typeof firstObj[key] === "number" && typeof secondObj[key] === "number")
      diff[key] = +getDiff(firstObj[key], secondObj[key]);
  });

  return diff as CA_Mastr_Api_v1_0_Models_BondPricing_Result;
};

export type AttributionDetailAddedFields = {
  id: string;
  structure: string;
  collateralCDU: string;
  replineAlgo: string;
};
/** Base data structure for attribution data -
 * [1]. primary_security - primary_security - default
 * [2]. primary_security - primary_security - single
 * [3]. primary_security - secondary_security - single
 * [4]. secondary_security - secondary_security - single
 * [5]. secondary_security - secondary_security - default
 */
export const getAttributionDetailData = (
  comparisionData: comparisonResultType,
  primary_security: string,
  secondary_security: string,
  repline_level: string,
  replineAlgo: string
) => {
  const { _default = [], collateral_primarySecurity = [], structure_primarySecurity = [] } = comparisionData || {};

  // results for [1]
  const primaryBondCompareResult = _default?.find(
    (result) => result.repline_algorithm === repline_level && result.input_security === primary_security
  )?.pricing_response?.results?.[0];

  // results for [5]
  const secondaryBondCompareResult = _default?.find(
    (result) => result.repline_algorithm === repline_level && result.input_security === secondary_security
  )?.pricing_response?.results?.[0];

  // results for [2]
  const primaryBondCollateralResult =
    collateral_primarySecurity?.find((row) => row.category === DEFAULT_CATEGORIES.NONE)?.pricing_response
      ?.results?.[0] ?? undefined;

  // results for [3]
  const primaryBondStructureResult =
    structure_primarySecurity?.find((row) => row.category === DEFAULT_CATEGORIES.ALL)?.pricing_response?.results?.[0] ??
    undefined;

  // results for [4]
  const secondaryBondStructureResult =
    structure_primarySecurity?.find((row) => row.category === DEFAULT_CATEGORIES.NONE)?.pricing_response
      ?.results?.[0] ?? undefined;

  // Prepare grid rows for all [1]...[5] data
  const PrimaryToPrimaryBondDefault: CA_Mastr_Api_v1_0_Models_BondPricing_Result & AttributionDetailAddedFields = {
    id: "1",
    structure: primary_security,
    collateralCDU: primary_security,
    replineAlgo,
    ...(primaryBondCompareResult ?? {}),
  };
  const PrimaryToPrimaryBondSingleCollateral: CA_Mastr_Api_v1_0_Models_BondPricing_Result &
    AttributionDetailAddedFields = {
    id: "2",
    structure: primary_security,
    collateralCDU: primary_security,
    replineAlgo: "Single",
    ...(primaryBondCollateralResult ?? {}),
  };
  const PrimaryToSecondayBondSingleStructure: CA_Mastr_Api_v1_0_Models_BondPricing_Result &
    AttributionDetailAddedFields = {
    id: "3",
    structure: primary_security,
    collateralCDU: secondary_security,
    replineAlgo: "Single",
    ...(primaryBondStructureResult ?? {}),
  };
  const SecondaryToSecondaryBondSingleStructure: CA_Mastr_Api_v1_0_Models_BondPricing_Result &
    AttributionDetailAddedFields = {
    id: "4",
    structure: secondary_security,
    collateralCDU: secondary_security,
    replineAlgo: "Single",
    ...(secondaryBondStructureResult ?? {}),
  };
  const SecondaryToSecondaryDefault: CA_Mastr_Api_v1_0_Models_BondPricing_Result & AttributionDetailAddedFields = {
    id: "5",
    structure: secondary_security,
    collateralCDU: secondary_security,
    replineAlgo,
    ...(secondaryBondCompareResult ?? {}),
  };

  const all = [
    PrimaryToPrimaryBondDefault,
    PrimaryToPrimaryBondSingleCollateral,
    PrimaryToSecondayBondSingleStructure,
    SecondaryToSecondaryBondSingleStructure,
    SecondaryToSecondaryDefault,
  ];

  // Structure is calculated by subtracting [4] from [3]
  const structure = [
    PrimaryToSecondayBondSingleStructure,
    SecondaryToSecondaryBondSingleStructure,
    {
      id: "3 - 4",
      structure: "Difference",
      collateralCDU: "",
      replineAlgo: "",
      ...getDiffValues(PrimaryToSecondayBondSingleStructure, SecondaryToSecondaryBondSingleStructure),
    },
  ];

  // Collateral is calculated by subtracting [3] from [2]
  const collateral = [
    PrimaryToPrimaryBondSingleCollateral,
    PrimaryToSecondayBondSingleStructure,
    {
      id: "2 - 3",
      structure: "Difference",
      collateralCDU: "",
      replineAlgo: "",
      ...getDiffValues(PrimaryToPrimaryBondSingleCollateral, PrimaryToSecondayBondSingleStructure),
    },
  ];

  const replineDefaultDiff = getDiffValues(PrimaryToPrimaryBondDefault, SecondaryToSecondaryDefault);
  const replineDefault = [
    PrimaryToPrimaryBondDefault,
    SecondaryToSecondaryDefault,
    {
      id: "1 - 5",
      structure: "Difference",
      collateralCDU: "",
      replineAlgo: "",
      ...replineDefaultDiff,
    },
  ];

  const replineSingleDiff = getDiffValues(
    PrimaryToPrimaryBondSingleCollateral,
    SecondaryToSecondaryBondSingleStructure
  );
  const replineSingle = [
    PrimaryToPrimaryBondSingleCollateral,
    SecondaryToSecondaryBondSingleStructure,
    {
      id: "2 - 4",
      structure: "Difference",
      collateralCDU: "",
      replineAlgo: "",
      ...replineSingleDiff,
    },
  ];

  const replineDefaultSingleDiff = getDiffValues(replineDefaultDiff, replineSingleDiff);
  const replineDefaultSingle: (CA_Mastr_Api_v1_0_Models_BondPricing_Result & AttributionDetailAddedFields)[] = [
    {
      id: "1 - 5",
      structure: primary_security,
      collateralCDU: primary_security,
      replineAlgo: "Single",
      ...getDiffValues(PrimaryToPrimaryBondDefault, PrimaryToPrimaryBondSingleCollateral),
    },
    {
      id: "2 - 4",
      structure: secondary_security,
      collateralCDU: secondary_security,
      replineAlgo: "Single",
      ...getDiffValues(SecondaryToSecondaryDefault, SecondaryToSecondaryBondSingleStructure),
    },
    {
      id: "",
      structure: "Difference",
      collateralCDU: "",
      replineAlgo: "",
      ...replineDefaultSingleDiff,
    },
  ];

  return {
    all,
    structure,
    replineDefault,
    replineDefaultSingle,
    replineSingle,
    collateral,
  };
};

const findDataBySliceId = (
  sliceId: string,
  primarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[],
  secondarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[]
) => {
  return (
    primarySecurityRepline.findIndex((row) => row.slice_id === sliceId) > -1 ||
    secondarySecurityRepline.findIndex((row) => row.slice_id === sliceId) > -1
  );
};

export const sliceDataAvailablity = (
  primarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[],
  secondarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[]
) => {
  return {
    hasConventional: findDataBySliceId(SliceIds.CONV, primarySecurityRepline, secondarySecurityRepline),
    hasFHA: findDataBySliceId(SliceIds.FHA, primarySecurityRepline, secondarySecurityRepline),
    hasVA: findDataBySliceId(SliceIds.VA, primarySecurityRepline, secondarySecurityRepline),
    hasRHS: findDataBySliceId(SliceIds.RHS, primarySecurityRepline, secondarySecurityRepline),
    hasPIH: findDataBySliceId(SliceIds.PIH, primarySecurityRepline, secondarySecurityRepline),
  };
};

type fieldType = number | string;
export const getReplineRowData = (
  primarySecurityReplineData: CA_Mastr_Api_v1_0_Models_BondRepline[],
  secondarySecurityReplineData: CA_Mastr_Api_v1_0_Models_BondRepline[],
  field: keyof CA_Mastr_Api_v1_0_Models_BondRepline,
  sliceId: string
) => {
  const primarySecurityRepline = primarySecurityReplineData.find((row) => row.slice_id === sliceId);
  const secondarySecurityRepline = secondarySecurityReplineData.find((row) => row.slice_id === sliceId);

  if (primarySecurityRepline?.[field] instanceof Array) return {};

  switch (sliceId) {
    case SliceIds.CONV:
      return {
        [CollateralAttrFields.CONV1]: primarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.CONV2]: secondarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.CONV_DIFF]: getDiff(
          primarySecurityRepline?.[field] as fieldType,
          secondarySecurityRepline?.[field] as fieldType
        ),
      };
    case SliceIds.FHA:
      return {
        [CollateralAttrFields.FHA1]: primarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.FHA2]: secondarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.FHA_DIFF]: getDiff(
          primarySecurityRepline?.[field] as fieldType,
          secondarySecurityRepline?.[field] as fieldType
        ),
      };
    case SliceIds.RHS:
      return {
        [CollateralAttrFields.RHS1]: primarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.RHS2]: secondarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.RHS_DIFF]: getDiff(
          primarySecurityRepline?.[field] as fieldType,
          secondarySecurityRepline?.[field] as fieldType
        ),
      };
    case SliceIds.VA:
      return {
        [CollateralAttrFields.VA1]: primarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.VA2]: secondarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.VA_DIFF]: getDiff(
          primarySecurityRepline?.[field] as fieldType,
          secondarySecurityRepline?.[field] as fieldType
        ),
      };
    case SliceIds.PIH:
      return {
        [CollateralAttrFields.PIH1]: primarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.PIH2]: secondarySecurityRepline?.[field] as fieldType,
        [CollateralAttrFields.PIH_DIFF]: getDiff(
          primarySecurityRepline?.[field] as fieldType,
          secondarySecurityRepline?.[field] as fieldType
        ),
      };
    default:
      return {};
  }
};

const tooltipValueGetter = (props: ITooltipParams) =>
  typeof props.value === "number"
    ? props.value?.toLocaleString("en-US", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 6,
      })
    : undefined;

const defaultColDef: ColDef = {
  width: 50,
  sortable: true,
  resizable: true,
  suppressHeaderMenuButton: true,
  maxWidth: 250,
  tooltipValueGetter,
};

const columnTypes = getColumnTypes("default");
export const REPLINE_CATEGORY_TO_COLUMN_TYPE = (category: string): "text" | "number0" | "number1" | "number2" => {
  switch (category) {
    case "ticker":
    case "wala":
    case "loan_size":
    case "orig_credit_score":
    case "llpa_pmi_mip":
    case "gn_llpa_pmi_mip":
    case "burnout":
    case "refi_eligibility":
    case "harp":
    case "pre_harp_pct":
    case "grandfather_pct":
    case "sato":
      return "number0";
    case "orig_ltv":
    case "curr_ltv":
    case "occupancy":
    case "origination_channel":
    case "gn_origination_channel":
    case "purpose":
    case "gn_purpose":
    case "piw":
    case "second_lien_pct":
    case "orig_dti":
    case "delinquency":
    case "gn_delinquency":
    case "servicer_buyout":
      return "number1";
    case "wac":
    case "state_unemployment":
      return "number2";
    case "weight":
      return "number0";
    case "servicer_distribution":
    case "geo":
    case "gn_geo":
      return "text";
    default:
      return "number2";
  }
};
export const getDetailCellRendererParams = ({
  primarySecurity,
  secondarySecurity,
  primarySecurityRepline,
  secondarySecurityRepline,
}: {
  primarySecurity: string | undefined;
  secondarySecurity: string | undefined;
  primarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[];
  secondarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[];
}) => {
  const detailGridOptions = {
    columnTypes,
    className: "ag-master-detail cagrid-condensed cagrid-center-cells",
    suppressColumnVirtualisation: true,
    defaultColDef,
    rowHeight: 32,
    onGridReady: (event: GridReadyEvent) => event.api.autoSizeAllColumns(false),
  } as IDetailCellRendererParams["detailGridOptions"];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getDetailRowData = (params: GetDetailRowDataParams & { context: any }) => {
    const { primarySecurityRepline, secondarySecurityRepline, replineAttributeConfig } = params.context || {};

    // check here whether we need to display convetional, rhs, va... or not
    const { hasConventional, hasRHS, hasVA, hasFHA, hasPIH } = sliceDataAvailablity(
      primarySecurityRepline,
      secondarySecurityRepline
    );

    // get attributes
    const replineAttr = replineAttributeConfig?.find(
      (c: ReplineAttributeConfigDataType) => c.value === params.data.category
    ) as ReplineAttributeConfigDataType;

    const rowData: DetailRowDataType[] = [];
    replineAttr.attributes
      ?.filter((attr) => attr.value !== "ticker") //ignore ticker for now
      ?.forEach((attr) => {
        rowData.push({
          attribute: attr.display_name ?? "",
          ...(hasConventional
            ? getReplineRowData(
                primarySecurityRepline,
                secondarySecurityRepline,
                attr.value as keyof CA_Mastr_Api_v1_0_Models_BondRepline,
                SliceIds.CONV
              )
            : {}),
          ...(hasFHA
            ? getReplineRowData(
                primarySecurityRepline,
                secondarySecurityRepline,
                attr.value as keyof CA_Mastr_Api_v1_0_Models_BondRepline,
                SliceIds.FHA
              )
            : {}),
          ...(hasRHS
            ? getReplineRowData(
                primarySecurityRepline,
                secondarySecurityRepline,
                attr.value as keyof CA_Mastr_Api_v1_0_Models_BondRepline,
                SliceIds.RHS
              )
            : {}),
          ...(hasVA
            ? getReplineRowData(
                primarySecurityRepline,
                secondarySecurityRepline,
                attr.value as keyof CA_Mastr_Api_v1_0_Models_BondRepline,
                SliceIds.VA
              )
            : {}),
          ...(hasPIH
            ? getReplineRowData(
                primarySecurityRepline,
                secondarySecurityRepline,
                attr.value as keyof CA_Mastr_Api_v1_0_Models_BondRepline,
                SliceIds.PIH
              )
            : {}),
        });
      });

    params.successCallback(rowData);
  };

  return (params: ICellRendererParams) => {
    const columnDefs =
      primarySecurity && secondarySecurity
        ? getCollateralAttributeDetailCols(
            primarySecurity,
            secondarySecurity,
            primarySecurityRepline,
            secondarySecurityRepline,
            REPLINE_CATEGORY_TO_COLUMN_TYPE(params.data.category)
          )
        : [];

    return {
      detailGridOptions: {
        ...detailGridOptions,
        columnDefs,
      },
      getDetailRowData,
    };
  };
};

// TODO: Move getTooltip functions around bond compare to this helper file.
