import React from "react";
import { VStack } from "@chakra-ui/react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { AttributeDetailColumnData } from "@/utils/grid/PricingColumnData";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import { comparisonResultType } from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { ApiError, CA_Mastr_Api_v1_0_Models_BondPricing_Result } from "@/utils/openapi";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { AttributionDetailAddedFields, getAttributionDetailData } from "../helpers";

export enum ActiveView {
  ALL = "all",
  COLLATERAL = "collateral",
  STRUCTURE = "structure",
  REPLINE_ALGO = "repline_algo",
}
export enum ReplineActiveView {
  DEFAULT = "default",
  SINGLE = "single",
  DEFAULT_SINGLE = "default-single",
}

interface AttributionDetailCardProps {
  comparisonResult: comparisonResultType | undefined;
  error: ApiError | undefined;
  replineAlgo: string;
}

export const AttributionDetailCard = ({ comparisonResult, replineAlgo, error }: AttributionDetailCardProps) => {
  const {
    state: {
      bond_name: primary_security,
      isTimerRunning,
      userSettingsCopy: { repline_level },
    },
  } = usePricerModule();
  const {
    state: {
      lastRun,
      bondComparePageSettings: { secondary_security: secondary_security },
    },
  } = usePricerBondComparePage();

  // STATE
  const [activeView, setActiveView] = React.useState<ActiveView>(ActiveView.COLLATERAL);
  const [replineActiveView, setReplineActiveView] = React.useState<ReplineActiveView>(ReplineActiveView.DEFAULT_SINGLE);

  // HELPERS
  const onChangeView = (view: ActiveView) => {
    // reset repline active view
    if (view === ActiveView.REPLINE_ALGO && replineActiveView !== ReplineActiveView.DEFAULT_SINGLE) {
      setReplineActiveView(ReplineActiveView.DEFAULT_SINGLE);
    }

    setActiveView(view);
  };

  // UI Component
  const DataToggle = React.useMemo(
    () => (
      <VStack alignItems={"flex-end"}>
        <ToggleButtonGroup
          buttons={[
            { label: "ALL", value: ActiveView.ALL },
            { label: "Collateral", value: ActiveView.COLLATERAL },
            { label: "Structure", value: ActiveView.STRUCTURE },
            { label: "Repline Algo", value: ActiveView.REPLINE_ALGO },
          ]}
          onChange={onChangeView}
          selectedButton={activeView}
        />

        {activeView === ActiveView.REPLINE_ALGO && (
          <ToggleButtonGroup
            buttons={[
              { label: replineAlgo, value: ReplineActiveView.DEFAULT },
              { label: "Single", value: ReplineActiveView.SINGLE },
              { label: `${replineAlgo} - Single`, value: ReplineActiveView.DEFAULT_SINGLE },
            ]}
            onChange={setReplineActiveView}
            selectedButton={replineActiveView}
          />
        )}
      </VStack>
    ),
    // Don't trigger rerender on onChangeView reinitialization
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeView, replineActiveView, replineAlgo]
  );

  // DATA
  const results = React.useMemo(() => {
    if (!comparisonResult || !primary_security || !secondary_security) return undefined;
    return getAttributionDetailData(
      comparisonResult,
      primary_security,
      secondary_security,
      repline_level ?? "",
      replineAlgo
    );
  }, [secondary_security, primary_security, comparisonResult, replineAlgo, repline_level]);

  const gridData = React.useMemo(() => {
    switch (activeView) {
      case ActiveView.ALL:
        return results?.all;
      case ActiveView.STRUCTURE:
        return results?.structure;
      case ActiveView.COLLATERAL:
        return results?.collateral;
      case ActiveView.REPLINE_ALGO:
        if (replineActiveView === ReplineActiveView.DEFAULT) return results?.replineDefault;
        if (replineActiveView === ReplineActiveView.SINGLE) return results?.replineSingle;
        if (replineActiveView === ReplineActiveView.DEFAULT_SINGLE) return results?.replineDefaultSingle;
    }
  }, [activeView, results, replineActiveView]);

  return (
    <CAGrid<CA_Mastr_Api_v1_0_Models_BondPricing_Result & AttributionDetailAddedFields>
      className="attribution-detail-grid"
      cardProps={{
        allowCollapse: true,
        cardKey: "comparison-attribution-detail",
        h: "fit-content",
        title: "Detail",
      }}
      showExpand={false}
      gridType="bond-compare-attribute-detail"
      hasRun={!!lastRun}
      hideSearch
      gridProps={{
        context: { activeView: activeView === ActiveView.REPLINE_ALGO ? replineActiveView : activeView },
        domLayout: "autoHeight",
        columnDefs: AttributeDetailColumnData,
        rowData: error ? [] : gridData,
        statusBar: undefined,
        enableCharts: false,
        loading: isTimerRunning && !comparisonResult?.allResult,
      }}
      headerStyles={{ mt: 1 }}
      headerActionButtons={DataToggle}
      initialMessage="Click ▷ to load Bond compare."
    />
  );
};
