import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { HStack, Text } from "@chakra-ui/layout";
import { Tooltip } from "@chakra-ui/tooltip";
import CACard from "@/design-system/molecules/CACard";
import { getBondCompareFormattedNumberFixed, getDiff } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { comparisonResultType } from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { ApiError } from "@/utils/openapi";

const getToolTip = (value: string) => {
  const isDisabled = value && value.length > 10 ? false : true;
  return (
    <HStack key="header" justifyContent="end" px={1}>
      <Tooltip label={value} isDisabled={isDisabled}>
        <Text
          variant="tableHead"
          maxW={75}
          isTruncated={!isDisabled}
          textAlign="end"
          noOfLines={isDisabled ? 1 : undefined}
        >
          {value}
        </Text>
      </Tooltip>
    </HStack>
  );
};

// eslint-disable-next-line @typescript-eslint/ban-types
type ReplineAlgoComparisonProps = {
  comparisonResult: comparisonResultType | undefined;
  error: ApiError | undefined;
};

const ReplineAlgoComparisonProjectionCard: React.FC<ReplineAlgoComparisonProps> = ({
  comparisonResult,
  error,
}: ReplineAlgoComparisonProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettingsCopy, bond_name: primary_security, isTimerRunning },
  } = usePricerModule();
  const {
    state: { bondComparePageSettings },
  } = usePricerBondComparePage();

  const ReplineAlgo = React.useMemo(() => {
    if (userSettingsCopy.repline_level) {
      const replineAlgo =
        metadata?.pricer_settings?.repline_level?.find((l) => l.value === userSettingsCopy.repline_level)
          ?.display_value ?? "";
      return replineAlgo;
    }
    return "Default";
  }, [userSettingsCopy.repline_level, metadata]);

  const headers: (string | React.JSX.Element)[] = [
    "",
    getToolTip(primary_security as string),
    getToolTip(bondComparePageSettings.secondary_security as string),
    "Diff",
  ];

  const defaultTableData = React.useMemo(() => {
    const { _default = [], single = [] } = comparisonResult || {};
    const rowData: {
      name: string;
      values: (string | number)[];
    }[] = [];
    const firstBondDefaultResult = _default.find((row) => row.input_security === primary_security)?.pricing_response
      ?.results?.[0];
    const secondBondDefaultResult = _default.find(
      (row) => row.input_security === bondComparePageSettings.secondary_security
    )?.pricing_response?.results?.[0];

    const firstBondSingleResult = single.find((row) => row.input_security === primary_security)?.pricing_response
      ?.results?.[0];
    const secondBondSingleResult = single.find(
      (row) => row.input_security === bondComparePageSettings.secondary_security
    )?.pricing_response?.results?.[0];

    const defaultValues = [
      getBondCompareFormattedNumberFixed(3)(firstBondDefaultResult?.price),
      getBondCompareFormattedNumberFixed(3)(secondBondDefaultResult?.price),
      getDiff(firstBondDefaultResult?.price, secondBondDefaultResult?.price, 3),
    ];
    if (defaultValues.find((dv) => dv && dv !== "-")) {
      rowData.push({
        name: ReplineAlgo,
        values: defaultValues,
      });
    }

    const singleValues = [
      getBondCompareFormattedNumberFixed(3)(firstBondSingleResult?.price),
      getBondCompareFormattedNumberFixed(3)(secondBondSingleResult?.price),
      getDiff(firstBondSingleResult?.price, secondBondSingleResult?.price, 3),
    ];
    if (singleValues.find((dv) => dv && dv !== "-")) {
      rowData.push({
        name: "Single",
        values: singleValues,
      });
    }

    if (rowData.length === 2) {
      const diffFirstBond = getDiff(firstBondDefaultResult?.price, firstBondSingleResult?.price, 3);
      const diffSecondBond = getDiff(secondBondDefaultResult?.price, secondBondSingleResult?.price, 3);
      rowData.push({
        name: `${ReplineAlgo} vs. Single`,
        values: [diffFirstBond, diffSecondBond, getDiff(diffFirstBond, diffSecondBond, 3)],
      });
    }
    return rowData;
  }, [comparisonResult, primary_security, bondComparePageSettings.secondary_security, ReplineAlgo]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard
      title={"REPLINE ALGO COMPARISON"}
      allowCollapse
      cardKey="bond-compare-repline-algo"
      cardBodyStyle={{ overflowX: "auto", minH: "134px" }}
      isPlaceholderCard={!!error}
    >
      {defaultTableData.length <= 0 && isTimerRunning ? (
        <Skeleton height="25px" />
      ) : (
        <CATable headers={headers} data={defaultTableData} headerStyles={{ textAlign: "right" }} />
      )}
    </CACard>
  );
};

export default React.memo(ReplineAlgoComparisonProjectionCard);
