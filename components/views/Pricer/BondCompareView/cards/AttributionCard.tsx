import * as React from "react";
import { Box, Skeleton } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import { ApiError } from "@/utils/openapi";
import CATable, { CATableDataType } from "@/design-system/molecules/CATable";
import { getBondCompareFormattedNumberFixed, getDiff } from "@/utils/helpers";
import {
  DEFAULT_CATEGORIES,
  comparisonResultType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import {
  diffCollateralFlatPriceBgColor,
  diffReplineAlgoBgColor,
  diffStructureFlatPriceBgColor,
  getHighlightedTableCell,
  totalBgColor,
} from "../helpers";

type AttributionCardProps = {
  comparisonResult: comparisonResultType | undefined;
  error: ApiError | undefined;
  replineAlgo: string;
};

type CompareDataType = CATableDataType[];

export const AttributionCard: React.FC<AttributionCardProps> = ({ comparisonResult, error }: AttributionCardProps) => {
  const {
    state: { bond_name: primary_security, isTimerRunning },
  } = usePricerModule();
  const {
    state: { bondComparePageSettings },
  } = usePricerBondComparePage();

  const { flatPriceDiffData = [] }: { flatPriceDiffData: CompareDataType } = React.useMemo(() => {
    const {
      collateral_primarySecurity = [],
      structure_primarySecurity = [],
      _default = [],
      single = [],
    } = comparisonResult || {};
    const flatPriceDiffData: CompareDataType = [];

    //Collateral
    const firstprimarySecurityResult =
      collateral_primarySecurity.find((row) => row.category === DEFAULT_CATEGORIES.NONE)?.pricing_response
        ?.results?.[0] ?? undefined;
    const secondprimarySecurityResult =
      collateral_primarySecurity.find((row) => row.category === DEFAULT_CATEGORIES.ALL)?.pricing_response
        ?.results?.[0] ?? undefined;
    const diffFlatePrice = getDiff(firstprimarySecurityResult?.price, secondprimarySecurityResult?.price) as number;

    // Structure
    const firstprimarySecurityStrResult =
      structure_primarySecurity.find((row) => row.category === DEFAULT_CATEGORIES.ALL)?.pricing_response
        ?.results?.[0] ?? undefined;
    const secondprimarySecurityStrResult =
      structure_primarySecurity.find((row) => row.category === DEFAULT_CATEGORIES.NONE)?.pricing_response
        ?.results?.[0] ?? undefined;
    const diffStrFlatePrice = getDiff(
      firstprimarySecurityStrResult?.price,
      secondprimarySecurityStrResult?.price
    ) as number;

    if (firstprimarySecurityResult && secondprimarySecurityResult) {
      flatPriceDiffData.push({
        name: "Collateral",
        values: [
          getHighlightedTableCell(
            getBondCompareFormattedNumberFixed(3)(diffFlatePrice),
            diffCollateralFlatPriceBgColor
          ),
        ],
      });
    }

    if (firstprimarySecurityStrResult && secondprimarySecurityStrResult) {
      flatPriceDiffData.push({
        name: "Structure",
        values: [
          getHighlightedTableCell(
            getBondCompareFormattedNumberFixed(3)(diffStrFlatePrice),
            diffStructureFlatPriceBgColor
          ),
        ],
      });
    }

    // Repline Algo
    const firstBondDefaultResult = _default.find((row) => row.input_security === primary_security)?.pricing_response
      ?.results?.[0];
    const secondBondDefaultResult = _default.find(
      (row) => row.input_security === bondComparePageSettings.secondary_security
    )?.pricing_response?.results?.[0];

    const firstBondSingleResult = single.find((row) => row.input_security === primary_security)?.pricing_response
      ?.results?.[0];
    const secondBondSingleResult = single.find(
      (row) => row.input_security === bondComparePageSettings.secondary_security
    )?.pricing_response?.results?.[0];

    const diffFirstBond = getDiff(firstBondSingleResult?.price, firstBondDefaultResult?.price);
    const diffSecondBond = getDiff(secondBondSingleResult?.price, secondBondDefaultResult?.price);
    const diffReplineAlgo = getDiff(diffSecondBond, diffFirstBond) as number;

    if (firstBondDefaultResult && secondBondDefaultResult && firstBondSingleResult && secondBondSingleResult) {
      flatPriceDiffData.push({
        name: `Repline Algo`,
        values: [
          getHighlightedTableCell(getBondCompareFormattedNumberFixed(3)(diffReplineAlgo), diffReplineAlgoBgColor),
        ],
      });
    }

    if (flatPriceDiffData.length) {
      let total: string | number = 0;
      if (!isNaN(Number(diffFlatePrice)) && !isNaN(Number(diffStrFlatePrice)) && !isNaN(diffReplineAlgo)) {
        total = getBondCompareFormattedNumberFixed(3)(
          Number(diffFlatePrice) + Number(diffStrFlatePrice) + Number(diffReplineAlgo)
        );
      } else {
        total = "-";
      }
      flatPriceDiffData.push({
        name: "Total",
        values: [getHighlightedTableCell(total, totalBgColor)],
      });
    }

    return {
      flatPriceDiffData,
    };
  }, [bondComparePageSettings.secondary_security, primary_security, comparisonResult]);

  return (
    <CACard
      title="ATTRIBUTION"
      allowCollapse
      cardKey="comparison-attribution"
      cardBodyStyle={{ overflowX: "auto" }}
      isPlaceholderCard={!!error}
    >
      {flatPriceDiffData.length <= 0 && isTimerRunning ? (
        <Skeleton h="25px" />
      ) : (
        <Box w={{ base: "10rem", lg: "10rem" }}>
          <CATable data={flatPriceDiffData} />
        </Box>
      )}
    </CACard>
  );
};
