import * as React from "react";
import { Box, Skeleton, Text } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import { comparisonResultType } from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { ApiError } from "@/utils/openapi";
import CATable from "@/design-system/molecules/CATable";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import { getBondCompareFormattedNumberFixed } from "@/utils/helpers";
import { getHighlightedTableCell, totalBgColor } from "../helpers";

type PriceCardProps = {
  comparisonResult: comparisonResultType | undefined;
  error: ApiError | undefined;
  replineAlgo: string;
};

export const PriceCard: React.FC<PriceCardProps> = ({ comparisonResult, error }: PriceCardProps) => {
  const {
    state: {
      bond_name: primary_security,
      isTimerRunning,
      userSettingsCopy: { repline_level },
    },
  } = usePricerModule();
  const {
    state: { bondComparePageSettings },
  } = usePricerBondComparePage();

  const { firstBondFlatPrice, secondBondFlatPrice, total } = React.useMemo(() => {
    const firstBondDefaultResult = comparisonResult?._default?.find(
      (result) => result.repline_algorithm === repline_level && result.input_security === primary_security
    )?.pricing_response?.results?.[0];
    const secondBondDefaultResult = comparisonResult?._default?.find(
      (result) =>
        result.repline_algorithm === repline_level &&
        result.input_security === bondComparePageSettings.secondary_security
    )?.pricing_response?.results?.[0];

    const firstBondFlatPrice = getBondCompareFormattedNumberFixed(3)(firstBondDefaultResult?.price);
    const secondBondFlatPrice = getBondCompareFormattedNumberFixed(3)(secondBondDefaultResult?.price);
    const total =
      isNaN(Number(firstBondFlatPrice)) && isNaN(Number(secondBondFlatPrice))
        ? "-"
        : getBondCompareFormattedNumberFixed(3)(
            Number(firstBondDefaultResult?.price) - Number(secondBondDefaultResult?.price)
          );
    return {
      firstBondFlatPrice,
      secondBondFlatPrice,
      total,
    };
  }, [bondComparePageSettings.secondary_security, primary_security, comparisonResult?._default, repline_level]);

  return (
    <CACard
      title="PRICE"
      allowCollapse
      cardKey="comparison-price"
      cardBodyStyle={{ overflowX: "auto" }}
      isPlaceholderCard={!!error}
    >
      {total === "-" && isTimerRunning ? (
        <Skeleton h="25px" />
      ) : (
        <Box maxW="20rem">
          <CATable
            data={[
              {
                name: nameColumnTextWrapper("Primary Security"),
                values: [
                  nameValueColumnWrapper(primary_security),
                  <Text maxW="4rem" variant="default" key="primary-flat-price">
                    {firstBondFlatPrice ?? "-"}
                  </Text>,
                ],
              },
              {
                name: nameColumnTextWrapper("Secondary Security"),
                values: [
                  nameValueColumnWrapper(bondComparePageSettings.secondary_security),
                  <Text maxW="4rem" variant="default" key="secondary-flat-price">
                    {secondBondFlatPrice ?? "-"}
                  </Text>,
                ],
              },
              {
                name: nameColumnTextWrapper("Difference"),
                values: [
                  <Text variant="default" textAlign="left" key="diff-bond" w="10rem">
                    {primary_security}{" "}
                    <Box display={"inline-block"}>- {bondComparePageSettings.secondary_security}</Box>
                  </Text>,
                  <Text w="4rem" variant="default" key="difference-flat-price">
                    {getHighlightedTableCell(total, totalBgColor)}
                  </Text>,
                ],
              },
            ]}
          />
        </Box>
      )}
    </CACard>
  );
};

const nameColumnTextWrapper = (text: string) => (
  <Text textAlign="left" minW={"8rem"} variant="tableLeft">
    {text}
  </Text>
);

const nameValueColumnWrapper = (text?: string) => (
  <Text variant="default" textAlign="left" minW="8rem">
    {text ?? "-"}
  </Text>
);
