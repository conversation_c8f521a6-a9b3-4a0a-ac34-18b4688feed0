import { Box, Text } from "@chakra-ui/layout";
import * as React from "react";
import { Tooltip } from "@chakra-ui/tooltip";
import { HTMLChakraProps, ThemingProps } from "@chakra-ui/system";
import { Flex, chakra } from "@chakra-ui/react";
import CATable from "@/design-system/molecules/CATable";
import colors from "@/design-system/theme/colors";
import { getBondCompareFormattedNumberFixed, getDiff, lightenColor } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_BondRepline } from "@/utils/openapi";
import { ReplineAttributeConfigDataType } from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { REPLINE_CATEGORY_TO_COLUMN_TYPE } from "../helpers";

type ReplineTableProps = {
  primarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[];
  secondarySecurityRepline: CA_Mastr_Api_v1_0_Models_BondRepline[];
  replineDataRow: ReplineAttributeConfigDataType;
  sliceId: string;
  showHeader?: boolean;
};

type NAHReplineTableProps = {
  replineDataRow: ReplineAttributeConfigDataType;
  showHeader?: boolean;
  replineDataLength: number;
};

type TableRecordType = {
  name: string | React.JSX.Element;
  values: (string | number | React.JSX.Element)[];
};

interface RowTextProps extends HTMLChakraProps<"p">, ThemingProps<"Text"> {
  value: string | number;
  variant?: string;
  textAlign?: "right" | "left" | "center";
  w?: number | string;
  h?: number | string;
  isTruncated?: boolean;
  colorPercentage?: number;
  cellBgColor?: string;
  cellTextColor?: string;
}

const RowText: React.FC<RowTextProps> = ({
  value,
  variant,
  textAlign,
  w,
  h,
  isTruncated,
  colorPercentage,
  cellBgColor,
  cellTextColor,
  ...props
}: RowTextProps) => {
  const isDisabled = typeof value === "string" && isNaN(Number(value)) && value.length > 10 ? true : false;
  const highlightedBgColor =
    colorPercentage != null && Math.abs(Number(value)) > 0.09
      ? colorPercentage < 0
        ? lightenColor(colors.magenta["500"], 100 - -1 * colorPercentage)
        : lightenColor(colors.gemBlue["500"], 100 - colorPercentage)
      : undefined;
  const highlightedColor = highlightedBgColor
    ? Math.abs(colorPercentage ?? 0) < 75
      ? "celloBlue.1200"
      : "white"
    : undefined;
  return (
    <Tooltip label={value} isDisabled={!isDisabled}>
      <Text
        w={w ?? 95}
        h={h}
        textAlign={textAlign ?? "left"}
        variant={variant ?? "default"}
        verticalAlign="top"
        textTransform="initial"
        isTruncated={isTruncated}
        {...props}
      >
        <chakra.span
          px={"8px"}
          py={"2px"}
          minW={"50px"}
          display="inline"
          bgColor={cellBgColor ?? highlightedBgColor}
          color={cellTextColor ?? highlightedColor}
        >
          {value}
        </chakra.span>
      </Text>
    </Tooltip>
  );
};

const NAHReplineTableComponent: React.FC<NAHReplineTableProps> = ({
  replineDataRow,
  showHeader = true,
  replineDataLength,
}: NAHReplineTableProps) => {
  const repliAttributeHeader = showHeader
    ? [
        <RowText key="category" h={"1.3rem"} value="Category" w={150} textAlign="left" variant="tableLeft" />,
        <RowText key="attributes" value={"Attributes"} w={250} textAlign={"left"} variant="tableLeft" h={"1.3rem"} />,
      ]
    : [];

  const repliAttribute = React.useMemo(() => {
    const NAHReplineData: TableRecordType[] = [];
    const categoryName = replineDataRow.displayValue;
    replineDataRow?.attributes?.forEach((row, index) => {
      NAHReplineData.push({
        name: (
          <RowText
            key={`name${index}`}
            w={150}
            textAlign="left"
            variant={"cardHeaderParent"}
            isTruncated
            value={index === 0 ? categoryName ?? "" : ""}
          />
        ),
        values: [
          <RowText
            key={`value${index}`}
            w={250}
            textAlign="left"
            variant="tableLeft"
            isTruncated
            ml={""}
            value={row.value ?? ""}
          />,
        ],
      });
    });
    return NAHReplineData;
  }, [replineDataRow]);

  if (repliAttribute && repliAttribute.length) {
    const headerstring = showHeader && replineDataLength > 1 ? "NMA Repline Attributes" : "";
    return (
      <Box w="full">
        {/* This empty box is needed to provide padding for extra heading row for GNM bonds */}
        {headerstring && <Text variant="tableHead" textAlign="left" h="1.2rem" />}
        <CATable
          headers={repliAttributeHeader}
          bodyStyles={{ variant: "default" }}
          headerStyles={{ variant: "tableLeft", backgroundColor: "initial" }}
          data={repliAttribute}
        />
      </Box>
    );
  } else {
    return null;
  }
};

const AttributeTableComponent: React.FC<ReplineTableProps> = ({
  primarySecurityRepline,
  secondarySecurityRepline,
  replineDataRow,
  sliceId,
  showHeader = true,
}: ReplineTableProps) => {
  const {
    state: { bond_name: primary_security },
  } = usePricerModule();
  const {
    state: { bondComparePageSettings },
  } = usePricerBondComparePage();
  const AttributeTableHeader = showHeader
    ? [
        <RowText
          key={`primarySecurity${sliceId}`}
          value={primary_security ?? ""}
          textAlign="left"
          variant="tableLeft"
          h={"1.3rem"}
          isTruncated={true}
        />,
        <RowText
          key={`secondarySecurity${sliceId}`}
          value={bondComparePageSettings.secondary_security ?? ""}
          textAlign="center"
          variant="tableLeft"
          h={"1.3rem"}
          isTruncated={true}
        />,
        <RowText key={`diff${sliceId}`} value={"Diff"} variant="tableLeft" textAlign="right" h={"1.3rem"} />,
      ]
    : [];
  const attributeData = React.useMemo(() => {
    const AttrTableData: TableRecordType[] = [];
    const indexprimarySecurity = primarySecurityRepline.findIndex((row) => row.slice_id === sliceId);
    const indexsecondarySecurity = secondarySecurityRepline.findIndex((row) => row.slice_id === sliceId);
    if (indexprimarySecurity > -1 && indexsecondarySecurity > -1) {
      const primarySecurityReplineData: CA_Mastr_Api_v1_0_Models_BondRepline =
        primarySecurityRepline[indexprimarySecurity];
      const secondarySecurityReplineData: CA_Mastr_Api_v1_0_Models_BondRepline =
        secondarySecurityRepline[indexsecondarySecurity];

      replineDataRow?.attributes?.forEach((row) => {
        const categoryValue = (row.value ?? "") as keyof CA_Mastr_Api_v1_0_Models_BondRepline;

        const columnType = REPLINE_CATEGORY_TO_COLUMN_TYPE(replineDataRow.value);
        const numberFixed = (function (columnType) {
          switch (columnType) {
            case "number1":
              return 1;
            case "number2":
              return 2;
            default:
              return 0;
          }
        })(columnType);

        const col1Value = !isNaN(Number(primarySecurityReplineData[categoryValue]))
          ? getBondCompareFormattedNumberFixed(numberFixed)(primarySecurityReplineData[categoryValue] as number)
          : (primarySecurityReplineData[categoryValue] as string);
        const col2Value = !isNaN(Number(secondarySecurityReplineData[categoryValue]))
          ? getBondCompareFormattedNumberFixed(numberFixed)(secondarySecurityReplineData[categoryValue] as number)
          : (secondarySecurityReplineData[categoryValue] as string);
        const diff = getBondCompareFormattedNumberFixed(numberFixed)(
          +getDiff(
            primarySecurityReplineData[categoryValue] as number,
            secondarySecurityReplineData[categoryValue] as number
          )
        );
        AttrTableData.push({
          name: <RowText key={`name${sliceId}`} value={col1Value} textAlign="right" isTruncated />,
          values: [
            <RowText key={`val1${sliceId}`} value={col2Value} textAlign="right" isTruncated />,
            <RowText key={`val2${sliceId}`} value={diff} textAlign="right" isTruncated />,
          ],
        });
      });
    }
    return AttrTableData;
  }, [primarySecurityRepline, secondarySecurityRepline, replineDataRow, sliceId]);

  if (attributeData.length) {
    const headerstring = showHeader && primarySecurityRepline.length > 1 ? sliceId.replace("TICKER_LOANTYPE:", "") : "";
    return (
      <Box w="full">
        {headerstring && (
          <Text variant="tableHead" textAlign="center" h="1.2rem">
            {headerstring}
          </Text>
        )}
        <CATable
          headers={AttributeTableHeader}
          bodyStyles={{ variant: "default" }}
          headerStyles={{ variant: "tableLeft", backgroundColor: "initial" }}
          data={attributeData}
          activeHover={false}
        />
      </Box>
    );
  } else {
    return null;
  }
};

const CollateralCDUComponent: React.FC = () => {
  const {
    state: { bondComparePageSettings },
  } = usePricerBondComparePage();
  const {
    state: { bond_name: primary_security },
  } = usePricerModule();
  const CollateralCDUHeader = ["", "Initial Step", "Intermediate Steps", "Final Step"];
  const StructureReplineCDUHeader = ["", "Structure"];
  const ReplineAlgoHeader = ["", "Repline Algo"];

  const StrucureReplineCDUData = React.useMemo(() => {
    return [
      {
        name: "",
        values: [primary_security ?? ""],
      },
    ];
  }, [primary_security]);

  const CollateralCDUData = React.useMemo(() => {
    return [
      {
        name: "",
        values: [primary_security ?? "", "Category Override", bondComparePageSettings.secondary_security ?? ""],
      },
    ];
  }, [primary_security, bondComparePageSettings.secondary_security]);

  const ReplineAlgoData = React.useMemo(() => {
    return [
      {
        name: "",
        values: ["Single"],
      },
    ];
  }, []);

  return (
    <Flex borderRadius="md" justify="flex-start" mb="5">
      <Box p="5">
        <Flex justifyContent="center" flexWrap="wrap" gridGap={{ sm: "10", md: "20" }}>
          <Box>
            <Text variant="tableHead" textAlign="center" h="1.6rem" />
            <CATable
              variant="caBasicCentered"
              headers={StructureReplineCDUHeader}
              bodyStyles={{ variant: "default" }}
              headerStyles={{ variant: "tableLeft", backgroundColor: "initial" }}
              data={StrucureReplineCDUData}
              activeHover={false}
            />
          </Box>
          <Box>
            <Text variant="tableHead" textAlign="center" h="1.6rem">
              {"COLLATERAL CDU"}
            </Text>
            <CATable
              variant="caBasicCentered"
              headers={CollateralCDUHeader}
              bodyStyles={{ variant: "default" }}
              headerStyles={{ variant: "tableLeft", backgroundColor: "initial" }}
              data={CollateralCDUData}
              activeHover={false}
            />
          </Box>
          <Box>
            <Text variant="tableHead" textAlign="center" h="1.6rem" />
            <CATable
              variant="caBasicCentered"
              headers={ReplineAlgoHeader}
              bodyStyles={{ variant: "default" }}
              headerStyles={{ variant: "tableLeft", backgroundColor: "initial" }}
              data={ReplineAlgoData}
              activeHover={false}
            />
          </Box>
        </Flex>
      </Box>
    </Flex>
  );
};

export const NAHReplineTable = React.memo(NAHReplineTableComponent);
export const AttributeTable = React.memo(AttributeTableComponent);
export const CollateralCDU = React.memo(CollateralCDUComponent);
