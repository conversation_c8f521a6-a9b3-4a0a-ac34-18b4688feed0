import * as React from "react";
import { Box } from "@chakra-ui/layout";
import {
  CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse,
  CA_Mastr_Api_v1_0_Models_Repline_ReplineAttributeConfigInfo_ReplineAttributeConfigDef,
} from "@/utils/openapi";
import {
  DEFAULT_CATEGORIES,
  ReplineAttributeConfigDataType,
  comparisonResultType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import CollateralAttribute from "./CollateralAttribute";

type CollateralCategoriesProps = {
  comparisonResult: comparisonResultType | undefined;
  indicativeResponses?: Array<CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse> | null;
};

const CollateralCategories: React.FC<CollateralCategoriesProps> = ({
  comparisonResult,
  indicativeResponses,
}: CollateralCategoriesProps) => {
  const {
    state: { security_info, bond_name: primary_security },
  } = usePricerModule();
  const {
    state: {
      replineAttributeConfig,
      bondComparePageSettings: { secondary_security: secondary_security },
    },
  } = usePricerBondComparePage();

  const responseDataPrimarySecurity = indicativeResponses?.find((r) => r.input_security === primary_security);
  const responseDataSecondarySecurity = indicativeResponses?.find((r) => r.input_security === secondary_security);

  const replineAttributeConfigData: ReplineAttributeConfigDataType[] = React.useMemo(() => {
    let configData:
      | Array<CA_Mastr_Api_v1_0_Models_Repline_ReplineAttributeConfigInfo_ReplineAttributeConfigDef>
      | undefined
      | null = undefined;
    const attributeConfigData: ReplineAttributeConfigDataType[] = [];
    if (security_info?.sub_type) {
      configData = replineAttributeConfig?.filter(
        (item) => !!(item.value && item.agencies?.includes(security_info?.sub_type ?? ""))
      );
      attributeConfigData.push({
        id: "none",
        value: "none",
        categories: "None",
      });
      configData?.forEach((row, index) => {
        attributeConfigData.push({
          ...row,
          id: row.value ?? `${index}`,
          value: row.value as string,
          displayValue: row.display_name ?? "",
          attributes: row.attributes ?? [],
          categories: [{ id: row.value ?? `${index}`, value: row.value as string, displayValue: row.display_name }],
        });
      });
      if (attributeConfigData.length) {
        attributeConfigData.push({
          id: DEFAULT_CATEGORIES.ALL,
          value: DEFAULT_CATEGORIES.ALL,
          categories: "All",
        });
      }
    }
    return attributeConfigData;
  }, [security_info?.sub_type, replineAttributeConfig]);

  return (
    <Box position="relative" display={replineAttributeConfigData.length > 0 ? "block" : "none"}>
      <CollateralAttribute
        replineAttributeConfig={replineAttributeConfigData}
        responseDataPrimarySecurity={responseDataPrimarySecurity}
        responseDataSecondarySecurity={responseDataSecondarySecurity}
        comparisonResult={comparisonResult}
      />
    </Box>
  );
};

export default CollateralCategories;
