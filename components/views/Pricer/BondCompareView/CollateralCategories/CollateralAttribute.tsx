import * as React from "react";
import { Button, ButtonGroup } from "@chakra-ui/react";
import {
  ReplineAttributeConfigDataType,
  comparisonResultType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse } from "@/utils/openapi";
import { DataFormat } from "@/utils/grid/PricingColumnData";
import { Grid } from "./Grid";

export type CollateralAttributeProps = {
  headerActionButton: React.JSX.Element | undefined;
  comparisonResult: comparisonResultType | undefined;
  responseDataPrimarySecurity: CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse | null | undefined;
  replineAttributeConfig: ReplineAttributeConfigDataType[];
  responseDataSecondarySecurity: CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse | null | undefined;
};

const CollateralAttribute: React.FC<Omit<CollateralAttributeProps, "headerActionButton">> = ({
  comparisonResult,
  responseDataPrimarySecurity,
  replineAttributeConfig,
  responseDataSecondarySecurity,
}) => {
  const [tabIndex, setTabIndex] = React.useState(1);

  const headerActionButtons = React.useMemo(() => {
    return (
      <ButtonGroup size="sm" isAttached variant="outline">
        <Button onClick={() => setTabIndex(0)} fontSize="sm" variant={tabIndex === 0 ? "primary" : "secondary"}>
          ABSOLUTE
        </Button>
        <Button onClick={() => setTabIndex(1)} fontSize="sm" variant={tabIndex === 1 ? "primary" : "secondary"}>
          DIFFERENCE
        </Button>
      </ButtonGroup>
    );
  }, [tabIndex]);

  return (
    <>
      <Grid
        dataFormat={tabIndex === 0 ? DataFormat.ABSOLUTE : DataFormat.DIFF}
        replineAttributeConfig={replineAttributeConfig}
        responseDataPrimarySecurity={responseDataPrimarySecurity}
        responseDataSecondarySecurity={responseDataSecondarySecurity}
        comparisonResult={comparisonResult}
        headerActionButton={headerActionButtons}
      />
    </>
  );
};

export default CollateralAttribute;
