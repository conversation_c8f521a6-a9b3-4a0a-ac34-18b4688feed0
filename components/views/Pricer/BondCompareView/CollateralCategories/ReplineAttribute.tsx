import * as React from "react";
import { Box, HStack, Text } from "@chakra-ui/layout";
import { debounce } from "lodash";
import { useColorModeValue } from "@chakra-ui/color-mode";
import {
  DEFAULT_CATEGORIES,
  ReplineAttributeConfigDataType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse } from "@/utils/openapi";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import CASearch from "@/design-system/molecules/CASearch";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { SliceIds } from "../types";
import { AttributeTable, NAHReplineTable } from "./AttributeTables";

type ReplineAttributeProps = {
  replineAttributeConfig: ReplineAttributeConfigDataType[];
  responseDataPrimarySecurity: CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse | null | undefined;
  responseDataSecondarySecurity: CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse | null | undefined;
};

//util function
const doesReplineAttributeMatchesAttributesOrCategories = (
  valueToFilter: string,
  replineAttributeConfigDataType: ReplineAttributeConfigDataType
): boolean => {
  const regex = new RegExp(valueToFilter, "i");

  //search in attributes
  if (replineAttributeConfigDataType.attributes)
    for (const at of replineAttributeConfigDataType.attributes) {
      //check by value and by displayed value
      if (at.value && regex.test(at.value)) return true;
      if (at.display_name && regex.test(at.display_name)) return true;
    }

  //search in categories. If category is a string then check it directly
  if (typeof replineAttributeConfigDataType.categories === "string") {
    if (regex.test(replineAttributeConfigDataType.categories)) return true;
  } else {
    for (const cat of replineAttributeConfigDataType.categories) {
      //check by value and by displayed value
      if (cat.value && regex.test(cat.value.toString())) return true;
      if (cat.displayValue && regex.test(cat.displayValue.toString())) return true;
    }
  }

  return false;
};

const ReplineAttribute: React.FC<ReplineAttributeProps> = ({
  replineAttributeConfig,
  responseDataPrimarySecurity,
  responseDataSecondarySecurity,
}: ReplineAttributeProps) => {
  const {
    state: { security_info },
  } = usePricerModule();
  const {
    state: { replineAttributeFilter, userCategorySelections },
    action: { setReplineAttributeFilter },
  } = usePricerBondComparePage();

  const altRowBgColor = useColorModeValue("misty.300", "celloBlue.900");
  const primarySecurityRepline = React.useMemo(() => {
    return responseDataPrimarySecurity?.bond_replines ?? [];
  }, [responseDataPrimarySecurity]);

  const secondarySecurityRepline = React.useMemo(() => {
    return responseDataSecondarySecurity?.bond_replines ?? [];
  }, [responseDataSecondarySecurity]);

  const replineAttrBasedOnUserCategorySelections: ReplineAttributeConfigDataType[] = React.useMemo(() => {
    if (security_info?.sub_type) {
      if (userCategorySelections && userCategorySelections.length > 0) {
        return userCategorySelections
          .filter((category) => category !== DEFAULT_CATEGORIES.ALL && category !== DEFAULT_CATEGORIES.NONE)
          .map((value) => value && replineAttributeConfig.find((c) => c.value === value))
          .filter((c) => !!c) as ReplineAttributeConfigDataType[];
      }
    }

    return replineAttributeConfig;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [security_info?.sub_type, replineAttributeConfig]);

  const filteredReplineAttrBasedOnUserCategorySelections: ReplineAttributeConfigDataType[] = React.useMemo(() => {
    //if user has typed nothing (or only white spaces) do not make deep filtering
    if (replineAttributeFilter.trim() === "") return replineAttrBasedOnUserCategorySelections;

    return replineAttrBasedOnUserCategorySelections.filter((c) => {
      if (c) return doesReplineAttributeMatchesAttributesOrCategories(replineAttributeFilter, c);
      else return true;
    }) as ReplineAttributeConfigDataType[];
  }, [replineAttrBasedOnUserCategorySelections, replineAttributeFilter]);

  const debouncedChangeHandler = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setReplineAttributeFilter(e.target.value);
  }, 250);

  return (
    <>
      <Box ml="1px" mb={6}>
        <CASearch
          name="filter-categories-and-attributes"
          maxW="15rem"
          onChange={debouncedChangeHandler}
          placeholder={"Quick Filter"}
          defaultValue={replineAttributeFilter}
        />
      </Box>
      {filteredReplineAttrBasedOnUserCategorySelections.map((row, index) => {
        const headerIndex = 0;
        const altRowBgColorIndex = 1;
        return (
          <Box bg={index % 2 === altRowBgColorIndex ? altRowBgColor : "inherit"} key={`index-${index}`}>
            <HStack
              spacing={6}
              bg={index % 2 === altRowBgColorIndex ? altRowBgColor : "inherit"}
              w="fit-content"
              alignItems="flex-start"
              key={index}
            >
              {primarySecurityRepline && primarySecurityRepline.length <= 0 ? (
                <Text>No data available</Text>
              ) : (
                <>
                  {primarySecurityRepline && primarySecurityRepline.length && (
                    <NAHReplineTable
                      showHeader={index === headerIndex}
                      replineDataRow={row}
                      replineDataLength={primarySecurityRepline.length}
                    />
                  )}
                  {primarySecurityRepline && secondarySecurityRepline && primarySecurityRepline.length && (
                    <AttributeTable
                      replineDataRow={row}
                      primarySecurityRepline={primarySecurityRepline}
                      sliceId={SliceIds.FHA}
                      showHeader={index === headerIndex}
                      secondarySecurityRepline={secondarySecurityRepline}
                    />
                  )}
                  {primarySecurityRepline && secondarySecurityRepline && primarySecurityRepline.length && (
                    <AttributeTable
                      replineDataRow={row}
                      sliceId={SliceIds.VA}
                      primarySecurityRepline={primarySecurityRepline}
                      showHeader={index === headerIndex}
                      secondarySecurityRepline={secondarySecurityRepline}
                    />
                  )}
                  {primarySecurityRepline && secondarySecurityRepline && primarySecurityRepline.length && (
                    <AttributeTable
                      replineDataRow={row}
                      sliceId={SliceIds.RHS}
                      primarySecurityRepline={primarySecurityRepline}
                      showHeader={index === headerIndex}
                      secondarySecurityRepline={secondarySecurityRepline}
                    />
                  )}
                  {primarySecurityRepline && secondarySecurityRepline && primarySecurityRepline.length && (
                    <AttributeTable
                      replineDataRow={row}
                      sliceId={SliceIds.PIH}
                      primarySecurityRepline={primarySecurityRepline}
                      showHeader={index === headerIndex}
                      secondarySecurityRepline={secondarySecurityRepline}
                    />
                  )}
                  {primarySecurityRepline && secondarySecurityRepline && primarySecurityRepline.length && (
                    <AttributeTable
                      replineDataRow={row}
                      sliceId={SliceIds.CONV}
                      primarySecurityRepline={primarySecurityRepline}
                      showHeader={index === headerIndex}
                      secondarySecurityRepline={secondarySecurityRepline}
                    />
                  )}
                </>
              )}
            </HStack>
          </Box>
        );
      })}
    </>
  );
};

export default ReplineAttribute;
