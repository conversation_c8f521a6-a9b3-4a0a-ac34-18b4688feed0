import { AgGridReact } from "ag-grid-react";
import React from "react";
import { GetRowIdParams } from "ag-grid-community";
import { IconButton, <PERSON>u, <PERSON>uButton, <PERSON>u<PERSON><PERSON>, <PERSON>uList, Skeleton } from "@chakra-ui/react";
import { IoDocumentTextOutline, IoEllipsisVerticalOutline, IoInformationCircleOutline } from "react-icons/io5";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import {
  DEFAULT_CATEGORIES,
  allResultType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import CAGrid from "@/design-system/molecules/CAGrid";
import { BondCompareCollateralAttributeColumns, DataFormat } from "@/utils/grid/PricingColumnData";
import { getDiff } from "@/utils/helpers";
import CAIcon from "@/design-system/atoms/CAIcon";
import CAModal from "@/design-system/molecules/CAModal";
import { getDetailCellRendererParams, rowWithoutMasterDetail } from "../helpers";
import { CollateralAttributeProps } from "./CollateralAttribute";
import ReplineAttribute from "./ReplineAttribute";
import { CollateralCDU } from "./AttributeTables";

const colDef = {
  flex: 1,
};

export const Grid: React.FC<CollateralAttributeProps & { dataFormat: DataFormat }> = ({
  comparisonResult,
  responseDataPrimarySecurity,
  replineAttributeConfig,
  responseDataSecondarySecurity,
  dataFormat,
  headerActionButton,
}) => {
  const {
    state: { security_info, isTimerRunning, bond_name: primarySecurity },
  } = usePricerModule();
  const {
    state: {
      lastRun,
      userCategorySelections,
      replineAttributeConfig: defaultReplineAttrConfig,
      bondComparePageSettings: { secondary_security: secondarySecurity },
    },
  } = usePricerBondComparePage();

  const [modalViewMode, setModalViewMode] = React.useState<"CollateralCDU" | "ReplineAttributes" | null>(null);

  const gridRef = React.useRef<AgGridReact>(null);

  const placeholderGridRows = React.useMemo(
    () => [
      { step: "0", category: DEFAULT_CATEGORIES.NONE, display_category: "None" },
      ...userCategorySelections
        .filter((category) => category !== DEFAULT_CATEGORIES.NONE && category !== DEFAULT_CATEGORIES.ALL)
        .map((category, index) => {
          const displayValue = defaultReplineAttrConfig?.find((r) => r.value === category)?.display_name;
          return { step: `${index + 1}`, category, display_category: displayValue };
        }),
      { step: `${userCategorySelections.length + 1}`, category: DEFAULT_CATEGORIES.ALL, display_category: "Residual" },
    ],
    [userCategorySelections, defaultReplineAttrConfig]
  );

  // calculation to find diff & cumm_diff value
  const results = React.useMemo(() => {
    const placeholderResultArr = placeholderGridRows.map((row) => {
      return {
        ...row,
        ...(comparisonResult?.allResult?.find((result) => result.category === row.category) ?? {}),
      };
    });

    const absolute_results = placeholderResultArr;

    // [INFO] Diff is calculated by subtrating current row value from the previous row value,
    const diff_results = placeholderResultArr?.map((result, index, allResults) => {
      if (index === 0)
        return { step: result.step, category: result.category, display_category: result.display_category };

      const diff = { ...result };
      const keys = Object.keys(result) as Array<keyof allResultType>;
      keys.forEach((key) => {
        if (typeof result[key] === "number") {
          (diff as Record<typeof key, string | number>)[key] = getDiff(allResults[index - 1][key], result[key]);
        }
      });

      return { ...diff, step: result.step };
    });

    // "Total" row is calculate by subtracting "all" row value from the first row value
    const calculateTotalRowData = () => {
      const allRowResult = placeholderResultArr.find((result) => result.category === DEFAULT_CATEGORIES.ALL);
      const total = { ...allRowResult };
      if (allRowResult) {
        const allRowResultKeys = Object.keys(allRowResult) as Array<keyof allResultType>;
        allRowResultKeys.forEach((key) => {
          if (typeof allRowResult[key] === "number") {
            (total as Record<typeof key, string | number>)[key] = getDiff(
              placeholderResultArr[0][key],
              allRowResult[key]
            );
          }
        });

        return {
          ...total,
          step: `${placeholderResultArr.length}`,
          category: "total",
          display_category: "Total",
        };
      }
    };

    const totalRowData = calculateTotalRowData();
    if (totalRowData) {
      diff_results.push(totalRowData);
    }

    return {
      [DataFormat.ABSOLUTE]: absolute_results,
      [DataFormat.DIFF]: diff_results,
    };
  }, [comparisonResult?.allResult, placeholderGridRows]);

  React.useEffect(() => {
    if (!results?.[dataFormat]?.length) {
      gridRef.current?.api?.setGridOption("loading", true);
      return;
    } else {
      gridRef.current?.api?.setGridOption("loading", false);
    }
  }, [dataFormat, gridRef, results]);

  const primarySecurityRepline = React.useMemo(() => {
    return responseDataPrimarySecurity?.bond_replines ?? [];
  }, [responseDataPrimarySecurity]);

  const secondarySecurityRepline = React.useMemo(() => {
    return responseDataSecondarySecurity?.bond_replines ?? [];
  }, [responseDataSecondarySecurity]);

  const expandCollapseRowsHandler = (action: "expand" | "collapse") => {
    const gridApi = gridRef?.current?.api;
    const groups = gridApi?.getColumnState()?.find((c) => c.rowGroup);
    const groupActive = groups ? true : false;

    if (groupActive) {
      action === "expand" ? gridApi?.expandAll() : gridApi?.collapseAll();
    } else {
      gridApi?.forEachNode((r) => r.setExpanded(action === "expand" ? true : false));
    }
  };

  const detailCellRendererParams = React.useMemo(() => {
    return getDetailCellRendererParams({
      primarySecurity,
      secondarySecurity,
      primarySecurityRepline,
      secondarySecurityRepline,
    });
  }, [primarySecurity, secondarySecurity, primarySecurityRepline, secondarySecurityRepline]);

  const isRowMaster = React.useCallback(
    (dataItem: { step: string; category: string; display_category: string | null | undefined }) => {
      return !rowWithoutMasterDetail.includes(dataItem.category);
    },
    []
  );

  const getRowId = React.useCallback(
    (
      params: GetRowIdParams<{
        step: string;
        category: string;
        display_category: string | null | undefined;
      }>
    ) => params.data.category,
    []
  );

  const context = React.useMemo(
    () => ({
      dataFormat,
      primarySecurityRepline,
      secondarySecurityRepline,
      replineAttributeConfig,
    }),
    [dataFormat, primarySecurityRepline, replineAttributeConfig, secondarySecurityRepline]
  );

  const rowData = React.useMemo(() => {
    const gridData = results[dataFormat];
    return !gridData.length && isTimerRunning ? placeholderGridRows : results[dataFormat];
  }, [dataFormat, isTimerRunning, placeholderGridRows, results]);

  const CollateralCardHeadingMenu = React.useMemo(
    () => (
      <Menu placement="right-start" isLazy closeOnSelect>
        <MenuButton
          as={IconButton}
          aria-label="query-options-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={5} variant="secondary" />}
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
        />
        <MenuList zIndex="popover" minW="10rem">
          <MenuItem
            icon={<CAIcon as={IoInformationCircleOutline} boxSize={5} variant="secondary" display="flex" />}
            onClick={(e) => {
              e.stopPropagation();
              setModalViewMode("CollateralCDU");
            }}
          >
            Collateral CDU
          </MenuItem>
          <MenuItem
            icon={<CAIcon as={IoDocumentTextOutline} boxSize={5} variant="secondary" display="flex" />}
            onClick={(e) => {
              e.stopPropagation();
              setModalViewMode("ReplineAttributes");
            }}
          >
            Repline Attributes
          </MenuItem>
        </MenuList>
      </Menu>
    ),
    []
  );
  return (
    <>
      <CAGrid<{
        step: string;
        category: string;
        display_category: string | null | undefined;
      }>
        ref={gridRef}
        cardProps={{
          headingRight: CollateralCardHeadingMenu,
          cardKey: "bond-compare-collateral-categories",
          title: "COLLATERAL BY CATEGORY",
          allowCollapse: true,
        }}
        showExpand={false}
        gridType={`bond-compare-${security_info?.sub_type}`}
        hasRun={!!lastRun}
        hideSearch
        gridProps={{
          className: "collateral-by-category",
          domLayout: "autoHeight",
          context,
          columnDefs: BondCompareCollateralAttributeColumns,
          defaultColDef: colDef,
          rowData,
          embedFullWidthRows: true,
          detailCellRendererParams,
          masterDetail: true,
          suppressGroupRowsSticky: true,
          detailRowAutoHeight: true,
          isRowMaster,
          getRowId,
          statusBar: undefined,
        }}
        headerActionButtons={headerActionButton}
        headerStyles={{ mt: 1 }}
        initialMessage="Click ▷ to load Bond compare."
        contextMenuHandlers={{
          expandAllHandler: () => expandCollapseRowsHandler("expand"),
          collapseAllHandler: () => expandCollapseRowsHandler("collapse"),
        }}
      />
      <CAModal
        isOpen={!!modalViewMode}
        onClose={() => setModalViewMode(null)}
        contentStyle={
          modalViewMode === "CollateralCDU"
            ? { minW: { base: "660px", md: "max-content" }, maxW: { base: "300px", md: "660px" } }
            : { minW: { base: "auto", md: "max-content" }, maxH: "53.5rem" }
        }
        showCloseIcon
        modalHeader={modalViewMode === "ReplineAttributes" ? "Repline Attributes" : ""}
        modalBodyProps={{ overflow: "auto" }}
      >
        {modalViewMode === "CollateralCDU" && <CollateralCDU />}
        {modalViewMode === "ReplineAttributes" && (
          <>
            {!responseDataPrimarySecurity?.bond_replines?.length ||
            !responseDataSecondarySecurity?.bond_replines?.length ? (
              <Skeleton height="25px" />
            ) : (
              <ReplineAttribute
                replineAttributeConfig={replineAttributeConfig}
                responseDataPrimarySecurity={responseDataPrimarySecurity}
                responseDataSecondarySecurity={responseDataSecondarySecurity}
              />
            )}
          </>
        )}
      </CAModal>
    </>
  );
};
