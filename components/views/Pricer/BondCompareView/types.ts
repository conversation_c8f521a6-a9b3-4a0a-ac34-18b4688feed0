export const SliceIds = Object.freeze({
  CONV: "TICKER_LOANTYPE:CONV",
  FHA: "TICKER_LOANTYPE:FHA",
  VA: "TICKER_LOANTYPE:VA",
  RHS: "TICKER_LOANTYPE:RHS",
  PIH: "TICKER_LOANTYPE:PIH",
});

export enum CollateralAttrFields {
  CONV1 = "conventional_field_1",
  CONV2 = "conventional_field_2",
  CONV_DIFF = "conventional_diff",
  FHA1 = "fha_field_1",
  FHA2 = "fha_field_2",
  FHA_DIFF = "fha_diff",
  VA1 = "va_field_1",
  VA2 = "va_field_2",
  VA_DIFF = "va_diff",
  RHS1 = "rhs_field_1",
  RHS2 = "rhs_field_2",
  RHS_DIFF = "rhs_diff",
  PIH1 = "pih_field_1",
  PIH2 = "pih_field_2",
  PIH_DIFF = "pih_diff",
}

export interface DetailRowDataType {
  attribute: string;
  [CollateralAttrFields.CONV1]?: number | string;
  [CollateralAttrFields.CONV2]?: number | string;
  [CollateralAttrFields.CONV_DIFF]?: number | string;
  [CollateralAttrFields.FHA1]?: number | string;
  [CollateralAttrFields.FHA2]?: number | string;
  [CollateralAttrFields.FHA_DIFF]?: number | string;
  [CollateralAttrFields.VA1]?: number | string;
  [CollateralAttrFields.VA2]?: number | string;
  [CollateralAttrFields.VA_DIFF]?: number | string;
  [CollateralAttrFields.RHS1]?: number | string;
  [CollateralAttrFields.RHS2]?: number | string;
  [CollateralAttrFields.RHS_DIFF]?: number | string;
  [CollateralAttrFields.PIH1]?: number | string;
  [CollateralAttrFields.PIH2]?: number | string;
  [CollateralAttrFields.PIH_DIFF]?: number | string;
}
