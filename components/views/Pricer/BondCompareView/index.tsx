import * as React from "react";
import { Box } from "@chakra-ui/react";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { getRequests } from "@/utils/helpers/pricer/bond-compare";
import { showErrorToast } from "@/design-system/theme/toast";
import { PricerModuleProps } from "..";
import PricerHeader from "../PricerHeader";
import { BondCompareStopWatchWrapper } from "../PricerStopWatchWrapper";
import { BondCompareProgressIndicator } from "../PricerProgressIndicatorWrapper";
import { BondCompareStopRunningWrapper } from "../PricerStopRunningWrapper";
import BondCompareInput, { BondCompareInputHandler } from "./BondCompareInputView/BondCompareInput";
import BondCompareViewWrapper from "./BondCompareViewWrapper";
import BondCompareCategories, { BondCompareCategoriesRef } from "./BondCompareInputView/BondCompareCategories";
const PricerBondCompareView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { userSettings, bond_name: primary_security, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, lastRun, isOldRun, api_start_time, api_end_time, ...bondCompareState },
    action: { updateBondComparePageSettings, run, setUserCategorySelections },
  } = usePricerBondComparePage();
  const { pushWithoutRendering } = useQueryParameters();

  const bondCompareInput = React.useRef<BondCompareInputHandler>(null);
  const bondCompareCategoriesInput = React.useRef<BondCompareCategoriesRef>(null);

  const { bondCompareRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRun,
    lastRunId,
    isOldRun,
    primary_security,
    ...bondCompareState,
  });

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    bondCompareInput.current?.handleSubmit(
      (data) => {
        // Validations
        if (data.secondary_security === primary_security) {
          showErrorToast("Validation Error", `Base Bond and Compare To Bond can not be the same.`);
          return;
        }

        const selectedReplineAttr = bondCompareCategoriesInput.current?.getSelectedReplineAttr() ?? [];
        if (security_info?.sub_type) {
          setUserCategorySelections(selectedReplineAttr, security_info.sub_type);
        }
        updateBondComparePageSettings(data);
        updatePricerUserSettingsCopy(userSettings);
        setIsTimerRunning(true);
        const run_id = run({ no_cache });
        pushWithoutRendering({ run_id });
      },
      (errors) => {
        if (errors?.pricing_level?.type === "validate") {
          showErrorToast("Validation", "Please enter a valid pricing value.");
        }
      }
    )();
  };

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={primary_security}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        needsBond={false}
        onRunClick={onSubmit}
        stopWatch={
          <BondCompareStopWatchWrapper
            bondCompareRequest={bondCompareRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BondCompareProgressIndicator bondCompareRequest={bondCompareRequest} requestOpts={requestOpts} />
        }
        stopRunning={
          <BondCompareStopRunningWrapper bondCompareRequest={bondCompareRequest} requestOpts={requestOpts} />
        }
      />
      <MainInputContentTemplate
        inputs={
          <>
            <BondCompareInput ref={bondCompareInput} />
            <BondCompareCategories ref={bondCompareCategoriesInput} />
          </>
        }
        maxW={{ base: "auto", md: "min-content" }}
        minW={{ base: "auto", md: "2xs" }}
      >
        <BondCompareViewWrapper bondCompareRequest={bondCompareRequest} requestOpts={requestOpts} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerBondCompareView;
