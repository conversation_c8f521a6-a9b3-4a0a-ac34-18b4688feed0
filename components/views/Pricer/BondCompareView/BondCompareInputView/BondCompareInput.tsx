import { Box, Flex, Text, VStack } from "@chakra-ui/react";
import * as React from "react";
import { Controller, UseFormHandleSubmit, useForm } from "react-hook-form";
import CACard from "@/design-system/molecules/CACard";
import { bondComparePageSettingsType } from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { CA_Mastr_Models_v1_0_Models_BondType, CA_Mastr_Models_v1_0_Models_PricingType } from "@/utils/openapi";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import CAInput from "@/design-system/molecules/CAInput";
import {
  DEC_REGEX,
  getCDUDateKeyFromSubType,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getFormattedYearMonth,
  getInfoMsg,
  tickToDecimal,
} from "@/utils/helpers";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import { BondSearchPopover } from "@/design-system/organisms/BondSearchPopover/BondSearchPopover";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import CADateInput from "@/design-system/molecules/CADateInput";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { runToastManager } from "@/utils/run-toast-manager";
import useUpdateSettleDate from "@/hooks/useUpdateSettleDate";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import { DateSettingsOverrides } from "@/components/helpers/DateSettingsOverrides/DateSettingsOverrides";
import ModelDialSelectorWrapper from "../../shared/ModelDialSelectorWrapper";
import PricerValueDisplayWrapper from "../../shared/PricerValueDisplayWrapper";

export interface BondCompareInputHandler {
  handleSubmit: UseFormHandleSubmit<bondComparePageSettingsType>;
}

const BondCompareInput: React.ForwardRefRenderFunction<BondCompareInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, userSettingsCopy, bond_name: primary_security, security_info },
  } = usePricerModule();
  const {
    state: { app, page, bondComparePageSettingsCopy, bondComparePageSettings, isOldRun, lastRun },
    action: { resetLastRun },
  } = usePricerBondComparePage();

  const { handleSubmit, register, watch, control, setValue, reset } = useForm<bondComparePageSettingsType>();
  const [watch_pricing_type, watch_pricing_level, watch_settle_date, watch_prepay_percentage, watch_modify_class] =
    watch(["pricing_type", "pricing_level", "settle_date", "prepay_percentage", "modify_class"]);

  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);

  useUpdateSettleDate<typeof bondComparePageSettings>({
    pageSettings: bondComparePageSettings,
    setSettleDate: (settleDate) => setValue("settle_date", settleDate),
    isOldRun,
    security_info,
    userSettings,
    userSettingsCopy,
    bond_name: primary_security,
    app,
    page,
  });

  React.useEffect(() => {
    reset(bondComparePageSettings);
    // Do not reset if just the base bond has changed because it was wiping out settle_date
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    bondComparePageSettings.modify_class,
    bondComparePageSettings.prepay_percentage,
    bondComparePageSettings.pricing_level,
    bondComparePageSettings.pricing_type,
    bondComparePageSettings.settle_date,
    isOldRun,
    reset,
  ]);

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  const defaultsecondarySecurity =
    safeSessionStorage.getItem(SESSION_STORAGE_KEY.BOND_COMPARE_SECONDARY_SECURITY) || undefined;
  return (
    <VStack spacing={4} alignItems="stretch">
      <CACard
        key={"Bond-Compare-Security"}
        title="Secondary Security"
        allowCollapse
        cardKey="security-inputs"
        overflow="visible"
      >
        <Flex alignItems="center" justifyContent="space-between">
          <Text variant="primary" whiteSpace="nowrap">
            Name
          </Text>
          <Controller
            control={control}
            rules={{
              required: true,
            }}
            defaultValue={defaultsecondarySecurity}
            name="secondary_security"
            render={({ field: { value, onChange } }) => {
              return (
                <BondSearchPopover
                  bondName={value ?? ""}
                  onBondSelected={(bondName) => {
                    safeSessionStorage.setItem(SESSION_STORAGE_KEY.BOND_COMPARE_SECONDARY_SECURITY, bondName);
                    onChange(bondName);
                    runToastManager.removeToasts();
                    resetLastRun();
                  }}
                  triggerEl={<CAInput maxInputWidth="96px" name="secondary_security" value={value} />}
                  contentProps={{
                    left: { base: "-2", md: "5" },
                  }}
                  canOpenNew={false}
                />
              );
            }}
          />
        </Flex>
      </CACard>

      <CACard key={"Bond-Compare-Pricing"} title="Pricing" allowCollapse cardKey="pricing-inputs" overflow="visible">
        <VStack alignItems="stretch" spacing={4}>
          <Box>
            <CASelectDropdown
              label={"Type"}
              width={"6rem"}
              info={getInfoMsg(watch_pricing_type, bondComparePageSettingsCopy.pricing_type)}
              {...register("pricing_type", {
                required: true,
              })}
              options={[
                // Price is not available in bond compare
                // { id: 0, value: CA_Mastr_Models_v1_0_Models_PricingType.PRICE, displayValue: "Price" },
                { id: 1, value: CA_Mastr_Models_v1_0_Models_PricingType.OAS, displayValue: "OAS" },
                { id: 2, value: CA_Mastr_Models_v1_0_Models_PricingType.YIELD, displayValue: "Yield" },
              ]}
            />
            {/* TODO Make Text Input to accept Ticks */}
            <CAInput
              label={"Value"}
              width={"6rem"}
              info={getInfoMsg(watch_pricing_level, bondComparePageSettingsCopy.pricing_level)}
              {...register("pricing_level", {
                required: true,
                validate: (v) => {
                  const value = `${v}`;
                  return !!value.match(DEC_REGEX) || !isNaN(tickToDecimal(value));
                },
              })}
            />
            <PricerValueDisplayWrapper
              name="Curve Date"
              link
              dateFormatter={getFormattedLocaleDate}
              value={getFormattedLocaleDate(userSettings.curve_date)}
              _key="curve_date"
            />
            <PricerValueDisplayWrapper
              name="Pricing Date"
              link
              dateFormatter={getFormattedLocaleDate}
              value={getFormattedLocaleDate(userSettings.pricing_date)}
              _key="pricing_date"
            />
            <Controller
              name={"settle_date"}
              control={control}
              rules={{ required: true }}
              render={({ field: { name, value, onChange, ref } }) => (
                <CADateInput
                  ref={ref}
                  selectedDate={value}
                  width={"6rem"}
                  minDate={userSettings.curve_date}
                  onChange={onChange}
                  name={name}
                  label={"Settle Date"}
                  info={getInfoMsg(watch_settle_date, bondComparePageSettingsCopy.settle_date)}
                  includeNonBusinessDays={true}
                />
              )}
            />
            <CAInput
              type="number"
              width={"6rem"}
              label={"Multiplier (%)"}
              {...register("prepay_percentage", {
                valueAsNumber: true,
                required: true,
              })}
              info={getInfoMsg(watch_prepay_percentage, bondComparePageSettingsCopy.prepay_percentage)}
            />
            <PricerValueDisplayWrapper name="Prepay Model" value="Cello" />
            <PricerValueDisplayWrapper
              name="Prepay Model Version"
              value={userSettings.model_version ?? ""}
              _key="model_version"
              link
            />
            <ModelDialSelectorWrapper lastRun={lastRun} />
            <PricerValueDisplayWrapper
              name="Current Coupon Model"
              value={
                metadata?.pricer_settings?.current_coupon_model?.find(
                  (l) => l.value === userSettings.current_coupon_model
                )?.display_value ?? ""
              }
              _key="current_coupon_model"
              link
            />
            <PricerValueDisplayWrapper
              name="Primary-Secondary Spread Model"
              link
              value={
                metadata?.pricer_settings?.primary_secondary_spread?.find(
                  (l) => l.value === userSettings.primary_secondary_spread
                )?.display_value ?? ""
              }
              _key="primary_secondary_spread"
            />
            <PricerValueDisplayWrapper
              name="Interest Rate Model"
              link
              value={metadata?.pricer_settings?.type?.find((l) => l.value === userSettings.type)?.display_value ?? ""}
              _key="type"
            />
            <PricerValueDisplayWrapper
              name="Interest Rate Paths"
              link
              value={userSettings.interestRatePaths?.toString() ?? ""}
              _key="interestRatePaths"
            />
            <PricerValueDisplayWrapper
              name="Calibration"
              value={
                metadata?.pricer_settings?.calibration?.find((l) => l.value === userSettings.calibration)
                  ?.display_value ?? ""
              }
              _key="calibration"
              link
            />
            <PricerValueDisplayWrapper
              name="Cello CDU Date"
              link
              value={getFormattedYearMonth(userSettings[cduDateKey])}
              _key={`cello_cdu_dates.${cduDateKey}`}
              dateFormatter={getFormattedYearMonth}
              drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
            />
            <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
            {security_info?.bond_type !== CA_Mastr_Models_v1_0_Models_BondType.CMO && (
              <CASelectDropdown
                label="Cash Flow"
                width="6rem"
                info={getInfoMsg(watch_modify_class, bondComparePageSettingsCopy.modify_class)}
                {...register("modify_class", {
                  required: true,
                })}
                options={getFormattedStringOptions(metadata?.pricer_settings?.bond_class)}
              />
            )}
          </Box>
          <DateSettingsOverrides />
        </VStack>
      </CACard>
    </VStack>
  );
};

export default React.forwardRef(BondCompareInput);
