import React from "react";
import { <PERSON>, <PERSON>Sta<PERSON>, I<PERSON>Button, Menu, MenuButton, MenuItem, MenuList } from "@chakra-ui/react";
import { IoEllipsisVerticalOutline, IoRefresh, IoTrashOutline } from "react-icons/io5";
import { DragEndEvent } from "@dnd-kit/core";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import {
  CategoriesType,
  ReplineAttributeConfigDataType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { CA_Mastr_Api_v1_0_Models_Repline_ReplineAttributeConfigInfo_ReplineAttributeConfigDef } from "@/utils/openapi";
import CAIcon from "@/design-system/atoms/CAIcon";
import { ReorderIcon } from "@/design-system/icons";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { arrayMove } from "@/utils/helpers";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import CACard from "@/design-system/molecules/CACard";
import { CADragDrop, CADragDropContainer } from "@/design-system/molecules/CADragDrop";

export interface BondCompareCategoriesRef {
  getSelectedReplineAttr: () => string[];
}

const BondCompareCategories: React.ForwardRefRenderFunction<BondCompareCategoriesRef> = (_, ref) => {
  const {
    state: { security_info },
  } = usePricerModule();
  const {
    state: { replineAttributeConfig },
  } = usePricerBondComparePage();

  const normalizedReplineAttributeConfig: ReplineAttributeConfigDataType[] = React.useMemo(() => {
    let configData:
      | Array<CA_Mastr_Api_v1_0_Models_Repline_ReplineAttributeConfigInfo_ReplineAttributeConfigDef>
      | undefined
      | null = undefined;
    const attributeConfigData: ReplineAttributeConfigDataType[] = [];
    if (security_info?.sub_type) {
      configData = replineAttributeConfig?.filter(
        (item) => !!(item.value && item.agencies?.includes(security_info?.sub_type ?? ""))
      );

      configData?.forEach((row, index) => {
        attributeConfigData.push({
          ...row,
          id: row.value ?? `${index}`,
          value: row.value as string,
          displayValue: row.display_name ?? "",
          attributes: row.attributes ?? [],
          categories: [{ id: row.value ?? `${index}`, value: row.value as string, displayValue: row.display_name }],
        });
      });
    }
    return attributeConfigData;
  }, [security_info?.sub_type, replineAttributeConfig]);

  const { selectedReplineAttr, setSelectedReplineAttr } = useOnCategorySelectionChange({
    normalizedReplineAttributeConfig,
  });

  React.useImperativeHandle(ref, () => ({
    getSelectedReplineAttr: () => selectedReplineAttr.map((attr) => attr.value),
  }));

  //   handler
  const removeRow = (index: number) => {
    const updatedReplineAttr = selectedReplineAttr.filter((r, i) => index !== i);
    setSelectedReplineAttr(updatedReplineAttr);
  };

  const dragColumnHandler = (event: DragEndEvent) => {
    const source = event.active.data.current?.sortable.index;
    const destination = event.over?.data.current?.sortable.index;
    if (!destination?.toString()) {
      return;
    }
    if (source === destination) return;

    const updatedReplineAttr = arrayMove(selectedReplineAttr, source, destination);
    setSelectedReplineAttr(updatedReplineAttr);
  };

  const displaySelectedReplineArrOptions = React.useMemo(() => {
    if (normalizedReplineAttributeConfig.length !== selectedReplineAttr.length) {
      return [...selectedReplineAttr, { id: "empty", value: "", categories: [] }];
    }
    return selectedReplineAttr;
  }, [normalizedReplineAttributeConfig.length, selectedReplineAttr]);

  const onCategoryChangeHandler = (action: "push" | "replace", selectedValue: string, index?: number) => {
    const selectedConfig = normalizedReplineAttributeConfig.find((x) => x.value === selectedValue);

    if (!selectedConfig) {
      console.error("Could not find repline attribute config object for selected value");
      return;
    }

    let updatedSelections: ReplineAttributeConfigDataType[] = [];
    if (action === "push") {
      updatedSelections = [...selectedReplineAttr, selectedConfig];
    } else if (action === "replace") {
      updatedSelections = [
        ...selectedReplineAttr.map((r, i) => {
          if (i === index) {
            return selectedConfig;
          }
          return r;
        }),
      ];
    }
    setSelectedReplineAttr(updatedSelections);
  };

  const CardHeadingRight = () => (
    <Box onClick={(e) => e.stopPropagation()}>
      <Menu placement="right-start" isLazy closeOnSelect>
        <MenuButton
          as={IconButton}
          aria-label="query-options-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={5} variant="secondary" />}
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
        />
        <MenuList zIndex="popover" minW="10rem">
          <MenuItem
            icon={<CAIcon as={IoRefresh} boxSize={4} variant="default" transform={"rotateY(180deg)"} />}
            onClick={() => setSelectedReplineAttr(normalizedReplineAttributeConfig)}
          >
            Reset
          </MenuItem>
        </MenuList>
      </Menu>
    </Box>
  );

  if (!security_info?.sub_type) {
    return (
      <CACard key="bond-categories" title="Categories" allowCollapse cardKey="bond-categories" overflow="visible">
        <Box h="100%" p="20">
          <CAInfo
            status="info"
            title="No bond selected"
            description={`Enter a bond and click ▷ to load Bond compare.`}
          />
        </Box>
      </CACard>
    );
  }

  return (
    <CACard
      key="bond-categories"
      title="Categories"
      allowCollapse
      cardKey="bond-categories"
      overflow="visible"
      headingRight={<CardHeadingRight />}
    >
      <CADragDropContainer onDragEnd={dragColumnHandler} items={displaySelectedReplineArrOptions.map((el) => el.id)}>
        {displaySelectedReplineArrOptions.map((replineAtt, index) => (
          <HStack key={replineAtt.id}>
            <CADragDrop
              id={replineAtt.id}
              disabled={replineAtt.id === "empty"}
              boxStyle={{
                w: "full",
              }}
            >
              <HStack alignItems="center">
                <Box visibility={replineAtt.id === "empty" ? "hidden" : "visible"}>
                  <CAIcon as={ReorderIcon} />
                </Box>
                <CASelectDropdown
                  name={`replineAttr.${index}.value`}
                  hasPlaceholderOption
                  hideLabel
                  options={[
                    ...(replineAtt.categories as CategoriesType[]),
                    ...(normalizedReplineAttributeConfig.filter((option) => {
                      return !selectedReplineAttr.find((selected) => selected.value === option.value);
                    }) as CategoriesType[]),
                  ]}
                  value={replineAtt.value}
                  onChange={({ target }) => {
                    if (replineAtt.id === "empty") onCategoryChangeHandler("push", target.value);
                    else onCategoryChangeHandler("replace", target.value, index);
                  }}
                />
              </HStack>
            </CADragDrop>
            <Box
              cursor="pointer"
              visibility={replineAtt.id === "empty" ? "hidden" : "visible"}
              onClick={() => removeRow(index)}
            >
              <CAIcon boxSize={4} as={IoTrashOutline} variant="secondary" display="flex" />
            </Box>
          </HStack>
        ))}
      </CADragDropContainer>
    </CACard>
  );
};

export default React.forwardRef(BondCompareCategories);

const useOnCategorySelectionChange = ({
  normalizedReplineAttributeConfig,
}: {
  normalizedReplineAttributeConfig: ReplineAttributeConfigDataType[];
}) => {
  const [selectedReplineAttr, setSelectedReplineAttr] = React.useState<ReplineAttributeConfigDataType[]>([]);

  const {
    state: { security_info },
  } = usePricerModule();
  const {
    state: { userCategorySelections },
  } = usePricerBondComparePage();

  React.useEffect(() => {
    if (security_info?.sub_type) {
      if (userCategorySelections && userCategorySelections.length > 0) {
        const selections = userCategorySelections
          .map((value) => value && normalizedReplineAttributeConfig.find((c) => c.value === value))
          .filter((c) => !!c) as ReplineAttributeConfigDataType[];
        setSelectedReplineAttr(selections);
        return;
      }
    }
    setSelectedReplineAttr(normalizedReplineAttributeConfig);
    // We only want to load this once during intialization or when the userCategorySelections changes
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [normalizedReplineAttributeConfig, JSON.stringify(userCategorySelections)]);

  return { selectedReplineAttr, setSelectedReplineAttr };
};
