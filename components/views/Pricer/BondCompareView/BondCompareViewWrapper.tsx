import * as React from "react";
import { Flex, VStack } from "@chakra-ui/react";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import CACard from "@/design-system/molecules/CACard";
import { CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest } from "@/utils/openapi";
import { CustomRequestOptions, usePostBondCompare } from "@/utils/swr-hooks";
import {
  DEFAULT_CATEGORIES,
  allResultType,
  comparisonResultType,
} from "@/contexts/PageContexts/PricerBondComparePageContext/PricerBondComparePageContextTypes";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { AttributionCard, AttributionDetailCard, PriceCard } from "./cards";
import CollateralCategories from "./CollateralCategories";

const BondCompareViewWrapper: React.FC<{
  bondCompareRequest: CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest;
  requestOpts: CustomRequestOptions;
}> = ({
  bondCompareRequest,
  requestOpts: { lastRun, isOldRun },
}: {
  bondCompareRequest: CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest;
  requestOpts: CustomRequestOptions;
}) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { bond_name: primary_security, security_info, userSettingsCopy },
  } = usePricerModule();

  const { data, error } = usePostBondCompare(bondCompareRequest, { lastRun, isOldRun });

  const comparisonResult: comparisonResultType | undefined = React.useMemo(() => {
    const rowData: comparisonResultType = {} as comparisonResultType;
    data?.pricing_comparison?.forEach((row) => {
      if (row.repline_algorithm !== "repline_calculate_combined_groups" && row.category === "none") {
        if (rowData["_default"] == undefined) {
          rowData["_default"] = [row];
        } else {
          rowData["_default"]?.push(row);
        }
      } else if (row.category === "none") {
        if (rowData["single"] == undefined) {
          rowData["single"] = [row];
        } else {
          rowData["single"]?.push(row);
        }
      }
      if (
        (row.category === "none" || row.category === DEFAULT_CATEGORIES.ALL) &&
        row.repline_algorithm === "repline_calculate_combined_groups" &&
        row.input_security === primary_security
      ) {
        if (rowData["collateral_primarySecurity"] == undefined) {
          rowData["collateral_primarySecurity"] = [row];
        } else {
          rowData["collateral_primarySecurity"]?.push(row);
        }
      }
      if (
        row.category === DEFAULT_CATEGORIES.ALL &&
        row.input_security === primary_security &&
        row.repline_algorithm === "repline_calculate_combined_groups"
      ) {
        if (rowData["structure_primarySecurity"] == undefined) {
          rowData["structure_primarySecurity"] = [row];
        } else {
          rowData["structure_primarySecurity"]?.push(row);
        }
      }
      if (
        row.category === "none" &&
        row.input_security === bondCompareRequest.secondary_security &&
        row.repline_algorithm === "repline_calculate_combined_groups"
      ) {
        if (rowData["structure_primarySecurity"] == undefined) {
          rowData["structure_primarySecurity"] = [row];
        } else {
          rowData["structure_primarySecurity"]?.push(row);
        }
      }
    });
    if (data && data.pricing_comparison) {
      const allResult =
        data?.pricing_comparison.filter(
          (item) =>
            item.repline_algorithm === "repline_calculate_combined_groups" && item.input_security === primary_security
        ) ?? [];
      const pricing_response: allResultType[] = [];

      const noneRecord =
        allResult.find((item) => item.category === "none")?.pricing_response?.results?.[0] ?? undefined;
      if (noneRecord) pricing_response.push({ ...noneRecord, category: "none", step: "0" });
      bondCompareRequest?.repline_categories?.forEach((element, index) => {
        const record = allResult.find((item) => item.category === element)?.pricing_response?.results?.[0] ?? undefined;
        if (record)
          pricing_response.push({
            ...record,
            category: element,
            step: `${index + 1}`,
          });
      });
      const allRecord =
        allResult.find((item) => item.category === DEFAULT_CATEGORIES.ALL)?.pricing_response?.results?.[0] ?? undefined;
      if (allRecord)
        pricing_response.push({
          ...allRecord,
          category: DEFAULT_CATEGORIES.ALL,
          step: bondCompareRequest?.repline_categories?.length
            ? `${bondCompareRequest?.repline_categories?.length + 1}`
            : undefined,
        });
      rowData["allResult"] = pricing_response;
    }
    return Object.keys(rowData).length ? rowData : undefined;
  }, [bondCompareRequest.secondary_security, bondCompareRequest?.repline_categories, primary_security, data]);

  const replineAlgo = React.useMemo(() => {
    if (userSettingsCopy.repline_level) {
      const replineAlgo =
        metadata?.pricer_settings?.repline_level?.find((l) => l.value === userSettingsCopy.repline_level)
          ?.display_value ?? "";
      return replineAlgo;
    }
    return "Default";
  }, [userSettingsCopy.repline_level, metadata]);

  if (!lastRun)
    return (
      <CACard h="full" maxH="80vh" py="20" display="flex" justifyContent="center" alignItems="center">
        <CAInfo status="info" description={`Click on ▷ to load Bond Compare.`} />
      </CACard>
    );
  return (
    <VStack spacing={4} alignItems="stretch">
      <Flex gridGap="4" flexDirection={{ base: "column", xl: "row" }}>
        <PriceCard comparisonResult={comparisonResult} error={error} replineAlgo={replineAlgo} />
        <AttributionCard comparisonResult={comparisonResult} error={error} replineAlgo={replineAlgo} />
      </Flex>
      <AttributionDetailCard comparisonResult={comparisonResult} replineAlgo={replineAlgo} error={error} />
      <CollateralCategories
        key={security_info?.sub_type}
        comparisonResult={comparisonResult}
        indicativeResponses={data?.indicative_responses}
      />
    </VStack>
  );
};

export default BondCompareViewWrapper;
