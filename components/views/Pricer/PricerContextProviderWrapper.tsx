import * as React from "react";
import Router from "next/router";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { queryStringToString } from "@/utils/helpers";

interface PricerContextProviderWrapperProps {
  wrapper?: React.FC<React.PropsWithChildren>;
  needsBond?: boolean;
}

const PricerContextProviderWrapper: React.FC<React.PropsWithChildren<PricerContextProviderWrapperProps>> = ({
  children,
  needsBond = true,
  wrapper: Wrapper = React.Fragment,
}) => {
  const {
    state: { bond_name },
  } = usePricerModule();

  const wrapperKey = React.useMemo(
    /**
     * Load bond_name from router query if it's **undefined** in the context,
     * just to prevent re-initialization of page context on load
     */
    () => (needsBond ? bond_name ?? queryStringToString(Router.query.bond_name) : undefined),
    [needsBond, bond_name]
  );

  return <Wrapper key={wrapperKey}>{children}</Wrapper>;
};

export default PricerContextProviderWrapper;
