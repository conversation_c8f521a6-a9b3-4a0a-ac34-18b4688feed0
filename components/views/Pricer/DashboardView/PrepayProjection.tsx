import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { Box, Flex, Text, VStack } from "@chakra-ui/layout";
import { CustomRequestOptions, useGetBondPrepayProjectionsDashboardSWR } from "@/utils/swr-hooks";
import { BondPrepayProjectionsRequest } from "@/types/swr";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import CABarChart from "@/design-system/molecules/CABarChart";
import { getChartTooltipFormatter, getFormattedNumberFixed, getFormattedYearMonth } from "@/utils/helpers";
import { InlineValueDisplay } from "@/components/helpers/ValueDisplay";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";

type PrepayProjectionsProps = {
  bondPrepayProjectionsRequest: BondPrepayProjectionsRequest;
  requestOpts: CustomRequestOptions;
  placeholder?: boolean;
};

const PrepayProjections: React.FC<PrepayProjectionsProps> = ({
  bondPrepayProjectionsRequest,
  requestOpts,
  placeholder,
}: PrepayProjectionsProps) => {
  const [rate, setRate] = React.useState<"cpr" | "crr" | "cdr">("cpr");
  const { data: data1, error: error1 } = useGetBondPrepayProjectionsDashboardSWR(
    {
      ...bondPrepayProjectionsRequest,
      curve_shift: -100,
    },
    requestOpts
  );
  const { data: data2, error: error2 } = useGetBondPrepayProjectionsDashboardSWR(
    {
      ...bondPrepayProjectionsRequest,
      curve_shift: -50,
    },
    requestOpts
  );
  const { data: data3, error: error3 } = useGetBondPrepayProjectionsDashboardSWR(
    {
      ...bondPrepayProjectionsRequest,
      curve_shift: 0,
    },
    requestOpts
  );
  const { data: data4, error: error4 } = useGetBondPrepayProjectionsDashboardSWR(
    {
      ...bondPrepayProjectionsRequest,
      curve_shift: 50,
    },
    requestOpts
  );
  const { data: data5, error: error5 } = useGetBondPrepayProjectionsDashboardSWR(
    {
      ...bondPrepayProjectionsRequest,
      curve_shift: 100,
    },
    requestOpts
  );

  const hasData = data1 && data2 && data3 && data4 && data5;
  const hasError = !!error1 || !!error2 || !!error3 || !!error4 || !!error5;

  let tableData: { name: string; values: string[] }[] = [];
  const barChartOptions = {
    xkey: "timeline",
    dataKeys: [
      { key: "minus100", name: "-100" },
      { key: "minus50", name: "-50" },
      { key: "zero", name: "0" },
      { key: "plus50", name: "+50" },
      { key: "plus100", name: "+100" },
    ],
    margin: { left: -35 },
    data: [{}],
    formatTooltip: getChartTooltipFormatter(1),
    yAxisToFixed: 0,
  };

  if (hasData) {
    const d1 = data1?.prepay_projection_speeds as { [key: string]: number };
    const d2 = data2?.prepay_projection_speeds as { [key: string]: number };
    const d3 = data3?.prepay_projection_speeds as { [key: string]: number };
    const d4 = data4?.prepay_projection_speeds as { [key: string]: number };
    const d5 = data5?.prepay_projection_speeds as { [key: string]: number };
    tableData = [
      {
        name: "1m",
        values: [
          getFormattedNumberFixed(1)(d1?.[`${rate}_1m`]),
          getFormattedNumberFixed(1)(d2?.[`${rate}_1m`]),
          getFormattedNumberFixed(1)(d3?.[`${rate}_1m`]),
          getFormattedNumberFixed(1)(d4?.[`${rate}_1m`]),
          getFormattedNumberFixed(1)(d5?.[`${rate}_1m`]),
        ],
      },
      {
        name: "1Yr",
        values: [
          getFormattedNumberFixed(1)(d1?.[`${rate}_1y`]),
          getFormattedNumberFixed(1)(d2?.[`${rate}_1y`]),
          getFormattedNumberFixed(1)(d3?.[`${rate}_1y`]),
          getFormattedNumberFixed(1)(d4?.[`${rate}_1y`]),
          getFormattedNumberFixed(1)(d5?.[`${rate}_1y`]),
        ],
      },
      {
        name: "3Yr",
        values: [
          getFormattedNumberFixed(1)(d1?.[`${rate}_3y`]),
          getFormattedNumberFixed(1)(d2?.[`${rate}_3y`]),
          getFormattedNumberFixed(1)(d3?.[`${rate}_3y`]),
          getFormattedNumberFixed(1)(d4?.[`${rate}_3y`]),
          getFormattedNumberFixed(1)(d5?.[`${rate}_3y`]),
        ],
      },
      {
        name: "5Yr",
        values: [
          getFormattedNumberFixed(1)(d1?.[`${rate}_5y`]),
          getFormattedNumberFixed(1)(d2?.[`${rate}_5y`]),
          getFormattedNumberFixed(1)(d3?.[`${rate}_5y`]),
          getFormattedNumberFixed(1)(d4?.[`${rate}_5y`]),
          getFormattedNumberFixed(1)(d5?.[`${rate}_5y`]),
        ],
      },
      {
        name: "LT",
        values: [
          getFormattedNumberFixed(1)(d1?.[`${rate}_lt`]),
          getFormattedNumberFixed(1)(d2?.[`${rate}_lt`]),
          getFormattedNumberFixed(1)(d3?.[`${rate}_lt`]),
          getFormattedNumberFixed(1)(d4?.[`${rate}_lt`]),
          getFormattedNumberFixed(1)(d5?.[`${rate}_lt`]),
        ],
      },
    ];

    barChartOptions.data = [
      {
        timeline: "1m",
        minus100: d1?.[`${rate}_1m`],
        minus50: d2?.[`${rate}_1m`],
        zero: d3?.[`${rate}_1m`],
        plus50: d4?.[`${rate}_1m`],
        plus100: d1?.[`${rate}_1m`],
      },
      {
        timeline: "1Yr",
        minus100: d1?.[`${rate}_1y`],
        minus50: d2?.[`${rate}_1y`],
        zero: d3?.[`${rate}_1y`],
        plus50: d4?.[`${rate}_1y`],
        plus100: d5?.[`${rate}_1y`],
      },
      {
        timeline: "3Yr",
        minus100: d1?.[`${rate}_3y`],
        minus50: d2?.[`${rate}_3y`],
        zero: d3?.[`${rate}_3y`],
        plus50: d4?.[`${rate}_3y`],
        plus100: d5?.[`${rate}_3y`],
      },
      {
        timeline: "5Yr",
        minus100: d1?.[`${rate}_5y`],
        minus50: d2?.[`${rate}_5y`],
        zero: d3?.[`${rate}_5y`],
        plus50: d4?.[`${rate}_5y`],
        plus100: d5?.[`${rate}_5y`],
      },
      {
        timeline: "LT",
        minus100: d1?.[`${rate}_lt`],
        minus50: d2?.[`${rate}_lt`],
        zero: d3?.[`${rate}_lt`],
        plus50: d4?.[`${rate}_lt`],
        plus100: d5?.[`${rate}_lt`],
      },
    ];
  }

  return (
    <CACard
      title="Scenario Prepay Projections"
      isPlaceholderCard={hasError || placeholder}
      maxW={500}
      allowCollapse
      cardKey="dashbaord-prepay-projections"
      cardBodyStyle={{ h: hasError || placeholder ? "5.5rem" : "auto" }}
    >
      <Box>
        <Flex mb="1" wrap="wrap" justify="space-between" align="center">
          {data3?.prepay_projection_speeds?.factor_month ? (
            <Box mb="2">
              <InlineValueDisplay
                name="Factor Month"
                value={getFormattedYearMonth(data3.prepay_projection_speeds.factor_month)}
              />
            </Box>
          ) : (
            <Box />
          )}
          <Box ml="auto">
            <ToggleButtonGroup
              buttons={[
                { label: "CPR", value: "cpr" },
                { label: "CRR", value: "crr" },
                { label: "CDR", value: "cdr" },
              ]}
              selectedButton={rate}
              onChange={setRate}
            />
          </Box>
        </Flex>
        <VStack spacing={3} alignItems="stretch">
          {!hasData ? (
            <Skeleton height="25px" />
          ) : (
            <>
              <Box>
                <Text variant="tableHead" textAlign="center" mb={1}>
                  Curve shift (bps)
                </Text>
                <CATable variant="caBasicCentered" headers={["", "-100", "-50", "0", "+50", "+100"]} data={tableData} />
              </Box>
              <CABarChart options={barChartOptions} />
            </>
          )}
        </VStack>
      </Box>
    </CACard>
  );
};

export default React.memo(PrepayProjections);
