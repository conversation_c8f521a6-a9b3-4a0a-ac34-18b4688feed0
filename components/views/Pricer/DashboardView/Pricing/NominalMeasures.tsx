import * as React from "react";
import { getFormattedNumberFixed, getFormattedYearMonth } from "@/utils/helpers";
import PricingCard from "./PricingCard";
import { PricingResultProps } from ".";

const NominalMeasures: React.FC<PricingResultProps> = ({ title, bondPricingResult }: PricingResultProps) => {
  const headers = ["", "Static", "Forward"];

  const tableData = React.useMemo(() => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "Yield (%)",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.yield),
          getFormattedNumberFixed(3)(bondPricingResult.forward_yield),
        ],
      },
      { name: "Spread (bps)", values: ["-", "-"] }, // TODO
      {
        name: "Average Life (years)",
        values: [
          getFormattedNumberFixed(2)(bondPricingResult.weighted_average_life),
          getFormattedNumberFixed(2)(bondPricingResult.forward_weighted_average_life),
        ],
      },
      {
        name: "Modified Duration",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.modified_duration),
          getFormattedNumberFixed(3)(bondPricingResult.forward_modified_duration),
        ],
      },
      {
        name: "Accrual Start",
        values: [
          getFormattedYearMonth(bondPricingResult.accrual_start_date ?? "-"),
          getFormattedYearMonth(bondPricingResult.forward_accrual_start_date ?? "-"),
        ],
      },
      {
        name: "1st Payment",
        values: [
          getFormattedYearMonth(bondPricingResult.payment_window_begin ?? "-"),
          getFormattedYearMonth(bondPricingResult.forward_payment_window_begin ?? "-"),
        ],
      },
      {
        name: "Last Payment",
        values: [
          getFormattedYearMonth(bondPricingResult.payment_window_end ?? "-"),
          getFormattedYearMonth(bondPricingResult.forward_payment_window_end ?? "-"),
        ],
      },
      {
        name: "1st Principal",
        values: [
          getFormattedYearMonth(bondPricingResult.principal_window_begin ?? "-"),
          getFormattedYearMonth(bondPricingResult.forward_principal_window_begin ?? "-"),
        ],
      },
      {
        name: "Last Principal",
        values: [
          getFormattedYearMonth(bondPricingResult.principal_window_end ?? "-"),
          getFormattedYearMonth(bondPricingResult.forward_principal_window_end ?? "-"),
        ],
      },
    ];
  }, [bondPricingResult]);

  return <PricingCard title={title} {...{ headers, tableData }} />;
};

export default NominalMeasures;
