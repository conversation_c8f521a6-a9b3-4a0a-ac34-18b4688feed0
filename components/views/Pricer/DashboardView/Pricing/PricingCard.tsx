import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard, { CACardProps } from "@/design-system/molecules/CACard";
import CATable, { CATableData } from "@/design-system/molecules/CATable";

interface PricingCardProps {
  Wrapper?: React.FC<CACardProps>;
  title: string;
  headers?: (string | React.JSX.Element)[];
  tableData: CATableData | undefined;
}

const PricingCard: React.FC<PricingCardProps> = ({ Wrapper = CACard, title, headers, tableData }: PricingCardProps) => {
  return (
    <Wrapper title={title} maxW={500} allowCollapse cardKey={`dashboard-pricing-${title}`}>
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </Wrapper>
  );
};

export default PricingCard;
