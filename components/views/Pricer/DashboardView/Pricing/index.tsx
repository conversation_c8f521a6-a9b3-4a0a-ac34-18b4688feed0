import * as React from "react";
import { Box, Flex } from "@chakra-ui/layout";
import { CustomRequestOptions, useGetBondPricingSWR } from "@/utils/swr-hooks";
import { BondPricingRequest } from "@/types/swr";
import { CA_Mastr_Api_v1_0_Models_BondPricing_Result } from "@/utils/openapi";
import CACard from "@/design-system/molecules/CACard";
import ColumnWrapper from "@/components/helpers/ColumnWrapper";
import Price from "./Price";
import NominalMeasures from "./NominalMeasures";
import OASMeasures from "./DashboardMeasures";
import RiskMeasures from "./RiskMeasures";
import ProjectionsContainer from "./Projections/ProjectionsContainer";
import Gpl from "./Gpl";

type PricingProps = {
  bondPricingRequest: BondPricingRequest;
  requestOpts: CustomRequestOptions;
  placeholder?: boolean;
};

export type PricingResultProps = {
  title: string;
  bondPricingResult: CA_Mastr_Api_v1_0_Models_BondPricing_Result | undefined;
};

const firstColumnComponents: {
  name: string;
  component: React.FC<PricingResultProps>;
  hidePlaceholder?: boolean;
}[] = [
  { name: "Price", component: Price },
  { name: "GPL", component: Gpl, hidePlaceholder: true },
  { name: "Nominal", component: NominalMeasures },
  { name: "OAS", component: OASMeasures },
  { name: "Risk", component: RiskMeasures },
];
const Pricing: React.FC<PricingProps> = ({ bondPricingRequest, requestOpts }: PricingProps) => {
  const { data, error } = useGetBondPricingSWR(bondPricingRequest, requestOpts);

  return (
    <Box>
      {error ? (
        <ColumnWrapper size="dashboard-col-level-four" w="100%">
          <CACard isPlaceholderCard title="Price" maxW={{ base: 350, md: "100%" }} />
        </ColumnWrapper>
      ) : (
        <Flex direction="row" flexWrap="wrap" position="relative" p={0}>
          <ColumnWrapper size="dashboard-col-level-four" mt={{ base: "0.5rem", sm: 0 }}>
            {firstColumnComponents.map(({ name, component: SubComponent }) => (
              <SubComponent key={name} title={name} bondPricingResult={error ? {} : data?.results?.[0] ?? undefined} />
            ))}
          </ColumnWrapper>
          <ColumnWrapper size="dashboard-col-level-four" mt={{ base: "0.5rem", sm: 0 }}>
            <ProjectionsContainer title="" bondPricingResult={data?.results?.[0] ?? undefined} />
          </ColumnWrapper>
        </Flex>
      )}
    </Box>
  );
};

const PricingWrapper: React.FC<PricingProps> = ({ bondPricingRequest, requestOpts, placeholder }: PricingProps) => {
  if (placeholder) {
    return (
      <Flex direction="row" flexWrap="wrap" position="relative" p={0}>
        <ColumnWrapper size="dashboard-col-level-four" mt={{ base: "0.5rem", sm: 0 }}>
          {firstColumnComponents.map(({ name, hidePlaceholder }) => {
            return hidePlaceholder ? null : (
              <CACard
                key={name}
                allowCollapse
                cardKey={`dashboard-pricing-${name}`}
                title={name}
                maxW={500}
                cardBodyStyle={{ h: "5.5rem" }}
              />
            );
          })}
        </ColumnWrapper>
        <ColumnWrapper size="dashboard-col-level-four" mt={{ base: "0.5rem", sm: 0 }}>
          <CACard
            allowCollapse
            cardKey="dashboard-prepay-projections"
            title={"Prepay Projections"}
            maxW={500}
            cardBodyStyle={{ h: "27rem" }}
          />
        </ColumnWrapper>
      </Flex>
    );
  } else {
    return <Pricing bondPricingRequest={bondPricingRequest} requestOpts={requestOpts} />;
  }
};

export default React.memo(PricingWrapper);
