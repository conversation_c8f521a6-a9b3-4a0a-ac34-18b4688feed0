import * as React from "react";
import { getDividedValue, getFormattedNumberFixed } from "@/utils/helpers";
import { CATableData } from "@/design-system/molecules/CATable";
import PricingCard from "./PricingCard";
import { PricingResultProps } from ".";

const RiskMeasures: React.FC<PricingResultProps> = ({ title, bondPricingResult }: PricingResultProps) => {
  const headers = ["", "Duration", "DV01"];

  const tableData = React.useMemo((): CATableData | undefined => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "Effective",
        values: [
          getFormattedNumberFixed(1)(bondPricingResult.effective_duration),
          getFormattedNumberFixed(4)(bondPricingResult.effective_dv01),
        ],
      },
      {
        name: "Convexity",
        values: [
          getFormattedNumberFixed(1)(bondPricingResult.effective_convexity),
          getFormattedNumberFixed(4)(bondPricingResult.effective_convexity_dv01),
        ],
      },
      {
        name: "Prepay",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.prepay_duration),
          getFormattedNumberFixed(4)(bondPricingResult.prepay_dv01),
        ],
      },
      {
        name: "dOAS/dPrepay",
        values: [
          getDividedValue(bondPricingResult.prepay_duration, bondPricingResult.spread_duration),
          getDividedValue(bondPricingResult.prepay_dv01, bondPricingResult.spread_dv01),
        ],
      },
      {
        name: "Current Coupon",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.current_coupon_duration),
          getFormattedNumberFixed(4)(bondPricingResult.current_coupon_dv01),
        ],
      },
      {
        name: "Spread",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.spread_duration),
          getFormattedNumberFixed(4)(bondPricingResult.spread_dv01),
        ],
      },
      {
        name: "Volatility",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.volatility_duration),
          getFormattedNumberFixed(4)(bondPricingResult.volatility_dv01),
        ],
      },
      bondPricingResult.key_rate2_yr_duration && !bondPricingResult.key_rate1_yr_duration
        ? undefined
        : {
            name: "1yr",
            values: [
              getFormattedNumberFixed(3)(bondPricingResult.key_rate1_yr_duration),
              getFormattedNumberFixed(4)(bondPricingResult.key_rate1_yr_dv01),
            ],
          },
      {
        name: "2yr",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.key_rate2_yr_duration),
          getFormattedNumberFixed(4)(bondPricingResult.key_rate2_yr_dv01),
        ],
      },
      {
        name: "5yr",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.key_rate5_yr_duration),
          getFormattedNumberFixed(4)(bondPricingResult.key_rate5_yr_dv01),
        ],
      },
      {
        name: "10yr",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.key_rate10_yr_duration),
          getFormattedNumberFixed(4)(bondPricingResult.key_rate10_yr_dv01),
        ],
      },
      {
        name: "30yr",
        values: [
          getFormattedNumberFixed(3)(bondPricingResult.key_rate30_yr_duration),
          getFormattedNumberFixed(4)(bondPricingResult.key_rate30_yr_dv01),
        ],
      },
    ].filter((row) => !!row) as CATableData;
  }, [bondPricingResult]);

  return <PricingCard title={title} {...{ headers, tableData }} />;
};

export default RiskMeasures;
