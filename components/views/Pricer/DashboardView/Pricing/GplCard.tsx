import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard, { CACardProps } from "@/design-system/molecules/CACard";
import CATable, { CATableData } from "@/design-system/molecules/CATable";
import { ApiError } from "@/utils/openapi";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { getAPIErrorMessage } from "@/utils/helpers";

interface GplCardProps {
  Wrapper?: React.FC<CACardProps>;
  title: string;
  headers?: (string | React.JSX.Element)[];
  tableData: CATableData | undefined;
  error?: ApiError;
}

const GplCard: React.FC<GplCardProps> = ({ Wrapper = CACard, title, headers, tableData, error }: GplCardProps) => {
  return (
    <Wrapper title={title} allowCollapse cardKey={`dashboard-${title}`}>
      {error ? (
        <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
      ) : !tableData ? (
        <Skeleton height="25px" />
      ) : (
        <CATable
          headers={headers}
          data={tableData}
          headerStyles={{ textAlign: "left" }}
          bodyStyles={{ textAlign: "left" }}
        />
      )}
    </Wrapper>
  );
};

export default GplCard;
