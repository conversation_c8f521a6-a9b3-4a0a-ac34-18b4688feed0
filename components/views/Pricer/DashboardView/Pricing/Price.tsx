import * as React from "react";
import { getFormattedNumberFixed } from "@/utils/helpers";
import PricingCard from "./PricingCard";
import { PricingResultProps } from ".";

const Price: React.FC<PricingResultProps> = ({ title, bondPricingResult }: PricingResultProps) => {
  const headers = ["", "Ticks", "Decimal"];

  const tableData = React.useMemo(() => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "Flat",
        values: [bondPricingResult.tick_price ?? "—", getFormattedNumberFixed(6)(bondPricingResult.price)],
      },
      {
        name: "Accrued Int",
        values: [
          bondPricingResult.tick_accrued_interest ?? "—",
          getFormattedNumberFixed(6)(bondPricingResult.accrued_interest),
        ],
      },
      {
        name: "Full",
        values: [
          bondPricingResult.tick_full_price ?? "—",
          getFormattedNumberFixed(6)(bondPricingResult.full_price) || "-",
        ],
      },
    ];
  }, [bondPricingResult]);

  return <PricingCard title={title} {...{ headers, tableData }} />;
};

export default Price;
