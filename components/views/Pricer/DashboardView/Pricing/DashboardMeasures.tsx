import * as React from "react";
import { getDifference, getFormattedNumberFixed } from "@/utils/helpers";
import PricingCard from "./PricingCard";
import { PricingResultProps } from ".";

const DashboardMeasures: React.FC<PricingResultProps> = ({ title, bondPricingResult }: PricingResultProps) => {
  const tableData = React.useMemo(() => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "OAS (bps)",
        values: [getFormattedNumberFixed(0)(bondPricingResult.oas)],
      },
      {
        name: "ZVOAS (bps)",
        values: [getFormattedNumberFixed(0)(bondPricingResult.zvoas)],
      },
      {
        name: "Opt Cost (bps)",
        values: [getDifference(bondPricingResult.zvoas, bondPricingResult.oas, 0)],
      },
    ];
  }, [bondPricingResult]);

  return <PricingCard title={title} {...{ tableData }} />;
};

export default DashboardMeasures;
