import * as React from "react";
import { getFormattedNumberFixed } from "@/utils/helpers";
import PricingCard from "../PricingCard";
import { PricingProjectionProps } from "./ProjectionsContainer";

const PrepayDefaultProjections: React.FC<PricingProjectionProps> = ({
  title,
  bondPricingResult,
  showForward,
}: PricingProjectionProps) => {
  const headers = ["", "CPR (%)", "CRR (%)", "CDR (%)"];

  const tableData = React.useMemo(() => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "1m",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_month_forward_cpr : bondPricingResult.one_month_cpr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_month_forward_crr : bondPricingResult.one_month_crr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_month_forward_cdr : bondPricingResult.one_month_cdr
          ),
        ],
      },
      {
        name: "1yr",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_year_forward_cpr : bondPricingResult.one_year_cpr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_year_forward_crr : bondPricingResult.one_year_crr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_year_forward_cdr : bondPricingResult.one_year_cdr
          ),
        ],
      },
      {
        name: "3yr",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.three_year_forward_cpr : bondPricingResult.three_year_cpr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.three_year_forward_crr : bondPricingResult.three_year_crr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.three_year_forward_cdr : bondPricingResult.three_year_cdr
          ),
        ],
      },
      {
        name: "5yr",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.five_year_forward_cpr : bondPricingResult.five_year_cpr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.five_year_forward_crr : bondPricingResult.five_year_crr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.five_year_forward_cdr : bondPricingResult.five_year_cdr
          ),
        ],
      },
      {
        name: "LT",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.forward_long_term_cpr : bondPricingResult.long_term_cpr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.forward_long_term_crr : bondPricingResult.long_term_crr
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.forward_long_term_cdr : bondPricingResult.long_term_cdr
          ),
        ],
      },
    ];
  }, [bondPricingResult, showForward]);

  return <PricingCard title={title} {...{ headers, tableData }} />;
};

export default PrepayDefaultProjections;
