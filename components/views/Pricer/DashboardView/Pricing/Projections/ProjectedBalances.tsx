import * as React from "react";
import { Text } from "@chakra-ui/layout";
import { getFormattedNumberFixed } from "@/utils/helpers";
import PricingCard from "../PricingCard";
import { PricingProjectionProps } from "./ProjectionsContainer";

const oneHundredMinus = (pct: number | null | undefined) => {
  if (typeof pct !== "number") return undefined;
  return 100 - pct;
};

const ProjectedBalances: React.FC<PricingProjectionProps> = ({
  title,
  bondPricingResult,
  showForward,
}: PricingProjectionProps) => {
  const headers = [
    "",
    <Text key="paid-down" variant="tableHead" title="Cumulative (CRR + CPR) + Amortization">
      Paid Down (%)
    </Text>,
    "Remaining (%)",
  ];

  const tableData = React.useMemo(() => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "1yr",
        values: [
          showForward ? "-" : getFormattedNumberFixed(1)(oneHundredMinus(bondPricingResult.one_year_rem_bal_pct)),
          showForward ? "-" : getFormattedNumberFixed(1)(bondPricingResult.one_year_rem_bal_pct),
        ],
      },
      {
        name: "3yr",
        values: [
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.three_year_forward_rem_bal_pct : bondPricingResult.three_year_rem_bal_pct
            )
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.three_year_forward_rem_bal_pct : bondPricingResult.three_year_rem_bal_pct
          ),
        ],
      },
      {
        name: "5yr",
        values: [
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.five_year_forward_rem_bal_pct : bondPricingResult.five_year_rem_bal_pct
            )
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.five_year_forward_rem_bal_pct : bondPricingResult.five_year_rem_bal_pct
          ),
        ],
      },
      {
        name: "LT",
        values: [
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.life_forward_rem_bal_pct : bondPricingResult.life_rem_bal_pct
            )
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.life_forward_rem_bal_pct : bondPricingResult.life_rem_bal_pct
          ),
        ],
      },
    ];
  }, [bondPricingResult, showForward]);

  return <PricingCard title={title} {...{ headers, tableData }} />;
};

export default ProjectedBalances;
