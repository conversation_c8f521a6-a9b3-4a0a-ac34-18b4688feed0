import * as React from "react";
import { Text } from "@chakra-ui/layout";
import { getFormattedNumberFixed } from "@/utils/helpers";
import PricingCard from "../PricingCard";
import { PricingProjectionProps } from "./ProjectionsContainer";

const CumulativeProjections: React.FC<PricingProjectionProps> = ({
  title,
  bondPricingResult,
  showForward,
}: PricingProjectionProps) => {
  const headers = [
    "",
    "CRR (%)",
    "CDR (%)",
    <Text key="amortization" variant="tableHead" title="Amortization (%)">
      Amort. (%)
    </Text>,
    <Text key="paid-down" variant="tableHead" title="Cumulative (CRR + CPR) + Amortization">
      Paid Down (%)
    </Text>,
  ];
  const oneHundredMinus = (pct: number | null | undefined) => {
    if (typeof pct !== "number") return undefined;
    return 100 - pct;
  };

  const tableData = React.useMemo(() => {
    if (!bondPricingResult) {
      return undefined;
    }

    return [
      {
        name: "1yr",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_year_forward_cum_prepay_pct : bondPricingResult.one_year_cum_prepay_pct
          ),
          getFormattedNumberFixed(1)(
            showForward
              ? bondPricingResult.one_year_forward_cum_default_pct
              : bondPricingResult.one_year_cum_default_pct
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.one_year_forward_cum_amort_pct : bondPricingResult.one_year_cum_amort_pct
          ),
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.one_year_forward_rem_bal_pct : bondPricingResult.one_year_rem_bal_pct
            )
          ),
        ],
      },
      {
        name: "3yr",
        values: [
          getFormattedNumberFixed(1)(
            showForward
              ? bondPricingResult.three_year_forward_cum_prepay_pct
              : bondPricingResult.three_year_cum_prepay_pct
          ),
          getFormattedNumberFixed(1)(
            showForward
              ? bondPricingResult.three_year_forward_cum_default_pct
              : bondPricingResult.three_year_cum_default_pct
          ),
          getFormattedNumberFixed(1)(
            showForward
              ? bondPricingResult.three_year_forward_cum_amort_pct
              : bondPricingResult.three_year_cum_amort_pct
          ),
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.three_year_forward_rem_bal_pct : bondPricingResult.three_year_rem_bal_pct
            )
          ),
        ],
      },
      {
        name: "5yr",
        values: [
          getFormattedNumberFixed(1)(
            showForward
              ? bondPricingResult.five_year_forward_cum_prepay_pct
              : bondPricingResult.five_year_cum_prepay_pct
          ),
          getFormattedNumberFixed(1)(
            showForward
              ? bondPricingResult.five_year_forward_cum_default_pct
              : bondPricingResult.five_year_cum_default_pct
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.five_year_forward_cum_amort_pct : bondPricingResult.five_year_cum_amort_pct
          ),
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.five_year_forward_rem_bal_pct : bondPricingResult.five_year_rem_bal_pct
            )
          ),
        ],
      },
      {
        name: "LT",
        values: [
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.life_forward_cum_prepay_pct : bondPricingResult.life_cum_prepay_pct
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.life_forward_cum_default_pct : bondPricingResult.life_cum_default_pct
          ),
          getFormattedNumberFixed(1)(
            showForward ? bondPricingResult.life_forward_cum_amort_pct : bondPricingResult.life_cum_amort_pct
          ),
          getFormattedNumberFixed(1)(
            oneHundredMinus(
              showForward ? bondPricingResult.life_forward_rem_bal_pct : bondPricingResult.life_rem_bal_pct
            )
          ),
        ],
      },
    ];
  }, [bondPricingResult, showForward]);

  return <PricingCard title={title} {...{ headers, tableData }} />;
};

export default CumulativeProjections;
