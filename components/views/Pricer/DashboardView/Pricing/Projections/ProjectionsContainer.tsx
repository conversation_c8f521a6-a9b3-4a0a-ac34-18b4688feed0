import * as React from "react";
import { Box, Flex, VStack } from "@chakra-ui/layout";
import CACard from "@/design-system/molecules/CACard";
import CAHeading from "@/design-system/atoms/CAHeading";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { PricingResultProps } from "..";
import PrepayDefaultProjections from "./PrepayDefaultProjections";
import CumulativeProjections from "./CumulativeProjections";
const projectionsComponents: { name: string; component: React.FC<PricingProjectionProps> }[] = [
  { name: "Average", component: PrepayDefaultProjections },
  { name: "Cumulative", component: CumulativeProjections },
];

const ProjectionsContainer: React.FC<PricingResultProps> = ({ bondPricingResult }: PricingResultProps) => {
  const [showForward, setShowForward] = React.useState(false);

  const toggleStaticForward = (key: string) => {
    if (key === "forward" && !showForward) {
      setShowForward(true);
    } else if (key === "static" && showForward) {
      setShowForward(false);
    }
  };

  return (
    <CACard
      title="Prepay Projections"
      cardBodyStyle={{ p: 0 }}
      maxW={500}
      allowCollapse
      cardKey="dashboard-prepay-projections"
    >
      <Box>
        <Flex justifyContent="flex-end" px={2}>
          <ToggleButtonGroup
            buttons={[
              { label: "Static", value: "static" },
              { label: "Forward", value: "forward" },
            ]}
            selectedButton={showForward ? "forward" : "static"}
            onChange={toggleStaticForward}
          />
        </Flex>
        <VStack spacing={0} pt={5} alignItems="stretch">
          {projectionsComponents.map(({ name, component: SubComponent }) => (
            <Box key={name}>
              <CAHeading as="h3" textAlign="center">
                {name}
              </CAHeading>
              <SubComponent title={""} bondPricingResult={bondPricingResult} showForward={showForward} />
            </Box>
          ))}
        </VStack>
      </Box>
    </CACard>
  );
};

export default ProjectionsContainer;

export interface PricingProjectionProps extends PricingResultProps {
  showForward?: boolean;
}
