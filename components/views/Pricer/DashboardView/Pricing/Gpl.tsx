import * as React from "react";
import { getFormattedNumberFixed, getSumValue } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_BondPricing_Result } from "@/utils/openapi";
import GplCard from "./GplCard";

type GplProps = {
  title: string;
  bondPricingResult: CA_Mastr_Api_v1_0_Models_BondPricing_Result | undefined;
};
const Gpl: React.FC<GplProps> = ({ title, bondPricingResult }: GplProps) => {
  const headers = ["", "$", "%"];
  const gplDataExists: boolean = bondPricingResult
    ? "price_coupon" in bondPricingResult && "price_penalty" in bondPricingResult
    : false;

  const getPercentageValue = (value: number, totalValue: number) => {
    const percentValue = (value / totalValue) * 100;
    return getFormattedNumberFixed(0)(percentValue);
  };

  const tableData = React.useMemo(() => {
    const couponPercentageValue = getPercentageValue(
      parseFloat(getFormattedNumberFixed(6)(bondPricingResult?.price_coupon)),
      parseFloat(getSumValue(bondPricingResult?.price_coupon, bondPricingResult?.price_penalty, 6))
    );
    const prepayPenaltyPercentageValue = 100 - +couponPercentageValue;

    return [
      {
        name: "Coupon",
        values: [getFormattedNumberFixed(6)(bondPricingResult?.price_coupon), couponPercentageValue],
      },
      {
        name: "Prepay Penalty",
        values: [getFormattedNumberFixed(6)(bondPricingResult?.price_penalty), prepayPenaltyPercentageValue],
      },
      {
        name: "Total",
        values: [
          getSumValue(bondPricingResult?.price_coupon, bondPricingResult?.price_penalty, 6),
          getSumValue(+couponPercentageValue, +prepayPenaltyPercentageValue, 0),
        ],
      },
    ];
  }, [bondPricingResult]);

  return gplDataExists ? <GplCard title={title} {...{ headers, tableData }} /> : null;
};

export default Gpl;
