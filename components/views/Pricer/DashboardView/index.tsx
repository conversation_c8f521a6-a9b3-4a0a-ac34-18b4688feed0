import * as React from "react";
import { Box, Flex } from "@chakra-ui/react";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerDashboardPage } from "@/contexts/PageContexts/PricerDashboardPageContext";
import {
  DEC_REGEX,
  POSITIVE_DEC_REGEX,
  TICK_REGEX,
  getUuid,
  parseDateToYYYYMMDD,
  tickToDecimal,
} from "@/utils/helpers";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import ColumnWrapper from "@/components/helpers/ColumnWrapper";
import { getRequests } from "@/utils/helpers/pricer/dashboard";
import { showErrorToast } from "@/design-system/theme/toast";
import EditColor, { EditColorHandler } from "@/components/helpers/EditColor";
import CACard from "@/design-system/molecules/CACard";
import PricerHeader from "../PricerHeader";
import { BondPricingStopWatchWrapper } from "../PricerStopWatchWrapper";
import { PricerModuleProps } from "..";
import { BondPricingProgressIndicator } from "../PricerProgressIndicatorWrapper";
import { BondPricingStopRunningWrapper } from "../PricerStopRunningWrapper";
import HistoricalPrepay from "../shared/HistoricalPrepay";
import Security from "./Security";
import Collateral from "./Collateral";
import DashboardInputs, { DashboardInputHandler } from "./DashboardInputs";
import Pricing from "./Pricing";
import InterestRates from "./InterestRates";
import MortgageRates from "./MortgageRates";
import PrepayTracking from "./PrepayTracking";
import PrepayProjections from "./PrepayProjection";
import Deal from "./Deal";
import Intex from "./Intex";

const PricerDashboardView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, userSettingsCopy, security_info, latestFactorDate },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { app, page, lastRunId, dashboardPageSettings, lastRun, noCache, isOldRun, api_start_time, api_end_time },
    action: { updateDashboardPageSettings, run },
  } = usePricerDashboardPage();
  const { pushWithoutRendering } = useQueryParameters();
  const [colorId, setColorId] = React.useState<number | null>();

  const dashboardInputRef = React.useRef<DashboardInputHandler>(null);
  const editColorRef = React.useRef<EditColorHandler>(null);

  const {
    bondIndicativesRequest,
    bondPricingRequest,
    bondPrepayTrackingRequest,
    bondPrepayProjectionsRequest,
    collateralSummaryRequest,
    requestOpts,
  } = getRequests({
    userSettings,
    dashboardPageSettings,
    latestFactorDate,
    app,
    page,
    noCache,
    security_info,
    lastRun,
    lastRunId,
    isOldRun,
    bond_name,
  });

  const submitDashboardInputs = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    dashboardInputRef.current?.handleSubmit(
      async (data) => {
        if (data.pricing_level && data.pricing_type !== "price") {
          data.pricing_level = `${data.pricing_level}`.match(DEC_REGEX)
            ? Number(data.pricing_level)
            : tickToDecimal(`${data.pricing_level}`);
        }

        if (
          data.pricing_type === "price" &&
          !(`${data.pricing_level}`.match(TICK_REGEX) || `${data.pricing_level}`.match(POSITIVE_DEC_REGEX))
        ) {
          showErrorToast("Validation", "Please enter a valid positive pricing value.");
          return;
        }

        updateDashboardPageSettings(data);
        updatePricerUserSettingsCopy(userSettings);
        const run_id = getUuid();
        run({ no_cache, runId: run_id });
        setIsTimerRunning(true);
        pushWithoutRendering({ run_id });
        //If color is changed, show modal to confirm if user wants to associate color with run
        if (colorId && !lastRunId) {
          await editColorRef.current?.associateWithRun(run_id);
        } else {
          editColorRef.current?.resetColorForm();
        }
      },
      (errors) => {
        if (errors?.pricing_level?.type === "validate") {
          showErrorToast("Validation", "Please enter a valid pricing value.");
        }
      }
    )();
  };

  return (
    <Box>
      <PricerHeader
        hasColorSupport
        title={pageTitle}
        bond_name={bond_name}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        onRunClick={submitDashboardInputs}
        stopWatch={
          <BondPricingStopWatchWrapper
            bondPricingRequest={bondPricingRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BondPricingProgressIndicator
            onSuccess={() => dashboardInputRef.current?.setFocus("pricing_level")}
            bondPricingRequest={bondPricingRequest}
            requestOpts={requestOpts}
          />
        }
        stopRunning={
          <BondPricingStopRunningWrapper bondPricingRequest={bondPricingRequest} requestOpts={requestOpts} />
        }
        runActionComponent={
          <EditColor
            colorId={colorId}
            ref={editColorRef}
            bondName={
              (security_info?.bond_type === "POOL" ? security_info?.poollist_name : security_info?.yieldbook_name) ??
              bond_name
            }
            runId={lastRunId}
            bondPricingRequest={bondPricingRequest}
            indicativesRequest={bondIndicativesRequest}
            requestOpts={requestOpts}
            updateColorId={setColorId}
            isOldRun={isOldRun}
          />
        }
      />
      <Flex direction="row" flexWrap="wrap" position="relative" pt="0" px="3" pb="3">
        <ColumnWrapper size="dashboard-col-container" p={0}>
          <ColumnWrapper size="dashboard-col-level-one" p={0}>
            <Flex direction="row" flexWrap="wrap" position="relative" p={0}>
              <ColumnWrapper size="dashboard-col-level-two">
                <DashboardInputs ref={dashboardInputRef} />
                <InterestRates
                  lastRun={lastRun}
                  marketRatesRequest={
                    isOldRun && userSettingsCopy.curve_date && userSettingsCopy.model_version
                      ? {
                          asOfDate: parseDateToYYYYMMDD(userSettingsCopy.curve_date),
                          modelVersion: userSettingsCopy.model_version,
                        }
                      : {
                          asOfDate: parseDateToYYYYMMDD(userSettings.curve_date),
                          modelVersion: userSettings.model_version,
                        }
                  }
                />
                <MortgageRates
                  lastRun={lastRun}
                  marketRatesRequest={
                    isOldRun && userSettingsCopy.curve_date && userSettingsCopy.model_version
                      ? {
                          asOfDate: parseDateToYYYYMMDD(userSettingsCopy.curve_date),
                          modelVersion: userSettingsCopy.model_version,
                        }
                      : {
                          asOfDate: parseDateToYYYYMMDD(userSettings.curve_date),
                          modelVersion: userSettings.model_version,
                        }
                  }
                />
              </ColumnWrapper>
              <ColumnWrapper size="dashboard-col-level-three" p={0}>
                <Flex direction="row" flexWrap="wrap" position="relative" p={0}>
                  <ColumnWrapper size="dashboard-col-level-four">
                    <Security bondIndicativesRequest={bondIndicativesRequest} />
                    <Deal bondIndicativesRequest={bondIndicativesRequest} />
                    <Intex bondIndicativesRequest={bondIndicativesRequest} />
                  </ColumnWrapper>
                  <ColumnWrapper size="dashboard-col-level-four">
                    <Collateral collateralSummaryRequest={collateralSummaryRequest} isOldRun={isOldRun} />
                    <CACard title="Historical" maxW={500} allowCollapse cardKey="dashboard-historical">
                      <HistoricalPrepay bond_name={bond_name} asOf={parseDateToYYYYMMDD(latestFactorDate)} />
                    </CACard>
                  </ColumnWrapper>
                </Flex>
              </ColumnWrapper>
            </Flex>
          </ColumnWrapper>
        </ColumnWrapper>
        <ColumnWrapper size="dashboard-col-container" p={0}>
          <Flex direction="row" flexWrap="wrap" position="relative" p={0}>
            <ColumnWrapper size="dashboard-col-level-three" p={0}>
              <Pricing
                placeholder={!bond_name || dashboardPageSettings.pricing_level == null || !lastRun}
                requestOpts={requestOpts}
                bondPricingRequest={bondPricingRequest}
              />
            </ColumnWrapper>
            <ColumnWrapper size="dashboard-col-level-two">
              {bondPrepayTrackingRequest && (
                <PrepayTracking
                  placeholder={!bond_name || dashboardPageSettings.pricing_level == null || !lastRun}
                  // do not stop the timer when tracking request is over (stop the timer only when pricing request is over)
                  requestOpts={{ ...requestOpts, bypassStopwatch: true }}
                  bondPrepayTrackingRequest={bondPrepayTrackingRequest}
                />
              )}
              {bondPrepayProjectionsRequest && (
                <PrepayProjections
                  placeholder={!bond_name || dashboardPageSettings.pricing_level == null || !lastRun}
                  // do not stop the timer when tracking request is over (stop the timer only when pricing request is over)
                  requestOpts={{ ...requestOpts, bypassStopwatch: true }}
                  bondPrepayProjectionsRequest={bondPrepayProjectionsRequest}
                />
              )}
            </ColumnWrapper>
          </Flex>
        </ColumnWrapper>
      </Flex>
    </Box>
  );
};

export default PricerDashboardView;
