import * as React from "react";
import { Controller, UseFormHandleSubmit, UseFormSetFocus, useForm } from "react-hook-form";
import { Box, Flex, Tooltip, VStack } from "@chakra-ui/react";
import {
  CA_Mastr_Models_v1_0_Models_BondType,
  CA_Mastr_Models_v1_0_Models_PrepayMethod,
  CA_Mastr_Models_v1_0_Models_PricingType,
} from "@/utils/openapi";
import CACard from "@/design-system/molecules/CACard";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CADateInput from "@/design-system/molecules/CADateInput";
import CAInput from "@/design-system/molecules/CAInput";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerDashboardPage } from "@/contexts/PageContexts/PricerDashboardPageContext";
import { dashboardPageSettingsType } from "@/contexts/PageContexts/PricerDashboardPageContext/PricerDashboardPageContextTypes";
import {
  DEC_REGEX,
  TICK_REGEX,
  getCDUDateKeyFromSubType,
  getDisplayValueByKey,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getFormattedYearMonth,
  getInfoMsg,
  tickToDecimal,
} from "@/utils/helpers";
import useResetFormData from "@/hooks/useResetFormData";
import useUpdateSettleDate from "@/hooks/useUpdateSettleDate";
import useOnPrepayModelChange from "@/hooks/useOnPrepayModelChange";
import useOnPricingLevelChange from "@/hooks/useOnPricingLevelChange";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import { DateSettingsOverrides } from "@/components/helpers/DateSettingsOverrides/DateSettingsOverrides";
import { CURVE_SHIFT_OPTIONS } from "@/constants/enums";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import ModelDialSelectorWrapper from "../shared/ModelDialSelectorWrapper";
import PrepayVectorSelector from "../shared/PrepayVectorSelector";
import ReplineDialsSwitch from "../shared/ReplineDialsSwitch";
import BondIndicativesValue from "../shared/BondIndicativesValue";

export interface DashboardInputHandler {
  handleSubmit: UseFormHandleSubmit<dashboardPageSettingsType>;
  setFocus: UseFormSetFocus<dashboardPageSettingsType>;
}

const calculationOutputParametersLabel = {
  oas: "OAS",
  effective_duration: "Effective Duration",
  key_rate_duration: "Key Rate Durations",
  current_coupon_duration: "Current Coupon Duration",
  volatility_duration: "Volatility Duration",
  prepay_duration: "Prepay Duration",
};

const DashboardInputs: React.ForwardRefRenderFunction<DashboardInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { bond_name, userSettings, userSettingsCopy, security_info },
  } = usePricerModule();
  const {
    state: { app, page, noCache, dashboardPageSettings, dashboardPageSettingsCopy, isOldRun, lastRun },
  } = usePricerDashboardPage();
  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);

  const {
    handleSubmit,
    register,
    watch,
    reset,
    control,
    setValue,
    setFocus,
    formState: { touchedFields },
  } = useForm<dashboardPageSettingsType>();
  const [
    watch_prepay_model_type,
    watch_prepay_percentage,
    watch_pricing_level,
    watch_pricing_type,
    watch_settle_date,
    watch_modify_class,
    watch_curve_shift,
    watch_index_rate_string,
    calculation_output_parameters,
  ] = watch([
    "prepay_model_type",
    "prepay_percentage",
    "pricing_level",
    "pricing_type",
    "settle_date",
    "modify_class",
    "curve_shift",
    "index_rate_string",
    "calculation_output_parameters",
  ]);

  useResetFormData({ reset, formData: dashboardPageSettings });

  useUpdateSettleDate<typeof dashboardPageSettings>({
    pageSettings: dashboardPageSettings,
    setSettleDate: (settleDate) => setValue("settle_date", settleDate),
    isOldRun,
    security_info,
    userSettings,
    userSettingsCopy,
    bond_name,
    app,
    page,
  });

  useOnPricingLevelChange<dashboardPageSettingsType>({
    currentPricingLevel: watch_pricing_level,
    setValue,
  });

  useOnPrepayModelChange({
    prepayModelType: watch_prepay_model_type,
    prepayPercentage: watch_prepay_percentage,
    setValue,
    isTouched: !!touchedFields.prepay_model_type,
    isOldRun,
  });

  React.useEffect(() => {
    if (!calculation_output_parameters?.includes("oas") && calculation_output_parameters?.length > 0) {
      setValue("calculation_output_parameters", [...(calculation_output_parameters ?? []), "oas"]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calculation_output_parameters]);

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
    setFocus,
  }));

  const toggleSwitch = React.useCallback(
    (name: "calculate_sections" | "calculation_output_parameters", values: string[], value: string) => {
      const newValues = [...(values || [])];
      if (newValues.includes(value)) {
        newValues.splice(values.indexOf(value), 1);
      } else {
        newValues.push(value);
      }
      setValue(name, newValues);
    },
    [setValue]
  );

  React.useEffect(() => {
    // FOcus on the pricing level input on a new bond load
    setTimeout(() => {
      setFocus("pricing_level");
    }, 500);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  getDisplayValueByKey(watch_pricing_type);
  return (
    <>
      <CACard
        key={`${dashboardPageSettings.loadedFromLocalStorage}-1`}
        maxW={500}
        title="Pricing"
        allowCollapse
        cardKey="dashboard-inputs"
        overflow="visible"
      >
        <VStack alignItems="stretch" spacing={4}>
          <Box>
            <CASelectDropdown
              data-testid="pricing-type"
              label={"Type"}
              width={"6rem"}
              info={getInfoMsg(
                getDisplayValueByKey(watch_pricing_type),
                getDisplayValueByKey(dashboardPageSettingsCopy.pricing_type)
              )}
              {...register("pricing_type", {
                required: true,
                onChange: (e) => {
                  if (`${watch_pricing_level}`.match(TICK_REGEX) && e.target.value !== "price") {
                    setValue("pricing_level", undefined);
                  }
                },
              })}
              options={[
                {
                  id: 0,
                  value: CA_Mastr_Models_v1_0_Models_PricingType.PRICE,
                  displayValue: getDisplayValueByKey(CA_Mastr_Models_v1_0_Models_PricingType.PRICE),
                },
                {
                  id: 1,
                  value: CA_Mastr_Models_v1_0_Models_PricingType.OAS,
                  displayValue: getDisplayValueByKey(CA_Mastr_Models_v1_0_Models_PricingType.OAS),
                },
                {
                  id: 2,
                  value: CA_Mastr_Models_v1_0_Models_PricingType.YIELD,
                  displayValue: getDisplayValueByKey(CA_Mastr_Models_v1_0_Models_PricingType.YIELD),
                },
              ]}
            />
            {/* TODO Make Text Input to accept Ticks */}
            <CAInput
              data-test="pricing-level"
              label={"Value"}
              width={"6rem"}
              info={getInfoMsg(watch_pricing_level, dashboardPageSettingsCopy.pricing_level)}
              {...register("pricing_level", {
                required: true,
                validate: (v) => {
                  const value = `${v}`;
                  if (watch_pricing_type === CA_Mastr_Models_v1_0_Models_PricingType.PRICE) {
                    return !!value.match(DEC_REGEX) || Boolean(value.match(TICK_REGEX));
                  }
                  return !!value.match(DEC_REGEX) || !isNaN(tickToDecimal(value));
                },
              })}
            />
          </Box>
          <Box>
            <PricerValueDisplayWrapper
              name="Curve Date"
              link
              dateFormatter={getFormattedLocaleDate}
              value={getFormattedLocaleDate(userSettings.curve_date)}
              _key="curve_date"
            />
            <PricerValueDisplayWrapper
              name="Pricing Date"
              link
              dateFormatter={getFormattedLocaleDate}
              value={getFormattedLocaleDate(userSettings.pricing_date)}
              _key="pricing_date"
            />
            <Controller
              name={"settle_date"}
              control={control}
              rules={{ required: true }}
              render={({ field: { name, value, onChange, ref } }) => (
                <CADateInput
                  ref={ref}
                  selectedDate={value}
                  width={"6rem"}
                  minDate={userSettings.curve_date}
                  onChange={onChange}
                  name={name}
                  label={"Settle Date"}
                  info={getInfoMsg(watch_settle_date, dashboardPageSettingsCopy.settle_date)}
                  includeNonBusinessDays={true}
                />
              )}
            />
          </Box>
          <Box>
            <CASelectDropdown
              label={"Prepay Model"}
              width={"6rem"}
              info={getInfoMsg(watch_prepay_model_type, dashboardPageSettingsCopy.prepay_model_type)}
              {...register("prepay_model_type", {
                required: true,
              })}
              options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_models)}
            />
            <CAInput
              type="number"
              label={"Multiplier (%)"}
              width={"6rem"}
              info={getInfoMsg(watch_prepay_percentage, dashboardPageSettingsCopy.prepay_percentage)}
              {...register("prepay_percentage", {
                valueAsNumber: true,
                required: true,
              })}
            />
            {watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR && (
              <PrepayVectorSelector lastRun={lastRun} />
            )}
            {(watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO ||
              watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR) && (
              <ModelDialSelectorWrapper lastRun={lastRun} />
            )}
            <Tooltip
              label={`Available options for Numerix: ${CURVE_SHIFT_OPTIONS.join(", ")}`}
              aria-label={`Available options for Numerix: ${CURVE_SHIFT_OPTIONS.join(", ")}`}
              isDisabled={
                metadata?.pricer_settings?.type?.find((l) => l.value === userSettings.type)?.display_value !== "Numerix"
              }
            >
              <CAInput
                type="number"
                inputType="digit"
                label="Curve shift (bps)"
                width="6rem"
                info={getInfoMsg(watch_curve_shift, dashboardPageSettingsCopy.curve_shift)}
                {...register("curve_shift", {
                  valueAsNumber: true,
                  required: true,
                })}
              />
            </Tooltip>
            <Tooltip
              label={
                <>
                  <Box>Enter one of:</Box>
                  <Box>・Index value (e.g. 4.35);</Box>
                  <Box>・C for constant rate;</Box>
                  <Box>・C+/-{`<bps>`} for basis-point adjustment (e.g. C-25, C+10).</Box>
                </>
              }
              placement="top"
            >
              <CAInput
                type="text"
                label="Index Override"
                width="6rem"
                info={getInfoMsg(watch_index_rate_string, dashboardPageSettingsCopy.index_rate_string)}
                {...register("index_rate_string")}
              />
            </Tooltip>
            <ReplineDialsSwitch />
            <PricerValueDisplayWrapper
              name="Prepay Model Version"
              link
              value={userSettings.model_version ?? ""}
              _key="model_version"
            />
            <PricerValueDisplayWrapper
              name="Current Coupon Model"
              link
              value={
                metadata?.pricer_settings?.current_coupon_model?.find(
                  (l) => l.value === userSettings.current_coupon_model
                )?.display_value ?? ""
              }
              _key="current_coupon_model"
            />
            <PricerValueDisplayWrapper
              name="Primary-Secondary Spread Model"
              link
              value={
                metadata?.pricer_settings?.primary_secondary_spread?.find(
                  (l) => l.value === userSettings.primary_secondary_spread
                )?.display_value ?? ""
              }
              _key="primary_secondary_spread"
            />

            <PricerValueDisplayWrapper
              name="Interest Rate Model"
              link
              value={metadata?.pricer_settings?.type?.find((l) => l.value === userSettings.type)?.display_value ?? ""}
              _key="type"
            />

            <PricerValueDisplayWrapper
              name="Interest Rate Paths"
              link
              value={userSettings.interestRatePaths?.toString() ?? ""}
              _key="interestRatePaths"
            />

            <PricerValueDisplayWrapper
              name="Calibration"
              value={
                metadata?.pricer_settings?.calibration?.find((l) => l.value === userSettings.calibration)
                  ?.display_value ?? ""
              }
              _key="calibration"
              link
            />
            <PricerValueDisplayWrapper
              name="Yield Curve Model"
              value={
                metadata?.pricer_settings?.yield_curve_model?.find((l) => l.value === userSettings.yield_curve_model)
                  ?.display_value ?? ""
              }
              _key="yield_curve_model"
              link
            />
            <PricerValueDisplayWrapper
              name="Cello CDU Date"
              link
              value={getFormattedYearMonth(userSettings[cduDateKey])}
              _key={`cello_cdu_dates.${cduDateKey}`}
              dateFormatter={getFormattedYearMonth}
              drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
            />

            <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
            <PricerValueDisplayWrapper
              name="Repline Count"
              value={
                bond_name ? (
                  <BondIndicativesValue
                    bond_name={bond_name}
                    noCache={noCache}
                    location="bond_summary.repline_count"
                    requestMetadata={{ app, page }}
                  />
                ) : (
                  "-"
                )
              }
              _key="repline_count"
            />
            {security_info?.bond_type !== CA_Mastr_Models_v1_0_Models_BondType.CMO && (
              <CASelectDropdown
                label="Cash Flow"
                width="6rem"
                info={getInfoMsg(watch_modify_class, dashboardPageSettingsCopy.modify_class)}
                {...register("modify_class", {
                  required: true,
                })}
                options={getFormattedStringOptions(metadata?.pricer_settings?.bond_class)}
              />
            )}

            <DateSettingsOverrides />
          </Box>
        </VStack>
      </CACard>
      <CACard
        key={`${dashboardPageSettings.loadedFromLocalStorage}-2`}
        maxW={500}
        title="Calculations"
        allowCollapse
        cardKey="dashboard-inputs-calculations"
      >
        <Flex justifyContent="space-between">
          <Box width="50%">
            <Controller
              name={"calculation_output_parameters"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = calculationOutputParametersLabel.oas;
                const value = "oas";

                const isDisabled =
                  calculation_output_parameters?.includes(value) && calculation_output_parameters?.length > 1;

                if (isDisabled) {
                  const otherParameters = calculation_output_parameters
                    ?.filter((p) => p !== value)
                    .map((p) => calculationOutputParametersLabel[p as keyof typeof calculationOutputParametersLabel])
                    .join(", ");
                  return (
                    <Tooltip
                      label={`OAS is required to calculate ${otherParameters}.`}
                      shouldWrapChildren
                      aria-label={`OAS is required to calculate ${otherParameters}.`}
                    >
                      <CASwitchInput
                        label={label}
                        name={name}
                        hideLabel={true}
                        value={value}
                        isChecked={true}
                        isDisabled={true}
                        labelStyle={{ whiteSpace: "normal" }}
                        _disabled={{ opacity: 0.5 }}
                      />
                    </Tooltip>
                  );
                }

                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    labelStyle={{ whiteSpace: "normal" }}
                  />
                );
              }}
            />
            <Controller
              name={"calculation_output_parameters"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = calculationOutputParametersLabel.effective_duration;
                const value = "effective_duration";
                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    marginTop={1}
                    labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                    switchContainerStyle={{
                      alignItems: "start",
                    }}
                  />
                );
              }}
            />
            <Controller
              name={"calculate_sections"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = "Prepay Tracking";
                const value = "tracking";
                const isCello = watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO;
                const tooltipLabel = !isCello
                  ? `Prepay Tracking is not supported for ${watch_prepay_model_type} Prepay Model`
                  : "Prepay Tracking is not supported for TBA Bonds";

                const isSwitchDisabled =
                  security_info?.bond_type === CA_Mastr_Models_v1_0_Models_BondType.TBA || !isCello;
                return (
                  <Tooltip
                    label={tooltipLabel}
                    aria-label={tooltipLabel}
                    isDisabled={!isSwitchDisabled}
                    shouldWrapChildren
                  >
                    <CASwitchInput
                      isDisabled={isSwitchDisabled}
                      label={label}
                      name={name}
                      hideLabel={true}
                      value={value}
                      isChecked={isSwitchDisabled ? false : (values || []).includes(value)}
                      onChange={() => toggleSwitch(name, values, value)}
                      marginTop={1}
                      labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                      switchContainerStyle={{
                        alignItems: "start",
                      }}
                    />
                  </Tooltip>
                );
              }}
            />
            <Controller
              name={"calculate_sections"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = "Prepay Projections";
                const value = "projections";
                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    marginTop={1}
                    labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                    switchContainerStyle={{
                      alignItems: "start",
                    }}
                  />
                );
              }}
            />
          </Box>
          <Box width="50%">
            <Controller
              name={"calculation_output_parameters"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = calculationOutputParametersLabel.key_rate_duration;
                const value = "key_rate_duration";
                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    marginTop={1}
                    labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                    switchContainerStyle={{
                      alignItems: "start",
                    }}
                  />
                );
              }}
            />
            <Controller
              name={"calculation_output_parameters"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = calculationOutputParametersLabel.current_coupon_duration;
                const value = "current_coupon_duration";
                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    marginTop={1}
                    labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                    switchContainerStyle={{
                      alignItems: "start",
                    }}
                  />
                );
              }}
            />
            <Controller
              name={"calculation_output_parameters"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = calculationOutputParametersLabel.volatility_duration;
                const value = "volatility_duration";
                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    marginTop={1}
                    labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                    switchContainerStyle={{
                      alignItems: "start",
                    }}
                  />
                );
              }}
            />
            <Controller
              name={"calculation_output_parameters"}
              control={control}
              render={({ field: { name, value: values } }) => {
                const label = calculationOutputParametersLabel.prepay_duration;
                const value = "prepay_duration";
                return (
                  <CASwitchInput
                    label={label}
                    name={name}
                    hideLabel={true}
                    value={value}
                    isChecked={(values || []).includes(value)}
                    onChange={() => toggleSwitch(name, values, value)}
                    marginTop={1}
                    labelStyle={{ whiteSpace: "normal", marginTop: 0.5 }}
                    switchContainerStyle={{
                      alignItems: "start",
                    }}
                  />
                );
              }}
            />
          </Box>
        </Flex>
      </CACard>
    </>
  );
};

export default React.forwardRef(DashboardInputs);
