import * as React from "react";
import { useGetBondIndicativesSWR } from "@/utils/swr-hooks";
import { BondIndicativesRequest } from "@/types/swr";
import CACard from "@/design-system/molecules/CACard";
import { formatAMPM, getAPIErrorMessage, getFormattedLocaleDate, getFormattedYearMonth } from "@/utils/helpers";
import CACardTable from "@/design-system/molecules/CACardTable";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { usePricerDashboardPage } from "@/contexts/PageContexts/PricerDashboardPageContext";
import { CATableDataType } from "@/design-system/molecules/CATable";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";

type IntexProps = {
  bondIndicativesRequest: BondIndicativesRequest;
};

const Intex: React.FC<IntexProps> = ({ bondIndicativesRequest }: IntexProps) => {
  const {
    state: { security_info },
  } = usePricerModule();
  const {
    state: { isOldRun, lastRun },
  } = usePricerDashboardPage();
  const { data, error } = useGetBondIndicativesSWR(bondIndicativesRequest, {
    lastRun: lastRun ?? "Default",
    isOldRun,
  });

  const tableData = React.useMemo(() => {
    const { deal_mode } = security_info ?? {};

    const { bond_structure } = data || {};

    let lastModified = "";
    if (bond_structure?.latest_cdu_ctrlno) {
      try {
        lastModified =
          getFormattedLocaleDate(bond_structure?.latest_cdu_ctrlno) +
          " " +
          formatAMPM(bond_structure?.latest_cdu_ctrlno.slice(-5));
      } finally {
        // Skip
      }
    }

    const _tableData = [
      bond_structure?.tranche_cdu_date && {
        name: "CDU Date",
        values: [getFormattedYearMonth(bond_structure.tranche_cdu_date)],
      },
      lastModified && { name: "Updated", values: [lastModified] },
      deal_mode && { name: "Deal Mode", values: [deal_mode] },
      bond_structure?.tranche_ground_groups && {
        name: "Group",
        values: [bond_structure.tranche_ground_groups.replace(/\|/g, " ").trim()],
      },
    ];

    return _tableData.filter(Boolean) as CATableDataType[];
  }, [data, security_info]);

  if (!tableData.length && !error) return null;

  return error ? (
    <CACard title="Intex">
      <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
    </CACard>
  ) : (
    <CACardTable
      title="Intex"
      data={tableData}
      isTwoColumn
      maxW={500}
      allowCollapse
      cardKey="dashboard-intex"
      mt={{ base: "0.5rem", sm: 0 }}
    />
  );
};

export default React.memo(Intex);
