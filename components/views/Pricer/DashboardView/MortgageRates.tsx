import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { useGetMortgageRatesSWR } from "@/utils/swr-hooks";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import { getAPIErrorMessage, getFormattedNumberFixed } from "@/utils/helpers";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";

type MortgageRatesProps = {
  marketRatesRequest: {
    asOfDate: string;
    modelVersion?: string;
  };
  lastRun: string | undefined;
};

const MortgageRates: React.FC<MortgageRatesProps> = ({ marketRatesRequest, lastRun }: MortgageRatesProps) => {
  const { asOfDate, modelVersion } = marketRatesRequest;
  const { data: responseData, error } = useGetMortgageRatesSWR(asOfDate, modelVersion, lastRun);

  const tableData = React.useMemo(() => {
    if (!responseData) return undefined;

    const data = responseData;

    const rateValues = data.filter((d) => d.rate_type === "Mortgage Rate (FNCL)");

    return rateValues.map((r) => ({
      name: r.label,
      values:
        r.label === "CMM102 Sprd to Curve (bps)"
          ? [getFormattedNumberFixed(0)(r.series_num_value)]
          : [getFormattedNumberFixed(2)(r.series_num_value)],
    }));
  }, [responseData]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title="Mortgage Rate (FNCL)" maxW={500} allowCollapse cardKey="dashboard-mortgage-FNCL">
      {error ? (
        <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
      ) : !tableData ? (
        <Skeleton height="25px" />
      ) : (
        <CATable data={tableData} />
      )}
    </CACard>
  );
};

export default React.memo(MortgageRates);
