import * as React from "react";
import { useGetBondIndicativesSWR } from "@/utils/swr-hooks";
import { BondIndicativesRequest } from "@/types/swr";
import CACard from "@/design-system/molecules/CACard";
import { getAPIErrorMessage, getFormattedLocaleDate } from "@/utils/helpers";
import CACardTable from "@/design-system/molecules/CACardTable";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { usePricerDashboardPage } from "@/contexts/PageContexts/PricerDashboardPageContext";
import { CATableDataType } from "@/design-system/molecules/CATable";

type DealProps = {
  bondIndicativesRequest: BondIndicativesRequest;
};

const Deal: React.FC<DealProps> = ({ bondIndicativesRequest }: DealProps) => {
  const {
    state: { isOldRun, lastRun },
  } = usePricerDashboardPage();
  const { data, error } = useGetBondIndicativesSWR(bondIndicativesRequest, {
    lastRun: lastRun ?? "Default",
    isOldRun,
  });

  const tableData = React.useMemo(() => {
    const { bond_structure } = data || {};

    const _tableData = [
      bond_structure?.deal_dealer && { name: "Dealer", values: [bond_structure?.deal_dealer] },
      bond_structure?.deal_settle_date && {
        name: "Deal Settle",
        values: [`${getFormattedLocaleDate(bond_structure.deal_settle_date)}`],
      },
    ];

    return _tableData.filter(Boolean) as CATableDataType[];
  }, [data]);

  if (!tableData.length && !error) return null;

  return error ? (
    <CACard title="Deal">
      <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
    </CACard>
  ) : (
    <CACardTable
      title="Deal"
      data={tableData}
      isTwoColumn
      maxW={500}
      allowCollapse
      cardKey="dashboard-deal"
      mt={{ base: "0.5rem", sm: 0 }}
    />
  );
};

export default React.memo(Deal);
