import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { useGetInterestRatesSWR } from "@/utils/swr-hooks";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import { getAPIErrorMessage, getFormattedNumberFixed } from "@/utils/helpers";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";

type InterestRatesProps = {
  marketRatesRequest: {
    asOfDate: string;
    modelVersion?: string;
  };
  lastRun: string | undefined;
};

const InterestRates: React.FC<InterestRatesProps> = ({ marketRatesRequest, lastRun }: InterestRatesProps) => {
  const { asOfDate } = marketRatesRequest;
  const { data: responseData, error } = useGetInterestRatesSWR(asOfDate, lastRun);

  const headers = ["", "Swap (%)", "Treasury (%)", "Spread (%)"];

  const tableData = React.useMemo(() => {
    if (!responseData) return undefined;

    const data = responseData;

    const swapValues = data.filter((d) => d.rate_type === "Swap");
    const treasuryValues = data.filter((d) => d.rate_type === "Treasury");
    const spreadValues = data.filter((d) => d.rate_type === "Spread");

    return [
      {
        name: "1y",
        values: [
          getFormattedNumberFixed(2)(swapValues[0]?.series_num_value),
          getFormattedNumberFixed(2)(treasuryValues[0]?.series_num_value),
          getFormattedNumberFixed(2)(spreadValues[0]?.series_num_value),
        ],
      },
      {
        name: "2y",
        values: [
          getFormattedNumberFixed(2)(swapValues[1]?.series_num_value),
          getFormattedNumberFixed(2)(treasuryValues[1]?.series_num_value),
          getFormattedNumberFixed(2)(spreadValues[1]?.series_num_value),
        ],
      },
      {
        name: "5y",
        values: [
          getFormattedNumberFixed(2)(swapValues[2]?.series_num_value),
          getFormattedNumberFixed(2)(treasuryValues[2]?.series_num_value),
          getFormattedNumberFixed(2)(spreadValues[2]?.series_num_value),
        ],
      },
      {
        name: "10y",
        values: [
          getFormattedNumberFixed(2)(swapValues[3]?.series_num_value),
          getFormattedNumberFixed(2)(treasuryValues[3]?.series_num_value),
          getFormattedNumberFixed(2)(spreadValues[3]?.series_num_value),
        ],
      },
      {
        name: "30y",
        values: [
          getFormattedNumberFixed(2)(swapValues[4]?.series_num_value),
          getFormattedNumberFixed(2)(treasuryValues[4]?.series_num_value),
          getFormattedNumberFixed(2)(spreadValues[4]?.series_num_value),
        ],
      },
    ];
  }, [responseData]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title="Interest Rates" maxW={500} allowCollapse cardKey="dashboard-interest-rates">
      {error ? (
        <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
      ) : !tableData ? (
        <Skeleton height="25px" />
      ) : (
        <CATable headers={headers} data={tableData} />
      )}
    </CACard>
  );
};

export default React.memo(InterestRates);
