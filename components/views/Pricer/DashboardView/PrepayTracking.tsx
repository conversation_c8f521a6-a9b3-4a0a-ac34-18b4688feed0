import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { Flex, VStack } from "@chakra-ui/layout";
import { Box } from "@chakra-ui/react";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { CustomRequestOptions, useGetBondPrepayTrackingSWR } from "@/utils/swr-hooks";
import { BondPrepayTrackingRequest } from "@/types/swr";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import CABarChart from "@/design-system/molecules/CABarChart";
import {
  getChartTooltipFormatter,
  getDifference,
  getDividedValue,
  getFormattedNumberFixed,
  getFormattedYearMonth,
} from "@/utils/helpers";
import { InlineValueDisplay } from "@/components/helpers/ValueDisplay";
import { CA_Mastr_Models_v1_0_Models_Stage } from "@/utils/openapi";

type PrepayTrackingProps = {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
  placeholder?: boolean;
};

const PrepayTracking: React.FC<PrepayTrackingProps> = ({
  bondPrepayTrackingRequest,
  requestOpts,
  placeholder,
}: PrepayTrackingProps) => {
  const { data, error, isLoading } = useGetBondPrepayTrackingSWR(bondPrepayTrackingRequest, requestOpts);
  const [rate, setRate] = React.useState<"cpr" | "crr" | "cdr">("cpr");
  let tableData: { name: string; values: string[] }[] = [];
  const barChartOptions = {
    xkey: "month",
    dataKeys: [
      { key: "actual", name: "Actual" },
      { key: "proj_cpr", name: "Projection" },
    ],
    margin: { left: -35 },
    data: [{}],
    formatTooltip: getChartTooltipFormatter(1),
    yAxisToFixed: 0,
  };
  if (data) {
    const d = data as { [key: string]: number };
    tableData = [
      {
        name: "1m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_1m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_1m`]),
          getDividedValue(d[`act_${rate}_1m`], d[`proj_${rate}_1m`], 2),
          getDifference(d[`act_${rate}_1m`], d[`proj_${rate}_1m`], 1),
        ],
      },
      {
        name: "3m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_3m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_3m`]),
          getDividedValue(d[`act_${rate}_3m`], d[`proj_${rate}_3m`], 2),
          getDifference(d[`act_${rate}_3m`], d[`proj_${rate}_3m`], 1),
        ],
      },
      {
        name: "6m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_6m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_6m`]),
          getDividedValue(d[`act_${rate}_6m`], d[`proj_${rate}_6m`], 2),
          getDifference(d[`act_${rate}_6m`], d[`proj_${rate}_6m`], 1),
        ],
      },
      {
        name: "12m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_12m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_12m`]),
          getDividedValue(d[`act_${rate}_12m`], d[`proj_${rate}_12m`], 2),
          getDifference(d[`act_${rate}_12m`], d[`proj_${rate}_12m`], 1),
        ],
      },
      {
        name: "24m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_24m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_24m`]),
          getDividedValue(d[`act_${rate}_24m`], d[`proj_${rate}_24m`], 2),
          getDifference(d[`act_${rate}_24m`], d[`proj_${rate}_24m`], 1),
        ],
      },
    ];

    barChartOptions.data = tableData.map((td) => ({
      month: td.name,
      actual: +td.values[0],
      proj_cpr: +td.values[1],
      div_act_proj: +td.values[2],
      diff_act_proj: +td.values[3],
    }));
  }

  return (
    <CACard
      isPlaceholderCard={!!error || placeholder}
      title="Prepay Tracking"
      maxW={500}
      allowCollapse
      cardKey="prepay-tracking"
      mt={{ base: "0.7rem", sm: 0 }}
      cardBodyStyle={{ h: !!error || placeholder ? "5.5rem" : "auto" }}
    >
      <Box>
        <Flex mb="1" wrap="wrap" justify="space-between" align="center">
          {bondPrepayTrackingRequest.bond_name ? (
            <Box mb="2">
              <InlineValueDisplay name="Factor Month" value={getFormattedYearMonth(data?.factor_month)} />
            </Box>
          ) : (
            <Box />
          )}
          <Box ml="auto">
            <ToggleButtonGroup
              buttons={[
                { label: "CPR", value: "cpr" },
                { label: "CRR", value: "crr" },
                { label: "CDR", value: "cdr" },
              ]}
              selectedButton={rate}
              onChange={setRate}
            />
          </Box>
        </Flex>
        <VStack spacing={3} alignItems="stretch">
          {!data || isLoading || data?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING ? (
            <Skeleton height="25px" />
          ) : (
            <>
              <CATable
                variant="caBasicCentered"
                headers={["", "Actual (%)", "Model (%)", "Act/Proj", "Act-Proj"]}
                data={tableData}
              />
              <CABarChart options={barChartOptions} />
            </>
          )}
        </VStack>
      </Box>
    </CACard>
  );
};

export default React.memo(PrepayTracking);
