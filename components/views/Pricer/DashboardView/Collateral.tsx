import * as React from "react";
import CACard from "@/design-system/molecules/CACard";
import { formatCurrency, getAPIErrorMessage, getFormattedNumberFixed, getFormattedYearMonth } from "@/utils/helpers";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest } from "@/utils/openapi";
import { useCollateralPoolLevelSummarySWR } from "@/utils/swr-hooks/CollateralInfo";
import CATwoColumnTable, { CATwoColumnDataType } from "@/design-system/molecules/CATwoColumnTable";
import SecuritySubHeading from "@/design-system/atoms/CASubHeading";

type CollateralProps = {
  collateralSummaryRequest: CA_Mastr_Api_v1_0_Models_Collateral_Summary_CollateralSummaryRequest;
  isOldRun?: boolean;
};

const Collateral: React.FC<CollateralProps> = ({ collateralSummaryRequest, isOldRun }: CollateralProps) => {
  const { data: response, error } = useCollateralPoolLevelSummarySWR(collateralSummaryRequest, { isOldRun });

  const { basicFields, loanSizeFields, deliquencyFields, otherFields } = React.useMemo(() => {
    const data = (response && response.pool_level) || {};
    const agency = (response && response.agency) || undefined;

    const basicFields = [
      { name: "Agency", value: agency ?? "-" },
      { name: "Current Balance ($)", value: formatCurrency(data.curr_balance, true) },
      { name: "Factor", value: getFormattedNumberFixed(8)(data.factor) ?? "-" },
      "issuer_name" in data && { name: "Issuer Name", value: data.issuer_name ?? "-" },
      "pool_count" in data && {
        name: "Pool Count",
        value: getFormattedNumberFixed(0)(data.pool_count) ?? "-",
      },
      "cello_loan_count" in data && {
        name: "WA Loan Count",
        value: getFormattedNumberFixed(0)(data.cello_loan_count) ?? "-",
      },
      { name: "Coupon (%)", value: getFormattedNumberFixed(2)(data.net_coupon) ?? "-" },
      { name: "WAC (%)", value: getFormattedNumberFixed(2)(data.wac) ?? "-" },
      { name: "WAM (months)", value: getFormattedNumberFixed(0)(data.wam) ?? "-" },
      { name: "WALA (months)", value: getFormattedNumberFixed(0)(data.age) ?? "-" },
    ].filter((r) => r && r.name) as CATwoColumnDataType[];

    const loanSizeFields = [
      { name: "WAOLS ($)", value: formatCurrency(data.waols, true) },
      "wacls" in data && {
        name: "WACLS ($)",
        value: formatCurrency(data.wacls, true),
      },
      { name: "AOLS ($)", value: formatCurrency(data.aols, true) },
      { name: "ACLS ($)", value: formatCurrency(data.acls, true) },
      "mols" in data && {
        name: "MOLS ($)",
        value: formatCurrency(data.mols, true),
      },
    ].filter((r) => r && r.name) as CATwoColumnDataType[];

    const deliquencyFields = [
      { name: "DQ30 (%)", value: getFormattedNumberFixed(1)(data.dlq30_pct) ?? "-" },
      { name: "DQ60 (%)", value: getFormattedNumberFixed(1)(data.dlq60_pct) ?? "-" },
      { name: "DQ90 (%)", value: getFormattedNumberFixed(1)(data.dlq90_pct) ?? "-" },
      !("dlq120pluspct" in data) &&
        "dlq90pluspct" in data && { name: "DQ90+ (%)", value: getFormattedNumberFixed(1)(data.dlq90pluspct) ?? "-" },
      "dlq120pluspct" in data && {
        name: "DQ120+ (%)",
        value: getFormattedNumberFixed(1)(data.dlq120pluspct) ?? "-",
      },
    ].filter((r) => r && r.name) as CATwoColumnDataType[];

    const otherFields = [
      "credit_score" in data && {
        name: "Credit Score",
        value: getFormattedNumberFixed(0)(data.credit_score) ?? "-",
      },
      "sato" in data && {
        name: "SATO (bps)",
        value: getFormattedNumberFixed(0)(data.sato) ?? "-",
      },
      "oltv" in data && {
        name: "Original LTV (%)",
        value: getFormattedNumberFixed(1)(data.oltv) ?? "-",
      },
      "cltv" in data && {
        name: "Current LTV (%)",
        value: getFormattedNumberFixed(1)(data.cltv) ?? "-",
      },
      "servicer1" in data && {
        name: "Top Servicer (%)",
        value: [data.servicer1 ?? "", getFormattedNumberFixed(1)(data.servicer1_pct) ?? ""].join(" ").trim() || "-",
      },
      "state1" in data && {
        name: "Top State (%)",
        value: [data.state1 ?? "", getFormattedNumberFixed(1)(data.state1_pct) ?? ""].join(" ").trim() || "-",
      },
      "refi_pct" in data && {
        name: "Refi (%)",
        value: getFormattedNumberFixed(1)(data.refi_pct) ?? "-",
      },
      "inv_pct" in data && {
        name: "Investor (%)",
        value: getFormattedNumberFixed(1)(data.inv_pct) ?? "-",
      },
      "tpo_pct" in data && {
        name: "TPO (%)",
        value: getFormattedNumberFixed(1)(data.tpo_pct) ?? "-",
      },
      "fha_pct" in data && {
        name: "FHA (%)",
        value: getFormattedNumberFixed(1)(data.fha_pct) ?? "-",
      },
      "va_pct" in data && {
        name: "VA (%)",
        value: getFormattedNumberFixed(1)(data.va_pct) ?? "-",
      },

      { name: "Factor Month", value: getFormattedYearMonth(data.asof_date ?? "-") },
    ].filter((r) => r && r.name) as CATwoColumnDataType[];

    return { basicFields, loanSizeFields, deliquencyFields, otherFields };
  }, [response]);

  return error ? (
    <CACard title="Collateral">
      <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
    </CACard>
  ) : (
    <CACard
      title="Collateral"
      maxW={500}
      allowCollapse
      cardKey="dashboard-collateral"
      mt={{ base: "0.5rem", sm: 0 }}
      cardBodyStyle={{
        px: 0,
      }}
    >
      {basicFields.length ? (
        <>
          <SecuritySubHeading label="Basic" />
          <CATwoColumnTable data={basicFields} />
        </>
      ) : null}

      {loanSizeFields.length ? (
        <>
          <SecuritySubHeading label="Loan Size" />
          <CATwoColumnTable data={loanSizeFields} />
        </>
      ) : null}

      {deliquencyFields.length ? (
        <>
          <SecuritySubHeading label="Deliquency" />
          <CATwoColumnTable data={deliquencyFields} />
        </>
      ) : null}

      {otherFields.length ? (
        <>
          <SecuritySubHeading label="Other" />
          <CATwoColumnTable data={otherFields} />
        </>
      ) : null}
    </CACard>
  );
};

export default React.memo(Collateral);
