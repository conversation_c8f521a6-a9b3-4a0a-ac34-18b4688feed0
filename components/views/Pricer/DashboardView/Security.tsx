import * as React from "react";
import { Skeleton } from "@chakra-ui/react";
import { useGetBondIndicativesSWR } from "@/utils/swr-hooks";
import { BondIndicativesRequest } from "@/types/swr";
import CACard from "@/design-system/molecules/CACard";
import {
  formatCurrency,
  getAPIErrorMessage,
  getFormattedBoolean,
  getFormattedLocaleDate,
  getFormattedNumberFixed,
} from "@/utils/helpers";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { usePricerDashboardPage } from "@/contexts/PageContexts/PricerDashboardPageContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import CATwoColumnTable, { CATwoColumnDataType } from "@/design-system/molecules/CATwoColumnTable";
import SecuritySubHeading from "@/design-system/atoms/CASubHeading";

type SecurityProps = {
  bondIndicativesRequest: BondIndicativesRequest;
};

const Security: React.FC<SecurityProps> = ({ bondIndicativesRequest }: SecurityProps) => {
  const {
    state: { security_info },
  } = usePricerModule();
  const {
    state: { isOldRun, lastRun },
  } = usePricerDashboardPage();
  const { data, error } = useGetBondIndicativesSWR(bondIndicativesRequest, {
    lastRun: lastRun ?? "Default",
    isOldRun,
  });

  const { nameFields, balanceFields, couponAndPrincipleFields, paymentDetailsFields, otherFields } =
    React.useMemo(() => {
      const { bloomberg_name, intex_name, yieldbook_name } = security_info ?? {};
      const { bond_structure } = data || {};

      const nameFields = [
        bloomberg_name && { name: "Bloomberg", value: bloomberg_name },
        intex_name && { name: "Intex", value: intex_name },
        yieldbook_name && { name: "Yieldbook", value: yieldbook_name },
      ].filter(Boolean) as CATwoColumnDataType[];

      const balanceFields = [
        typeof bond_structure?.tranche_orig_balance === "number" && {
          name: "Original ($)",
          value: formatCurrency(bond_structure.tranche_orig_balance, true),
        },
        typeof bond_structure?.tranche_current_balance === "number" && {
          name: "Current ($)",
          value: formatCurrency(bond_structure.tranche_current_balance, true),
        },
        typeof bond_structure?.tranche_factor === "number" && {
          name: "Factor",
          value: `${getFormattedNumberFixed(8)(bond_structure.tranche_factor)}`,
        },
      ].filter(Boolean) as CATwoColumnDataType[];

      const couponAndPrincipleFields = [
        bond_structure?.tranche_type && {
          name: "Structure",
          value: bond_structure.tranche_type.split("_").join(" "),
        },
        bond_structure?.tranche_pac_range && {
          name: "PAC Range",
          value: bond_structure.tranche_pac_range,
        },
        typeof bond_structure?.has_structured_collat === "boolean" && {
          name: "Reremic",
          value: `${getFormattedBoolean(bond_structure.has_structured_collat)}`,
        },
        typeof bond_structure?.tranche_coupon === "number" && {
          name: "Coupon (%)",
          value: `${getFormattedNumberFixed(2)(bond_structure.tranche_coupon)}`,
        },
        typeof bond_structure?.tranche_lev === "number" && {
          name: "Leverage",
          value: `${getFormattedNumberFixed(2)(bond_structure.tranche_lev)}`,
        },
        bond_structure?.tranche_index && { name: "Index", value: bond_structure.tranche_index },
        typeof bond_structure?.tranche_margin === "number" && {
          name: "Margin (%)",
          value: `${getFormattedNumberFixed(2)(bond_structure.tranche_margin)}`,
        },
        typeof bond_structure?.tranche_cap === "number" && {
          name: "Cap (%)",
          value: `${getFormattedNumberFixed(2)(bond_structure.tranche_cap)}`,
        },
        typeof bond_structure?.tranche_floor === "number" && {
          name: "Floor (%)",
          value: `${getFormattedNumberFixed(2)(bond_structure.tranche_floor)}`,
        },
      ].filter(Boolean) as CATwoColumnDataType[];

      const paymentDetailsFields = [
        bond_structure?.tranche_accrual_start_date && {
          name: "Dated Date",
          value: getFormattedLocaleDate(bond_structure.tranche_accrual_start_date),
        },
        typeof bond_structure?.tranche_delay === "number" && {
          name: "Days Delay",
          value: `${bond_structure.tranche_delay}`,
        },
        bond_structure?.tranche_next_pay_date && {
          name: "Next Pay Date",
          value: getFormattedLocaleDate(bond_structure.tranche_next_pay_date),
        },
        bond_structure?.tranche_maturity_date && {
          name: "Maturity",
          value: getFormattedLocaleDate(bond_structure.tranche_maturity_date),
        },
      ].filter(Boolean) as CATwoColumnDataType[];

      const otherFields = [
        // Add fields for Pool
        bond_structure?.pool && { name: "Pool", value: bond_structure.pool },
        bond_structure?.production_year && { name: "Production Year", value: bond_structure.production_year },
        bond_structure?.issue_date && {
          name: "Issue Date",
          value: `${getFormattedLocaleDate(bond_structure.issue_date)}`,
        },
        bond_structure?.issuer_name && { name: "Issuer Name", value: bond_structure.issuer_name },
        typeof bond_structure?.wac === "number" && {
          name: "WAC (%)",
          value: `${getFormattedNumberFixed(2)(bond_structure.wac)}`,
        },
        typeof bond_structure?.wam === "number" && {
          name: "WAM (months)",
          value: `${bond_structure.wam}`,
        },
        typeof bond_structure?.wala === "number" && {
          name: "WALA (months)",
          value: `${bond_structure.wala}`,
        },
        typeof bond_structure?.orig_wac === "number" && {
          name: "Orig WAC (%)",
          value: `${getFormattedNumberFixed(2)(bond_structure.orig_wac)}`,
        },
        typeof bond_structure?.orig_wam === "number" && {
          name: "Orig WAM (months)",
          value: `${bond_structure.orig_wam}`,
        },
        typeof bond_structure?.orig_wala === "number" && {
          name: "Orig WALA (months)",
          value: `${bond_structure.orig_wala}`,
        },
        bond_structure?.mega && { name: "Mega", value: bond_structure.mega },
        bond_structure?.prefix && { name: "Prefix", value: bond_structure.prefix },
        bond_structure?.market_ticker && { name: "Market Ticker", value: bond_structure.market_ticker },
        bond_structure?.coupon_type && { name: "Coupon Type", value: bond_structure.coupon_type },
        bond_structure?.factor_date && {
          name: "Factor Date",
          value: `${getFormattedLocaleDate(bond_structure.factor_date)}`,
        },
        typeof bond_structure?.reset_per_init === "number" && {
          name: "Reset Per Init",
          value: `${bond_structure.reset_per_init}`,
        },
        typeof bond_structure?.reset_per_subseq === "number" && {
          name: "Reset Per Subseq",
          value: `${bond_structure.reset_per_subseq}`,
        },
        bond_structure?.cap_struct && { name: "Cap Struct", value: bond_structure.cap_struct },
      ].filter(Boolean) as CATwoColumnDataType[];

      return { nameFields, balanceFields, couponAndPrincipleFields, paymentDetailsFields, otherFields };
    }, [data, security_info]);

  if (!data && !error)
    return (
      <CACard title="Security" minH="0">
        <Skeleton height="25px" />
      </CACard>
    );

  return error ? (
    <CACard title="Security">
      <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
    </CACard>
  ) : (
    <CACard
      title="Security"
      maxW={500}
      allowCollapse
      cardKey="dashboard-security"
      mt={{ base: "0.5rem", sm: 0 }}
      cardBodyStyle={{
        px: 0,
      }}
    >
      {nameFields.length ? (
        <>
          <SecuritySubHeading label="Name" />
          <CATwoColumnTable data={nameFields} />
        </>
      ) : null}

      {balanceFields.length ? (
        <>
          <SecuritySubHeading label="Balance" />
          <CATwoColumnTable data={balanceFields} />
        </>
      ) : null}

      {couponAndPrincipleFields.length ? (
        <>
          <SecuritySubHeading label="Principal and Interest" />
          <CATwoColumnTable data={couponAndPrincipleFields} />
        </>
      ) : null}

      {paymentDetailsFields.length ? (
        <>
          <SecuritySubHeading label="Payment Details" />
          <CATwoColumnTable data={paymentDetailsFields} />
        </>
      ) : null}

      {otherFields.length ? (
        <>
          <SecuritySubHeading label="Other" />
          <CATwoColumnTable data={otherFields} />
        </>
      ) : null}
    </CACard>
  );
};

export default React.memo(Security);
