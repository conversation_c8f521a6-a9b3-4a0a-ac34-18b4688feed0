import { Box, Center, Flex, Icon, Text, Tooltip, VStack } from "@chakra-ui/react";
import * as React from "react";
import { IoInformationCircleOutline } from "react-icons/io5";
import ValueDisplay from "@/components/helpers/ValueDisplay";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import PopoverMenu from "@/design-system/molecules/PopoverMenu";
import { safeLocalStorage } from "@/utils/local-storage";
import {
  CA_Mastr_Models_v1_0_Models_BondReplineInOut,
  CA_Mastr_Models_v1_0_Models_ReplineModelDial,
} from "@/utils/openapi";
import Link from "@/design-system/atoms/Link";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { qs } from "@/hooks/useQueryParameters";

const ReplineDialsSwitch: React.FC = () => {
  const {
    state: {
      bond_name,
      userSettings: { use_repline_dials_override, model_version, repline_level },
      userSettingsCopy: { use_repline_dials_override: use_repline_dials_override_copy },
    },
    action: { toggleUseReplineDials },
  } = usePricerModule();

  const error: {
    show: boolean;
    msg1?: string;
    msg2?: string;
  } = { show: false };

  if (
    use_repline_dials_override !== undefined &&
    use_repline_dials_override_copy !== undefined &&
    use_repline_dials_override !== use_repline_dials_override_copy
  ) {
    error.show = true;
    error.msg1 = `Value changed from ${use_repline_dials_override_copy} to ${use_repline_dials_override}`;
    error.msg2 = "Click run to update your calculation";
  }

  React.useEffect(() => {
    const savedModelDialsList = safeLocalStorage.getItem(
      `${LOCAL_STORAGE_KEY.PRICER_REPLINE_MODEL_DIALS}_${bond_name}_${model_version}_${repline_level}`
    );
    const savedEditedCellList = safeLocalStorage.getItem(
      `${LOCAL_STORAGE_KEY.PRICER_REPLINE_EDITED_CELL}_${bond_name}_${model_version}_${repline_level}`
    );
    const savedModelDialsArr: Array<CA_Mastr_Models_v1_0_Models_ReplineModelDial> = savedModelDialsList
      ? JSON.parse(savedModelDialsList)
      : undefined;

    const savedEditedCellListArray: Array<CA_Mastr_Models_v1_0_Models_BondReplineInOut> = savedEditedCellList
      ? JSON.parse(savedEditedCellList)
      : undefined;
    if (
      (!savedModelDialsArr || !savedModelDialsArr.length) &&
      (!savedEditedCellListArray || !savedEditedCellListArray.length)
    ) {
      toggleUseReplineDials(undefined);
    } else if (
      ((savedModelDialsArr && savedModelDialsArr.length) ||
        (savedEditedCellListArray && savedEditedCellListArray.length)) &&
      use_repline_dials_override
    ) {
      toggleUseReplineDials(true);
    } else {
      toggleUseReplineDials(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [model_version, repline_level]);

  return (
    <ValueDisplay
      name="Repline Override and Dials"
      value={
        <Box position="relative">
          {error?.show && (
            <Box position="absolute" left="-25px" top="2px">
              <PopoverMenu
                triggerElement={<Icon as={IoInformationCircleOutline} color="safetyOrange.500" fontSize="large" />}
                placement="bottom"
                popoverContent={{
                  showArrow: true,
                  maxWidth: "250",
                  body: (
                    <VStack px="2" alignItems="flex-start">
                      {error?.msg1 && <Box>{error?.msg1}</Box>}
                      {error?.msg2 && <Box>{error?.msg2}</Box>}
                    </VStack>
                  ),
                }}
                trigger="click"
              />
            </Box>
          )}
          <Flex>
            <Center>
              <Tooltip
                label="Click Add to add Repline Override and Dials"
                aria-label="Click Add to add Repline Override and Dials"
                isDisabled={use_repline_dials_override !== undefined}
                shouldWrapChildren
              >
                <CASwitchInput
                  name={"use_repline_dials_override"}
                  hideLabel={true}
                  value={"use_repline_dials_override"}
                  isChecked={use_repline_dials_override ?? false}
                  isDisabled={use_repline_dials_override === undefined}
                  onChange={() => toggleUseReplineDials(!use_repline_dials_override)}
                />
              </Tooltip>

              <Text color="inherit" textDecoration="underline" variant="default" textAlign="right">
                <Link
                  key={"repline"}
                  href={`/pricer/pricing/repline?${qs.stringify({
                    bond_name: `${bond_name}`,
                  })}`}
                >
                  {use_repline_dials_override === undefined ? "Add" : "Edit"}
                </Link>
              </Text>
            </Center>
          </Flex>
        </Box>
      }
    />
  );
};

export default ReplineDialsSwitch;
