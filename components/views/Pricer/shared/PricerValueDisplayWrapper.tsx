import * as React from "react";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { getDefaultsOfMetadata, getDisplayValueByKey } from "@/utils/helpers";
import PricerValueDisplay from "./PricerValueDisplay";

export type PricerValueDisplayWrapperProps = {
  name: string;
  link?: boolean;
  _key?: string;
  runid?: string;
  value: string | React.JSX.Element;
  dateFormatter?: (dateString: Date | string | undefined | null) => void;
  drawerKey?: string;
  ignoreChanges?: boolean;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type NestedObject = { [key: string]: any };

function getValueFromPath<T>(obj: NestedObject, path: string): T | undefined {
  return path.split(".").reduce((acc, key) => (acc ? acc[key] : undefined), obj) as T | undefined;
}

const PricerValueDisplayWrapper: React.FC<PricerValueDisplayWrapperProps> = ({
  name,
  link,
  _key,
  value,
  dateFormatter,
  drawerKey,
  ...rest
}: PricerValueDisplayWrapperProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const {
    state: { userSettings, userSettingsCopy },
  } = usePricerModule();
  const currentValue = getValueFromPath<string>(
    {
      ...userSettings,
      // TODO: - This is done to match the schema between metadata and user_settings, update this on user_settings type so that we don't have to do this
      cello_cdu_dates: {
        cdu_date_conventional: userSettings.cdu_date_conventional,
        cdu_date_gnm: userSettings.cdu_date_gnm,
        cdu_date_gpl: userSettings.cdu_date_gpl,
      },
    },
    _key ?? ""
  );
  const prevValue = getValueFromPath<string>(
    {
      ...userSettingsCopy,
      // TODO: - This is done to match the schema between metadata and user_settings, update this on user_settings type so that we don't have to do this
      cello_cdu_dates: {
        cdu_date_conventional: userSettingsCopy.cdu_date_conventional,
        cdu_date_gnm: userSettingsCopy.cdu_date_gnm,
        cdu_date_gpl: userSettingsCopy.cdu_date_gpl,
      },
    },
    _key ?? ""
  );

  //Message if value changes
  const hasChanged = currentValue?.toString() !== prevValue?.toString() && prevValue !== undefined;

  const changedMsg = `Value changed from ${
    dateFormatter ? dateFormatter(prevValue) : getDisplayValueByKey(prevValue, metadata)
  } to ${dateFormatter ? dateFormatter(currentValue) : getDisplayValueByKey(currentValue, metadata)}`;

  const splitKey = _key?.includes(".") ? _key?.split(".") : _key;

  //Get default Values from metadata based on `is_default` field
  const defaultValues = Array.isArray(splitKey)
    ? getDefaultsOfMetadata(splitKey[0], metadata?.pricer_settings, splitKey[1])
    : getDefaultsOfMetadata(splitKey, metadata?.pricer_settings);

  const defaultValue = dateFormatter
    ? new Date(defaultValues?.value || "").toString()
    : defaultValues?.value?.toString();

  //Show this message if settings values are different from default values from metadata
  const hasOutdated = currentValue?.toString() !== defaultValue && !hasChanged && defaultValue !== undefined;

  const msg2 = hasOutdated
    ? `Default value is ${dateFormatter ? dateFormatter(defaultValue) : defaultValues?.display_value}`
    : "Click run to update your calculation";

  return (
    <PricerValueDisplay
      name={name}
      drawerKey={drawerKey}
      link={link}
      value={value}
      info={{
        show: hasChanged || hasOutdated,
        msg1: hasChanged ? changedMsg : undefined,
        msg2: msg2,
        type: hasChanged ? "changed" : hasOutdated ? "outdated" : "unset",
      }}
      {...rest}
    />
  );
};

export default PricerValueDisplayWrapper;
