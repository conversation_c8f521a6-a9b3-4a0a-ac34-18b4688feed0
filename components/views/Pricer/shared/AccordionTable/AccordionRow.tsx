import React from "react";
import { Box, Flex, Icon, LayoutProps, ListItem, useColorModeValue } from "@chakra-ui/react";
import { IoMdShare } from "react-icons/io";
import { IoChevronDown, IoChevronUp, IoCopyOutline, IoPencil } from "react-icons/io5";
import { RiDeleteBin6Fill } from "react-icons/ri";
import ConfirmationModalPopup from "@/design-system/organisms/ConfirmationModalPopup";
import S from "@/constants/strings";
import SharePopup from "@/components/helpers/SharePopup";
import ClickableIcon from "./ClickableIcon";
import { AccordionContentProps, HeaderMeta, ItemProps } from "./types";
import { toggleColumn } from "./constants";

interface RowItemProps {
  headers: HeaderMeta[];
  item: ItemProps;
  index: number;
  lastSavedItemId?: number;
  onDelete?: (itemId?: number, itemName?: string | null) => void;
  rowMinWidth?: LayoutProps["minWidth"];
}

const RowItem: React.FC<RowItemProps> = ({
  item,
  index,
  lastSavedItemId,
  onDelete,
  headers,
  rowMinWidth,
}: RowItemProps) => {
  const [open, toggleRow] = React.useState(item.itemId === lastSavedItemId);
  const [contentMode, setContentMode] = React.useState(item.itemId === lastSavedItemId ? "view" : item.mode);
  const hoverColor = useColorModeValue("celloBlue.75", "celloBlue.500");
  const openListItemBackgroundColor = useColorModeValue("celloBlue.75", "celloBlue.500");
  const listItemColor = useColorModeValue("celloBlue.900", "white");
  const [isDeleteModalOpen, toggleDeleteModal] = React.useState(false);
  const [itemToDelete, setItemToDelete] = React.useState<ItemProps>();
  const isEven = index % 2 === 0;
  const backgroundColorObj = {
    light: isEven ? "white" : "misty.300",
    dark: isEven ? "celloBlue.1000" : "celloBlue.1100",
  };
  const iconColor = useColorModeValue("celloBlue.900", "turmericRoot.500");
  const disabledIconColor = useColorModeValue("charcoal.200", "classicGray.600");
  const listItemBackgroundColor = useColorModeValue(backgroundColorObj.light, backgroundColorObj.dark);
  const rowIcon = open ? (
    <Icon
      as={IoChevronUp}
      fontSize="20"
      ml="1.25rem"
      color={item.mode === "add" ? disabledIconColor : iconColor}
      maxW={toggleColumn.maxWidth}
      _hover={{
        cursor: item.mode === "add" ? "not-allowed" : "inherit",
      }}
    />
  ) : (
    <Icon
      as={IoChevronDown}
      fontSize="20"
      ml="1.25rem"
      color={item.mode === "add" ? disabledIconColor : iconColor}
      maxW={toggleColumn.maxWidth}
    />
  );

  const openDeleteModal = () => {
    toggleDeleteModal(true);
  };

  const closeDeleteModal = () => {
    toggleDeleteModal(false);
  };

  React.useEffect(() => {
    if (item.mode === "add") {
      toggleRow(true);
      setContentMode("add");
    }
  }, [item]);

  const createItemCopy = (item: ItemProps) => {
    item?.duplicateDial?.();
    setTimeout(() => {
      window.scrollTo({ behavior: "smooth", top: 0 });
    }, 0);
  };

  const editItem = () => {
    setContentMode("edit");
    toggleRow(true);
  };

  const deleteItem = (itemToDelete: ItemProps) => {
    setItemToDelete(itemToDelete);
    openDeleteModal();
  };

  const onCancelClick = () => {
    if (item.mode === "add" && item.toggleAddMode) {
      toggleRow(false);
      item.toggleAddMode();
    } else {
      setContentMode("view");
      toggleRow(false);
    }
  };

  const handleArrowClick = () => {
    if (item.mode === "add") return;
    setContentMode("view");
    toggleRow(!open);
  };

  return (
    <>
      <ListItem
        backgroundColor={listItemBackgroundColor}
        minHeight="1.875rem"
        position="relative"
        minWidth={rowMinWidth}
      >
        <Flex
          flexDirection="row"
          alignItems="center"
          minHeight={open ? "3.6rem" : "2rem"}
          backgroundColor={open ? openListItemBackgroundColor : "inherit"}
          _hover={{ cursor: "pointer", backgroundColor: hoverColor }}
          onClick={handleArrowClick}
        >
          {[rowIcon, ...item.values].map((value, index) => {
            const maxWidth = typeof value === "object" ? value.props.maxW : headers[index].maxWidth;
            return (
              <Box
                display="flex"
                width="100%"
                key={index}
                maxW={maxWidth}
                minW={headers[index].useMaxAsMin ? maxWidth : headers[index].minWidth}
                color={listItemColor}
                py={0.5}
                pr={1}
              >
                {index === 1 && contentMode === "edit" ? "" : value}
              </Box>
            );
          })}
          <ClickableIcon show={item.mode !== "add"} as={IoCopyOutline} onClick={() => createItemCopy(item)} />
          <ClickableIcon show={item.can_edit && contentMode === "view"} as={IoPencil} onClick={editItem} />
          {item.can_share && contentMode === "view" ? (
            <SharePopup
              recordId={item.itemId as number}
              entity={item.entity}
              recordName={item.itemName ?? ""}
              triggerElement={(open) => <ClickableIcon show as={IoMdShare} onClick={open} />}
            />
          ) : (
            <Box minW="4.5rem" />
          )}
          <ClickableIcon
            show={item.can_delete && contentMode === "view"}
            as={RiDeleteBin6Fill}
            onClick={() => deleteItem(item)}
          />
        </Flex>
        {open && (
          <RowContent
            Content={item.content}
            mode={contentMode}
            itemId={item.itemId}
            onCancelClick={onCancelClick}
            setContentMode={setContentMode}
          />
        )}
      </ListItem>
      <ConfirmationModalPopup
        isOpen={isDeleteModalOpen}
        onModalClose={closeDeleteModal}
        onCancel={() => {
          setItemToDelete(undefined);
          closeDeleteModal();
        }}
        onConfirm={() => {
          if (onDelete) {
            onDelete(itemToDelete?.itemId, itemToDelete?.itemName);
          }
          closeDeleteModal();
        }}
        headerText={S.COMMON.DELETE_CONFIRMATION.HEADER}
        description={S.COMMON.DELETE_CONFIRMATION.DESCRIPTION}
        showCloseIcon
      />
    </>
  );
};
export default React.memo(RowItem);

//   ROW CONTENT
type RowContentProps = {
  Content?: React.FunctionComponent<AccordionContentProps>;
  mode?: "view" | "edit" | "add";
  onCancelClick?: () => void;
  itemId: number | undefined;
  toggleAddMode?: () => void;
  duplicateDial?: () => void;
  setContentMode: (mode: "view" | "edit") => void;
};

const RowContent: React.FC<RowContentProps> = ({
  Content,
  mode,
  itemId,
  onCancelClick,
  setContentMode,
}: RowContentProps) => {
  const ContentComponent = Content as React.ElementType;
  const openListItemBorderColor = useColorModeValue("celloBlue.75", "celloBlue.500");
  return (
    <Box
      className="content"
      py="1rem"
      borderWidth={mode ? "3px" : 0}
      borderTopWidth="0"
      borderColor={openListItemBorderColor}
    >
      <ContentComponent mode={mode} itemId={itemId} closeModal={onCancelClick} setContentMode={setContentMode} />
    </Box>
  );
};
