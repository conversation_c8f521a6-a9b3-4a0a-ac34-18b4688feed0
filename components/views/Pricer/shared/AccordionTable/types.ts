import { CA_Mastr_Models_v1_0_Models_Entity } from "@/utils/openapi";

export interface AccordionContentProps {
  mode: "view" | "edit" | "add";
  itemId: number;
  closeModal?: () => void;
  setContentMode: (mode: "view" | "edit") => void;
}

export type ItemProps = {
  content?: React.FunctionComponent<AccordionContentProps>;
  values: (string | number | React.JSX.Element)[];
  can_edit?: boolean;
  can_delete?: boolean;
  can_share?: boolean;
  mode?: "view" | "edit" | "add";
  itemId?: number;
  itemName?: string | null;
  createdBy?: string;
  toggleAddMode?: () => void;
  duplicateDial?: () => void;
  entity: CA_Mastr_Models_v1_0_Models_Entity;
};

export interface HeaderMeta {
  name: string;
  maxWidth?: string;
  minWidth?: string;
  isActionCol?: boolean;
  useMaxAsMin?: boolean;
}
