import * as React from "react";
import { Box, Flex, LayoutProps, List, Text } from "@chakra-ui/react";
import { HeaderMeta, ItemProps } from "./types";
import RowItem from "./AccordionRow";

type AccordionTableProps = {
  data: ItemProps[];
  headers: HeaderMeta[];
  lastSavedItemId?: number;
  onDelete?: (itemId?: number, itemName?: string | null) => void;
  minWidth?: LayoutProps["minWidth"];
};

const AccordionTable: React.FC<AccordionTableProps> = ({
  data,
  headers,
  lastSavedItemId,
  onDelete,
  minWidth,
}: AccordionTableProps) => {
  const headersToRender = headers.map((item, index) => {
    return (
      <Flex
        key={index}
        width="100%"
        maxW={item.maxWidth ?? "initial"}
        minW={item.useMaxAsMin ? item.maxWidth : item.minWidth ?? "unset"}
        justifyContent={item.isActionCol ? "center" : "flex-start"}
        alignItems="flex-start"
      >
        <Text variant="tableHead">{item.name}</Text>
      </Flex>
    );
  });

  const rows = data.map((item: ItemProps, index: number) => {
    const key = item.itemId ? (item.mode === "add" ? `add-${item?.itemId}` : item.itemId) : index;
    return (
      <RowItem
        item={item}
        key={key}
        index={index}
        lastSavedItemId={lastSavedItemId}
        onDelete={onDelete}
        headers={headers}
        rowMinWidth={minWidth}
      />
    );
  });

  return (
    <Box>
      <Box overflowX={"auto"}>
        <Flex flexDirection="row" alignItems="stretch" flexWrap="nowrap" minWidth={minWidth}>
          {headersToRender}
        </Flex>
        <List>{rows}</List>
      </Box>
    </Box>
  );
};

export default AccordionTable;
