import React from "react";
import { Box, BoxProps, Icon, useColorModeValue } from "@chakra-ui/react";
import { IconType } from "react-icons";

interface ClickableIconProps extends BoxProps {
  as?: IconType | undefined;
  show?: boolean;
}

const ClickableIcon = ({ as, show, onClick, ...rest }: ClickableIconProps) => {
  const listItemColor = useColorModeValue("celloBlue.900", "white");
  const iconColor = useColorModeValue("celloBlue.900", "turmericRoot.500");

  const clickHandler = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
    onClick?.(e);
  };

  if (!show) return <Box minW="4.5rem" />;
  return (
    <Box
      display="flex"
      justifyContent="center"
      width="100%"
      maxW="4.5rem"
      minW="4.5rem"
      color={listItemColor}
      onClick={clickHandler}
      {...rest}
    >
      <Icon ml="0.4rem" as={as} fontSize="20" color={iconColor} />
    </Box>
  );
};

export default ClickableIcon;
