import { HeaderMeta } from "./types";

export const actionColumnWidth = "4.5rem";

export const actionColumns: HeaderMeta[] = [
  {
    name: "DUPLICATE",
    maxWidth: actionColumnWidth,
    isActionCol: true,
    useMaxAsMin: true,
  },
  {
    name: "EDIT",
    maxWidth: actionColumnWidth,
    isActionCol: true,
    useMaxAsMin: true,
  },
  {
    name: "SHARE",
    maxWidth: actionColumnWidth,
    isActionCol: true,
    useMaxAsMin: true,
  },
  {
    name: "DELETE",
    maxWidth: actionColumnWidth,
    isActionCol: true,
    useMaxAsMin: true,
  },
];

export const toggleColumn: HeaderMeta = {
  name: "",
  maxWidth: "5.31rem",
  useMaxAsMin: true,
};
