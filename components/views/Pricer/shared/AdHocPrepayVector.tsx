import React from "react";
import { <PERSON>, <PERSON><PERSON>, HStack, Text, chakra } from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import { KeyedMutator } from "swr";
import { IoSyncCircleOutline } from "react-icons/io5";
import CAModal from "@/design-system/molecules/CAModal";
import {
  CA_Mastr_Api_v1_0_Models_UserVector_UserVector,
  CA_Mastr_Api_v1_0_Models_UserVector_UserVectorsResponse,
  CA_Mastr_Models_v1_0_Models_AdHocVector,
} from "@/utils/openapi";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { useConfirmation } from "@/contexts/ConfirmationContext/ConfirmationContextProvider";
import S from "@/constants/strings";
import CAInput from "@/design-system/molecules/CAInput";
import { usePostUserVectorSWR } from "@/utils/swr-hooks/UserVector";
import PrepayVectorInput, { PrepayVectorInputHandler } from "../Utilities/PrepayVectorView/PrepayVectorInput";

interface VectorNameForm {
  user_vector_name: string;
}

const PrepayVectorEditModal = ({
  isOpen,
  toggleIsOpen,
  addAdHocVector,
  adHocVectorData,
  mutate,
  onDuplicateVectorHandler,
}: {
  isOpen: boolean;
  toggleIsOpen: (isOpen: boolean) => void;
  addAdHocVector: (data: CA_Mastr_Models_v1_0_Models_AdHocVector | undefined, id?: number) => void;
  adHocVectorData?: CA_Mastr_Models_v1_0_Models_AdHocVector | undefined;
  mutate: KeyedMutator<CA_Mastr_Api_v1_0_Models_UserVector_UserVectorsResponse | null>;
  onDuplicateVectorHandler: (vector: CA_Mastr_Api_v1_0_Models_UserVector_UserVector) => void;
}) => {
  const { trigger: postUserVector } = usePostUserVectorSWR();

  const confirmation = useConfirmation();
  const {
    register,
    formState: { isSubmitting },
    handleSubmit,
    reset,
  } = useForm<VectorNameForm>();

  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = React.useState(false);
  const vectorInputRef = React.useRef<PrepayVectorInputHandler>(null);

  const saveAdHocVector = () => {
    const data = vectorInputRef.current?.onSubmit();
    addAdHocVector(data);
    toggleIsOpen(false);
  };

  const duplicateAdHocVector = async (formData: VectorNameForm) => {
    const data = vectorInputRef.current?.onSubmit();
    const vectorName = formData?.user_vector_name;

    await postUserVector(
      {
        user_vector_name: vectorName,
        ...data,
      },
      {
        onSuccess: (res) => {
          addAdHocVector(undefined);
          onDuplicateVectorHandler(res?.user_vector as CA_Mastr_Api_v1_0_Models_UserVector_UserVector);
          toggleIsOpen(false);
          setIsDuplicateModalOpen(false);
          mutate();
          reset();
          showSuccessToast("Created", `Vector ${vectorName} has been added.`);
        },
        onError: (err) => {
          console.error(err);
          showErrorToast("Error", "Failed to save ad hoc vector");
        },
      }
    );
  };

  const closeModalHandler = async () => {
    if (vectorInputRef.current?.hasChanged()) {
      await confirmation.confirm({
        headerText: S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER,
        description: S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION,
      });
    }

    toggleIsOpen(false);
  };

  const resetVector = () => {
    vectorInputRef.current?.reset(true);
  };

  return (
    <>
      <CAModal
        size="2xl"
        isOpen={!!isOpen}
        onClose={closeModalHandler}
        showCloseIcon
        modalHeader="Ad hoc Prepay Vector"
        modalFooterElem={
          <HStack justifyContent="space-between" mt={6} mb={4} px={6}>
            <Button
              leftIcon={<IoSyncCircleOutline />}
              onClick={resetVector}
              variant="secondary"
              title="Reset"
              aria-label="Reset"
              size="sm"
              fontSize="2xl"
              type="button"
            >
              <Text>Reset</Text>
            </Button>
            <HStack spacing={2}>
              <Button variant={"secondary"} size="sm" onClick={closeModalHandler}>
                Cancel
              </Button>
              <Button variant={"primary"} size="sm" onClick={() => setIsDuplicateModalOpen(true)}>
                {S.COMMON.DUPLICATE}
              </Button>
              <Button variant={"primary"} size="sm" onClick={saveAdHocVector}>
                Save
              </Button>
            </HStack>
          </HStack>
        }
      >
        <Box maxW="fit-content" w="full" mt={2} overflow="auto">
          <PrepayVectorInput ref={vectorInputRef} userVector={adHocVectorData ?? undefined} />
        </Box>
      </CAModal>
      <CAModal
        size="lg"
        isOpen={!!isDuplicateModalOpen}
        onClose={() => setIsDuplicateModalOpen(false)}
        showCloseIcon
        modalHeader={S.COMMON.DUPLICATE}
      >
        <chakra.form onSubmit={handleSubmit(duplicateAdHocVector)}>
          <CAInput placeholder="Duplicate vector as" {...register("user_vector_name", { required: true })} />
          <HStack justifyContent="flex-end" mt={6} mb={4}>
            <Button variant={"secondary"} size="sm" onClick={() => setIsDuplicateModalOpen(false)}>
              Cancel
            </Button>
            <Button variant={"primary"} isLoading={isSubmitting} size="sm" type="submit">
              {S.COMMON.DUPLICATE}
            </Button>
          </HStack>
        </chakra.form>
      </CAModal>
    </>
  );
};

export default PrepayVectorEditModal;
