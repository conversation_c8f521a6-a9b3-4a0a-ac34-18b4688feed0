import * as React from "react";
import { Box } from "@chakra-ui/layout";
import CATable from "@/design-system/molecules/CATable";
import { getAPIErrorMessage, getFormattedNumberFixed, getFormattedYearMonth } from "@/utils/helpers";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { InlineValueDisplay } from "@/components/helpers/ValueDisplay";
import { useGetPrepayProjectionsSWR } from "@/utils/swr-hooks/CollateralInfo";

type CollateralProps = {
  bond_name: string | undefined;
  showFactorMonth?: boolean;
  asOf?: string;
};

const headers = ["Month", "CPR (%)", "CRR (%)", "CDR (%)"];

const HistoricalPrepay: React.FC<CollateralProps> = ({ bond_name, showFactorMonth = true, asOf }: CollateralProps) => {
  const { data: response, error } = useGetPrepayProjectionsSWR(bond_name, asOf);

  const tableData = React.useMemo(() => {
    const data = response?.prepay;

    return [
      {
        name: "1",
        values: [
          getFormattedNumberFixed(1)(data?.cpr_1m) ?? "-",
          getFormattedNumberFixed(1)(data?.crr_1m) ?? "-",
          getFormattedNumberFixed(1)(data?.cdr_1m) ?? "-",
        ],
      },
      {
        name: "3",
        values: [
          getFormattedNumberFixed(1)(data?.cpr_3m) ?? "-",
          getFormattedNumberFixed(1)(data?.crr_3m) ?? "-",
          getFormattedNumberFixed(1)(data?.cdr_3m) ?? "-",
        ],
      },
      {
        name: "6",
        values: [
          getFormattedNumberFixed(1)(data?.cpr_6m) ?? "-",
          getFormattedNumberFixed(1)(data?.crr_6m) ?? "-",
          getFormattedNumberFixed(1)(data?.cdr_6m) ?? "-",
        ],
      },
      {
        name: "12",
        values: [
          getFormattedNumberFixed(1)(data?.cpr_12m) ?? "-",
          getFormattedNumberFixed(1)(data?.crr_12m) ?? "-",
          getFormattedNumberFixed(1)(data?.cdr_12m) ?? "-",
        ],
      },
      {
        name: "24",
        values: [
          getFormattedNumberFixed(1)(data?.cpr_24m) ?? "-",
          getFormattedNumberFixed(1)(data?.crr_24m) ?? "-",
          getFormattedNumberFixed(1)(data?.cdr_24m) ?? "-",
        ],
      },
    ];
  }, [response]);

  return error ? (
    <CAAlertCard title={"Error"} description={getAPIErrorMessage(error)} status="error" fullWidth />
  ) : (
    <>
      {showFactorMonth && bond_name && (
        <Box mb={3}>
          <InlineValueDisplay name="Factor Month" value={getFormattedYearMonth(response?.prepay?.asof)} />
        </Box>
      )}
      <CATable headers={headers} data={tableData} />
    </>
  );
};

export default React.memo(HistoricalPrepay);
