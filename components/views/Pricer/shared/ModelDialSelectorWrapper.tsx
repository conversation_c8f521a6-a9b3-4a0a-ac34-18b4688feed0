import * as React from "react";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial } from "@/utils/openapi";
import { useGetUserModelDialsSWR } from "@/utils/swr-hooks";
import { stringifiedModelDials } from "@/utils/helpers/model-dials";
import ModelDialSelector from "./ModelDialSelector";

type ModelDialSelectorWrapperProps = {
  label?: string;
  lastRun: string | undefined;
};
const ModelDialSelectorWrapper: React.FC<ModelDialSelectorWrapperProps> = ({
  label,
  lastRun,
}: ModelDialSelectorWrapperProps) => {
  const {
    state: {
      userSettings: { user_model_dial_id, ad_hoc_deal_dial, ad_hoc_dial_selected },
      userSettingsCopy,
    },
    action: { updateUserModelDialId, updateAdHocDealDial, toggleAdHocSelected },
  } = usePricerModule();

  const [selectedDealDial, setSelectedDealDial] =
    React.useState<CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial>();

  const { data, mutate } = useGetUserModelDialsSWR();
  const userModelDials = data?.user_model_dials;

  React.useEffect(() => {
    if (userModelDials && user_model_dial_id) {
      const dial = userModelDials?.find((dial) => dial.user_model_dial_id === user_model_dial_id);
      if (!dial && !ad_hoc_deal_dial) {
        setSelectedDealDial(undefined);
        updateUserModelDialId(undefined);
      } else {
        setSelectedDealDial(dial);
      }
    }
  }, [userModelDials]); // eslint-disable-line react-hooks/exhaustive-deps

  const onUserModelChange = (updatedUserModelDial?: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial) => {
    setSelectedDealDial(updatedUserModelDial);
    updateUserModelDialId(updatedUserModelDial?.user_model_dial_id);
  };

  const getModelDialById = React.useCallback(
    (id?: number) => {
      if (userModelDials && id) {
        for (let i = 0; i < userModelDials?.length; i++) {
          if (id === userModelDials[i].user_model_dial_id) {
            return userModelDials[i];
          }
        }
      }
      return undefined;
    },
    [userModelDials]
  );

  const onAdHocSelection = (value: boolean) => {
    if (value) {
      updateUserModelDialId(undefined);
    }
    toggleAdHocSelected(value);
  };

  let info = {
    show: false,
    msg1: "",
    msg2: "",
  };

  if (lastRun) {
    if (ad_hoc_dial_selected && userSettingsCopy.ad_hoc_dial_selected) {
      if (
        ad_hoc_dial_selected != userSettingsCopy.ad_hoc_dial_selected ||
        stringifiedModelDials(ad_hoc_deal_dial) !== stringifiedModelDials(userSettingsCopy.ad_hoc_deal_dial)
      ) {
        info = {
          show: true,
          msg1: "Ad hoc dial values has been updated from the last run.",
          msg2: "Click run to update your calculation",
        };
      }
    } else if (
      user_model_dial_id != userSettingsCopy.user_model_dial_id ||
      ad_hoc_dial_selected != userSettingsCopy.ad_hoc_dial_selected
    ) {
      const prevWasAdHoc = !!userSettingsCopy.ad_hoc_dial_selected;
      const changedToAdHoc = !!ad_hoc_dial_selected;

      const changedFromDial = prevWasAdHoc
        ? "Ad Hoc"
        : getModelDialById(userSettingsCopy.user_model_dial_id)?.user_model_dial_name ?? "None";
      const changedToDial = changedToAdHoc ? "Ad Hoc" : selectedDealDial?.user_model_dial_name ?? "None";

      if (changedFromDial !== changedToDial) {
        info = {
          show: true,
          msg1: `Value changed from "${changedFromDial}" to "${changedToDial}"`,
          msg2: "Click run to update your calculation",
        };
      }
    }
  }

  return (
    <ModelDialSelector
      label={label || "Model Dial"}
      onUserModelDialChange={onUserModelChange}
      ad_hoc_dial_selected={ad_hoc_dial_selected}
      toggleAdHocSelected={onAdHocSelection}
      updateAdHocDealDial={updateAdHocDealDial}
      adHocDealDial={ad_hoc_deal_dial}
      isSearchDisabled={!data}
      selectedDealDial={selectedDealDial}
      userModelDials={userModelDials}
      info={info}
      mutate={mutate}
    />
  );
};

export default ModelDialSelectorWrapper;
