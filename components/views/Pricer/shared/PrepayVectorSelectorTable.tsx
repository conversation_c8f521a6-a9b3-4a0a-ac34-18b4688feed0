import React from "react";
import { Box, Center, HStack, Text, Tooltip, useColorModeValue } from "@chakra-ui/react";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { KeyedMutator } from "swr";
import CASearch from "@/design-system/molecules/CASearch";
import CATable from "@/design-system/molecules/CATable";
import {
  CA_Mastr_Api_v1_0_Models_UserVector_UserVector,
  CA_Mastr_Api_v1_0_Models_UserVector_UserVectorsResponse,
  CA_Mastr_Models_v1_0_Models_EntityPermissionRole,
} from "@/utils/openapi";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import CAIcon from "@/design-system/atoms/CAIcon";
import colors from "@/design-system/theme/colors";
import { getPrepayVectorDisplayValue } from "@/utils/helpers/pricer";
import { useDeleteUserVectorSWR } from "@/utils/swr-hooks/UserVector";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { useSortTable } from "@/hooks/useSortTable";
import { ColumnHeader } from "@/design-system/molecules/CATable/CATableHeader";

interface PrepayVectorSelectorTableProps {
  userVectors: CA_Mastr_Api_v1_0_Models_UserVector_UserVector[];
  onTableItemSelect: (selectedVector: CA_Mastr_Api_v1_0_Models_UserVector_UserVector) => void;
  mutate: KeyedMutator<CA_Mastr_Api_v1_0_Models_UserVector_UserVectorsResponse | null>;
  selectedVectorId?: number | undefined;
}

const headers: ColumnHeader[] = [
  { header: "ID", accessor: "user_vector_id" },
  { header: "Name", accessor: "user_vector_name" },
  { header: "Created by", accessor: "user.firstName" },
  { header: "Prepay" },
  { header: "Default" },
  { header: "Role", accessor: "role" },
  { header: "" },
];

const TooltipWrappedText = ({ text }: { text: string }) => {
  return (
    <Tooltip label={text}>
      <Text noOfLines={1} ml={1} minW="7rem">
        {text}
      </Text>
    </Tooltip>
  );
};

const PrepayVectorSelectorTable: React.FC<PrepayVectorSelectorTableProps> = ({
  userVectors,
  onTableItemSelect,
  mutate,
  selectedVectorId,
}) => {
  const { trigger: deleteUserVector } = useDeleteUserVectorSWR();

  const tableRowActiveColor = useColorModeValue(colors.celloBlue[75], colors.celloBlue[500]);
  //control switch for view ownership
  const [isAllViews, setIsAllViews] = React.useState(false);

  const filteredByOwnership = isAllViews
    ? userVectors
    : userVectors?.filter((view) => view.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER);

  const { filteredList, getInputProps } = useKeywordsSearch({
    getSearchableText: (item) =>
      `$V${item.user_vector_id} ${item.user_vector_name} ${item.user?.firstName} ${item.user?.lastName}`,
    list: filteredByOwnership,
  });
  const { getTableProps, sortedData } = useSortTable({ data: filteredList });

  // UI Wrappers
  const wrapTableCellInsideBox = (cellContent: string) => (
    <Box pl={1} maxW={"9rem"} minW={"9rem"}>
      {cellContent}
    </Box>
  );

  const vectorTableData = React.useMemo(() => {
    return sortedData.map((vector) => ({
      name: `V${vector.user_vector_id}`,
      values: [
        wrapTableCellInsideBox(vector.user_vector_name ?? ""),
        wrapTableCellInsideBox(`${vector?.user?.firstName} ${vector?.user?.lastName}`),
        <TooltipWrappedText
          key="prepay_vector"
          text={getPrepayVectorDisplayValue(vector.interpolation, vector.prepay_vector_values)}
        />,
        <TooltipWrappedText
          key="default_vector"
          text={getPrepayVectorDisplayValue(vector.interpolation, vector.default_vector_values)}
        />,
        `${vector.role}`,
        vector.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER ? (
          <Center
            p={0.5}
            onClick={(e) => {
              e.stopPropagation();
              deleteVectorDial(vector.user_vector_id, vector.user_vector_name ?? "", vector.updated ?? undefined);
            }}
            key={"delete" + vector.user_vector_id}
          >
            <CAIcon as={RiDeleteBin6Fill} variant="secondary" />
          </Center>
        ) : (
          <Box />
        ),
      ],
      rowBackgroundColor: vector.user_vector_id === selectedVectorId ? tableRowActiveColor : "inherit",
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortedData]);

  // Helpers
  const onTableItemSelectHandler = (
    _selectedItem: { name: string | React.JSX.Element; values: (string | number | React.JSX.Element)[] },
    index: number
  ) => {
    if (vectorTableData?.length) {
      const selectedUserVector = filteredList[index];
      onTableItemSelect(selectedUserVector);
    }
  };

  const deleteVectorDial = async (id: number | undefined, vectorDialName: string, lastUpdated: string | undefined) => {
    if (!id) return;
    await deleteUserVector(
      {
        userVectorId: id,
        lastUpdated,
      },
      {
        onSuccess: () => {
          showSuccessToast("Deleted", `"${vectorDialName}" deleted successfully.`);
          mutate({ user_vectors: userVectors?.filter((vector) => vector.user_vector_id !== id) });
        },
        onError: () => {
          showErrorToast("Error", `Error while deleting "${vectorDialName}"`);
        },
      }
    );
  };

  const handleViewChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsAllViews(event.target.checked);
  };

  return (
    <Box
      backgroundColor={useColorModeValue("white", "celloBlue.1100")}
      w={{ base: "85vw", md: "auto" }}
      maxH="30rem"
      overflowY="auto"
      overflowX="auto"
    >
      <HStack
        position="absolute"
        width="full"
        background={useColorModeValue("white", "celloBlue.1100")}
        justifyContent="space-between"
      >
        <Box pl="3" py="2" width={"full"} maxW="15rem">
          <CASearch
            name="model-dial-search"
            maxW="15rem"
            placeholder="Search Vector"
            isDisabled={!filteredByOwnership || filteredByOwnership.length === 0}
            {...getInputProps()}
          />
        </Box>
        <CASwitchInput
          label={"All Views"}
          hideLabel
          isChecked={isAllViews}
          onChange={handleViewChange}
          formControlStyleProps={{
            display: "block",
            width: "fit-content",
          }}
        />
      </HStack>
      <Box pt="3.25rem" pb="2" maxW="45rem">
        <CATable
          headers={headers}
          data={vectorTableData}
          variant="caFullCentered"
          onItemClick={onTableItemSelectHandler}
          headerStyles={{
            backgroundColor: useColorModeValue("white", "celloBlue.1100"),
            stickyPosition: "3.25rem",
            textAlign: "left",
          }}
          columnsToLeftAlign={[0, 1, 2, 3, 4, 5]}
          condensed
          {...getTableProps()}
        />
      </Box>
    </Box>
  );
};

export default PrepayVectorSelectorTable;
