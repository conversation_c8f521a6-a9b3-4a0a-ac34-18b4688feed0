import React from "react";
import { <PERSON>, <PERSON><PERSON>, HStack, Text, chakra } from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import { KeyedMutator } from "swr";
import { IoSyncCircleOutline } from "react-icons/io5";
import CAModal from "@/design-system/molecules/CAModal";
import {
  CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial,
  CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDialsResponse,
  CA_Mastr_Models_v1_0_Models_ModelDialData,
} from "@/utils/openapi";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { useConfirmation } from "@/contexts/ConfirmationContext/ConfirmationContextProvider";
import S from "@/constants/strings";
import CAInput from "@/design-system/molecules/CAInput";
import { usePostUserModelDialSWR } from "@/utils/swr-hooks/UserModelDial";
import ModelDialsEditForm, {
  ModelDialsEditFormHandler,
} from "../Utilities/ModelDialsView/ModelDialsAccordionContent/EditForm";

interface DialNameForm {
  user_model_dial_name: string;
}

export const ModalDialEditModal = ({
  isOpen,
  toggleIsOpen,
  addAdHocDial,
  adHocDialData,
  mutate,
  onUserModelDialChange,
}: {
  isOpen: boolean;
  toggleIsOpen: (isOpen: boolean) => void;
  addAdHocDial: (data: Array<CA_Mastr_Models_v1_0_Models_ModelDialData> | undefined, id?: number) => void;
  adHocDialData: Array<CA_Mastr_Models_v1_0_Models_ModelDialData> | undefined;
  mutate: KeyedMutator<CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDialsResponse | null>;
  onUserModelDialChange: (updatedUserModelDial?: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial) => void;
}) => {
  const confirmation = useConfirmation();
  const { trigger: postUserModelDial } = usePostUserModelDialSWR();

  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = React.useState(false);
  const {
    register,
    formState: { isSubmitting },
    handleSubmit,
    reset,
  } = useForm<DialNameForm>();

  const modelDialFormRef = React.useRef<ModelDialsEditFormHandler>(null);

  const saveAdHocDial = () => {
    const data = modelDialFormRef.current?.getSubmitData();
    addAdHocDial(data);
    toggleIsOpen(false);
  };

  const duplicateAdHocDial = async (data: DialNameForm) => {
    const modelDials = modelDialFormRef.current?.getSubmitData();
    const dialName = data.user_model_dial_name;

    await postUserModelDial(
      { user_model_dial_name: dialName, model_dial_data: modelDials },
      {
        onSuccess: (res) => {
          addAdHocDial(undefined);
          onUserModelDialChange(res?.user_model_dial);
          toggleIsOpen(false);
          setIsDuplicateModalOpen(false);
          mutate();
          reset();
          showSuccessToast("Created", `Model dial ${dialName} has been added.`);
        },
        onError: (err) => {
          console.error(err);
          showErrorToast("Error", "Failed to save ad hoc dial");
        },
      }
    );
  };

  const closeModalHandler = async () => {
    if (modelDialFormRef.current?.isDataEdited()) {
      await confirmation.confirm({
        headerText: S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER,
        description: S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION,
      });
    }

    toggleIsOpen(false);
  };

  const resetModelDials = () => {
    addAdHocDial(undefined);
    modelDialFormRef.current?.reset();
  };

  return (
    <>
      <CAModal
        size="7xl"
        isOpen={!!isOpen}
        onClose={closeModalHandler}
        showCloseIcon
        modalHeader="Ad hoc Dial"
        modalFooterElem={
          <HStack justifyContent="space-between" mt={6} mb={4} px={6}>
            <Button
              leftIcon={<IoSyncCircleOutline />}
              onClick={resetModelDials}
              variant="secondary"
              title="Reset"
              aria-label="Reset"
              size="sm"
              fontSize="2xl"
            >
              <Text>Reset</Text>
            </Button>
            <HStack spacing={2}>
              <Button variant={"secondary"} size="sm" onClick={closeModalHandler}>
                Cancel
              </Button>
              <Button variant={"primary"} size="sm" onClick={() => setIsDuplicateModalOpen(true)}>
                {S.COMMON.DUPLICATE}
              </Button>
              <Button variant={"primary"} size="sm" onClick={saveAdHocDial}>
                Apply
              </Button>
            </HStack>
          </HStack>
        }
      >
        <Box maxW="fit-content" w="full" mt={2} overflow="auto">
          <ModelDialsEditForm ref={modelDialFormRef} modelDialData={adHocDialData ?? undefined} />
        </Box>
      </CAModal>
      <CAModal
        size="lg"
        isOpen={!!isDuplicateModalOpen}
        onClose={() => setIsDuplicateModalOpen(false)}
        showCloseIcon
        modalHeader={S.COMMON.DUPLICATE}
      >
        <chakra.form onSubmit={handleSubmit(duplicateAdHocDial)}>
          <CAInput placeholder="Duplicate modal dial as" {...register("user_model_dial_name", { required: true })} />
          <HStack justifyContent="flex-end" mt={6} mb={4}>
            <Button variant={"secondary"} size="sm" onClick={() => setIsDuplicateModalOpen(false)}>
              Cancel
            </Button>
            <Button variant={"primary"} isLoading={isSubmitting} size="sm" type="submit">
              {S.COMMON.DUPLICATE}
            </Button>
          </HStack>
        </chakra.form>
      </CAModal>
    </>
  );
};
