import * as React from "react";
import {
  Box,
  Button,
  Center,
  Flex,
  HStack,
  IconButton,
  Spacer,
  Text,
  Tooltip,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoClose } from "react-icons/io5";
import { RiDeleteBin6Fill } from "react-icons/ri";
import { KeyedMutator } from "swr";
import { IoMdCheckmark } from "react-icons/io";
import CAIcon from "@/design-system/atoms/CAIcon";
import CAInput from "@/design-system/molecules/CAInput";
import CASearch from "@/design-system/molecules/CASearch";
import CATable, { CATableProps } from "@/design-system/molecules/CATable";
import PopoverMenu from "@/design-system/molecules/PopoverMenu";
import colors from "@/design-system/theme/colors";
import { getFormattedLocaleDate, getFormattedTime } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial,
  CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDialsResponse,
  CA_Mastr_Models_v1_0_Models_EntityPermissionRole,
  CA_Mastr_Models_v1_0_Models_ModelDialData,
} from "@/utils/openapi";
import { InfoMsg } from "@/design-system/molecules/InfoPopover/InfoPopover";
import { showErrorToast, showSuccessToast } from "@/design-system/theme/toast";
import { useDeleteUserModelDialSWR } from "@/utils/swr-hooks/UserModelDial";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { useSortTable } from "@/hooks/useSortTable";
import { ColumnHeader } from "@/design-system/molecules/CATable/CATableHeader";
import { safeLocalStorage } from "@/utils/local-storage";
import { LOCAL_STORAGE_KEY } from "@/constants/storage-keys";
import { ModalDialEditModal } from "./AdHocDealDialModal";

type ModelDialSelectorProps = {
  label?: string;
  fullWidth?: boolean;
  info?: InfoMsg;
  isSearchDisabled: boolean;
  userModelDials: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial[] | null | undefined;
  selectedDealDial?: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial;
  triggerElement?: React.JSX.Element;
  isDisabled?: boolean;
  onUserModelDialChange: (updatedUserModelDial?: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial) => void;
  adHocDealDial?: Array<CA_Mastr_Models_v1_0_Models_ModelDialData> | undefined;
  updateAdHocDealDial?: (data: Array<CA_Mastr_Models_v1_0_Models_ModelDialData> | undefined) => void;
  ad_hoc_dial_selected?: boolean;
  toggleAdHocSelected?: (isSelected: boolean) => void;
  mutate: KeyedMutator<CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDialsResponse | null>;
  hasAdHoc?: boolean;
};

const headers: ColumnHeader[] = [
  { header: "Dial", accessor: "user_model_dial_name" },
  { header: "Created by", accessor: "user.firstName" },
  { header: "Created", accessor: "inserted" },
  { header: "Updated", accessor: "updated" },
  { header: "Role", accessor: "role" },
  { header: "" },
];

const wrapTableCellInsideBox = (cellContent: string) => (
  <Box whiteSpace="nowrap" pr={2}>
    {cellContent}
  </Box>
);

const ModelDialSelector: React.FC<ModelDialSelectorProps> = ({
  label,
  onUserModelDialChange,
  isSearchDisabled,
  userModelDials,
  selectedDealDial,
  info,
  triggerElement,
  fullWidth = false,
  isDisabled,
  mutate,
  adHocDealDial,
  updateAdHocDealDial,
  ad_hoc_dial_selected,
  toggleAdHocSelected,
  hasAdHoc = true,
}: ModelDialSelectorProps) => {
  const { trigger: deleteUserModelDial } = useDeleteUserModelDialSWR();
  //control switch for view ownership
  const [modelDialsViewMode, setModelDialsViewMode] = React.useState<"all" | "owner">("all");

  const filteredByOwnership =
    modelDialsViewMode === "all"
      ? userModelDials
      : userModelDials?.filter((view) => view.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER);

  const { filteredList, getInputProps, resetSearch } = useKeywordsSearch({
    getSearchableText: (item) => `${item.user_model_dial_name} ${item.user?.firstName} ${item.user?.lastName}`,
    list: filteredByOwnership,
  });

  const { getTableProps, sortedData } = useSortTable({ data: filteredList });

  React.useEffect(() => {
    const viewMode = safeLocalStorage.getItem(LOCAL_STORAGE_KEY.MODEL_DIAL_VIEW_MODE);
    setModelDialsViewMode(viewMode as "all" | "owner");
  }, [setModelDialsViewMode]);

  const [adHocModalOpen, toggleAdHocModal] = React.useState(false);
  const [isPopoverOpen, togglePopover] = React.useState(false);
  const tableRowActiveColor = useColorModeValue(colors.celloBlue[75], colors.celloBlue[500]);

  const deleteModelDial = async (id: number | undefined, dialName: string, lastUpdated: string | undefined) => {
    if (!id) return;
    await deleteUserModelDial(
      {
        userModelDialId: id,
        lastUpdated,
      },
      {
        onSuccess: () => {
          showSuccessToast("Deleted", `"${dialName}" deleted successfully.`);
          mutate({ user_model_dials: userModelDials?.filter((modelDial) => modelDial.user_model_dial_id !== id) });
        },
        onError: () => {
          showErrorToast("Error", `Error while deleting "${dialName}"`);
        },
      }
    );
  };

  const tableData = React.useMemo(() => {
    return sortedData?.map((item) => {
      const rowData: CATableProps["data"][0] = {
        name: item.user_model_dial_name ?? "",
        values: [
          wrapTableCellInsideBox(`${item.user?.firstName} ${item.user?.lastName}`),
          wrapTableCellInsideBox(`${getFormattedLocaleDate(item.inserted)} ${getFormattedTime(item.inserted)}`),
          wrapTableCellInsideBox(`${getFormattedLocaleDate(item.updated)} ${getFormattedTime(item.updated)}`),
          wrapTableCellInsideBox(item.role ?? ""),
          item.role === CA_Mastr_Models_v1_0_Models_EntityPermissionRole.OWNER ? (
            <Center
              p={0.5}
              onClick={(e) => {
                e.stopPropagation();
                deleteModelDial(item.user_model_dial_id, item.user_model_dial_name ?? "", item.updated ?? undefined);
              }}
            >
              <CAIcon as={RiDeleteBin6Fill} variant="secondary" />
            </Center>
          ) : (
            <Box />
          ),
        ],
      };

      rowData.name = (
        <Box whiteSpace="nowrap">
          <Text variant="tableLeft">{rowData.name}</Text>
        </Box>
      );

      if (selectedDealDial?.user_model_dial_id && item.user_model_dial_id === selectedDealDial?.user_model_dial_id) {
        rowData.rowBackgroundColor = `${tableRowActiveColor} !important`;
      }

      return rowData;
    });
    /**
     * prevent recalculation when **deleteModelDial** and **toggleSharedStatusHandler** changes
     * these two functions are not wrapped in useCallback for performance reasons
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedDealDial?.user_model_dial_id, tableRowActiveColor, userModelDials, sortedData]);

  const onPopoverClose = () => {
    resetSearch();
    togglePopover(false);
  };

  const tableItemClick = (
    _selectedItem: { name: string | React.JSX.Element; values: (string | number | React.JSX.Element)[] },
    index: number
  ) => {
    if (userModelDials?.length) {
      const selectedUserModelDial = filteredList[index];
      onUserModelDialChange(selectedUserModelDial);
      toggleAdHocSelected?.(false);
    }
    onPopoverClose();
  };

  const onDealDialInputClick = () => {
    if (!isPopoverOpen) {
      togglePopover(true);
      mutate();
    }
  };

  const onClear = () => {
    onUserModelDialChange(undefined);
    toggleAdHocSelected?.(false);
  };

  const addAdHocDialHandler = (dealDial: Array<CA_Mastr_Models_v1_0_Models_ModelDialData> | undefined) => {
    onUserModelDialChange(undefined);
    updateAdHocDealDial?.(dealDial);
    toggleAdHocSelected?.(Boolean(dealDial && dealDial.length > 0));
  };

  const handleViewChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setModelDialsViewMode(event.target.checked ? "all" : "owner");
    safeLocalStorage.setItem(LOCAL_STORAGE_KEY.MODEL_DIAL_VIEW_MODE, event.target.checked ? "all" : "owner");
  };

  const showSelectAdHoc = !ad_hoc_dial_selected && adHocDealDial && adHocDealDial.length > 0;

  return (
    <Flex onClick={(e) => e.stopPropagation()}>
      {label && (
        <Text variant="tableLeft" mr={1} alignItems="center" display="flex">
          {label}
        </Text>
      )}
      <Spacer />

      <Box position="relative" w={fullWidth ? "100%" : "auto"}>
        <PopoverMenu
          triggerElement={
            triggerElement ? (
              <Box onClick={isDisabled ? undefined : onDealDialInputClick}>{triggerElement}</Box>
            ) : (
              <HStack
                role="group"
                position="relative"
                alignItems="center"
                spacing={"0.2rem"}
                maxW={fullWidth ? "inherit" : "6rem"}
              >
                <Tooltip
                  label={ad_hoc_dial_selected ? "Ad Hoc" : selectedDealDial?.user_model_dial_name}
                  aria-label={ad_hoc_dial_selected ? "Ad Hoc" : selectedDealDial?.user_model_dial_name ?? ""}
                  placement="bottom"
                  isDisabled={!selectedDealDial?.user_model_dial_name}
                >
                  <Box position="relative">
                    <CAInput
                      width={fullWidth ? "inherit" : "6rem"}
                      pr={"1.25rem"}
                      name={"dealdial"}
                      value={ad_hoc_dial_selected ? "Ad Hoc" : selectedDealDial?.user_model_dial_name ?? ""}
                      onClick={onDealDialInputClick}
                      clickOnly
                      info={info}
                      _groupHover={{ borderTopRightRadius: { base: "4px", md: 0 } }}
                    />
                    <IconButton
                      onClick={onClear}
                      aria-label="Clear"
                      zIndex={1}
                      visibility={selectedDealDial?.user_model_dial_name || ad_hoc_dial_selected ? "visible" : "hidden"}
                      position="absolute"
                      top="50%"
                      transform="translateY(-50%)"
                      right="0"
                      size="xs"
                      fontSize="sm"
                      variant="ghost"
                      background="transparent"
                      border="0"
                      fontWeight="bold"
                      color={info?.show ? "#000" : "inherit"}
                      icon={<CAIcon as={IoClose} variant="default" />}
                    />
                  </Box>
                </Tooltip>
                {hasAdHoc && (
                  <HStack
                    position="absolute"
                    zIndex="dropdown"
                    flexDirection={{ base: "row-reverse", md: "row" }}
                    left={{ base: showSelectAdHoc ? "-5.8rem" : "-4.2rem", md: "5.2rem" }}
                    display={isPopoverOpen ? "flex" : "none"}
                    _groupHover={{ display: "flex" }}
                    spacing={"0.1rem"}
                    ml="0.81rem"
                  >
                    <Button
                      h="26px"
                      variant="primary"
                      my={1}
                      size="xs"
                      borderRadius={{ base: "4px 0 0 4px", md: "0 4px 4px 0" }}
                      onClick={() => toggleAdHocModal(true)}
                      textTransform="capitalize"
                    >
                      Ad Hoc
                    </Button>
                    {showSelectAdHoc && (
                      <IconButton
                        onClick={() => toggleAdHocSelected?.(true)}
                        aria-label="select ad hoc"
                        variant="primary"
                        size="xs"
                        icon={<CAIcon as={IoMdCheckmark} variant="default" />}
                      />
                    )}
                  </HStack>
                )}
              </HStack>
            )
          }
          onClose={onPopoverClose}
          isPopoverOpen={isPopoverOpen}
          placement="bottom-start"
          popoverContent={{
            showArrow: false,
            renderInPortal: true,
            maxWidth: "100%",
            body: (
              <Box
                backgroundColor={useColorModeValue("white", "celloBlue.1100")}
                w={{ base: "85vw", md: "auto" }}
                maxH="30rem"
                overflowY="auto"
                overflowX="auto"
              >
                <HStack
                  position="absolute"
                  width="full"
                  background={useColorModeValue("white", "celloBlue.1100")}
                  justifyContent="space-between"
                >
                  <Box pl="3" py="2" width={"full"} maxW="15rem">
                    <CASearch
                      name="model-dial-search"
                      placeholder={"Search Name"}
                      isDisabled={isSearchDisabled || !filteredByOwnership?.length}
                      {...getInputProps()}
                    />
                  </Box>
                  <CASwitchInput
                    label={"All"}
                    hideLabel
                    isChecked={modelDialsViewMode === "all"}
                    onChange={handleViewChange}
                    formControlStyleProps={{
                      display: "block",
                      width: "fit-content",
                    }}
                  />
                </HStack>
                <Box pt="3.25rem" pb="2">
                  <CATable
                    headers={headers}
                    data={tableData ?? []}
                    variant="caFullCentered"
                    onItemClick={tableItemClick}
                    headerStyles={{
                      backgroundColor: useColorModeValue("white", "celloBlue.1100"),
                      stickyPosition: "3.25rem",
                      textAlign: "left",
                    }}
                    columnsToLeftAlign={[0, 1, 2, 3]}
                    {...getTableProps()}
                  />
                </Box>
              </Box>
            ),
          }}
          trigger="click"
        />
      </Box>

      {hasAdHoc && (
        <ModalDialEditModal
          isOpen={adHocModalOpen}
          toggleIsOpen={toggleAdHocModal}
          adHocDialData={adHocDealDial}
          addAdHocDial={addAdHocDialHandler}
          mutate={mutate}
          onUserModelDialChange={onUserModelDialChange}
        />
      )}
    </Flex>
  );
};

export default ModelDialSelector;
