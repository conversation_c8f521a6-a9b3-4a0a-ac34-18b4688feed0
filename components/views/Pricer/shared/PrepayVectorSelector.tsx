import * as React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>lex, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spacer, Text, Tooltip } from "@chakra-ui/react";
import { IoClose } from "react-icons/io5";
import { IoMdCheckmark } from "react-icons/io";
import CAIcon from "@/design-system/atoms/CAIcon";
import CAInput from "@/design-system/molecules/CAInput";
import PopoverMenu from "@/design-system/molecules/PopoverMenu";
import {
  CA_Mastr_Api_v1_0_Models_UserVector_UserVector,
  CA_Mastr_Models_v1_0_Models_AdHocVector,
} from "@/utils/openapi";
import { useGetUserVectorsSWR } from "@/utils/swr-hooks";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { stringifiedPrepayVector } from "@/utils/helpers/prepay-vector";
import PrepayVectorEditModal from "./AdHocPrepayVector";
import PrepayVectorSelectorTable from "./PrepayVectorSelectorTable";

type PrepayVectorSelectorProps = {
  label?: string;
  lastRun: string | undefined;
};
const PrepayVectorSelector: React.FC<PrepayVectorSelectorProps> = ({ label, lastRun }: PrepayVectorSelectorProps) => {
  const {
    state: {
      userSettings: { user_vector_id, ad_hoc_vector_selected, ad_hoc_vector },
      userSettingsCopy,
    },
    action: { updateUserVectorId, toggleAdHocVectorSelected, updateAdHocVector },
  } = usePricerModule();

  const [adHocModalOpen, toggleAdHocModal] = React.useState(false);
  const [selectedVector, setSelectedVector] = React.useState<CA_Mastr_Api_v1_0_Models_UserVector_UserVector>();
  const [isPopoverOpen, togglePopover] = React.useState(false);
  const { data, mutate } = useGetUserVectorsSWR();

  const userVectors = data?.user_vectors;
  const msg2 = "Click run to update your calculation";

  const getVectorById = React.useCallback(
    (id?: number) => {
      if (userVectors && id) {
        for (let i = 0; i < userVectors?.length; i++) {
          if (id === userVectors[i].user_vector_id) {
            return userVectors[i];
          }
        }
      }
      return undefined;
    },
    [userVectors]
  );

  React.useEffect(() => {
    if (userVectors) {
      const vector = getVectorById(user_vector_id);
      setSelectedVector(vector);
    }
  }, [getVectorById, userVectors, user_vector_id]);

  const onPopoverClose = () => {
    togglePopover(false);
  };

  const tableItemClick = (selectedVector: CA_Mastr_Api_v1_0_Models_UserVector_UserVector) => {
    setSelectedVector(selectedVector);
    updateUserVectorId(selectedVector?.user_vector_id);
    if (ad_hoc_vector_selected) {
      toggleAdHocVectorSelected(false);
    }
    onPopoverClose();
  };

  const onVectorInputInputClick = () => {
    if (!isPopoverOpen) {
      togglePopover(true);
      mutate();
    }
  };

  const onClear = () => {
    setSelectedVector(undefined);
    updateUserVectorId(undefined);
    toggleAdHocVectorSelected?.(false);
  };

  const addAdHocVectorHandler = (vector: CA_Mastr_Models_v1_0_Models_AdHocVector | undefined) => {
    setSelectedVector(undefined);
    updateUserVectorId(undefined);
    updateAdHocVector?.(vector);
    toggleAdHocVectorSelected?.(
      !!(
        (vector?.default_vector_values && vector?.default_vector_values?.length > 0) ||
        (vector?.prepay_vector_values && vector?.prepay_vector_values?.length > 0)
      )
    );
  };

  const onDuplicateVectorHandler = (vector: CA_Mastr_Api_v1_0_Models_UserVector_UserVector) => {
    setSelectedVector(vector);
    updateUserVectorId(vector.user_vector_id);
  };

  const isAdHocValid =
    (ad_hoc_vector?.default_vector_values && ad_hoc_vector?.default_vector_values?.length > 0) ||
    (ad_hoc_vector?.prepay_vector_values && ad_hoc_vector?.prepay_vector_values?.length > 0);

  const hasError =
    (user_vector_id != userSettingsCopy.user_vector_id ||
      ad_hoc_vector_selected != userSettingsCopy.ad_hoc_vector_selected ||
      stringifiedPrepayVector(ad_hoc_vector) !== stringifiedPrepayVector(userSettingsCopy.ad_hoc_vector)) &&
    !!lastRun;

  const showSelectAdHocVector = !ad_hoc_vector_selected && isAdHocValid;
  return (
    <Flex>
      <Text variant="tableLeft" mr={1} alignItems="center" display="flex">
        {label || "Prepay Vector"}
      </Text>
      <Spacer />

      <Box position="relative">
        <PopoverMenu
          triggerElement={
            <HStack role="group" position="relative" alignItems="center" spacing={"0.2rem"} maxW={"6rem"}>
              <Tooltip
                label={ad_hoc_vector_selected ? "Ad Hoc" : selectedVector?.user_vector_name}
                aria-label={ad_hoc_vector_selected ? "Ad Hoc" : selectedVector?.user_vector_name ?? ""}
                placement="bottom"
                isDisabled={!selectedVector?.user_vector_name}
              >
                <Box position="relative">
                  <CAInput
                    width={"6rem"}
                    pr={"1.25rem"}
                    name={"vector"}
                    value={ad_hoc_vector_selected ? "Ad Hoc" : selectedVector?.user_vector_name ?? ""}
                    onClick={onVectorInputInputClick}
                    clickOnly
                    info={{
                      show: hasError,
                      msg1:
                        ad_hoc_vector_selected &&
                        userSettingsCopy.ad_hoc_vector_selected &&
                        stringifiedPrepayVector(ad_hoc_vector) !==
                          stringifiedPrepayVector(userSettingsCopy.ad_hoc_vector)
                          ? "Ad hoc values has been updated from the last run."
                          : `Value changed from "${
                              userSettingsCopy.ad_hoc_vector_selected
                                ? "Ad Hoc"
                                : getVectorById(userSettingsCopy.user_vector_id)?.user_vector_name ?? "None"
                            }" to "${ad_hoc_vector_selected ? "Ad Hoc" : selectedVector?.user_vector_name ?? "None"}"`,
                      msg2: msg2,
                    }}
                  />
                  <IconButton
                    onClick={onClear}
                    aria-label="Clear Prepay Vector Selection"
                    zIndex={1}
                    visibility={selectedVector?.user_vector_name || ad_hoc_vector_selected ? "visible" : "hidden"}
                    position="absolute"
                    top="50%"
                    transform="translateY(-50%)"
                    right="0"
                    size="xs"
                    fontSize="sm"
                    variant="ghost"
                    background="transparent"
                    border="0"
                    fontWeight="bold"
                    color={hasError ? "#000" : "inherit"}
                    icon={<CAIcon as={IoClose} variant="default" />}
                  />
                </Box>
              </Tooltip>
              <HStack
                position="absolute"
                flexDirection={{ base: "row-reverse", md: "row" }}
                left={{ base: showSelectAdHocVector ? "-5.8rem" : "-4.2rem", md: "5.2rem" }}
                zIndex="dropdown"
                display={isPopoverOpen ? "flex" : "none"}
                _groupHover={{ display: "flex" }}
                spacing={"0.1rem"}
              >
                <Button
                  h="26px"
                  ml="0.5rem"
                  variant="primary"
                  my={1}
                  size="xs"
                  borderRadius={{ base: "4px 0 0 4px", md: "0 4px 4px 0" }}
                  onClick={() => {
                    toggleAdHocModal(true);
                  }}
                  textTransform="capitalize"
                >
                  Ad Hoc
                </Button>

                {showSelectAdHocVector && (
                  <IconButton
                    onClick={() => {
                      toggleAdHocVectorSelected?.(true);
                      setSelectedVector(undefined);
                      updateUserVectorId(undefined);
                    }}
                    aria-label="select ad hoc"
                    variant="primary"
                    size="xs"
                    icon={<CAIcon as={IoMdCheckmark} variant="default" />}
                  />
                )}
              </HStack>
            </HStack>
          }
          onClose={onPopoverClose}
          isPopoverOpen={isPopoverOpen}
          placement="bottom-end"
          popoverContent={{
            showArrow: false,
            renderInPortal: true,
            maxWidth: "100%",
            body: (
              <PrepayVectorSelectorTable
                mutate={mutate}
                userVectors={userVectors ?? []}
                onTableItemSelect={tableItemClick}
                selectedVectorId={selectedVector?.user_vector_id}
              />
            ),
          }}
          trigger="click"
        />
      </Box>

      <PrepayVectorEditModal
        isOpen={adHocModalOpen}
        toggleIsOpen={toggleAdHocModal}
        addAdHocVector={addAdHocVectorHandler}
        mutate={mutate}
        onDuplicateVectorHandler={onDuplicateVectorHandler}
        adHocVectorData={ad_hoc_vector}
      />
    </Flex>
  );
};

export default PrepayVectorSelector;
