import * as React from "react";
import { Text } from "@chakra-ui/layout";
import { Box, ColorMode, Icon, VStack, useColorMode } from "@chakra-ui/react";
import { IoInformationCircleOutline, IoWarningOutline } from "react-icons/io5";
import ValueDisplay from "@/components/helpers/ValueDisplay";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import colors from "@/design-system/theme/colors";
import PopoverMenu from "@/design-system/molecules/PopoverMenu";

interface InfoType {
  show: boolean;
  msg1?: string;
  msg2?: string;
  type?: "changed" | "outdated" | "unset" | "disabled";
}
export interface PricerValueDisplayProps {
  name: string | React.JSX.Element;
  value: string | number | React.JSX.Element;
  link?: boolean;
  info?: InfoType;
  drawerKey?: string;
}

const getColorFromInfo = (info: InfoType | undefined, colorMode: ColorMode) => {
  switch (info?.type) {
    case "changed":
      return colors.safetyOrange["500"];
    case "outdated":
      return colors.magenta["500"];
    case "disabled":
      return colorMode === "light" ? colors.balticGray["600"] : colors.balticGray["300"];
    default:
      return "inherit";
  }
};

const getIconFromInfo = (info: InfoType | undefined) => {
  switch (info?.type) {
    case "outdated":
    case "disabled":
      return IoInformationCircleOutline;
    default:
      return IoWarningOutline;
  }
};

const PricerValueDisplay = ({ name, value, link = false, info, drawerKey }: PricerValueDisplayProps) => {
  const {
    action: { togglePricerSettingsDrawer, setActiveSettingsDrawerKey },
  } = usePricerModule();
  const { colorMode } = useColorMode();

  const onItemClick = (itemName: string) => {
    setActiveSettingsDrawerKey(itemName);
    togglePricerSettingsDrawer();
  };

  const color = getColorFromInfo(info, colorMode);
  const icon = getIconFromInfo(info);

  const isDisabled = info?.type === "disabled";
  return (
    <ValueDisplay
      name={name}
      value={
        (link || isDisabled) && typeof name === "string" ? (
          <Box position="relative" display={"flex"}>
            {info?.show && (
              <Box>
                <PopoverMenu
                  triggerElement={<Icon as={icon} color={color} fontSize="large" cursor="pointer" />}
                  placement="bottom"
                  popoverContent={{
                    showArrow: true,
                    maxWidth: "250",
                    body: (
                      <VStack px="2" alignItems="flex-start">
                        {info?.msg1 && <Box>{info?.msg1}</Box>}
                        {info?.msg2 && <Box>{info?.msg2}</Box>}
                      </VStack>
                    ),
                  }}
                  trigger="hover"
                />
              </Box>
            )}
            <Text
              _hover={{ cursor: isDisabled ? "default" : "pointer" }}
              color={color}
              textDecoration={isDisabled ? "unset" : "underline"}
              variant="default"
              textAlign="right"
              flexShrink={0}
              maxW={"85px"}
              onClick={() => {
                if (isDisabled) return;
                onItemClick(drawerKey ? drawerKey : name);
              }}
            >
              {value}
            </Text>
          </Box>
        ) : (
          value
        )
      }
    />
  );
};

export default PricerValueDisplay;
