import * as React from "react";
import { useGetBondIndicativesSWR } from "@/utils/swr-hooks";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { getCDUDateKeyFromSubType, getReplineLevel, parseDateToYYYYMMDD } from "@/utils/helpers";
import { RequestMetadata } from "@/types";

type BondIndicativesValueProps = {
  bond_name: string;
  location: string;
  formatter?: (s: string) => string;
  noCache?: boolean;
  // [INFO] - run_id is not required for bondIndicatives request made from this component
  requestMetadata: Omit<RequestMetadata, "run_id">;
};

const BondIndicativesValue: React.FC<BondIndicativesValueProps> = ({
  bond_name,
  location,
  formatter,
  noCache,
  requestMetadata,
}: BondIndicativesValueProps) => {
  const {
    state: { security_info, userSettings },
  } = usePricerModule();
  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);
  const { data, error } = useGetBondIndicativesSWR(
    {
      app: requestMetadata.app,
      bond_name,
      cello_cdu_date: parseDateToYYYYMMDD(userSettings[cduDateKey]),
      model_version: userSettings.model_version,
      repline_algorithm: getReplineLevel(security_info?.sub_type, userSettings.repline_level),
      ignore_cdu_paydate_after: parseDateToYYYYMMDD(userSettings.ignore_cdu_paydate_after),
      output_bond_structure: true,
      output_bond_summary: true,
      output_bond_replines: false,
      no_cache: noCache || !userSettings.useCache,
    },
    { lastRun: `Default` }
  );

  let output = "";

  if (!error && data) {
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      output = location.split(".").reduce((o, i) => o && o[i], data as any);
    } finally {
      // Skip
    }
  }

  if (output && formatter) {
    output = formatter(output);
  }

  return <span>{output || "-"}</span>;
};

export default BondIndicativesValue;
