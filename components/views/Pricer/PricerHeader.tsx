import * as React from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Box, Flex, HStack, Text, useBreakpointValue, useColorModeValue } from "@chakra-ui/react";
import { useSWRConfig } from "swr";
import CAHeading from "@/design-system/atoms/CAHeading";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { blurActiveElement } from "@/utils/helpers";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { RunInfo, RunTag, RunTagButtonGroup } from "@/design-system/molecules/RunTag/RunTag";
import { runToastManager } from "@/utils/run-toast-manager";
import RunAlertButton from "@/components/helpers/RunAlertButton";
import HeaderActionsMenu from "@/components/helpers/HeaderActionsMenu";
import { swrKeyActivityService } from "@/utils/swr-hooks/ActivityStream";
import { useScrollDirection } from "@/hooks/useScrollDirection";

type PricerHeader = {
  title: string;
  bond_name?: string;
  needsBond?: boolean;
  onRunClick?: (opts: { no_cache: boolean }) => void;
  stopWatch?: React.JSX.Element;
  progressIndicator?: React.JSX.Element;
  stopRunning?: React.JSX.Element;
  runInfo?: RunInfo;
  withRun?: boolean;
  runActionComponent?: React.JSX.Element | null;
  hasColorSupport?: boolean;
  rightSideContent?: React.JSX.Element;
  middleElement?: React.ReactNode;
};

const PricerHeader: React.FC<PricerHeader> = ({
  title,
  bond_name,
  needsBond = true,
  onRunClick,
  stopWatch,
  progressIndicator,
  stopRunning,
  runInfo,
  withRun = true,
  runActionComponent,
  hasColorSupport,
  rightSideContent,
  middleElement,
}: PricerHeader) => {
  const runDisabled = needsBond && !bond_name;
  const scrollDir = useScrollDirection();
  const {
    state: { isTimerRunning, userSettings, security_info },
  } = usePricerModule();
  const { mutate: globalMutate } = useSWRConfig();
  const bg = useColorModeValue("celloBlue.75", "celloBlue.1200");
  const isMobile = useBreakpointValue({ sm: true, md: false });

  const onRun = React.useCallback(() => {
    runToastManager.removeToasts();
    if (!runDisabled) {
      blurActiveElement();
      onRunClick?.({ no_cache: !userSettings.useCache });
      // Revalidate search on activity stream, in order to show the icon on Pricer Header immediately after the update
      setTimeout(() => {
        globalMutate(swrKeyActivityService.getActivitiesByBond(bond_name));
      }, 1000);
    }
  }, [bond_name, globalMutate, onRunClick, runDisabled, userSettings.useCache]);

  useHotkeys("Ctrl+Enter, Command+Enter", onRun, {
    enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
  });

  useHotkeys(
    "Ctrl+Shift+Enter, Command+Shift+Enter",
    () => {
      if (!runDisabled) {
        blurActiveElement();
        runToastManager.removeToasts();
        onRunClick?.({ no_cache: true });
      }
    },
    {
      enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
    }
  );

  const isProgressIndicatorVisible = isTimerRunning && progressIndicator;
  const isScrollingUp = scrollDir === "up";
  return (
    <>
      <Flex
        px={{ base: "4", sm: "5", md: "7" }}
        py={2}
        mb={-2}
        alignItems={{ base: "flex-start", md: "center" }}
        justifyContent="space-between"
        minH="1.75rem"
        bg={bg}
        position="sticky"
        top={{ base: !withRun ? 0 : isScrollingUp ? "116px" : "0", lg: "64px" }}
        zIndex={1}
        flexWrap="wrap"
        gap={2}
      >
        <Box minW="8rem">
          <CAHeading as="h1" lineHeight="2rem">
            {title}
          </CAHeading>
        </Box>

        <Flex
          ml="auto"
          justifyContent={{ base: "center", lg: "unset" }}
          alignSelf={{
            base: "center",
            lg: "unset",
          }}
        >
          <HStack>
            {bond_name ? (
              <>
                <Text variant="primary" fontSize="md" fontWeight="bold" whiteSpace="nowrap">
                  {bond_name}
                </Text>
                <HeaderActionsMenu
                  security_info={security_info}
                  bond_name={bond_name}
                  hasColorSupport={!!hasColorSupport}
                />
              </>
            ) : middleElement ? (
              middleElement
            ) : null}
          </HStack>
        </Flex>

        <Flex
          my="auto"
          ml="auto"
          alignItems={{ base: "flex-end", md: "center" }}
          justifyContent="flex-end"
          flexDirection={{ base: "row-reverse", md: "row" }}
          gap={runInfo?.id ? 2 : { base: 0, md: 4 }}
          display={{ base: "none", md: "flex" }}
        >
          {!isMobile && (
            <HStack display={isProgressIndicatorVisible ? "inline" : "none"} w="full">
              {progressIndicator}
            </HStack>
          )}
          {rightSideContent}
          <Box display={isProgressIndicatorVisible ? "none" : "block"}>
            <RunTagButtonGroup>
              <RunAlertButton />
              {(runInfo?.id || runActionComponent) && <RunTag runInfo={runInfo} />}
              {runActionComponent}
            </RunTagButtonGroup>
          </Box>
          {withRun && !isMobile ? (
            <Flex alignItems="center" gap={4}>
              {stopWatch}
              {isTimerRunning && stopRunning ? (
                stopRunning
              ) : (
                <CARun onClick={onRun} disabled={runDisabled || isTimerRunning} title={S.MODULES.PRICER.RUN} />
              )}
            </Flex>
          ) : (
            stopWatch
          )}
        </Flex>
        {isMobile && (
          <HStack display={isProgressIndicatorVisible ? "inline" : "none"} w="full">
            {progressIndicator}
          </HStack>
        )}
      </Flex>
      {withRun && isMobile && (
        <Flex alignItems="center" gap={4}>
          {isTimerRunning && stopRunning ? (
            stopRunning
          ) : (
            <CARun onClick={onRun} disabled={runDisabled || isTimerRunning} title={S.MODULES.PRICER.RUN} />
          )}
        </Flex>
      )}
    </>
  );
};

export default PricerHeader;
