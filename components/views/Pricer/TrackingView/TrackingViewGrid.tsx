import * as React from "react";
import { Box } from "@chakra-ui/layout";
import { CustomRequestOptions, useGetBondPrepayTrackingSWR } from "@/utils/swr-hooks";
import { BondPrepayTrackingRequest } from "@/types/swr";
import CAGrid from "@/design-system/molecules/CAGrid";
import trackingColumnData from "@/utils/grid/TrackingColumnData";
import { CA_Mastr_Api_v1_0_Models_BondPrepayTracking_Detail, CA_Mastr_Models_v1_0_Models_Stage } from "@/utils/openapi";

const TrackingViewGrid: React.FC<{
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
  headerActionButtons: React.JSX.Element;
}> = ({
  bondPrepayTrackingRequest,
  requestOpts: { lastRun, isOldRun },
  headerActionButtons,
}: {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
  headerActionButtons: React.JSX.Element;
}) => {
  const {
    data: responseData,
    isLoading,
    error,
  } = useGetBondPrepayTrackingSWR(bondPrepayTrackingRequest, {
    lastRun,
    isOldRun,
  });

  const { details } = responseData || {};

  return (
    <Box h="full" minH="calc(100vh - 12.3rem)">
      <CAGrid<CA_Mastr_Api_v1_0_Models_BondPrepayTracking_Detail>
        hasRun={!!lastRun}
        gridDataType="historical"
        error={error}
        gridProps={{
          columnDefs: trackingColumnData,
          rowData: details,
          pivotMode: false,
          enableCharts: false,
          cellSelection: false,
          loading: isLoading || responseData?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING,
        }}
        gridType="tracking"
        initialMessage="Click on ▷ to load Tracking."
        showExpand
        headerActionButtons={headerActionButtons}
        cardProps={{
          title: " ",
          cardKey: "tracking-view-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
        }}
        stackStyles={{
          h: "calc(100vh - 12rem)",
        }}
      />
    </Box>
  );
};

export default TrackingViewGrid;
