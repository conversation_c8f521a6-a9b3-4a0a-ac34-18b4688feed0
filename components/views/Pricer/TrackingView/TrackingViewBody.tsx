import { Box } from "@chakra-ui/react";
import * as React from "react";
import { usePricerTrackingPage } from "@/contexts/PageContexts/PricerTrackingPageContext";
import CACard from "@/design-system/molecules/CACard";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import { BondPrepayTrackingRequest } from "@/types/swr";
import { CustomRequestOptions } from "@/utils/swr-hooks";
import GridChartToggle, { GridView, GridViewValuesType } from "@/components/helpers/GridChartToggle";
import TrackingViewGrid from "./TrackingViewGrid";
import TrackingViewChart from "./TrackingViewChart";

interface TrackingViewBodyProps {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
}

const TrackingViewBody: React.FC<TrackingViewBodyProps> = ({
  bondPrepayTrackingRequest,
  requestOpts,
}: TrackingViewBodyProps) => {
  const {
    state: { lastRun },
  } = usePricerTrackingPage();
  const [gridChartToggle, setGridChartToggle] = React.useState<GridViewValuesType>(GridView.GRID);

  const HeaderActionButtons = () => (
    <GridChartToggle selectedView={gridChartToggle} setSelectedView={setGridChartToggle} />
  );

  return !lastRun ? (
    <CACard flexGrow={1} display="flex" justifyContent="center" alignItems="center" minH="calc(100vh - 12rem)">
      <CAInfo status="info" description={"Click on ▷ to load Tracking."} />
    </CACard>
  ) : (
    <>
      <Box
        h={gridChartToggle === GridView.GRID ? "full" : "0"}
        visibility={gridChartToggle === GridView.GRID ? "visible" : "hidden"}
        display={gridChartToggle === GridView.GRID ? "block" : "none"}
      >
        <TrackingViewGrid
          requestOpts={requestOpts}
          bondPrepayTrackingRequest={bondPrepayTrackingRequest}
          headerActionButtons={<HeaderActionButtons />}
        />
      </Box>
      <Box
        h={gridChartToggle === GridView.CHART ? "fit-content" : "0"}
        visibility={gridChartToggle === GridView.CHART ? "visible" : "hidden"}
        display={gridChartToggle === GridView.CHART ? "block" : "none"}
      >
        <TrackingViewChart
          requestOpts={requestOpts}
          bondPrepayTrackingRequest={bondPrepayTrackingRequest}
          headerActionButtons={<HeaderActionButtons />}
        />
      </Box>
    </>
  );
};
export default TrackingViewBody;
