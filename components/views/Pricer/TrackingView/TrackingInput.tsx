import { Box, VStack } from "@chakra-ui/react";
import * as React from "react";
import { UseFormHandleSubmit, useForm } from "react-hook-form";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerTrackingPage } from "@/contexts/PageContexts/PricerTrackingPageContext";
import { trackingPageSettingsType } from "@/contexts/PageContexts/PricerTrackingPageContext/PricerTrackingPageContextTypes";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import { getFormattedLocaleDate, getInfoMsg } from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "@/utils/openapi";
import useResetFormData from "@/hooks/useResetFormData";
import useOnPrepayModelChange from "@/hooks/useOnPrepayModelChange";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import { DateSettingsOverrides } from "@/components/helpers/DateSettingsOverrides/DateSettingsOverrides";
import ModelDialSelectorWrapper from "../shared/ModelDialSelectorWrapper";
import PrepayVectorSelector from "../shared/PrepayVectorSelector";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import ReplineDialsSwitch from "../shared/ReplineDialsSwitch";

export interface TrackingInputHandler {
  handleSubmit: UseFormHandleSubmit<trackingPageSettingsType>;
}

const TrackingInput: React.ForwardRefRenderFunction<TrackingInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, security_info },
  } = usePricerModule();
  const {
    state: { trackingPageSettingsCopy, trackingPageSettings, isOldRun, lastRun },
  } = usePricerTrackingPage();

  const {
    handleSubmit,
    register,
    watch,
    reset,
    setValue,
    formState: { touchedFields },
  } = useForm<trackingPageSettingsType>();
  const [watch_prepay_model_type, watch_prepay_percentage, watch_max_tracking_periods] = watch([
    "prepay_model_type",
    "prepay_percentage",
    "max_tracking_periods",
  ]);
  useResetFormData({ reset, formData: trackingPageSettings });
  useOnPrepayModelChange({
    prepayModelType: watch_prepay_model_type,
    prepayPercentage: watch_prepay_percentage,
    setValue,
    isTouched: !!touchedFields.prepay_model_type,
    isOldRun,
  });

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="tracking-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
      overflow="visible"
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.curve_date)}
            _key="curve_date"
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
        </Box>
        <Box>
          <PricerValueDisplayWrapper
            name="Prepay Model"
            value={
              metadata?.pricer_settings?.prepay_models?.find((l) => l.value === trackingPageSettings.prepay_model_type)
                ?.display_value ?? ""
            }
            _key="prepay_model_type"
          />
          <CAInput
            type="number"
            label={"Multiplier (%)"}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_percentage, trackingPageSettingsCopy.prepay_percentage)}
            {...register("prepay_percentage", {
              valueAsNumber: true,
              required: true,
            })}
          />
          {watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR && (
            <PrepayVectorSelector lastRun={lastRun} />
          )}
          {(watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO ||
            watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR) && (
            <ModelDialSelectorWrapper lastRun={lastRun} />
          )}
          <ReplineDialsSwitch />
          <PricerValueDisplayWrapper
            name="Prepay Model Version"
            link
            value={userSettings.model_version ?? ""}
            _key="model_version"
          />
          <PricerValueDisplayWrapper
            name="Current Coupon Model"
            link
            value={
              metadata?.pricer_settings?.current_coupon_model?.find(
                (l) => l.value === userSettings.current_coupon_model
              )?.display_value ?? ""
            }
            _key="current_coupon_model"
          />
          <PricerValueDisplayWrapper name="Interest Rate Paths" value="1" _key="interestRatePaths" />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
        </Box>

        <Box>
          <CAHeading as="h3" mb={4}>
            Models
          </CAHeading>
          <CAInput
            type="number"
            inputType="positive-integer"
            label={"Periods"}
            width={"6rem"}
            info={getInfoMsg(watch_max_tracking_periods, trackingPageSettingsCopy.max_tracking_periods)}
            {...register("max_tracking_periods", {
              valueAsNumber: true,
              required: true,
            })}
          />
        </Box>
        <DateSettingsOverrides />
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(TrackingInput);
