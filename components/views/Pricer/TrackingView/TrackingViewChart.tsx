import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { SimpleGrid, VStack } from "@chakra-ui/layout";
import { Box, HStack, useBreakpointValue } from "@chakra-ui/react";
import { CustomRequestOptions, useGetBondPrepayTrackingSWR } from "@/utils/swr-hooks";
import { BondPrepayTrackingRequest } from "@/types/swr";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import CAAdvancedChart, { CAAdvancedChartOptions } from "@/design-system/molecules/CAAdvancedChart";
import { getChartTooltipFormatter, getDifference, getDividedValue, getFormattedNumberFixed } from "@/utils/helpers";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { CA_Mastr_Models_v1_0_Models_Stage } from "@/utils/openapi";

type PrepayTrackingProps = {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
  placeholder?: boolean;
  headerActionButtons?: React.JSX.Element;
};

const TrackingViewChart: React.FC<PrepayTrackingProps> = ({
  bondPrepayTrackingRequest,
  requestOpts: { lastRun, isOldRun },
  headerActionButtons,
}: PrepayTrackingProps) => {
  const { data, isLoading, error } = useGetBondPrepayTrackingSWR(bondPrepayTrackingRequest, { lastRun, isOldRun });
  const [rate, setRate] = React.useState<"cpr" | "crr" | "cdr">("cpr");

  const aspectRatio = useBreakpointValue({ base: 1.5, md: 1.5, lg: 2, xl: 2, "2xl": 2.5, "3xl": 3 });

  let tableData: { name: string; values: string[] }[] = [];
  const chartOptions: CAAdvancedChartOptions = {
    xkey: "month",
    dataKeys: [
      { key: "actual", name: "Actual", type: "bar" },
      { key: "proj_cpr", name: "Projection", type: "bar" },
      { key: "diff_act_proj", name: "Act-Proj", type: "bar" },
      { key: "div_act_proj", name: "Act/Proj", type: "line" },
    ],
    margin: {
      top: useBreakpointValue({ base: 30, md: 15 }),
      left: useBreakpointValue({ base: -35, md: 0 }),
    },
    data: [{}],
    aspectRatio,
    formatTooltip: getChartTooltipFormatter(1),
    primaryAxisToFixed: 0,
    secondaryAxisToFixed: 2,
  };
  if (data) {
    const d = data as { [key: string]: number };
    tableData = [
      {
        name: "1m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_1m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_1m`]),
          getDividedValue(d[`act_${rate}_1m`], d[`proj_${rate}_1m`], 2),
          getDifference(d[`act_${rate}_1m`], d[`proj_${rate}_1m`], 1),
        ],
      },
      {
        name: "3m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_3m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_3m`]),
          getDividedValue(d[`act_${rate}_3m`], d[`proj_${rate}_3m`], 2),
          getDifference(d[`act_${rate}_3m`], d[`proj_${rate}_3m`], 1),
        ],
      },
      {
        name: "6m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_6m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_6m`]),
          getDividedValue(d[`act_${rate}_6m`], d[`proj_${rate}_6m`], 2),
          getDifference(d[`act_${rate}_6m`], d[`proj_${rate}_6m`], 1),
        ],
      },
      {
        name: "12m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_12m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_12m`]),
          getDividedValue(d[`act_${rate}_12m`], d[`proj_${rate}_12m`], 2),
          getDifference(d[`act_${rate}_12m`], d[`proj_${rate}_12m`], 1),
        ],
      },
      {
        name: "24m",
        values: [
          getFormattedNumberFixed(1)(d[`act_${rate}_24m`]),
          getFormattedNumberFixed(1)(d[`proj_${rate}_24m`]),
          getDividedValue(d[`act_${rate}_24m`], d[`proj_${rate}_24m`], 2),
          getDifference(d[`act_${rate}_24m`], d[`proj_${rate}_24m`], 1),
        ],
      },
    ];

    chartOptions.data = tableData.map((td) => ({
      month: td.name,
      actual: +td.values[0],
      proj_cpr: +td.values[1],
      div_act_proj: +td.values[2],
      diff_act_proj: +td.values[3],
    }));
  }

  return (
    <CACard
      title=" "
      cardKey="tracking-view-chart"
      cardStyleOnOpen={{
        height: "full",
      }}
      allowCollapse
    >
      <Box>
        <VStack spacing={3} alignItems="stretch">
          <HStack alignSelf="flex-end" spacing={5}>
            <ToggleButtonGroup
              buttons={[
                { label: "CPR", value: "cpr" },
                { label: "CRR", value: "crr" },
                { label: "CDR", value: "cdr" },
              ]}
              selectedButton={rate}
              onChange={setRate}
            />
            {headerActionButtons}
          </HStack>
          {(isLoading || data?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING) && !error ? (
            <Skeleton height="25px" />
          ) : (
            <SimpleGrid columns={{ base: 1, md: 2 }}>
              <CATable
                headers={["", "Actual (%)", "Model (%)", "Act/Proj", "Act-Proj"]}
                variant="caFullCentered"
                data={tableData}
              />
              <CAAdvancedChart options={chartOptions} />
            </SimpleGrid>
          )}
        </VStack>
      </Box>
    </CACard>
  );
};

const PrepayTrackingWrapper: React.FC<PrepayTrackingProps> = ({
  bondPrepayTrackingRequest,
  requestOpts,
  placeholder,
  headerActionButtons,
}: PrepayTrackingProps) => {
  if (placeholder) {
    return <CACard title="Prepay Tracking" />;
  } else {
    return (
      <TrackingViewChart
        bondPrepayTrackingRequest={bondPrepayTrackingRequest}
        requestOpts={requestOpts}
        headerActionButtons={headerActionButtons}
      />
    );
  }
};

export default React.memo(PrepayTrackingWrapper);
