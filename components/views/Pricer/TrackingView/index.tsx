import * as React from "react";
import { Box } from "@chakra-ui/react";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { usePricerTrackingPage } from "@/contexts/PageContexts/PricerTrackingPageContext";
import { getRequests } from "@/utils/helpers/pricer/tracking";
import { BondPrepayTrackingStopWatchWrapper } from "../PricerStopWatchWrapper";
import PricerHeader from "../PricerHeader";
import { PricerModuleProps } from "..";
import { BondPrepayTrackingProgressIndicatorWrapper } from "../PricerProgressIndicatorWrapper";
import { BondPrepayTrackingStopRunningWrapper } from "../PricerStopRunningWrapper";
import TrackingViewBody from "./TrackingViewBody";
import TrackingInput, { TrackingInputHandler } from "./TrackingInput";

const PricerTrackingView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, lastRun, isOldRun, api_start_time, api_end_time, ...trackingState },
    action: { updateTrackingPageSettings, run },
  } = usePricerTrackingPage();
  const { pushWithoutRendering } = useQueryParameters();

  const trackingInputRef = React.useRef<TrackingInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    trackingInputRef.current?.handleSubmit((data) => {
      updateTrackingPageSettings(data);
      updatePricerUserSettingsCopy(userSettings);
      setIsTimerRunning(true);
      const run_id = run({ no_cache });
      pushWithoutRendering({ run_id });
    })();
  };

  const { bondPrepayTrackingRequest, requestOpts } = getRequests({
    userSettings,
    lastRunId,
    lastRun,
    isOldRun,
    bond_name,
    security_info,
    ...trackingState,
  });

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <BondPrepayTrackingStopWatchWrapper
            bondPrepayTrackingRequest={bondPrepayTrackingRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BondPrepayTrackingProgressIndicatorWrapper
            bondPrepayTrackingRequest={bondPrepayTrackingRequest}
            requestOpts={requestOpts}
          />
        }
        stopRunning={
          <BondPrepayTrackingStopRunningWrapper
            bondPrepayTrackingRequest={bondPrepayTrackingRequest}
            requestOpts={requestOpts}
          />
        }
      />
      <MainInputContentTemplate inputs={<TrackingInput ref={trackingInputRef} />}>
        <TrackingViewBody bondPrepayTrackingRequest={bondPrepayTrackingRequest} requestOpts={requestOpts} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerTrackingView;
