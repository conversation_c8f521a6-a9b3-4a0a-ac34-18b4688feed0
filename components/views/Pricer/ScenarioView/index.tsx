import * as React from "react";
import { Box } from "@chakra-ui/react";
import { showErrorToast, showWarningToast } from "@/design-system/theme/toast";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerScenarioPage } from "@/contexts/PageContexts/PricerScenarioPageContext";
import { DEC_REGEX, POSITIVE_DEC_REGEX, TICK_REGEX, findDuplicates, tickToDecimal } from "@/utils/helpers";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { getRequests } from "@/utils/helpers/pricer/scenario";
import { PricerModuleProps } from "..";
import PricerHeader from "../PricerHeader";
import { BatchRequestStopWatch } from "../PricerStopWatchWrapper";
import { BatchRequestProgressIndicator } from "../PricerProgressIndicatorWrapper";
import { BatchRequestStopRunningWrapper } from "../PricerStopRunningWrapper";
import ScenarioInput, { ScenarioInputHandler } from "./ScenarioInput";
import ScenarioViewBody from "./ScenarioViewBody";

const PricerScenarioView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, curve_shifts, lastRun, isOldRun, api_start_time, api_end_time, ...restScenarioState },
    action: { updateScenarioPageSettings, run, resetLastRun },
  } = usePricerScenarioPage();
  const { pushWithoutRendering } = useQueryParameters();

  const scenarioInputRef = React.useRef<ScenarioInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    if (curve_shifts.length === 0) {
      showErrorToast("Error", "Please add a curve shift.");
      return;
    }

    const hasDuplicate = findDuplicates(curve_shifts)?.length;
    if (hasDuplicate) {
      showWarningToast("Duplicate entry", "Please ensure no duplicates are entered");
      resetLastRun();
      return;
    }

    const { no_cache } = opts;
    scenarioInputRef.current?.handleSubmit(
      (data) => {
        if (data.pricing_level && data.pricing_type !== "price") {
          data.pricing_level = `${data.pricing_level}`.match(DEC_REGEX)
            ? Number(data.pricing_level)
            : tickToDecimal(`${data.pricing_level}`);
        }

        if (
          data.pricing_type === "price" &&
          !(`${data.pricing_level}`.match(TICK_REGEX) || `${data.pricing_level}`.match(POSITIVE_DEC_REGEX))
        ) {
          showErrorToast("Validation", "Please enter a valid positive pricing value.");
          return;
        }

        updateScenarioPageSettings(data);
        updatePricerUserSettingsCopy(userSettings);
        const run_id = run({ no_cache });
        setIsTimerRunning(true);
        pushWithoutRendering({ run_id });
      },
      (errors) => {
        if (errors?.pricing_level?.type === "validate") {
          showErrorToast("Validation", "Please enter a valid pricing value.");
        }
      }
    )();
  };

  const { batchRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRun,
    lastRunId,
    isOldRun,
    bond_name,
    curve_shifts,
    ...restScenarioState,
  });

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <BatchRequestStopWatch
            request={batchRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BatchRequestProgressIndicator
            onSuccess={() => scenarioInputRef.current?.setFocus("pricing_level")}
            request={batchRequest}
            requestOpts={requestOpts}
          />
        }
        stopRunning={
          <BatchRequestStopRunningWrapper
            request={batchRequest}
            requestOpts={requestOpts}
            resetLastRun={resetLastRun}
          />
        }
      />
      <MainInputContentTemplate inputs={<ScenarioInput ref={scenarioInputRef} />}>
        {batchRequest && <ScenarioViewBody bondPricingRequest={batchRequest} requestOpts={requestOpts} />}
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerScenarioView;
