import { ButtonGroup } from "@chakra-ui/button";
import { Box, Button, Icon, Text, Tooltip } from "@chakra-ui/react";
import {
  CellValueChangedEvent,
  ColumnRowGroupChangedEvent,
  GridApi,
  GridReadyEvent,
  ICellRendererParams,
  RowSelectionOptions,
  SelectionChangedEvent,
} from "ag-grid-community";
import * as React from "react";
import { IoRemoveCircleSharp } from "react-icons/io5";
import {
  CA_Mastr_Api_v1_0_Models_Batch_BatchRequest,
  CA_Mastr_Api_v1_0_Models_BondPricing_Result,
  CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView,
} from "@/utils/openapi";
import { DEFAULT_CURVE_SHIFTS, usePricerScenarioPage } from "@/contexts/PageContexts/PricerScenarioPageContext";
import scenarioColumnData from "@/utils/grid/ScenarioColumnData";
import { CAGridViewState } from "@/design-system/molecules/CAGrid/types";
import { CustomRequestOptions, useRunIdBatchRequestSWR } from "@/utils/swr-hooks";
import CAInput from "@/design-system/molecules/CAInput";
import CAGrid from "@/design-system/molecules/CAGrid";
import { inMemoryObjectStorage } from "@/utils/local-storage";
import { IN_MEMORY_OBJECT_STORAGE_KEY } from "@/constants/storage-keys";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { CURVE_SHIFT_OPTIONS } from "@/constants/enums";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";

interface ScenarioViewBodyProps {
  bondPricingRequest: CA_Mastr_Api_v1_0_Models_Batch_BatchRequest;
  requestOpts: CustomRequestOptions;
}

const rowSelection: RowSelectionOptions | "single" | "multiple" = {
  mode: "multiRow",
  checkboxes: true,
  groupSelects: "descendants",
  copySelectedRows: true,
  headerCheckbox: true,
  isRowSelectable: (rowNode) => rowNode.data.rateshift !== undefined,
};

const EditableCellRendrer = (params: ICellRendererParams) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings },
  } = usePricerModule();
  const formattedValue = params.value === "" ? "" : params.valueFormatted; //Prevent 0 from displaying on Input after focus
  const value = formattedValue || (typeof params.value === "object" ? params.value?.value : params.value);
  const startEditing = () => {
    params.api.setFocusedCell(params.node.rowIndex ?? 0, params.column?.getColId() ?? "", undefined);
    params.api.startEditingCell({
      rowIndex: params.node.rowIndex ?? 0,
      colKey: params.column?.getColId() ?? "",
    });
  };
  return (
    <Box
      position="relative"
      bottom="0"
      display="flex"
      justifyContent="flex-end"
      alignItems="center"
      h="2rem"
      role="group"
      w="8rem"
    >
      {params.api?.getRowGroupColumns().length === 0 ? (
        <Tooltip
          label={`Available options for Numerix: ${CURVE_SHIFT_OPTIONS.join(", ")}`}
          aria-label={`Available options for Numerix: ${CURVE_SHIFT_OPTIONS.join(", ")}`}
          isDisabled={
            metadata?.pricer_settings?.type?.find((l) => l.value === userSettings.type)?.display_value !== "Numerix"
          }
        >
          <CAInput
            type="number"
            inputType="digit"
            textAlign="center"
            clickOnly={true}
            onClick={startEditing}
            value={value}
            name="rateshift"
          />
        </Tooltip>
      ) : (
        <Text textAlign="center">{value}</Text>
      )}
    </Box>
  );
};

const components = {
  editableCellRendrer: EditableCellRendrer,
};

const ScenarioViewBody: React.FC<ScenarioViewBodyProps> = ({
  bondPricingRequest,
  requestOpts: { lastRun, isOldRun },
}: ScenarioViewBodyProps) => {
  const {
    state: { curve_shifts, curve_shifts_copy },
    action: { setCurveShifts, setCurveShiftsCopy, resetCurveShifts },
  } = usePricerScenarioPage();

  const [hasSelectedRows, setHasSelectedRows] = React.useState(false);
  const [gridApi, setGridApi] = React.useState<GridApi>();
  const {
    data,
    isLoading: isBatchRequestUnderProgress,
    error,
  } = useRunIdBatchRequestSWR(bondPricingRequest, { lastRun, isOldRun });
  const isLoading = isBatchRequestUnderProgress && data?.pricing_responses?.length === 0;

  const allData = React.useMemo(() => {
    if (isLoading) return undefined;

    return data?.pricing_responses?.map((response) => {
      // grab the index value from ui_request_id to map results to the correct table cell
      const rateShift = Number((response.ui_request_id as string)?.split(":").at(-1));
      return { ...response?.results?.[0], curve_shift: rateShift };
    });
  }, [data, isLoading]);

  const initRowsInGrid = React.useCallback(
    (
      api: GridApi | undefined,
      rowData: ((CA_Mastr_Api_v1_0_Models_BondPricing_Result & { curve_shift: number | string }) | null | undefined)[]
    ) => {
      if (!api) return;

      const modifiedRowData: CA_Mastr_Api_v1_0_Models_BondPricing_Result[] = [];
      for (let i = 0; i < curve_shifts?.length; i++) {
        if (curve_shifts[i] === curve_shifts_copy?.[i]) {
          const existingRow = rowData?.find((d) => d?.curve_shift === curve_shifts[i]);
          if (existingRow) {
            Object.assign(existingRow, { rateshift: curve_shifts[i] });
            modifiedRowData.push(existingRow);
          } else {
            modifiedRowData.push({ rateshift: curve_shifts[i] } as CA_Mastr_Api_v1_0_Models_BondPricing_Result);
          }
        } else {
          modifiedRowData.push({ rateshift: curve_shifts[i] } as CA_Mastr_Api_v1_0_Models_BondPricing_Result);
        }
      }

      const gridRowCount = api?.getDisplayedRowCount() ? api?.getDisplayedRowCount() - 1 : 0;
      const dataCount = modifiedRowData.length;
      if (gridRowCount > dataCount) {
        api.setGridOption("rowData", modifiedRowData.concat([{}]));
      }

      //Check if there is a row in the grid
      if (!api?.getRowNode("0")) {
        //If nothing rendered in the grid, add the default rows
        api.setGridOption("rowData", modifiedRowData.concat([{}]));
      } else {
        api.setGridOption("rowData", modifiedRowData);

        const lastRowIndex = api.getLastDisplayedRowIndex();
        const lastRow = api.getDisplayedRowAtIndex(lastRowIndex);
        if (lastRow?.data?.rateshift?.toString()) {
          api?.applyTransaction({
            add: [{}],
          });
        }
      }
    },
    [curve_shifts, curve_shifts_copy]
  );

  React.useEffect(() => {
    if (gridApi && allData) {
      initRowsInGrid(gridApi, allData);
    } else if (!isLoading && !allData && !lastRun) {
      initRowsInGrid(gridApi, []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allData, gridApi, lastRun]);

  React.useEffect(() => {
    if (lastRun) {
      setHasSelectedRows(false);
      if (gridApi) gridApi.stopEditing();
    }
  }, [lastRun, gridApi]);

  React.useEffect(() => {
    if (error) {
      initRowsInGrid(gridApi, []);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [error, gridApi]);

  const headerActionButtons = React.useMemo(() => {
    const onClickRemoveRateShift = () => {
      if (gridApi) {
        const newShiftRates: (number | null)[] = [];
        const rowsToRemoveIndexes: number[] = [];
        const newRateShifts_copy: (number | null)[] | undefined = curve_shifts_copy ? [] : undefined;

        const newRowData: CA_Mastr_Api_v1_0_Models_BondPricing_Result[] = [];
        gridApi.forEachNode((n) => {
          if (n.isSelected()) {
            rowsToRemoveIndexes.push(Number(n.id));
          } else {
            newRowData.push(n.data);
          }
        });

        for (let i = 0; i < curve_shifts.length; i++) {
          // if shift rate is in removal list
          if (rowsToRemoveIndexes.find((ri) => ri === i) === undefined) {
            newShiftRates.push(curve_shifts[i]);
            if (newRateShifts_copy && curve_shifts_copy) {
              newRateShifts_copy.push(curve_shifts_copy[i]);
            }
          }
        }
        setCurveShifts(newShiftRates);
        if (newRateShifts_copy) setCurveShiftsCopy(newRateShifts_copy);
        setHasSelectedRows(false);
        gridApi.setGridOption("rowData", newRowData);
      }
    };

    return (
      <ButtonGroup size="xs" isAttached variant="outline">
        <Box mr={{ base: "0.5rem", sm: "2" }}>
          <Button
            isDisabled={!hasSelectedRows}
            height={8}
            leftIcon={<Icon as={IoRemoveCircleSharp} fontSize="20" />}
            variant="iconButton"
            onClick={onClickRemoveRateShift}
          >
            <Text color="inherit">Remove Rows</Text>
          </Button>
        </Box>
      </ButtonGroup>
    );
  }, [hasSelectedRows, gridApi, curve_shifts_copy, setCurveShifts, setCurveShiftsCopy, curve_shifts]);

  const getExistingRateShiftResults = (rateShift: number) => {
    return allData?.find((data) => +data.curve_shift === rateShift) ?? {};
  };

  const onCellValueChanged = (params: CellValueChangedEvent) => {
    const id = Number(params.node.id);
    const isLastRow = curve_shifts[id] === undefined;
    if (!isLastRow) {
      const newValue = Number(params.newValue);
      if (curve_shifts[id] !== newValue) {
        params.node.setData({ rateshift: newValue, ...getExistingRateShiftResults(newValue) });
        const newRateShifts = [...curve_shifts];
        newRateShifts[id] = newValue;
        setCurveShifts(newRateShifts);
      }
    } else {
      if (params.newValue === "" || params.newValue === undefined) {
        if (params.newValue !== params.oldValue) {
          params.node.setDataValue(params.column.getId(), undefined);
        }
      } else {
        const newValue = Number(params.newValue);

        const newRateShifts = [...curve_shifts];
        newRateShifts.push(newValue);
        setCurveShifts(newRateShifts);

        if (curve_shifts_copy) {
          const newRateShifts_copy = [...curve_shifts_copy];
          newRateShifts_copy.push(newValue);
          setCurveShiftsCopy(newRateShifts_copy);
        }
        params.api.applyTransaction({ add: [{}] });
      }
    }
    try {
      params.api.stopEditing();
    } catch (e) {
      // Prevent child not found errors when using tab key to move to next cell
    }
  };

  const handleGridViewChange = (
    selectedGridView: CA_Mastr_Api_v1_0_Models_UserGridView_UserGridView | undefined
    // reset: boolean
  ) => {
    const persistedState = inMemoryObjectStorage.getItem(
      `${IN_MEMORY_OBJECT_STORAGE_KEY.GRID_VIEW_STATE}_${selectedGridView?.user_grid_view_id}`
    );

    const gridViewState: CAGridViewState =
      persistedState ?? (selectedGridView?.grid_view_state && JSON.parse(selectedGridView?.grid_view_state));

    const rateShifts: (number | null)[] = gridViewState?.customState?.curve_shifts || DEFAULT_CURVE_SHIFTS;

    const modifiedRowData = rateShifts?.map((el) => ({
      rateshift: el,
      ...allData?.find((data) => data.curve_shift === el),
    }));

    setCurveShifts(rateShifts);
    setCurveShiftsCopy(rateShifts);

    if (gridApi) {
      gridApi?.setGridOption("rowData", modifiedRowData?.concat([{ rateshift: null }]));
    }
  };

  const handleSelectionChanged = React.useCallback(
    (event: SelectionChangedEvent) => {
      const _hasSelectedRows = event.api.getSelectedRows()?.length > 0;
      if (_hasSelectedRows !== hasSelectedRows) {
        setHasSelectedRows(_hasSelectedRows);
      }
    },
    [hasSelectedRows]
  );

  const handleColumnRowGroupChanged = React.useCallback((event: ColumnRowGroupChangedEvent) => {
    if (event.columns?.length === 0) {
      event.api.setColumnWidths([{ key: "rateshift", newWidth: 198 }], true);
    }
  }, []);

  const onGridReady = React.useCallback(
    (e: GridReadyEvent<CA_Mastr_Api_v1_0_Models_BondPricing_Result[]>) => {
      !lastRun && initRowsInGrid(e.api, allData || []);
      setGridApi(e.api);
    },
    [allData, initRowsInGrid, lastRun]
  );

  return (
    <Box h="600px" minH="calc(100vh - 12rem)">
      <CAGrid<CA_Mastr_Api_v1_0_Models_BondPricing_Result[]>
        hasRun={!!lastRun}
        gridType="scenario-0.1"
        gridDataType="futuristic"
        headerActionButtons={headerActionButtons}
        gridProps={{
          className: "Scenario",
          columnDefs: scenarioColumnData,
          onGridReady,
          components,
          onSelectionChanged: handleSelectionChanged,
          onCellValueChanged,
          rowSelection,
          singleClickEdit: true,
          onColumnRowGroupChanged: handleColumnRowGroupChanged,
          loading: isLoading || isBatchRequestUnderProgress,
          rowData: null, // Will be set automatically using setGridOption("rowData")
        }}
        customState={{ curve_shifts }}
        showExpand
        hideSearch
        onUpdateGridView={handleGridViewChange}
        onDeleteGridView={resetCurveShifts}
        cardProps={{
          title: " ",
          cardKey: "scenario-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
          overflow: "unset",
        }}
        stackStyles={{
          h: "calc(100vh - 15rem)",
          padding: "0 10px",
        }}
      />
    </Box>
  );
};

export default ScenarioViewBody;
