import { Box, VStack } from "@chakra-ui/react";
import * as React from "react";
import { Controller, UseFormHandleSubmit, UseFormSetFocus, useForm } from "react-hook-form";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerScenarioPage } from "@/contexts/PageContexts/PricerScenarioPageContext";
import { scenarioPageSettingsType } from "@/contexts/PageContexts/PricerScenarioPageContext/PricerScenarioPageContextTypes";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import CADateInput from "@/design-system/molecules/CADateInput";
import CAInput from "@/design-system/molecules/CAInput";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import {
  DEC_REGEX,
  TICK_REGEX,
  getCDUDateKeyFromSubType,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getFormattedYearMonth,
  getInfoMsg,
  tickToDecimal,
} from "@/utils/helpers";
import {
  CA_Mastr_Models_v1_0_Models_BondType,
  CA_Mastr_Models_v1_0_Models_PrepayMethod,
  CA_Mastr_Models_v1_0_Models_PricingType,
} from "@/utils/openapi";
import useResetFormData from "@/hooks/useResetFormData";
import useOnPrepayModelChange from "@/hooks/useOnPrepayModelChange";
import useUpdateSettleDate from "@/hooks/useUpdateSettleDate";
import useOnPricingLevelChange from "@/hooks/useOnPricingLevelChange";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import { DateSettingsOverrides } from "@/components/helpers/DateSettingsOverrides/DateSettingsOverrides";
import BondIndicativesValue from "../shared/BondIndicativesValue";
import ModelDialSelectorWrapper from "../shared/ModelDialSelectorWrapper";
import PrepayVectorSelector from "../shared/PrepayVectorSelector";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import ReplineDialsSwitch from "../shared/ReplineDialsSwitch";

export interface ScenarioInputHandler {
  handleSubmit: UseFormHandleSubmit<scenarioPageSettingsType>;
  setFocus: UseFormSetFocus<scenarioPageSettingsType>;
}

const ScenarioInput: React.ForwardRefRenderFunction<ScenarioInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings, bond_name, userSettingsCopy, security_info },
  } = usePricerModule();
  const {
    state: { app, page, noCache, scenarioPageSettingsCopy, scenarioPageSettings, isOldRun, lastRun },
  } = usePricerScenarioPage();

  const {
    handleSubmit,
    reset,
    register,
    watch,
    control,
    setValue,
    setFocus,
    formState: { touchedFields },
  } = useForm<scenarioPageSettingsType>();
  const [
    watch_pricing_type,
    watch_pricing_level,
    watch_prepay_percentage,
    watch_settle_date,
    watch_prepay_model_type,
    watch_modify_class,
  ] = watch(["pricing_type", "pricing_level", "prepay_percentage", "settle_date", "prepay_model_type", "modify_class"]);

  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
    setFocus,
  }));

  useResetFormData({ reset, formData: scenarioPageSettings });

  useOnPrepayModelChange({
    prepayModelType: watch_prepay_model_type,
    prepayPercentage: watch_prepay_percentage,
    setValue,
    isTouched: !!touchedFields.prepay_model_type,
    isOldRun,
  });

  useUpdateSettleDate<typeof scenarioPageSettings>({
    pageSettings: scenarioPageSettings,
    setSettleDate: (settleDate) => setValue("settle_date", settleDate),
    isOldRun,
    security_info,
    userSettings,
    userSettingsCopy,
    bond_name,
    app,
    page,
  });

  useOnPricingLevelChange<scenarioPageSettingsType>({
    currentPricingLevel: watch_pricing_level,
    setValue,
  });

  React.useEffect(() => {
    // FOcus on the pricing level input on a new bond load
    setTimeout(() => {
      setFocus("pricing_level");
    }, 500);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="scenario-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
      overflow="visible"
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <CAHeading as="h3" mb={3}>
            Pricing
          </CAHeading>
          <CASelectDropdown
            data-testid="pricing-type"
            label={"Type"}
            width={"6rem"}
            info={getInfoMsg(watch_pricing_type, scenarioPageSettingsCopy.pricing_type)}
            {...register("pricing_type", {
              required: true,
              onChange: (e) => {
                if (`${watch_pricing_level}`.match(TICK_REGEX) && e.target.value !== "price") {
                  setValue("pricing_level", undefined);
                }
              },
            })}
            options={[
              { id: 0, value: CA_Mastr_Models_v1_0_Models_PricingType.PRICE, displayValue: "Price" },
              { id: 1, value: CA_Mastr_Models_v1_0_Models_PricingType.OAS, displayValue: "OAS" },
              { id: 2, value: CA_Mastr_Models_v1_0_Models_PricingType.YIELD, displayValue: "Yield" },
            ]}
          />
          {/* TODO Make Text Input to accept Ticks */}
          <CAInput
            label={"Value"}
            width={"6rem"}
            info={getInfoMsg(watch_pricing_level, scenarioPageSettingsCopy.pricing_level)}
            {...register("pricing_level", {
              required: true,
              validate: (v) => {
                const value = `${v}`;
                if (watch_pricing_type === CA_Mastr_Models_v1_0_Models_PricingType.PRICE) {
                  return !!value.match(DEC_REGEX) || Boolean(value.match(TICK_REGEX));
                }
                return !!value.match(DEC_REGEX) || !isNaN(tickToDecimal(value));
              },
            })}
          />
        </Box>
        <Box>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.curve_date)}
            _key="curve_date"
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
          <Controller
            name={"settle_date"}
            control={control}
            rules={{ required: true }}
            render={({ field: { name, value, onChange, ref } }) => (
              <CADateInput
                ref={ref}
                selectedDate={value}
                width={"6rem"}
                minDate={userSettings.curve_date}
                onChange={onChange}
                name={name}
                label={"Settle Date"}
                info={getInfoMsg(watch_settle_date, scenarioPageSettingsCopy.settle_date)}
                includeNonBusinessDays={true}
              />
            )}
          />
        </Box>
        <Box>
          <CAHeading as="h3" mb={4}>
            Intex CDU
          </CAHeading>
          <PricerValueDisplayWrapper
            name="Date"
            value={
              bond_name ? (
                <BondIndicativesValue
                  bond_name={bond_name}
                  location="bond_structure.tranche_cdu_date"
                  formatter={getFormattedYearMonth}
                  noCache={noCache}
                  requestMetadata={{ app, page }}
                />
              ) : (
                "-"
              )
            }
          />

          <PricerValueDisplayWrapper name="Deal Mode" value={security_info?.deal_mode ?? "-"} />
        </Box>
        <Box>
          <CASelectDropdown
            label={"Prepay Model"}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_model_type, scenarioPageSettingsCopy.prepay_model_type)}
            {...register("prepay_model_type", {
              required: true,
            })}
            options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_models)}
          />
          <CAInput
            type="number"
            width={"6rem"}
            label={"Multiplier (%)"}
            {...register("prepay_percentage", {
              valueAsNumber: true,
              required: true,
            })}
            info={getInfoMsg(watch_prepay_percentage, scenarioPageSettingsCopy.prepay_percentage)}
          />
          {watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR && (
            <PrepayVectorSelector lastRun={lastRun} />
          )}
          {(watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO ||
            watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR) && (
            <ModelDialSelectorWrapper lastRun={lastRun} />
          )}
          <ReplineDialsSwitch />
          <PricerValueDisplayWrapper
            name="Prepay Model Version"
            value={userSettings.model_version ?? ""}
            _key="model_version"
            link
          />
          <PricerValueDisplayWrapper
            name="Current Coupon Model"
            link
            value={
              metadata?.pricer_settings?.current_coupon_model?.find(
                (l) => l.value === userSettings.current_coupon_model
              )?.display_value ?? ""
            }
            _key="current_coupon_model"
          />
          <PricerValueDisplayWrapper
            name="Primary-Secondary Spread Model"
            link
            value={
              metadata?.pricer_settings?.primary_secondary_spread?.find(
                (l) => l.value === userSettings.primary_secondary_spread
              )?.display_value ?? ""
            }
            _key="primary_secondary_spread"
          />

          <PricerValueDisplayWrapper
            name="Interest Rate Model"
            link
            value={metadata?.pricer_settings?.type?.find((l) => l.value === userSettings.type)?.display_value ?? ""}
            _key="type"
          />

          <PricerValueDisplayWrapper
            name="Interest Rate Paths"
            link
            value={userSettings.interestRatePaths?.toString() ?? ""}
            _key="interestRatePaths"
          />
          <PricerValueDisplayWrapper
            name="Calibration"
            value={
              metadata?.pricer_settings?.calibration?.find((l) => l.value === userSettings.calibration)
                ?.display_value ?? ""
            }
            _key="calibration"
            link
          />
          <PricerValueDisplayWrapper
            name="Cello CDU Date"
            link
            value={getFormattedYearMonth(userSettings[cduDateKey])}
            _key={`cello_cdu_dates.${cduDateKey}`}
            dateFormatter={getFormattedYearMonth}
            drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
          />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
        </Box>
        {security_info?.bond_type !== CA_Mastr_Models_v1_0_Models_BondType.CMO && (
          <CASelectDropdown
            label="Cash Flow"
            width="6rem"
            info={getInfoMsg(watch_modify_class, scenarioPageSettingsCopy.modify_class)}
            {...register("modify_class", {
              required: true,
            })}
            options={getFormattedStringOptions(metadata?.pricer_settings?.bond_class)}
          />
        )}
        <DateSettingsOverrides />
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(ScenarioInput);
