import { CA_Mastr_Models_v1_0_Models_Method } from "@/utils/openapi";

export const prepayModelSelectOptions = [
  {
    id: 0,
    value: CA_Mastr_Models_v1_0_Models_Method.MODEL,
  },
  {
    id: 1,
    value: CA_Mastr_Models_v1_0_Models_Method.CPR,
  },
  {
    id: 2,
    value: CA_Mastr_Models_v1_0_Models_Method.PSA,
  },
  {
    id: 3,
    value: CA_Mastr_Models_v1_0_Models_Method.CPJ,
  },
].map((option) => ({ ...option, displayValue: option.value }));

export const defaultModelSelectOptions = [
  {
    id: 0,
    value: CA_Mastr_Models_v1_0_Models_Method.CDR,
    displayValue: CA_Mastr_Models_v1_0_Models_Method.CDR,
  },
  {
    id: 1,
    value: CA_Mastr_Models_v1_0_Models_Method.PLD,
    displayValue: CA_Mastr_Models_v1_0_Models_Method.PLD,
  },
].map((option) => ({ ...option, displayValue: option.value }));

export const vectorInputSectionHeaderStyle = {
  fontSize: "lg",
  fontWeight: "bold",
};

export const defaultInputValue = {
  month: NaN,
  value: NaN,
  method: undefined,
};
