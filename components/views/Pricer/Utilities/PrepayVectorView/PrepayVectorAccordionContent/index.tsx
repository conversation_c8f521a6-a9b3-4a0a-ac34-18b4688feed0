import * as React from "react";
import { Box, Skeleton } from "@chakra-ui/react";
import { useGetUserVectorSWR } from "@/utils/swr-hooks";
import CACard from "@/design-system/molecules/CACard";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { AccordionContentProps } from "../../../shared/AccordionTable/types";
import PrepayVectorViewContent from "./ViewContent";
import PrepayVectorEditContent from "./EditContent";

const PrepayVectorAccordionContent: React.FC<AccordionContentProps> = ({
  mode = "view",
  itemId,
  closeModal,
  ...props
}: AccordionContentProps) => {
  const { data: response, error } = useGetUserVectorSWR(itemId);
  const userVector = response?.user_vector;

  return (
    <Box {...props} mr={30} ml={"3.4rem"}>
      {error ? (
        <CACard flexGrow={1} display="flex" justifyContent="center" alignItems="center" bg="inherit">
          <CAAlertCard title={"Error"} description={error.message} status="error" />
        </CACard>
      ) : !userVector && mode !== "add" ? (
        <Skeleton height="25px" />
      ) : (
        <Box px={8}>
          {mode === "view" ? (
            <PrepayVectorViewContent data={userVector} />
          ) : (
            <PrepayVectorEditContent data={userVector} closeModal={closeModal} mode={mode} {...props} />
          )}
        </Box>
      )}
    </Box>
  );
};

export default PrepayVectorAccordionContent;
