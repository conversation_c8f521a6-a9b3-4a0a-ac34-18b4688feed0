import * as React from "react";
import { Box, HStack, I<PERSON><PERSON><PERSON>on } from "@chakra-ui/react";
import { IoClose, IoSaveSharp, IoSyncCircleOutline } from "react-icons/io5";
import { useSWRConfig } from "swr";
import { CA_Mastr_Api_v1_0_Models_UserVector_UserVector } from "@/utils/openapi";
import { usePricerPrepayVectorPage } from "@/contexts/PageContexts/PricerPrepayVectorPageContext";
import CAInput from "@/design-system/molecules/CAInput";
import S from "@/constants/strings";
import ConfirmationModalPopup from "@/design-system/organisms/ConfirmationModalPopup";
import { userVectorKey } from "@/utils/swr-hooks/UserVector";
import PrepayVectorInput, { PrepayVectorInputHandler } from "../PrepayVectorInput";
import { AccordionContentProps } from "../../../shared/AccordionTable/types";

type PrepayVectorEditContentProps = {
  data: CA_Mastr_Api_v1_0_Models_UserVector_UserVector | undefined;
  mode: "add" | "edit";
  closeModal?: () => void;
} & Pick<AccordionContentProps, "setContentMode">;

const PrepayVectorEditContent: React.FC<PrepayVectorEditContentProps> = ({
  data,
  mode,
  closeModal,
  setContentMode,
}: PrepayVectorEditContentProps) => {
  const { mutate } = useSWRConfig();
  const {
    action: { createVector, updateVector },
  } = usePricerPrepayVectorPage();
  const [vectorName, setVectorName] = React.useState<string>("");
  const [isEditModalOpen, toggleEditModal] = React.useState(false);
  const vectorNameInputRef = React.useRef<HTMLInputElement>(null);
  const prepayVectorInputRef = React.useRef<PrepayVectorInputHandler>(null);

  React.useEffect(() => {
    let vectorName = data?.user_vector_name ?? "";
    if (mode === "add" && data?.user_vector_id) {
      vectorName = `Copy of ${data?.user_vector_name}`;
    }
    setVectorName(vectorName);
  }, [data?.user_vector_id, data?.user_vector_name, mode]);

  const onCancelClick = () => {
    if (prepayVectorInputRef.current?.hasChanged()) {
      toggleEditModal(true);
    } else {
      closeModal?.();
    }
  };

  const resetHandler = () => {
    prepayVectorInputRef.current?.reset();
  };

  const submitHandler = async () => {
    const result = prepayVectorInputRef.current?.onSubmit();
    if (!result) return;

    if (!vectorName.trim()) {
      vectorNameInputRef?.current?.focus();
      return;
    }
    window.hasUnsavedChanges = false;
    result.prepay_vector_values = result.prepay_vector_values?.map((prepay_vector) => ({
      ...prepay_vector,
      value: typeof prepay_vector?.value === "string" ? +prepay_vector?.value : prepay_vector?.value,
    }));
    result.default_vector_values = result.default_vector_values?.map((default_vector) => ({
      ...default_vector,
      value: typeof default_vector?.value === "string" ? +default_vector?.value : default_vector?.value,
    }));

    if (data?.user_vector_id && mode !== "add") {
      await updateVector(
        {
          ...data,
          ...result,
          user_vector_id: data.user_vector_id,
          user_vector_name: vectorName.trim(),
        },
        data.updated
      );
      mutate(userVectorKey.getUserVector());
      mutate(userVectorKey.getUserVectorById(data?.user_vector_id));
      setContentMode("view");
    } else {
      await createVector({ ...result, user_vector_name: vectorName.trim() });
      mutate(userVectorKey.getUserVector());
      closeModal?.();
    }
  };

  return (
    <Box mb={4}>
      <Box position="absolute" maxHeight="3.813rem" top="11px" w={"14.75rem"}>
        <CAInput
          name="model-dial-name"
          value={vectorName}
          ref={vectorNameInputRef}
          onChange={(e) => setVectorName(e.target.value)}
        />
      </Box>
      <Box position="absolute" top="12px" left={"90%"} maxWidth="13rem">
        <HStack spacing={2}>
          <IconButton
            icon={<IoSyncCircleOutline />}
            onClick={resetHandler}
            size="sm"
            fontSize="3xl"
            variant="secondary"
            title="Reset"
            aria-label="Reset"
            transform="scaleX(-1)"
          />
          <IconButton
            icon={<IoClose />}
            onClick={onCancelClick}
            size="sm"
            fontSize="xl"
            variant="secondary"
            title="Close"
            aria-label="Close"
            transform="scaleX(-1)"
          />
          <IconButton
            icon={<IoSaveSharp />}
            onClick={submitHandler}
            size="sm"
            fontSize="xl"
            variant="secondary"
            title="Save"
            aria-label="Save"
            transform="scaleX(-1)"
          />
        </HStack>
      </Box>
      <PrepayVectorInput userVector={data} ref={prepayVectorInputRef} />
      <ConfirmationModalPopup
        isOpen={isEditModalOpen}
        onModalClose={() => toggleEditModal(false)}
        onCancel={() => toggleEditModal(false)}
        onConfirm={() => {
          closeModal?.();
          toggleEditModal(false);
        }}
        headerText={S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER}
        description={S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION}
        showCloseIcon
      />
    </Box>
  );
};

export default PrepayVectorEditContent;
