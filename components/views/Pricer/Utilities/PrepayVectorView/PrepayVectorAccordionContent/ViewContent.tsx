import * as React from "react";
import { Box, HStack, Text, Tooltip } from "@chakra-ui/react";
import { CA_Mastr_Api_v1_0_Models_UserVector_UserVector } from "@/utils/openapi";
import { getPrepayVectorDisplayValue } from "@/utils/helpers/pricer";

type PrepayVectorViewContentProps = {
  data: CA_Mastr_Api_v1_0_Models_UserVector_UserVector | undefined;
};

const PrepayVectorViewContent: React.FC<PrepayVectorViewContentProps> = ({ data }: PrepayVectorViewContentProps) => {
  const prepayVectorText = getPrepayVectorDisplayValue(data?.interpolation, data?.prepay_vector_values);
  const defaultVectorText = getPrepayVectorDisplayValue(data?.interpolation, data?.default_vector_values);

  return (
    <HStack spacing={16} alignItems="flex-start">
      <Box maxW="max-content">
        <Text variant="tableHead" mb={2} fontSize="0.82rem">
          Prepay
        </Text>
        <Tooltip label={prepayVectorText} aria-label={prepayVectorText} placement="bottom">
          <Text overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
            {prepayVectorText}
          </Text>
        </Tooltip>
      </Box>
      <Box maxW="max-content">
        <Text variant="tableHead" mb={2} fontSize="0.82rem">
          Default
        </Text>
        <Tooltip label={defaultVectorText} aria-label={defaultVectorText} placement="bottom">
          <Text overflow="hidden" textOverflow="ellipsis" whiteSpace="nowrap">
            {defaultVectorText}
          </Text>
        </Tooltip>
      </Box>
    </HStack>
  );
};

export default PrepayVectorViewContent;
