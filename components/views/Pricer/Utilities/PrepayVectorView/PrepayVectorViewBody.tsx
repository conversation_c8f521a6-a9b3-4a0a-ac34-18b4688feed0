import * as React from "react";
import {
  Box,
  Button,
  Center,
  Flex,
  HStack,
  Icon,
  Spacer,
  Spinner,
  Text,
  Tooltip,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoAddCircleSharp } from "react-icons/io5";
import CACard, { CACardWithExpand } from "@/design-system/molecules/CACard";
import { usePricerPrepayVectorPage } from "@/contexts/PageContexts/PricerPrepayVectorPageContext";
import { canDelete, canEdit, canShare, getFormattedLocaleDate, getFormattedTime } from "@/utils/helpers";
import { useGetUserVectorsSWR } from "@/utils/swr-hooks";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import CASearch from "@/design-system/molecules/CASearch";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import { getPrepayVectorDisplayValue } from "@/utils/helpers/pricer";
import { CA_Mastr_Models_v1_0_Models_Entity } from "@/utils/openapi";
import AccordionTable from "../../shared/AccordionTable/AccordionTable";
import { actionColumns, toggleColumn } from "../../shared/AccordionTable/constants";
import { ItemProps } from "../../shared/AccordionTable/types";
import PrepayVectorAccordionContent from "./PrepayVectorAccordionContent";

const headers = [
  toggleColumn,
  {
    name: "ID",
    maxWidth: "4rem",
    useMaxAsMin: true,
  },
  {
    name: "Name",
    maxWidth: "600px",
    minWidth: "370px",
  },
  {
    name: "Created by",
    maxWidth: "10rem",
    useMaxAsMin: true,
  },
  {
    name: "Prepay",
    maxWidth: "16.75rem",
    minWidth: "16.75rem",
  },
  {
    name: "Default",
    maxWidth: "16.75rem",
    minWidth: "16.75rem",
  },
  {
    name: "Created",
    maxWidth: "10rem",
    useMaxAsMin: true,
  },
  {
    name: "Updated",
    maxWidth: "10rem",
    useMaxAsMin: true,
  },
  ...actionColumns,
];

const PrepayVectorViewBody: React.FC = () => {
  const {
    state: { lastSavedPrepayVectorId },
    action: { removePrepayVectorFromOpenState, deleteVector },
  } = usePricerPrepayVectorPage();
  const { data, error, mutate, isLoading } = useGetUserVectorsSWR();

  const [addMode, setAddMode] = React.useState(false);
  const [currentAddModeData, setCurrentAddModeData] = React.useState<null | {
    id: number | undefined;
    name: string | null | undefined;
  }>(null);

  React.useEffect(() => {
    if (addMode) {
      setAddMode(false);
      setCurrentAddModeData(null);
    }

    // when the page updates, clear the history/state of dial which needs to stay open
    setTimeout(() => removePrepayVectorFromOpenState(), 2000);
  }, [lastSavedPrepayVectorId]); // eslint-disable-line react-hooks/exhaustive-deps

  const userVectors = data?.user_vectors;

  const TooltipWrappedText = ({ text, maxW }: { text: string; maxW: string }) => {
    return (
      <Tooltip label={text}>
        <Text noOfLines={1} mr={10} maxW={maxW}>
          {text}
        </Text>
      </Tooltip>
    );
  };

  const accordionData: ItemProps[] = React.useMemo(() => {
    if (userVectors) {
      return userVectors?.map((vector) => {
        return {
          values: [
            <Box maxW="4rem" key="id">
              {`V${vector.user_vector_id}`}
            </Box>,
            vector.user_vector_name ?? "",
            `${vector.user?.firstName ?? ""} ${vector.user?.lastName ?? ""}`,
            <TooltipWrappedText
              key="prepay_vector"
              maxW="16.75rem"
              text={getPrepayVectorDisplayValue(vector.interpolation, vector.prepay_vector_values)}
            />,
            <TooltipWrappedText
              key="default_vector"
              maxW="16.75rem"
              text={getPrepayVectorDisplayValue(vector.interpolation, vector.default_vector_values)}
            />,
            `${getFormattedLocaleDate(vector.inserted)} ${getFormattedTime(vector.inserted)}`,
            `${getFormattedLocaleDate(vector.updated)} ${getFormattedTime(vector.updated)}`,
          ],
          createdBy: `${vector.user?.firstName ?? ""} ${vector.user?.lastName ?? ""}`,
          content: PrepayVectorAccordionContent,
          can_edit: canEdit(vector.role),
          can_delete: canDelete(vector.role),
          can_share: canShare(vector.role),
          mode: "view",
          itemId: vector.user_vector_id,
          itemName: vector.user_vector_name,
          duplicateDial: () => {
            setAddMode(true);
            setCurrentAddModeData({ id: vector.user_vector_id, name: vector.user_vector_name });
          },
          entity: CA_Mastr_Models_v1_0_Models_Entity.USER_VECTOR,
        };
      });
    }
    return [];
  }, [userVectors]);

  const list: ItemProps[] = addMode
    ? [
        {
          toggleAddMode: () => {
            setAddMode(false);
            setCurrentAddModeData(null);
          },
          content: PrepayVectorAccordionContent,
          mode: "add",
          itemId: currentAddModeData?.id,
          values: [...new Array(7).fill("")],
          entity: CA_Mastr_Models_v1_0_Models_Entity.USER_VECTOR,
        },
        ...accordionData,
      ]
    : accordionData;

  const { filteredList, getInputProps } = useKeywordsSearch<ItemProps>({
    getSearchableText: (item) => `${item.itemName} ${item.createdBy} V${item.itemId}`,
    additionalFilterCheck: (item) => item.mode === "add",
    list,
  });

  return (
    <CACardWithExpand
      cardStyleOnOpen={{
        h: "full",
        minHeight: "calc(100vh - 11.9rem)",
        width: {
          base: "100%",
          md: "100%",
          lg: "100%",
          xl: "calc(100vw - 255px)",
          "2xl": "calc(100vw - 255px)",
        },
      }}
      cardBodyStyle={{
        minHeight: "calc(100vh - 14.9rem)",
      }}
      pb={0}
      borderWidth={useColorModeValue("0", "2px")}
      title=" "
      allowCollapse
    >
      {error ? (
        <CACard flexGrow={1} display="flex" justifyContent="center" alignItems="center">
          <CAAlertCard title={"Error"} description={error.message} status="error" />
        </CACard>
      ) : isLoading ? (
        <Center mt="20rem">
          <HStack>
            <Spinner mr={4} emptyColor="celloBlue.200" />
            <Text fontSize="2xl" fontWeight="bold" textAlign="center">
              Loading...
            </Text>
          </HStack>
        </Center>
      ) : (
        <Box>
          <Flex>
            <Box w="full" maxW="12.5rem">
              <CASearch
                name="prepay-vector-search"
                placeholder="Quick Filter"
                isDisabled={!data}
                {...getInputProps()}
              />
            </Box>
            <Spacer />
            <Button
              height={9}
              leftIcon={<Icon as={IoAddCircleSharp} fontSize="20" />}
              variant="iconButton"
              isDisabled={addMode}
              onClick={() => setAddMode(true)}
            >
              <Text color="inherit">New</Text>
            </Button>
          </Flex>
          <Box mt="5" mx={-4} overflowX={"hidden"}>
            <AccordionTable
              onDelete={(itemId, itemName) =>
                deleteVector(
                  itemId,
                  itemName,
                  userVectors?.find((vector) => vector.user_vector_id === itemId)?.updated,
                  mutate
                )
              }
              lastSavedItemId={lastSavedPrepayVectorId}
              data={filteredList}
              headers={headers}
              minWidth={"max-content"}
            />
          </Box>
        </Box>
      )}
    </CACardWithExpand>
  );
};

export default PrepayVectorViewBody;
