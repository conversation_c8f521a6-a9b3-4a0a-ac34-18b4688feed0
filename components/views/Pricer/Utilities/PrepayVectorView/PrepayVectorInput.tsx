import * as React from "react";
import { <PERSON>, HStack, I<PERSON><PERSON>utton, Text } from "@chakra-ui/react";
import { useFieldArray, useForm } from "react-hook-form";
import { IoTrashOutline } from "react-icons/io5";
import { CA_Mastr_Api_v1_0_Models_UserVector_UserVector } from "@/utils/openapi/models/CA_Mastr_Api_v1_0_Models_UserVector_UserVector";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import {
  CA_Mastr_Api_v1_0_Models_UserVector_InsertUserVector,
  CA_Mastr_Api_v1_0_Models_UserVector_UpdateUserVector,
  CA_Mastr_Models_v1_0_Models_Interpolation,
  CA_Mastr_Models_v1_0_Models_Method,
  CA_Mastr_Models_v1_0_Models_VectorData,
} from "@/utils/openapi";
import CAInput from "@/design-system/molecules/CAInput";
import { sortAscending<PERSON><PERSON><PERSON>ield } from "@/utils/helpers";
import { showErrorToast } from "@/design-system/theme/toast";
import CAIcon from "@/design-system/atoms/CAIcon";
import S from "@/constants/strings";
import useInterruptRouteChange from "@/hooks/useInterruptRouteChange";
import {
  defaultInputValue,
  defaultModelSelectOptions,
  prepayModelSelectOptions,
  vectorInputSectionHeaderStyle,
} from "./constants";

export type PrepayVectorInputProps = {
  userVector?: CA_Mastr_Api_v1_0_Models_UserVector_UserVector;
};
export interface PrepayVectorInputHandler {
  onSubmit: () =>
    | CA_Mastr_Api_v1_0_Models_UserVector_InsertUserVector
    | CA_Mastr_Api_v1_0_Models_UserVector_UpdateUserVector;
  reset: (hardReset?: boolean) => void;
  hasChanged: () => boolean;
}

type PrepayVectorInputFormProps = {
  interpolation: CA_Mastr_Models_v1_0_Models_Interpolation;
  prepay_vector_values: {
    month?: number | null;
    value?: string | number | null;
    method?: CA_Mastr_Models_v1_0_Models_Method | null;
  }[];
  default_vector_values: {
    month?: number | null;
    value?: string | number | null;
    method?: CA_Mastr_Models_v1_0_Models_Method | null;
  }[];
};

// constants
const minPeriodWidth = "3rem";
const minValueWidth = "3.5rem";
const minModelWidth = "5.5rem";

const PrepayVectorInput: React.ForwardRefRenderFunction<PrepayVectorInputHandler, PrepayVectorInputProps> = (
  { userVector }: PrepayVectorInputProps,
  ref
) => {
  const {
    register,
    setValue,
    formState: { dirtyFields },
    getValues,
    reset,
    control,
    setFocus,
  } = useForm<PrepayVectorInputFormProps>({
    defaultValues: {
      default_vector_values: [defaultInputValue],
      prepay_vector_values: [defaultInputValue],
    },
  });

  const {
    fields: PrepayVectorFields,
    remove: removePrepayVectorRow,
    append: appendPrepayVectorRow,
    move: movePrepayVectorRow,
  } = useFieldArray({
    control,
    name: "prepay_vector_values",
  });
  const {
    fields: DefaultVectorFields,
    remove: removeDefaultVectorRow,
    append: appendDefaultVectorRow,
    move: moveDefaultVectorRow,
  } = useFieldArray({
    control,
    name: "default_vector_values",
  });

  React.useEffect(() => {
    if (userVector) {
      const emptyInput = defaultInputValue;
      const prepayVectorValues = userVector?.prepay_vector_values ?? [];
      const defaultVectorValues = userVector?.default_vector_values ?? [];

      reset({
        interpolation: userVector?.interpolation ?? CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR,
        prepay_vector_values: [...prepayVectorValues, { ...emptyInput }],
        default_vector_values: [...defaultVectorValues, { ...emptyInput }],
      });
    }
  }, [reset, userVector]);

  useInterruptRouteChange(Object.keys(dirtyFields).length !== 0, () =>
    window.confirm(`${S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER}\n\n${S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION}`)
  );

  React.useImperativeHandle(ref, () => ({
    onSubmit,
    reset: onReset,
    hasChanged: () => Object.keys(dirtyFields).length !== 0,
  }));

  const removeRow = (index: number, isDefault = false) => {
    if (isDefault) {
      removeDefaultVectorRow(index);
    } else {
      removePrepayVectorRow(index);
    }
  };

  const appendNewRow = (isDefault = false) => {
    if (isDefault) {
      const rowData = getValues("default_vector_values");
      const lastRow = rowData.at(-1) ?? {};
      if (lastRow.month || lastRow.method || lastRow.value) {
        appendDefaultVectorRow(defaultInputValue, {
          shouldFocus: false,
        });
      }
    } else {
      const rowData = getValues("prepay_vector_values");
      const lastRow = rowData.at(-1) ?? {};
      if (lastRow.month || lastRow.method || lastRow.value) {
        appendPrepayVectorRow(defaultInputValue, {
          shouldFocus: false,
        });
      }
    }
  };

  const sortFields = (value: number, currentIndex: number, isDefault = false) => {
    const fieldName = isDefault ? "default_vector_values" : "prepay_vector_values";

    const inputs = getValues(fieldName) ?? [];
    const sortedInputs = sortAscendingByField(
      inputs.filter((v) => v.month || v.month === 0),
      "month"
    );

    const updatedIndex = sortedInputs.findIndex((i) => i.month === value);
    if (currentIndex === updatedIndex || updatedIndex === -1) {
      return;
    }

    if (isDefault) {
      moveDefaultVectorRow(currentIndex, updatedIndex);
    } else {
      movePrepayVectorRow(currentIndex, updatedIndex);
    }
    setTimeout(() => {
      setFocus(`${fieldName}.${updatedIndex}.value`);
    }, 0);
  };

  const onMonthInputBlur = (e: React.ChangeEvent<HTMLInputElement>, index: number, isDefault = false) => {
    const value = +e.target.value;
    const fieldName = isDefault ? "default_vector_values" : "prepay_vector_values";

    // Do not allow decimal values
    if (!Number.isInteger(value)) {
      showErrorToast("Error", 'Only integer values are allowed in "Period".');
      setValue(`${fieldName}.${index}.month`, NaN);
    }

    // check for max period value
    if (value && value > 600) {
      setValue(`${fieldName}.${index}.month`, 600);
    }

    appendNewRow(isDefault);
    sortFields(value, index, isDefault);
  };

  const getInputFields = (type: "prepay_vector_values" | "default_vector_values") => {
    const inputs = type === "prepay_vector_values" ? PrepayVectorFields : DefaultVectorFields;
    const isDefault = type === "default_vector_values";
    return inputs?.map((input, index) => {
      return (
        <HStack spacing={2} key={input.id} role="group" alignItems="center">
          <Box w="6rem" maxW={minPeriodWidth}>
            <CAInput
              type="number"
              inputType="positive-integer"
              key={`${type}.${index}.month`}
              {...register(`${type}.${index}.month`, {
                valueAsNumber: true,
                value: input.month ?? null,
              })}
              onBlur={(e) => onMonthInputBlur(e, index, isDefault)}
            />
          </Box>
          <Box w="6rem" maxW={minValueWidth}>
            <CAInput
              type="number"
              inputType="no-negative"
              key={`${type}.${index}.value`}
              {...register(`${type}.${index}.value`, {
                valueAsNumber: true,
                value: input.value ?? null,
              })}
              onBlur={() => appendNewRow(isDefault)}
            />
          </Box>
          <Box w="6rem" maxW={minModelWidth}>
            <CASelectDropdown
              {...register(`${type}.${index}.method`, { value: input.method ?? null })}
              options={type === "prepay_vector_values" ? prepayModelSelectOptions : defaultModelSelectOptions}
              onBlur={() => appendNewRow(isDefault)}
            />
          </Box>
          <IconButton
            visibility="hidden"
            _groupHover={{ visibility: "visible" }}
            isDisabled={inputs.length === 1}
            aria-label="remove-prepay-vector"
            size="xs"
            variant="unstyled"
            icon={<CAIcon as={IoTrashOutline} variant="secondary" boxSize={4} display="flex" />}
            onClick={() => removeRow(index, isDefault)}
          />
        </HStack>
      );
    });
  };

  const onReset = (hardReset?: boolean) => {
    const emptyInput = defaultInputValue;

    if (hardReset) {
      reset(
        {
          interpolation: CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR,
          prepay_vector_values: [{ ...emptyInput }],
          default_vector_values: [{ ...emptyInput }],
        },
        {
          keepDefaultValues: true,
        }
      );
      return;
    }

    const prepayVectorValues = !userVector?.prepay_vector_values
      ? []
      : userVector?.prepay_vector_values?.map((value) => ({
          value: value.value,
          month: value.month,
          method: value.method,
        }));
    const defaultVectorValues = !userVector?.default_vector_values
      ? []
      : userVector?.default_vector_values?.map((value) => ({
          value: value.value,
          month: value.month,
          method: value.method,
        }));
    reset({
      interpolation: userVector?.interpolation ?? CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR,
      prepay_vector_values: [...prepayVectorValues, { ...emptyInput }],
      default_vector_values: [...defaultVectorValues, { ...emptyInput }],
    });
  };

  const onSubmit = ():
    | CA_Mastr_Api_v1_0_Models_UserVector_InsertUserVector
    | CA_Mastr_Api_v1_0_Models_UserVector_UpdateUserVector => {
    const data: PrepayVectorInputFormProps = getValues();
    const prepayVectorData = data.prepay_vector_values?.filter(
      (prepay) => (prepay.value || prepay.value === 0) && (prepay.month || prepay.month === 0) && prepay.method
    ) as Array<CA_Mastr_Models_v1_0_Models_VectorData> | null;
    const defaultVectorData = data.default_vector_values?.filter(
      (prepay) => (prepay.value || prepay.value === 0) && (prepay.month || prepay.month === 0) && prepay.method
    ) as Array<CA_Mastr_Models_v1_0_Models_VectorData> | null;
    const payload = { ...data, prepay_vector_values: prepayVectorData, default_vector_values: defaultVectorData };
    return payload;
  };

  return (
    <Box>
      <Box maxW="11.2rem" mb={4}>
        <CASelectDropdown
          {...register("interpolation")}
          label="Interpolation"
          labelVariant="tableHead"
          defaultValue={CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR}
          options={[
            {
              id: 0,
              value: CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR,
              displayValue: "Linear",
            },
            {
              id: 1,
              value: CA_Mastr_Models_v1_0_Models_Interpolation.STEP,
              displayValue: "Step",
            },
          ]}
        />
      </Box>
      <HStack spacing={16} alignItems="flex-start">
        <Box>
          <Text variant="tableHead" {...vectorInputSectionHeaderStyle} mb={2}>
            Prepay
          </Text>
          <HStack spacing={2}>
            <Text variant="tableLeft" minW={minPeriodWidth}>
              Period
            </Text>
            <Text variant="tableLeft" minW={minValueWidth}>
              Value
            </Text>
            <Text variant="tableLeft" minW={minModelWidth}>
              Model
            </Text>
          </HStack>
          {getInputFields("prepay_vector_values")}
        </Box>
        <Box>
          <Text variant="tableHead" {...vectorInputSectionHeaderStyle} mb={2}>
            Default
          </Text>
          <HStack>
            <Text variant="tableLeft" minW={minPeriodWidth}>
              Period
            </Text>
            <Text variant="tableLeft" minW={minValueWidth}>
              Value
            </Text>
            <Text variant="tableLeft" minW={minModelWidth}>
              Model
            </Text>
          </HStack>
          {getInputFields("default_vector_values")}
        </Box>
      </HStack>
    </Box>
  );
};

export default React.forwardRef(PrepayVectorInput);
