import React from "react";
import { Box, HStack, Text, useColorModeValue } from "@chakra-ui/react";
import { FormProvider, useForm } from "react-hook-form";
import CATable from "@/design-system/molecules/CATable";
import { normalizeDialsData } from "@/utils/helpers/model-dials";
import {
  CA_Mastr_Models_v1_0_Models_ModelDialData,
  CA_Mastr_Models_v1_0_Models_ModelType,
  CA_Mastr_Models_v1_0_Models_Operation,
  CA_Mastr_Models_v1_0_Models_SubmodelDialName,
} from "@/utils/openapi";
import useInterruptRouteChange from "@/hooks/useInterruptRouteChange";
import S from "@/constants/strings";
import { CA_Mastr_Models_v1_0_Models_ModelDialUnits } from "@/utils/openapi/models/CA_Mastr_Models_v1_0_Models_ModelDialUnits";
import { DialInputProps } from "../DialInput";
import {
  EMPTY_CELL,
  ModelTypes,
  SubModelDials,
  defaultUnit,
  headers,
  headersGroup,
  tableCellLeftMargin,
} from "./EditConfigs";
import { ModelDialInput, OperationDropDown, PrepayRateDropdown } from "./EditFormComponents";
import { SubModelLabels } from ".";

export type ModelDialFormType = {
  [key in `${CA_Mastr_Models_v1_0_Models_SubmodelDialName}`]: {
    operation?: CA_Mastr_Models_v1_0_Models_Operation;
    units?: CA_Mastr_Models_v1_0_Models_ModelDialUnits;
  } & {
    [key: string]: CA_Mastr_Models_v1_0_Models_ModelDialData;
  };
};
interface ModelDialsEditFormProps {
  modelDialData: CA_Mastr_Models_v1_0_Models_ModelDialData[] | null | undefined;
}

export interface ModelDialsEditFormHandler {
  getSubmitData: () => CA_Mastr_Models_v1_0_Models_ModelDialData[];
  reset: () => void;
  isDataEdited: () => boolean;
}

const ModelDialsEditForm: React.ForwardRefRenderFunction<ModelDialsEditFormHandler, ModelDialsEditFormProps> = (
  { modelDialData }: ModelDialsEditFormProps,
  ref
) => {
  const labelRowBackgroundColor = useColorModeValue("celloBlue.75", "celloBlue.900");

  const methods = useForm<ModelDialFormType>({
    defaultValues: normalizeDialsData(modelDialData ?? []),
  });
  const { isDirty } = methods.formState;

  useInterruptRouteChange(isDirty, () =>
    window.confirm(`${S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER}\n\n${S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION}`)
  );

  React.useEffect(() => {
    const normalizedData = normalizeDialsData(modelDialData ?? []);
    methods.reset(normalizedData);
  }, [modelDialData, methods]);

  const getSubmitDataHandler = (): CA_Mastr_Models_v1_0_Models_ModelDialData[] => {
    let modelDials: CA_Mastr_Models_v1_0_Models_ModelDialData[] = [];
    const data = methods.getValues();
    for (const submodel in data) {
      const submodelData = data[submodel as `${CA_Mastr_Models_v1_0_Models_SubmodelDialName}`];
      for (const modeltype in submodelData) {
        const modelTypeData = submodelData[modeltype as `${CA_Mastr_Models_v1_0_Models_ModelType}`];
        if (typeof modelTypeData === "object") {
          const modelDial = {
            ...modelTypeData,
            operation: submodelData.operation,
          };
          if (modelTypeData?.submodel_dial_name?.includes("prepay_rate")) {
            modelDial.units = submodelData.units ?? defaultUnit;
          }
          modelDials.push(modelDial);
        }
      }
    }

    /**
     * Discard values which are "defaultValues" (set in tableInput)
     * This is a hacky way to make sure there are correct number of keys in the model dials object
     * TODO - Let's look into ways to improve this check in the future
     */
    modelDials = modelDials.filter((d) => ("units" in d ? Object.keys(d).length > 4 : Object.keys(d).length > 3));

    return modelDials;
  };

  const resetHandler = () => {
    const normalizedData = normalizeDialsData(modelDialData ?? []);
    methods.reset(normalizedData);
  };

  React.useImperativeHandle(ref, () => ({
    getSubmitData: getSubmitDataHandler,
    reset: resetHandler,
    isDataEdited: () => isDirty,
  }));

  const wrapEditTableCell = React.useCallback(
    (value: string | React.JSX.Element, index: number) => (
      <Box minW={index === 0 ? "12rem" : "6rem"} ml={index !== 0 ? tableCellLeftMargin[index + 1] : ""}>
        <Box maxW={index === 0 ? "12rem" : "full"} pl={1} my={0}>
          {typeof value !== "string" ? (
            value
          ) : (
            <Text variant={index === 0 ? "tableLeft" : "default"} fontWeight="normal">
              {value}
            </Text>
          )}
        </Box>
      </Box>
    ),
    []
  );

  const getEmptyRow = () => ({
    name: <Box minH={"0.8rem"}></Box>,
    values: [],
  });

  const getOperationDropdown = (submodelDialName: CA_Mastr_Models_v1_0_Models_SubmodelDialName, isPrepay = false) => (
    <HStack>
      <OperationDropDown submodelDialName={submodelDialName} />
      {isPrepay && <PrepayRateDropdown submodelDialName={submodelDialName} />}
    </HStack>
  );

  const getTableInput = (
    submodelDialName: CA_Mastr_Models_v1_0_Models_SubmodelDialName,
    modelType: CA_Mastr_Models_v1_0_Models_ModelType,
    opts?: { customDial?: DialInputProps["customDial"] }
  ) => <ModelDialInput modelType={modelType} submodelDialName={submodelDialName} opts={opts} />;

  const getEditModeRowData = React.useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (submodelName: CA_Mastr_Models_v1_0_Models_SubmodelDialName) => {
      switch (submodelName) {
        case SubModelDials.FINAL_PREPAY_RATE:
          return {
            name: "",
            values: [
              "Prepay Rate (%)",
              getOperationDropdown(SubModelDials.FINAL_PREPAY_RATE, true),
              getTableInput(SubModelDials.FINAL_PREPAY_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.FINAL_PREPAY_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.FINAL_PREPAY_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.FINAL_PREPAY_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.FINAL_PREPAY_RATE, ModelTypes.PIH),
              getTableInput(SubModelDials.FINAL_PREPAY_RATE, ModelTypes.PROJECT_LOAN),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.ALL_MORTGAGE_RATE:
          return {
            name: "",
            values: [
              "Mortgage Rate (%)",
              getOperationDropdown(SubModelDials.ALL_MORTGAGE_RATE),
              getTableInput(SubModelDials.ALL_MORTGAGE_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.ALL_MORTGAGE_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.ALL_MORTGAGE_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.ALL_MORTGAGE_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.ALL_MORTGAGE_RATE, ModelTypes.PIH),
              getTableInput(SubModelDials.ALL_MORTGAGE_RATE, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.ALL_MIP:
          return {
            name: "",
            values: [
              "FHA Annual MIP (bp)",
              getOperationDropdown(SubModelDials.ALL_MIP),
              getTableInput(SubModelDials.ALL_MIP, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.ALL_MIP, ModelTypes.FHA),
              EMPTY_CELL,
              EMPTY_CELL,
              EMPTY_CELL,
              EMPTY_CELL,
            ],
          };

        case SubModelDials.ALL_GEO_EFFECT:
          return {
            name: "",
            values: [
              "Geo Effect for HPA and Mortgage Tax (%)",
              getOperationDropdown(SubModelDials.ALL_GEO_EFFECT),
              getTableInput(SubModelDials.ALL_GEO_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.ALL_GEO_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.ALL_GEO_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.ALL_GEO_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.ALL_GEO_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.REFINANCING_PREPAY_RATE:
          return {
            name: "",
            values: [
              "Prepay Rate (%)",
              getOperationDropdown(SubModelDials.REFINANCING_PREPAY_RATE, true),
              getTableInput(SubModelDials.REFINANCING_PREPAY_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.REFINANCING_PREPAY_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.REFINANCING_PREPAY_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.REFINANCING_PREPAY_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.REFINANCING_PREPAY_RATE, ModelTypes.PIH),
              getTableInput(SubModelDials.REFINANCING_PREPAY_RATE, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.REFINANCING_INCENTIVE:
          return {
            name: "",
            values: [
              "Incentive (bp)",
              getOperationDropdown(SubModelDials.REFINANCING_INCENTIVE),
              getTableInput(SubModelDials.REFINANCING_INCENTIVE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.REFINANCING_INCENTIVE, ModelTypes.FHA),
              getTableInput(SubModelDials.REFINANCING_INCENTIVE, ModelTypes.VA),
              getTableInput(SubModelDials.REFINANCING_INCENTIVE, ModelTypes.RHS),
              getTableInput(SubModelDials.REFINANCING_INCENTIVE, ModelTypes.PIH),
              getTableInput(SubModelDials.REFINANCING_INCENTIVE, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.REFINANCING_BURNOUT:
          return {
            name: "",
            values: [
              "Burnout (bp)",
              getOperationDropdown(SubModelDials.REFINANCING_BURNOUT),
              getTableInput(SubModelDials.REFINANCING_BURNOUT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.REFINANCING_BURNOUT, ModelTypes.FHA),
              getTableInput(SubModelDials.REFINANCING_BURNOUT, ModelTypes.VA),
              getTableInput(SubModelDials.REFINANCING_BURNOUT, ModelTypes.RHS),
              getTableInput(SubModelDials.REFINANCING_BURNOUT, ModelTypes.PIH),
              getTableInput(SubModelDials.REFINANCING_BURNOUT, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.REFINANCING_MEDIA_EFFECT:
          return {
            name: "",
            values: [
              "Media Effect (%)",
              getOperationDropdown(SubModelDials.REFINANCING_MEDIA_EFFECT),
              getTableInput(SubModelDials.REFINANCING_MEDIA_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.REFINANCING_MEDIA_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.REFINANCING_MEDIA_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.REFINANCING_MEDIA_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.REFINANCING_MEDIA_EFFECT, ModelTypes.PIH),
              getTableInput(SubModelDials.REFINANCING_MEDIA_EFFECT, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.REFINANCING_SERVICER_EFFECT:
          return {
            name: "",
            values: [
              "Servicer Effect (%)",
              getOperationDropdown(SubModelDials.REFINANCING_SERVICER_EFFECT),
              getTableInput(SubModelDials.REFINANCING_SERVICER_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.REFINANCING_SERVICER_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.REFINANCING_SERVICER_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.REFINANCING_SERVICER_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.REFINANCING_SERVICER_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.REFINANCING_GEO_EFFECT:
          return {
            name: "",
            values: [
              "Geo Effect (%)",
              getOperationDropdown(SubModelDials.REFINANCING_GEO_EFFECT),
              getTableInput(SubModelDials.REFINANCING_GEO_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.REFINANCING_GEO_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.REFINANCING_GEO_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.REFINANCING_GEO_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.REFINANCING_GEO_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.CASHOUT_PREPAY_RATE:
          return {
            name: "",
            values: [
              "Prepay Rate (%)",
              getOperationDropdown(SubModelDials.CASHOUT_PREPAY_RATE, true),
              getTableInput(SubModelDials.CASHOUT_PREPAY_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.CASHOUT_PREPAY_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.CASHOUT_PREPAY_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.CASHOUT_PREPAY_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.CASHOUT_PREPAY_RATE, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.CASHOUT_SERVICER_EFFECT:
          return {
            name: "",
            values: [
              "Servicer Effect (%)",
              getOperationDropdown(SubModelDials.CASHOUT_SERVICER_EFFECT),
              getTableInput(SubModelDials.CASHOUT_SERVICER_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.CASHOUT_SERVICER_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.CASHOUT_SERVICER_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.CASHOUT_SERVICER_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.CASHOUT_SERVICER_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.CASHOUT_GEO_EFFECT:
          return {
            name: "",
            values: [
              "Geo Effect (%)",
              getOperationDropdown(SubModelDials.CASHOUT_GEO_EFFECT),
              getTableInput(SubModelDials.CASHOUT_GEO_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.CASHOUT_GEO_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.CASHOUT_GEO_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.CASHOUT_GEO_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.CASHOUT_GEO_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.TURNOVER_PREPAY_RATE:
          return {
            name: "",
            values: [
              "Prepay Rate (%)",
              getOperationDropdown(SubModelDials.TURNOVER_PREPAY_RATE, true),
              getTableInput(SubModelDials.TURNOVER_PREPAY_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.TURNOVER_PREPAY_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.TURNOVER_PREPAY_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.TURNOVER_PREPAY_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.TURNOVER_PREPAY_RATE, ModelTypes.PIH),
              getTableInput(SubModelDials.TURNOVER_PREPAY_RATE, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.TURNOVER_LOCKIN:
          return {
            name: "",
            values: [
              "Lockin (%)",
              getOperationDropdown(SubModelDials.TURNOVER_LOCKIN),
              getTableInput(SubModelDials.TURNOVER_LOCKIN, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.TURNOVER_LOCKIN, ModelTypes.FHA),
              getTableInput(SubModelDials.TURNOVER_LOCKIN, ModelTypes.VA),
              getTableInput(SubModelDials.TURNOVER_LOCKIN, ModelTypes.RHS),
              getTableInput(SubModelDials.TURNOVER_LOCKIN, ModelTypes.PIH),
              getTableInput(SubModelDials.TURNOVER_LOCKIN, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.TURNOVER_SERVICER_EFFECT:
          return {
            name: "",
            values: [
              "Servicer Effect (%)",
              getOperationDropdown(SubModelDials.TURNOVER_SERVICER_EFFECT),
              getTableInput(SubModelDials.TURNOVER_SERVICER_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.TURNOVER_SERVICER_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.TURNOVER_SERVICER_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.TURNOVER_SERVICER_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.TURNOVER_SERVICER_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.TURNOVER_GEO_EFFECT:
          return {
            name: "",
            values: [
              "Geo Effect (%)",
              getOperationDropdown(SubModelDials.TURNOVER_GEO_EFFECT),
              getTableInput(SubModelDials.TURNOVER_GEO_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.TURNOVER_GEO_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.TURNOVER_GEO_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.TURNOVER_GEO_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.TURNOVER_GEO_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.INVOLUNTARY_DEFAULT_RATE:
          return {
            name: "",
            values: [
              "Default Rate (SMM)",
              getOperationDropdown(SubModelDials.INVOLUNTARY_DEFAULT_RATE),
              getTableInput(SubModelDials.INVOLUNTARY_DEFAULT_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.INVOLUNTARY_DEFAULT_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.INVOLUNTARY_DEFAULT_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.INVOLUNTARY_DEFAULT_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.INVOLUNTARY_DEFAULT_RATE, ModelTypes.PIH),
              getTableInput(SubModelDials.INVOLUNTARY_DEFAULT_RATE, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT:
          return {
            name: "",
            values: [
              "Buyout Servicer Effect (%)",
              getOperationDropdown(SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT),
              EMPTY_CELL,
              getTableInput(SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.INVOLUNTARY_GEO_EFFECT:
          return {
            name: "",
            values: [
              "Geo Effect (%)",
              getOperationDropdown(SubModelDials.INVOLUNTARY_GEO_EFFECT),
              getTableInput(SubModelDials.INVOLUNTARY_GEO_EFFECT, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.INVOLUNTARY_GEO_EFFECT, ModelTypes.FHA),
              getTableInput(SubModelDials.INVOLUNTARY_GEO_EFFECT, ModelTypes.VA),
              getTableInput(SubModelDials.INVOLUNTARY_GEO_EFFECT, ModelTypes.RHS),
              getTableInput(SubModelDials.INVOLUNTARY_GEO_EFFECT, ModelTypes.PIH),
              EMPTY_CELL,
            ],
          };

        case SubModelDials.CURTAILMENT_PREPAY_RATE:
          return {
            name: "",
            values: [
              "Prepay Rate (%)",
              getOperationDropdown(SubModelDials.CURTAILMENT_PREPAY_RATE, true),
              getTableInput(SubModelDials.CURTAILMENT_PREPAY_RATE, ModelTypes.CONVENTIONAL),
              getTableInput(SubModelDials.CURTAILMENT_PREPAY_RATE, ModelTypes.FHA),
              getTableInput(SubModelDials.CURTAILMENT_PREPAY_RATE, ModelTypes.VA),
              getTableInput(SubModelDials.CURTAILMENT_PREPAY_RATE, ModelTypes.RHS),
              getTableInput(SubModelDials.CURTAILMENT_PREPAY_RATE, ModelTypes.PIH),
              getTableInput(SubModelDials.CURTAILMENT_PREPAY_RATE, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.GPL_TAX_CREDIT:
          return {
            name: "",
            values: [
              "Tax Credit (%)",
              getOperationDropdown(SubModelDials.GPL_TAX_CREDIT),
              ...new Array(5).fill(EMPTY_CELL),
              getTableInput(SubModelDials.GPL_TAX_CREDIT, ModelTypes.PROJECT_LOAN),
            ],
          };

        case SubModelDials.GPL_REFINANCING_PENALTY:
          return {
            name: "",
            values: [
              "Refinancing Penalty (%)",
              getOperationDropdown(SubModelDials.GPL_REFINANCING_PENALTY),
              ...new Array(5).fill(EMPTY_CELL),
              getTableInput(SubModelDials.GPL_REFINANCING_PENALTY, ModelTypes.PROJECT_LOAN, {
                customDial: {
                  anchorLabel: "Penalty Rate (%)",
                  anchor: [10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
                },
              }),
            ],
          };

        case SubModelDials.GPL_TURNOVER_PENALTY:
          return {
            name: "",
            values: [
              "Turnover Penalty (%)",
              getOperationDropdown(SubModelDials.GPL_TURNOVER_PENALTY),
              ...new Array(5).fill(EMPTY_CELL),
              getTableInput(SubModelDials.GPL_TURNOVER_PENALTY, ModelTypes.PROJECT_LOAN, {
                customDial: {
                  anchorLabel: "Penalty Rate (%)",
                  anchor: [10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0],
                },
              }),
            ],
          };
        default:
          return { name: "", values: [EMPTY_CELL] };
      }
    },
    []
  );

  const getEmptyRowWithLabel = React.useCallback(
    (label: string) => ({
      name: "",
      values: [
        <Box minH="30px" key={label} verticalAlign="center">
          <Text lineHeight="30px" variant="tableHead" verticalAlign="center">
            {label}
          </Text>
        </Box>,
        ...new Array(7).fill(EMPTY_CELL),
      ],
      rowBackgroundColor: labelRowBackgroundColor,
    }),
    [labelRowBackgroundColor]
  );

  // Table data to be shown when in "edit" mode
  const editData = React.useMemo(() => {
    const tableData = [
      // Final
      getEmptyRowWithLabel(SubModelLabels.Final),
      getEmptyRow(),
      getEditModeRowData(SubModelDials.FINAL_PREPAY_RATE),
      getEmptyRow(),

      // ALL MORTGAGE RATE
      getEmptyRowWithLabel(SubModelLabels.All),
      getEmptyRow(),
      getEditModeRowData(SubModelDials.ALL_MORTGAGE_RATE),
      getEditModeRowData(SubModelDials.ALL_MIP),
      getEditModeRowData(SubModelDials.ALL_GEO_EFFECT),

      getEmptyRow(),
      getEmptyRowWithLabel(SubModelLabels.Refinancing),
      getEmptyRow(),
      // Refinancing: Prepay rate
      getEditModeRowData(SubModelDials.REFINANCING_PREPAY_RATE),
      // Refinancing: Incentive
      getEditModeRowData(SubModelDials.REFINANCING_INCENTIVE),
      // Refinancing: Burnout
      getEditModeRowData(SubModelDials.REFINANCING_BURNOUT),
      // Refinancing Media Effect
      getEditModeRowData(SubModelDials.REFINANCING_MEDIA_EFFECT),
      // Refinancing: Servicer Effect
      getEditModeRowData(SubModelDials.REFINANCING_SERVICER_EFFECT),
      // Refinancing: Geo Effect
      getEditModeRowData(SubModelDials.REFINANCING_GEO_EFFECT),

      getEmptyRow(),
      getEmptyRowWithLabel(SubModelLabels.Cashout),
      getEmptyRow(),
      // Cashout: Prepay Rate
      getEditModeRowData(SubModelDials.CASHOUT_PREPAY_RATE),
      // Cashout: Servicer Effect
      getEditModeRowData(SubModelDials.CASHOUT_SERVICER_EFFECT),
      // Cashout: Geo Effect
      getEditModeRowData(SubModelDials.CASHOUT_GEO_EFFECT),

      getEmptyRow(),
      getEmptyRowWithLabel(SubModelLabels.Turnover),
      getEmptyRow(),
      // Turnover: Prepay Rate
      getEditModeRowData(SubModelDials.TURNOVER_PREPAY_RATE),
      // Turnover: Lockin
      getEditModeRowData(SubModelDials.TURNOVER_LOCKIN),
      // Turnover: Servicer Effect
      getEditModeRowData(SubModelDials.TURNOVER_SERVICER_EFFECT),
      // Turnover: Geo Effect
      getEditModeRowData(SubModelDials.TURNOVER_GEO_EFFECT),

      getEmptyRow(),
      getEmptyRowWithLabel(SubModelLabels.Involuntary),
      getEmptyRow(),
      // Invouluntary: Default Rate
      getEditModeRowData(SubModelDials.INVOLUNTARY_DEFAULT_RATE),
      // Invouluntary: Incentive / Buyout Servicer Effect
      getEditModeRowData(SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT),
      // Invouluntary: Geo Effect
      getEditModeRowData(SubModelDials.INVOLUNTARY_GEO_EFFECT),

      getEmptyRow(),
      getEmptyRowWithLabel(SubModelLabels.Curtailment),
      getEmptyRow(),
      // Curtailment: Prepay Rate
      getEditModeRowData(SubModelDials.CURTAILMENT_PREPAY_RATE),

      getEmptyRow(),
      getEmptyRowWithLabel(SubModelLabels.Other),
      getEmptyRow(),
      // GPL: Tax Credit
      getEditModeRowData(SubModelDials.GPL_TAX_CREDIT),
      // GPL: Refinancing Penalty
      getEditModeRowData(SubModelDials.GPL_REFINANCING_PENALTY),
      // GPL: Turnover Penalty
      getEditModeRowData(SubModelDials.GPL_TURNOVER_PENALTY),
    ];
    return [
      ...tableData.map((data) => ({
        ...data,
        name: data.name,
        values: data.values.map((value, i) => wrapEditTableCell(value, i)),
      })),
    ];
  }, [getEditModeRowData, getEmptyRowWithLabel, wrapEditTableCell]);

  return (
    <FormProvider {...methods}>
      <CATable
        headers={headers}
        headersGroup={headersGroup}
        data={editData}
        headerStyles={{
          backgroundColor: "inherit",
          verticalAlign: "top",
          textAlign: "left",
        }}
        bodyStyles={{
          textAlign: "left",
          cellPadding: { py: 0 },
        }}
      />
    </FormProvider>
  );
};

export default React.forwardRef(ModelDialsEditForm);
