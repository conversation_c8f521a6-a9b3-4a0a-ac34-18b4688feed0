import { Box, SpaceProps, Text, TypographyProps } from "@chakra-ui/react";
import { HeadersGroupStyles } from "@/design-system/molecules/CATable/CATableHeader";
import { CA_Mastr_Models_v1_0_Models_ModelType, CA_Mastr_Models_v1_0_Models_SubmodelDialName } from "@/utils/openapi";
import { CA_Mastr_Models_v1_0_Models_ModelDialUnits } from "@/utils/openapi/models/CA_Mastr_Models_v1_0_Models_ModelDialUnits";

// ---- CONSTANTS ----
export const EMPTY_CELL = "";

export const tableCellLeftMargin: { [key: string]: string } = {
  // [header_column_index] : value
  "3": "2rem", // Fannie Mae & Freddie Mac
  "8": "3rem", // FHA Project Loan
};

export const SubModelDials = CA_Mastr_Models_v1_0_Models_SubmodelDialName;
export const ModelTypes = CA_Mastr_Models_v1_0_Models_ModelType;
export const defaultUnit = CA_Mastr_Models_v1_0_Models_ModelDialUnits.SMM;

// ---- HEADERS ----
const wrapHeaderCells = (text: string, index: number) => (
  <Box minW="6rem" py={1} px={0.5} mr="1.25rem" ml={tableCellLeftMargin[index] ?? null}>
    <Box maxW="10.5rem" my={0} pb="9px">
      <Text align="left" variant="tableLeft" pl={0.5} pr={0} py={0.5}>
        {text}
      </Text>
    </Box>
  </Box>
);

export const headers = [
  "",
  "Parameter",
  "Operation",
  "Fannie Mae & Freddie Mac",
  "FHA",
  "VA",
  "RHS",
  "PIH",
  "FHA Project Loan",
].map((header, i) => (header === "" ? "" : wrapHeaderCells(header, i)));

// ---- HEADER GROUPS ----
const headerGroupTextStyle: { variant: string } & TypographyProps & SpaceProps = {
  textTransform: "uppercase",
  textAlign: "left",
  whiteSpace: "nowrap",
  fontSize: "lg",
  variant: "tableHead",
  marginBottom: 3,
};

export const headersGroup: HeadersGroupStyles[] = [
  {
    displayName: "",
    colspan: 3,
  },
  {
    displayName: (
      <Text ml={tableCellLeftMargin["3"]} mr="1.5rem" {...headerGroupTextStyle}>
        Residential
      </Text>
    ),
    colspan: 5,
    padding: "1",
  },
  {
    displayName: (
      <Text ml={tableCellLeftMargin["8"]} {...headerGroupTextStyle}>
        Commercial
      </Text>
    ),
    colspan: 1,
    padding: "1",
  },
];
