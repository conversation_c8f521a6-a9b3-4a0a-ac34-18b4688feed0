import * as React from "react";
import { Box, Skeleton } from "@chakra-ui/react";
// UI components
import { useGetUserModelDialSWR } from "@/utils/swr-hooks";
import CACard from "@/design-system/molecules/CACard";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import { AccordionContentProps } from "../../../shared/AccordionTable/types";
import ModelDialsAccordionViewMode from "./ViewContent";
import ModelDialsAccordionEditMode from "./EditContent";

export const SubModelLabels: { [key: string]: string } = {
  Final: "Final",
  All: "All Submodels",
  Refinancing: "Refinancing Submodel",
  Cashout: "Cashout Submodel",
  Turnover: "Turnover Submodel",
  Involuntary: "Involuntary Submodel",
  Curtailment: "Curtailment Submodel",
  Gpl: "Other",
  Other: "Other",
};

const ModelDialsAccordionContent: React.FC<AccordionContentProps> = ({
  mode = "view",
  itemId,
  ...props
}: AccordionContentProps) => {
  const { data: response, error } = useGetUserModelDialSWR(itemId);
  const metadata = response?.user_model_dial;
  const data = response?.user_model_dial?.model_dial_data;

  return (
    <Box mr={30} ml="5.2rem">
      {error ? (
        <CACard flexGrow={1} display="flex" justifyContent="center" alignItems="center" bg="inherit">
          <CAAlertCard title={"Error"} description={error.message} status="error" />
        </CACard>
      ) : !data && mode !== "add" ? (
        <Skeleton height="25px" />
      ) : mode === "view" ? (
        <ModelDialsAccordionViewMode data={data} />
      ) : (
        <ModelDialsAccordionEditMode
          data={data}
          dialId={itemId}
          dialName={metadata?.user_model_dial_name}
          mode={mode}
          metadata={metadata ?? {}}
          {...props}
        />
      )}
    </Box>
  );
};

export default ModelDialsAccordionContent;
