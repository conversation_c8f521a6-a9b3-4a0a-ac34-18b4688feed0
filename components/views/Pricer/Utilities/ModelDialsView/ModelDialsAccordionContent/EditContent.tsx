import * as React from "react";
import { <PERSON>, HStack, I<PERSON><PERSON><PERSON><PERSON> } from "@chakra-ui/react";
import { IoClose, IoSaveSharp, IoSyncCircleOutline } from "react-icons/io5";
import { useSWRConfig } from "swr";
import {
  CA_Mastr_Api_v1_0_Models_UserModelDial_InsertUserModelDial,
  CA_Mastr_Api_v1_0_Models_UserModelDial_UpdateUserModelDial,
  CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial,
  CA_Mastr_Models_v1_0_Models_ModelDialData,
} from "@/utils/openapi";
import { usePricerModelDialsPage } from "@/contexts/PageContexts/PricerModelDialsPageContext";
import CAInput from "@/design-system/molecules/CAInput";
import ConfirmationModalPopup from "@/design-system/organisms/ConfirmationModalPopup";
import S from "@/constants/strings";
import { userModelDialsKey } from "@/utils/swr-hooks/UserModelDial";
import { AccordionContentProps } from "../../../shared/AccordionTable/types";
import ModelDialsEditForm, { ModelDialsEditFormHandler } from "./EditForm";

export interface ModelDialsAccordionEditModeProps extends Pick<AccordionContentProps, "setContentMode"> {
  data: CA_Mastr_Models_v1_0_Models_ModelDialData[] | null | undefined;
  dialId?: number;
  dialName?: string | null;
  mode: "add" | "edit";
  closeModal?: () => void;
  metadata: CA_Mastr_Api_v1_0_Models_UserModelDial_UserModelDial | undefined;
}

const ModelDialsAccordionEditMode: React.FC<ModelDialsAccordionEditModeProps> = ({
  data: modelDialData,
  mode,
  dialId,
  dialName,
  closeModal,
  metadata,
  setContentMode,
}: ModelDialsAccordionEditModeProps) => {
  // State
  const {
    action: { createModelDial, updateModelDial },
  } = usePricerModelDialsPage();
  const { mutate } = useSWRConfig();

  const [modelDialName, setModelDialName] = React.useState<string>("");
  const modelDialNameInputRef = React.useRef<HTMLInputElement>(null);
  const modelDialFormRef = React.useRef<ModelDialsEditFormHandler>(null);

  const [isConfirmationModalOpen, toggleConfirmationModal] = React.useState(false);

  React.useEffect(() => {
    let modelDialName = dialName ?? "";
    if (mode === "add" && dialId) {
      modelDialName = `Copy of ${dialName}`;
    }
    setModelDialName(modelDialName);
  }, [dialId, dialName, mode]);

  const onCancelClick = () => {
    if (modelDialFormRef.current?.isDataEdited()) {
      openEditModal();
    } else {
      closeModal?.();
    }
  };

  const resetHandler = () => {
    modelDialFormRef.current?.reset();
  };

  const submitHandler = async () => {
    const modelDialData = modelDialFormRef.current?.getSubmitData();

    if (!modelDialName.trim()) {
      modelDialNameInputRef?.current?.focus();
      return;
    }
    window.hasUnsavedChanges = false;
    if (dialId && mode !== "add") {
      const payload: CA_Mastr_Api_v1_0_Models_UserModelDial_UpdateUserModelDial = {
        user_model_dial_id: dialId,
        user_model_dial_name: modelDialName.trim() ?? dialName,
        model_dial_data: modelDialData,
      };
      await updateModelDial(payload, metadata?.updated);
      mutate(userModelDialsKey.getModelDials());
      mutate(userModelDialsKey.getModelDialById(dialId));
      setContentMode("view");
    } else {
      const payload: CA_Mastr_Api_v1_0_Models_UserModelDial_InsertUserModelDial = {
        user_model_dial_name: modelDialName.trim(),
        model_dial_data: modelDialData,
      };
      await createModelDial(payload);
      closeModal?.();
      mutate(userModelDialsKey.getModelDials());
    }
  };

  const openEditModal = () => {
    toggleConfirmationModal(true);
  };

  const closeEditModal = () => {
    toggleConfirmationModal(false);
  };

  return (
    <Box>
      <Box
        ml="-2px"
        position="absolute"
        maxHeight="3.813rem"
        top="11px"
        w={{
          base: "3rem",
          lg: "5.5rem",
          xl: "6rem",
          "2xl": "9rem",
          "3xl": "14.75rem",
        }}
      >
        <CAInput
          name="model-dial-name"
          value={modelDialName}
          ref={modelDialNameInputRef}
          onChange={(e) => setModelDialName(e.target.value)}
        />
      </Box>
      <Box position="absolute" top="12px" left={"75%"} maxWidth="13rem">
        <HStack spacing={2}>
          <IconButton
            icon={<IoSyncCircleOutline />}
            onClick={resetHandler}
            size="sm"
            fontSize="3xl"
            variant="secondary"
            title="Reset"
            aria-label="Reset"
            transform="scaleX(-1)"
          />
          <IconButton
            icon={<IoClose />}
            onClick={onCancelClick}
            size="sm"
            fontSize="xl"
            variant="secondary"
            title="Cancel"
            aria-label="Cancel"
            transform="scaleX(-1)"
          />
          <IconButton
            icon={<IoSaveSharp />}
            onClick={submitHandler}
            size="sm"
            fontSize="xl"
            variant="secondary"
            title="Save"
            aria-label="Save"
            transform="scaleX(-1)"
          />
        </HStack>
      </Box>
      <Box maxW="fit-content" w="full" mt={2} overflow="auto">
        <ModelDialsEditForm ref={modelDialFormRef} modelDialData={modelDialData} />
      </Box>
      <ConfirmationModalPopup
        isOpen={isConfirmationModalOpen}
        onModalClose={closeEditModal}
        onCancel={() => closeEditModal()}
        onConfirm={() => {
          closeModal?.();
          closeEditModal();
        }}
        headerText={S.COMMON.QUIT_EDITING_CONFIRMATION.HEADER}
        description={S.COMMON.QUIT_EDITING_CONFIRMATION.DESCRIPTION}
        showCloseIcon
      />
    </Box>
  );
};

export default ModelDialsAccordionEditMode;
