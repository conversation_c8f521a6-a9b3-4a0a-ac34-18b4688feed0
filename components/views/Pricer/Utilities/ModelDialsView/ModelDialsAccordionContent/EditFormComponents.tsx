import { Box, HStack } from "@chakra-ui/react";
import { useFormContext, useWatch } from "react-hook-form";
import { allOperations, defaultOperationValues, operation } from "@/constants/model-dials";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getUnitDropdownOptions } from "@/utils/helpers/model-dials";
import {
  CA_Mastr_Models_v1_0_Models_ModelDialData,
  CA_Mastr_Models_v1_0_Models_ModelDialUnits,
  CA_Mastr_Models_v1_0_Models_ModelType,
  CA_Mastr_Models_v1_0_Models_Operation,
  CA_Mastr_Models_v1_0_Models_SubmodelDialName,
} from "@/utils/openapi";
import DialInput, { DialInputProps } from "../DialInput";
import { ModelDialFormType } from "./EditForm";
import { defaultUnit } from "./EditConfigs";

// ----- OPERATION DROPDOWN INPUT ----- //

interface OperationDropDownProps {
  submodelDialName: CA_Mastr_Models_v1_0_Models_SubmodelDialName;
}

export const OperationDropDown = ({ submodelDialName }: OperationDropDownProps) => {
  const { register, setValue } = useFormContext();

  return (
    <Box w="5.2rem">
      <CASelectDropdown
        variant="secondary"
        options={operation?.[submodelDialName] ?? allOperations}
        {...register(`${submodelDialName}.operation`, {
          onChange: (e) => {
            if (e.target.value === CA_Mastr_Models_v1_0_Models_Operation.MULTIPLY) {
              setValue(`${submodelDialName}.units`, CA_Mastr_Models_v1_0_Models_ModelDialUnits.SMM);
            }
          },
        })}
      />
    </Box>
  );
};

// ----- MODEL DIAL INPUT ----- //
interface ModelDialInputProps {
  submodelDialName: CA_Mastr_Models_v1_0_Models_SubmodelDialName;
  modelType: CA_Mastr_Models_v1_0_Models_ModelType;
  opts?: { customDial?: DialInputProps["customDial"] };
}
export const ModelDialInput = ({ modelType, submodelDialName, opts }: ModelDialInputProps) => {
  const { setValue } = useFormContext<ModelDialFormType>();
  const operation: CA_Mastr_Models_v1_0_Models_Operation =
    useWatch({ name: `${submodelDialName}.operation` }) ?? defaultOperationValues[submodelDialName];

  const defaultValue = {
    submodel_dial_name: submodelDialName,
    model_type: modelType,
    operation: operation,
  };
  const value = useWatch({ name: `${submodelDialName}.${modelType}` }) ?? defaultValue;

  return (
    <DialInput
      name={`${submodelDialName}-${modelType}`}
      modelDial={{ ...value, operation }}
      setModelDial={(dial: CA_Mastr_Models_v1_0_Models_ModelDialData) => {
        setValue(`${submodelDialName}.${modelType}`, dial, { shouldDirty: true });
      }}
      {...(opts ?? {})}
    />
  );
};

// ----- PREPAY RATE DROPDOWN INPUT ----- //
interface PrepayRateDropdownProps {
  submodelDialName: CA_Mastr_Models_v1_0_Models_SubmodelDialName;
}
export const PrepayRateDropdown = ({ submodelDialName }: PrepayRateDropdownProps) => {
  const { register, control } = useFormContext();

  const [watchOperation] = useWatch({
    control,
    name: [`${submodelDialName}.operation`],
  });

  return (
    <HStack>
      {/* <Text variant="tableLeft" fontWeight="normal" whiteSpace="nowrap">
        Prepay Rate (%)
      </Text> */}
      <CASelectDropdown
        hideLabel
        {...register(`${submodelDialName}.units`)}
        defaultValue={defaultUnit}
        width="4.2rem"
        options={getUnitDropdownOptions(watchOperation ?? defaultOperationValues?.[submodelDialName])}
      />
    </HStack>
  );
};
