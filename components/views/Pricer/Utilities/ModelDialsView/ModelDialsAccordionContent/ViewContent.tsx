import * as React from "react";
import { Box, Text, Tooltip, useBreakpointValue, useColorModeValue } from "@chakra-ui/react";
import CATable from "@/design-system/molecules/CATable";
import { capitalizeFirstLetter } from "@/utils/helpers";
import {
  ModelDialsNormalizedDataType,
  getModelDialFieldFormattedValue,
  getUnitDropdownOptions,
  normalizeDialsData,
} from "@/utils/helpers/model-dials";
import {
  CA_Mastr_Models_v1_0_Models_ModelDialData,
  CA_Mastr_Models_v1_0_Models_SubmodelDialName,
} from "@/utils/openapi";
import { CA_Mastr_Models_v1_0_Models_ModelDialUnits } from "@/utils/openapi/models/CA_Mastr_Models_v1_0_Models_ModelDialUnits";
import { headersGroup } from "./EditConfigs";
import { SubModelLabels } from ".";

interface ModelDialsAccordionViewModeProps {
  data: CA_Mastr_Models_v1_0_Models_ModelDialData[] | null | undefined;
}

const textVariants: { [key: number]: string } = { 0: "tableLeft" };
const tableCellLeftMargin: { [key: string]: string } = {
  // [header_column_index] : value
  "2": "2rem",
  "3": "2rem", // Fannie Mae & Freddie Mac
  "4": "2rem",
  "5": "1.375rem",
  "6": "1.375rem",
  "7": "1.375rem",
  "8": "3rem", // FHA Project Loan
};
const tableCellMaxWidth: { [key: string]: string } = {
  "0": "10rem", // Parameter
  "2": "15rem", // Fannie Mae & Freddie Mac
};
const unitOptions = getUnitDropdownOptions();

const ModelDialsAccordionViewMode: React.FC<ModelDialsAccordionViewModeProps> = ({
  data,
}: ModelDialsAccordionViewModeProps) => {
  const isSmallerScreen = useBreakpointValue({ base: false, sm: true, "3xl": false });
  const SubModelDials = CA_Mastr_Models_v1_0_Models_SubmodelDialName;
  const EMPTY_CELL = "";
  const labelRowBackgroundColor = useColorModeValue("celloBlue.75", "celloBlue.900");

  const wrapTableHeaderCells = React.useCallback(
    (text: string, index: number) => (
      <Box minW={"7rem"} py={1} px={0.5} ml={tableCellLeftMargin[index] ?? null}>
        <Box maxW={"12rem"} my={0} pb="9px">
          <Text align="left" variant="tableLeft" p={0.5}>
            {text}
          </Text>
        </Box>
      </Box>
    ),
    []
  );

  const wrapTableBodyCells = React.useCallback(
    (text: string, index: number, hasTooltip?: boolean, applyTextStyling = true) => (
      <Box minW="10rem" py={1} px={0.5}>
        <Box maxW={tableCellMaxWidth[index] ?? "12rem"} my={0} ml={tableCellLeftMargin[index + 1]}>
          <Tooltip
            isDisabled={!hasTooltip || index < 2 || text.length < 10}
            label={text}
            aria-label={text}
            placement="bottom"
          >
            <Text
              align="left"
              variant={applyTextStyling ? textVariants[index] ?? "default" : "default"}
              fontWeight="normal"
              textOverflow="ellipsis"
              whiteSpace={index === 0 ? undefined : "nowrap"}
              overflow="hidden"
              p={0.5}
            >
              {text}
            </Text>
          </Tooltip>
        </Box>
      </Box>
    ),
    []
  );

  const headers = [
    "",
    "Parameter",
    "Operation",
    "Fannie Mae & Freddie Mac",
    "FHA",
    "VA",
    "RHS",
    "PIH",
    "FHA Project Loan",
  ].map((header, i) => (header === "" ? "" : wrapTableHeaderCells(header, i)));

  const getViewModeRowData = React.useCallback(
    (
      submodelName: CA_Mastr_Models_v1_0_Models_SubmodelDialName,
      modelDialsData: ModelDialsNormalizedDataType | undefined
    ) => {
      if (!modelDialsData) {
        return { name: "no_data", values: [""] };
      }

      const operation = capitalizeFirstLetter(modelDialsData?.operation?.toString());
      const unit =
        unitOptions?.find(
          (unit) => unit.value === (modelDialsData?.units as unknown as CA_Mastr_Models_v1_0_Models_ModelDialUnits)
        )?.displayValue ?? unitOptions?.find((unit) => unit.isDefault)?.displayValue;
      const conventional = getModelDialFieldFormattedValue(modelDialsData?.["CONVENTIONAL"]);
      const FHA = getModelDialFieldFormattedValue(modelDialsData?.["FHA"]);
      const VA = getModelDialFieldFormattedValue(modelDialsData?.["VA"]);
      const RHS = getModelDialFieldFormattedValue(modelDialsData?.["RHS"]);
      const PIH = getModelDialFieldFormattedValue(modelDialsData?.["PIH"]);
      const PROJECT_LOAN = getModelDialFieldFormattedValue(modelDialsData?.["PROJECT_LOAN"]);

      const tableValues = [];
      if (FHA || VA || RHS || PIH || conventional || PROJECT_LOAN) {
        tableValues.push(conventional ?? "-", FHA ?? "-", VA ?? "-", RHS ?? "-", PIH ?? "-", PROJECT_LOAN ?? "-");
      }

      switch (submodelName) {
        case SubModelDials.FINAL_PREPAY_RATE:
          tableValues.unshift(`${operation} (${unit})`);
          tableValues.unshift("Prepay Rate (%)");
          break;

        case SubModelDials.ALL_MORTGAGE_RATE:
          tableValues.unshift(operation);
          tableValues.unshift("Mortgage Rate (%)");
          break;

        case SubModelDials.ALL_MIP:
          tableValues.unshift(operation);
          tableValues.unshift("FHA Annual MIP (bp)");
          break;

        case SubModelDials.ALL_GEO_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Geo Effect for HPA and Mortgage Tax (%)");
          break;

        case SubModelDials.REFINANCING_PREPAY_RATE:
          tableValues.unshift(`${operation} (${unit})`);
          tableValues.unshift("Prepay Rate (%)");
          break;

        case SubModelDials.REFINANCING_INCENTIVE:
          tableValues.unshift(operation);
          tableValues.unshift("Incentive (bp)");
          break;

        case SubModelDials.REFINANCING_BURNOUT:
          tableValues.unshift(operation);
          tableValues.unshift("Burnout (bp)");
          break;

        case SubModelDials.REFINANCING_MEDIA_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Media Effect (%)");
          break;

        case SubModelDials.REFINANCING_SERVICER_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Servicer Effect (%)");
          break;

        case SubModelDials.REFINANCING_GEO_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Geo Effect (%)");
          break;

        case SubModelDials.CASHOUT_PREPAY_RATE:
          tableValues.unshift(`${operation} (${unit})`);
          tableValues.unshift("Prepay Rate (%)");
          break;

        case SubModelDials.CASHOUT_SERVICER_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Servicer Effect (%)");
          break;

        case SubModelDials.CASHOUT_GEO_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Geo Effect (%)");
          break;

        case SubModelDials.TURNOVER_PREPAY_RATE:
          tableValues.unshift(`${operation} (${unit})`);
          tableValues.unshift("Prepay Rate (%)");
          break;

        case SubModelDials.TURNOVER_LOCKIN:
          tableValues.unshift(operation);
          tableValues.unshift("Lockin (%)");
          break;

        case SubModelDials.TURNOVER_SERVICER_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Servicer Effect (%)");
          break;

        case SubModelDials.TURNOVER_GEO_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Geo Effect (%)");
          break;

        case SubModelDials.INVOLUNTARY_DEFAULT_RATE:
          tableValues.unshift(operation);
          tableValues.unshift("Default Rate (SMM)");
          break;

        case SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Buyout Servicer Effect (%)");
          break;

        case SubModelDials.INVOLUNTARY_GEO_EFFECT:
          tableValues.unshift(operation);
          tableValues.unshift("Geo Effect (%)");
          break;

        case SubModelDials.CURTAILMENT_PREPAY_RATE:
          tableValues.unshift(`${operation} (${unit})`);
          tableValues.unshift("Prepay Rate (%)");
          break;

        case SubModelDials.GPL_TAX_CREDIT:
          tableValues.unshift(operation);
          tableValues.unshift("Tax Credit (%)");
          break;

        case SubModelDials.GPL_REFINANCING_PENALTY:
          tableValues.unshift(operation);
          tableValues.unshift("Refinancing Penalty (%)");
          break;

        case SubModelDials.GPL_TURNOVER_PENALTY:
          tableValues.unshift(operation);
          tableValues.unshift("Turnover Penalty (%)");
          break;
      }

      return {
        name: "",
        values: tableValues?.map((value, i) => wrapTableBodyCells(value ? value.toString() : "", i, true)),
      };
    },
    [SubModelDials, wrapTableBodyCells]
  );

  const getEmptyRowWithLabel = React.useCallback(
    (label: string, isDataAvailable: boolean) => {
      if (!isDataAvailable) {
        return [{ name: "no_data", values: [] }];
      }
      return [
        {
          name: <Box minH="0.2rem"></Box>,
          values: [],
        },
        {
          name: "",
          values: [
            <Box minW="10rem" maxW="13rem" minH="25px" key={label} verticalAlign="center" px={1.5}>
              <Text lineHeight="25px" variant="tableHead" verticalAlign="center">
                {label}
              </Text>
            </Box>,
            ...new Array(7).fill(EMPTY_CELL),
          ],
          rowBackgroundColor: labelRowBackgroundColor,
        },
        {
          name: <Box minH="0.2rem"></Box>,
          values: [],
        },
      ];
    },
    [labelRowBackgroundColor]
  );

  // table data to be shown when in "view" mode
  const viewData = React.useMemo(() => {
    if (!data || data.length === 0)
      return [
        {
          name: "",
          values: [...new Array(8).fill("-")].map((cell, i) => wrapTableBodyCells(cell, i, false, false)),
        },
      ];

    // Prepare table rows data
    const normalizedData: ModelDialsNormalizedDataType = normalizeDialsData(data);
    const normalizedDataKeys = Object.keys(normalizedData).map((key) => key.split("_")[0]);

    const tableData = [
      ...getEmptyRowWithLabel(SubModelLabels.Final, normalizedDataKeys.includes("final")),
      // FINAL PREPAY RATE
      getViewModeRowData(SubModelDials.FINAL_PREPAY_RATE, normalizedData?.[SubModelDials.FINAL_PREPAY_RATE]),

      ...getEmptyRowWithLabel(SubModelLabels.All, normalizedDataKeys.includes("all")),
      // ALL MORTGAGE RATE
      getViewModeRowData(SubModelDials.ALL_MORTGAGE_RATE, normalizedData?.[SubModelDials.ALL_MORTGAGE_RATE]),
      getViewModeRowData(SubModelDials.ALL_MIP, normalizedData?.[SubModelDials.ALL_MIP]),
      getViewModeRowData(SubModelDials.ALL_GEO_EFFECT, normalizedData?.[SubModelDials.ALL_GEO_EFFECT]),

      ...getEmptyRowWithLabel(SubModelLabels.Refinancing, normalizedDataKeys.includes("refinancing")),
      // Refinancing: Prepay rate
      getViewModeRowData(
        SubModelDials.REFINANCING_PREPAY_RATE,
        normalizedData?.[SubModelDials.REFINANCING_PREPAY_RATE]
      ),
      // Refinancing: Incentive
      getViewModeRowData(SubModelDials.REFINANCING_INCENTIVE, normalizedData?.[SubModelDials.REFINANCING_INCENTIVE]),
      // Refinancing: Burnout
      getViewModeRowData(SubModelDials.REFINANCING_BURNOUT, normalizedData?.[SubModelDials.REFINANCING_BURNOUT]),
      // Refinancing Media Effect
      getViewModeRowData(
        SubModelDials.REFINANCING_MEDIA_EFFECT,
        normalizedData?.[SubModelDials.REFINANCING_MEDIA_EFFECT]
      ),
      // Refinancing: Servicer Effect
      getViewModeRowData(
        SubModelDials.REFINANCING_SERVICER_EFFECT,
        normalizedData?.[SubModelDials.REFINANCING_SERVICER_EFFECT]
      ),
      // Refinancing: Geo Effect
      getViewModeRowData(SubModelDials.REFINANCING_GEO_EFFECT, normalizedData?.[SubModelDials.REFINANCING_GEO_EFFECT]),

      ...getEmptyRowWithLabel(SubModelLabels.Cashout, normalizedDataKeys.includes("cashout")),
      // Cashout: Prepay Rate
      getViewModeRowData(SubModelDials.CASHOUT_PREPAY_RATE, normalizedData?.[SubModelDials.CASHOUT_PREPAY_RATE]),
      // Cashout: Servicer Effect
      getViewModeRowData(
        SubModelDials.CASHOUT_SERVICER_EFFECT,
        normalizedData?.[SubModelDials.CASHOUT_SERVICER_EFFECT]
      ),
      // Cashout: Geo Effect
      getViewModeRowData(SubModelDials.CASHOUT_GEO_EFFECT, normalizedData?.[SubModelDials.CASHOUT_GEO_EFFECT]),

      ...getEmptyRowWithLabel(SubModelLabels.Turnover, normalizedDataKeys.includes("turnover")),
      // Turnover: Prepay Rate
      getViewModeRowData(SubModelDials.TURNOVER_PREPAY_RATE, normalizedData?.[SubModelDials.TURNOVER_PREPAY_RATE]),
      // Turnover: Lockin
      getViewModeRowData(SubModelDials.TURNOVER_LOCKIN, normalizedData?.[SubModelDials.TURNOVER_LOCKIN]),
      // Turnover: Servicer Effect
      getViewModeRowData(
        SubModelDials.TURNOVER_SERVICER_EFFECT,
        normalizedData?.[SubModelDials.TURNOVER_SERVICER_EFFECT]
      ),
      // Turnover: Geo Effect
      getViewModeRowData(SubModelDials.TURNOVER_GEO_EFFECT, normalizedData?.[SubModelDials.TURNOVER_GEO_EFFECT]),

      ...getEmptyRowWithLabel(SubModelLabels.Involuntary, normalizedDataKeys.includes("involuntary")),
      // Invouluntary: Default Rate
      getViewModeRowData(
        SubModelDials.INVOLUNTARY_DEFAULT_RATE,
        normalizedData?.[SubModelDials.INVOLUNTARY_DEFAULT_RATE]
      ),
      // Invouluntary: Incentive / Buyout Servicer Effect
      getViewModeRowData(
        SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT,
        normalizedData?.[SubModelDials.INVOLUNTARY_BUYOUT_SERVICER_EFFECT]
      ),
      // Invouluntary: Geo Effect
      getViewModeRowData(SubModelDials.INVOLUNTARY_GEO_EFFECT, normalizedData?.[SubModelDials.INVOLUNTARY_GEO_EFFECT]),

      ...getEmptyRowWithLabel(SubModelLabels.Curtailment, normalizedDataKeys.includes("curtailment")),
      // Curtailment: Prepay Rate
      getViewModeRowData(
        SubModelDials.CURTAILMENT_PREPAY_RATE,
        normalizedData?.[SubModelDials.CURTAILMENT_PREPAY_RATE]
      ),

      ...getEmptyRowWithLabel(SubModelLabels.Other, normalizedDataKeys.includes("gpl")),
      // GPL: Tax Credit
      getViewModeRowData(SubModelDials.GPL_TAX_CREDIT, normalizedData?.[SubModelDials.GPL_TAX_CREDIT]),
      // GPL: Refinancing Penalty
      getViewModeRowData(
        SubModelDials.GPL_REFINANCING_PENALTY,
        normalizedData?.[SubModelDials.GPL_REFINANCING_PENALTY]
      ),
      // GPL: Turnover Penalty
      getViewModeRowData(SubModelDials.GPL_TURNOVER_PENALTY, normalizedData?.[SubModelDials.GPL_TURNOVER_PENALTY]),
    ].filter((row) => row.name !== "no_data");

    return tableData;
  }, [data, getViewModeRowData, SubModelDials, wrapTableBodyCells, getEmptyRowWithLabel]);

  return (
    <Box maxW="fit-content" w={isSmallerScreen ? "100%" : "95%"} overflow="auto">
      <CATable
        headers={headers}
        headersGroup={headersGroup}
        data={viewData}
        headerStyles={{
          verticalAlign: "top",
          backgroundColor: "inherit",
          textAlign: "left",
          groupHeaderFontSize: "md",
        }}
        bodyStyles={{
          textAlign: "left",
        }}
      />
    </Box>
  );
};

export default React.memo(ModelDialsAccordionViewMode);
