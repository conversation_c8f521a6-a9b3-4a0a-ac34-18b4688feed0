import * as React from "react";
import { Box } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { PricerModuleProps } from "../..";
import PricerHeader from "../../PricerHeader";
import ModelDialsViewBody from "./ModelDialsViewBody";

const PricerUtilitiesModelDialsView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  return (
    <Box>
      <PricerHeader title={pageTitle} withRun={false} />
      <MainInputContentTemplate>
        <ModelDialsViewBody />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerUtilitiesModelDialsView;
