import * as React from "react";
import { Box, Button, Center, Flex, HStack, Icon, Spacer, Spinner, Text, useColorModeValue } from "@chakra-ui/react";
import { IoAddCircleSharp } from "react-icons/io5";
import CACard, { CACardWithExpand } from "@/design-system/molecules/CACard";
import { usePricerModelDialsPage } from "@/contexts/PageContexts/PricerModelDialsPageContext";
import { canDelete, canEdit, canShare, getFormattedLocaleDate, getFormattedTime } from "@/utils/helpers";
import { useGetUserModelDialsSWR } from "@/utils/swr-hooks";
import { useKeywordsSearch } from "@/hooks/useKeywordsSearch";
import CAAlertCard from "@/design-system/molecules/CAAlertCard";
import CASearch from "@/design-system/molecules/CASearch";
import { CA_Mastr_Models_v1_0_Models_Entity } from "@/utils/openapi";
import AccordionTable from "../../shared/AccordionTable/AccordionTable";
import { ItemProps } from "../../shared/AccordionTable/types";
import { actionColumns, toggleColumn } from "../../shared/AccordionTable/constants";
import ModelDialsAccordionContent from "./ModelDialsAccordionContent";

const headers = [
  toggleColumn,
  {
    name: "Dial",
    maxWidth: "600px",
    useMaxAsMin: true,
  },
  {
    name: "Created by",
    maxWidth: "10rem",
    useMaxAsMin: true,
  },
  {
    name: "Created",
    maxWidth: "10rem",
    useMaxAsMin: true,
  },
  {
    name: "Updated",
    maxWidth: "10rem",
    useMaxAsMin: true,
  },
  ...actionColumns,
];

const ModelDialsViewBody: React.FC = () => {
  const {
    state: { lastSavedModelDialId },
    action: { removeModelDialFromOpenState, deleteModelDial },
  } = usePricerModelDialsPage();
  const { data, error, mutate, isLoading } = useGetUserModelDialsSWR();

  const [addMode, setAddMode] = React.useState(false);
  const [currentAddModeData, setCurrentAddModeData] = React.useState<null | {
    id: number | undefined;
    name: string | null | undefined;
  }>(null);

  React.useEffect(() => {
    if (addMode) {
      setAddMode(false);
      setCurrentAddModeData(null);
    }

    // when the page updates, clear the history/state of dial which needs to stay open
    setTimeout(() => removeModelDialFromOpenState(), 2000);
  }, [lastSavedModelDialId]); // eslint-disable-line react-hooks/exhaustive-deps

  const userModelDials = data?.user_model_dials;

  const accordionData: ItemProps[] = React.useMemo(() => {
    if (userModelDials) {
      return userModelDials?.map((modelDial) => {
        return {
          values: [
            modelDial.user_model_dial_name ?? "",
            `${modelDial.user?.firstName ?? ""} ${modelDial.user?.lastName ?? ""}`,
            `${getFormattedLocaleDate(modelDial.inserted)} ${getFormattedTime(modelDial.inserted)}`,
            `${getFormattedLocaleDate(modelDial.updated)} ${getFormattedTime(modelDial.updated)}`,
          ],
          createdBy: `${modelDial.user?.firstName ?? ""} ${modelDial.user?.lastName ?? ""}`,
          content: ModelDialsAccordionContent,
          can_edit: canEdit(modelDial.role),
          can_delete: canDelete(modelDial.role),
          can_share: canShare(modelDial.role),
          mode: "view",
          itemId: modelDial.user_model_dial_id,
          itemName: modelDial.user_model_dial_name,
          duplicateDial: () => {
            setAddMode(true);
            setCurrentAddModeData({ id: modelDial.user_model_dial_id, name: modelDial.user_model_dial_name });
          },
          entity: CA_Mastr_Models_v1_0_Models_Entity.USER_MODEL_DIAL,
        };
      });
    }
    return [];
  }, [userModelDials]);

  const list: ItemProps[] = addMode
    ? [
        {
          toggleAddMode: () => {
            setAddMode(false);
            setCurrentAddModeData(null);
          },
          content: ModelDialsAccordionContent,
          mode: "add",
          itemId: currentAddModeData?.id,
          values: [...new Array(5).fill("")],
          entity: CA_Mastr_Models_v1_0_Models_Entity.USER_MODEL_DIAL,
        },
        ...accordionData,
      ]
    : accordionData;

  const { filteredList, getInputProps } = useKeywordsSearch<ItemProps>({
    getSearchableText: (item) => `${item.itemName} ${item.createdBy}`,
    additionalFilterCheck: (item) => item.mode === "add",
    list,
  });

  return (
    <CACardWithExpand
      cardStyleOnOpen={{
        h: "full",
        minHeight: "calc(100vh - 11.9rem)",
        overflowY: "auto",
        width: {
          base: "100%",
          md: "100%",
          lg: "100%",
          xl: "calc(100vw - 260px)",
          "2xl": "calc(100vw - 260px)",
        },
      }}
      cardBodyStyle={{
        minHeight: "calc(100vh - 14.9rem)",
      }}
      title=" "
      allowCollapse
      cardKey="model-dials-view"
      pb={0}
      borderWidth={useColorModeValue("0", "0.5px")}
    >
      {error ? (
        <CACard flexGrow={1} display="flex" justifyContent="center" alignItems="center">
          <CAAlertCard title={"Error"} description={error.message} status="error" />
        </CACard>
      ) : isLoading ? (
        <Center mt="20rem">
          <HStack>
            <Spinner mr={4} emptyColor="celloBlue.200" />
            <Text fontSize="2xl" fontWeight="bold" textAlign="center">
              Loading...
            </Text>
          </HStack>
        </Center>
      ) : (
        <Box>
          <Flex>
            <CASearch
              maxW="12.5rem"
              name="model-dials-search"
              placeholder="Quick Filter"
              isDisabled={!data}
              {...getInputProps()}
            />
            <Spacer />
            <Button
              height={9}
              leftIcon={<Icon as={IoAddCircleSharp} fontSize="20" />}
              variant="iconButton"
              isDisabled={addMode}
              onClick={() => setAddMode(true)}
            >
              <Text color="inherit">New</Text>
            </Button>
          </Flex>

          <Box mt="5" mx={-4}>
            <AccordionTable
              onDelete={(itemId, itemName) =>
                deleteModelDial(
                  itemId,
                  itemName,
                  userModelDials?.find((dial) => dial.user_model_dial_id === itemId)?.updated,
                  mutate
                )
              }
              lastSavedItemId={lastSavedModelDialId}
              data={filteredList}
              headers={headers}
              minWidth={"max-content"}
            />
          </Box>
        </Box>
      )}
    </CACardWithExpand>
  );
};

export default ModelDialsViewBody;
