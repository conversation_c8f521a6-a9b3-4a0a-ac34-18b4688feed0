import * as React from "react";
import { Controller, useForm } from "react-hook-form";
import { Text } from "@chakra-ui/layout";
import {
  Box,
  Button,
  Flex,
  HStack,
  Popover,
  PopoverArrow,
  PopoverCloseButton,
  <PERSON>overContent,
  <PERSON>overTrigger,
  Portal,
  Stack,
  Tooltip,
  chakra,
} from "@chakra-ui/react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CAInput from "@/design-system/molecules/CAInput";
import { showErrorToast } from "@/design-system/theme/toast";
import {
  CA_Mastr_Models_v1_0_Models_AnchorType,
  CA_Mastr_Models_v1_0_Models_Interpolation,
  CA_Mastr_Models_v1_0_Models_ModelDialData,
} from "@/utils/openapi";
import {
  getAnchorDisplayValueByKey,
  getModelDialFieldFormattedValue,
  getModelDialFromDisplayValue,
  getParameterFromSubmodel,
} from "@/utils/helpers/model-dials";
import { capitalizeFirstLetter, parseStringToDate, sortAscendingByField } from "@/utils/helpers";
import CADateInput from "@/design-system/molecules/CADateInput";
import ValueDisplay from "@/components/helpers/ValueDisplay";
import CACheckboxInput from "@/design-system/molecules/CACheckboxInput";
import { SubModelLabels } from "./ModelDialsAccordionContent";

export type DialInputProps = {
  name: string;
  modelDial: CA_Mastr_Models_v1_0_Models_ModelDialData;
  setModelDial: (modelDial: CA_Mastr_Models_v1_0_Models_ModelDialData) => void;
  customDial?: { anchorLabel: string; anchor: number[] };
};

type dialInputFormProps = {
  extrapolate: boolean;
  anchorType?: CA_Mastr_Models_v1_0_Models_AnchorType;
  interpolation?: CA_Mastr_Models_v1_0_Models_Interpolation;
  inputArr: { field1: number | undefined; field2: string | number | undefined }[];
};

const DialInput = React.forwardRef<HTMLInputElement, DialInputProps>(
  ({ name, modelDial, setModelDial, customDial }: DialInputProps, ref) => {
    const [isOpen, setIsOpen] = React.useState(false);
    const [closePopoverOnBlur, toggleClosePopoverOnBlur] = React.useState(true);
    const [modelDialInputValue, setModelDialInputValue] = React.useState<string | undefined>("");
    const [isConstant, setIsConstant] = React.useState(false);
    const { register, handleSubmit, reset, setValue, setFocus, watch, control } = useForm<dialInputFormProps>();
    const [watch_anchorType] = watch(["anchorType"]);

    const [modalInputs, setModalInputs] = React.useState<dialInputFormProps["inputArr"]>([
      {
        field1: undefined,
        field2: undefined,
      },
    ]);

    React.useEffect(() => {
      if (modelDial) {
        if (!modelDial.value && !modelDial.anchor) {
          setModelDialInputValue("");
        } else if (modelDial.value || modelDial.anchor) {
          const formattedModelDialValue = getModelDialFieldFormattedValue(modelDial);
          setModelDialInputValue(formattedModelDialValue);
          const isConstantType = modelDial.value && !modelDial.anchor ? true : false;
          setValue(
            "anchorType",
            modelDial.anchor_type ??
              (customDial
                ? CA_Mastr_Models_v1_0_Models_AnchorType.CUSTOM
                : CA_Mastr_Models_v1_0_Models_AnchorType.MONTH)
          );
          setValue("extrapolate", modelDial.extrapolate ?? true);
          if (!customDial) {
            setValue("interpolation", modelDial.interpolation ?? CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR);
          }
          setIsConstant(isConstantType);
        }
      }
    }, [modelDial, setValue, setModelDial, customDial]);

    const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newModalInputs = [...modalInputs];
      const lastIndex = newModalInputs.length - 1;
      const touchedFieldIndex = e.target.name.split(".")[1];
      const touchedField = e.target.name.split(".")[2];
      if (touchedField === "field1") {
        newModalInputs[parseInt(touchedFieldIndex)].field1 = e.target.value ? parseInt(e.target.value) : undefined;
      } else if (touchedField === "field2") {
        newModalInputs[parseFloat(touchedFieldIndex)].field2 = e.target.value ?? undefined;
      }

      if (touchedField === "field2" && parseInt(touchedFieldIndex) === lastIndex && e.target.value && !customDial) {
        newModalInputs.push({
          field1: undefined,
          field2: undefined,
        });
        setModalInputs(newModalInputs);
      }

      setValue("inputArr", newModalInputs);
    };

    const onInputBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newModalInputs = [...modalInputs];
      const lastIndex = newModalInputs.length - 1;
      const touchedFieldIndex = e.target.name.split(".")[1];
      const touchedFieldName = e.target.name.split(".")[2];
      const touchedField1Value = newModalInputs[parseInt(touchedFieldIndex)].field1;
      const touchedField2Value = newModalInputs[parseInt(touchedFieldIndex)].field2;

      if (
        newModalInputs[parseInt(touchedFieldIndex)].field1 === undefined &&
        newModalInputs[parseInt(touchedFieldIndex)].field2 === undefined &&
        parseInt(touchedFieldIndex) !== lastIndex
      ) {
        newModalInputs.splice(parseInt(touchedFieldIndex), 1);
      }

      if (touchedFieldName === "field1") {
        const sortedInputs = sortAscendingByField([...newModalInputs], "field1");
        const lastInput = sortedInputs[sortedInputs.length - 1];
        const isLastRowEmpty = !lastInput.field1 && !lastInput.field2;
        const updatedTouchedFieldIndexAfterSort = sortedInputs.findIndex((input) => {
          if (touchedField2Value) {
            return input.field1 === touchedField1Value && input.field2 === touchedField2Value;
          } else {
            return input.field1 === touchedField1Value && isNaN(input.field2);
          }
        });
        if (updatedTouchedFieldIndexAfterSort !== parseInt(touchedFieldIndex) && !isLastRowEmpty) {
          sortedInputs.push({
            field1: undefined,
            field2: undefined,
          });
        }
        setModalInputs(sortedInputs);
        setValue("inputArr", sortedInputs);
        if (updatedTouchedFieldIndexAfterSort !== -1) {
          setFocus(`inputArr.${updatedTouchedFieldIndexAfterSort}.field2`);
        }
      } else {
        setModalInputs(newModalInputs);
        setValue("inputArr", newModalInputs);
      }
    };

    const onFactorDateChange = React.useCallback(
      (factorDate: Date, onChange: (date: Date) => void, name: string, fromSelect = false) => {
        onChange(factorDate);
        const newModalInputs = [...modalInputs];
        const touchedFieldIndex = name.split(".")[1];
        const touchedFieldName = name.split(".")[2];
        const lastIndex = newModalInputs.length - 1;
        newModalInputs[parseInt(touchedFieldIndex)].field1 = factorDate
          ? factorDate.getFullYear() * 100 + (factorDate.getMonth() + 1)
          : undefined;
        const touchedField1Value = newModalInputs[parseInt(touchedFieldIndex)].field1;
        const touchedField2Value = newModalInputs[parseInt(touchedFieldIndex)].field2;

        if (parseInt(touchedFieldIndex) === lastIndex && !customDial) {
          newModalInputs.push({
            field1: undefined,
            field2: undefined,
          });
        }

        if (touchedFieldName === "field1" && fromSelect) {
          const sortedInputs = sortAscendingByField([...newModalInputs], "field1");
          const updatedTouchedFieldIndexAfterSort = sortedInputs.findIndex(
            (input) => input.field1 === touchedField1Value && input.field2 === touchedField2Value
          );
          setValue("inputArr", sortedInputs);
          setModalInputs(sortedInputs);
          updatedTouchedFieldIndexAfterSort !== -1 && setFocus(`inputArr.${updatedTouchedFieldIndexAfterSort}.field2`);
        } else {
          setValue("inputArr", [...newModalInputs]);
          setModalInputs([...newModalInputs]);
        }
      },
      [customDial, modalInputs, setFocus, setValue]
    );

    const onFactorDateBlur = (name: string) => {
      const newModalInputs = [...modalInputs];
      const touchedFieldIndex = name.split(".")[1];
      const touchedField1Value = newModalInputs[parseInt(touchedFieldIndex)].field1;
      const touchedField2Value = newModalInputs[parseInt(touchedFieldIndex)].field2;
      const sortedInputs = sortAscendingByField([...newModalInputs], "field1");
      const updatedTouchedFieldIndexAfterSort = sortedInputs.findIndex(
        (input) => input.field1 === touchedField1Value && input.field2 === touchedField2Value
      );
      setModalInputs(sortedInputs);
      setValue("inputArr", [...sortedInputs]);
      updatedTouchedFieldIndexAfterSort !== -1 && setFocus(`inputArr.${updatedTouchedFieldIndexAfterSort}.field2`);
    };

    const onCalendarClose = () => {
      setTimeout(() => {
        toggleClosePopoverOnBlur(true);
      }, 100);
    };

    const getInputFields = () => {
      return modalInputs.map((_, index) => {
        return (
          <HStack spacing="5" key={`input-${index}`}>
            {watch_anchorType === CA_Mastr_Models_v1_0_Models_AnchorType.FACTOR_MONTH ? (
              <Box w="5.5rem">
                <Controller
                  key={`inputArr.${index}.field1`}
                  name={`inputArr.${index}.field1`}
                  control={control}
                  render={({ field: { name, value, onChange } }) => {
                    let dateValue: Date | undefined = undefined;
                    const factorYear = value?.toString().substring(0, 4);
                    const factorMonth = value?.toString().substring(4, 6);
                    if (factorYear && factorMonth) {
                      dateValue = parseStringToDate(`${factorYear}-${factorMonth}-01`);
                    }

                    return (
                      <CADateInput
                        showMonthYearPicker
                        onCalendarOpen={() => toggleClosePopoverOnBlur(false)}
                        onCalendarClose={onCalendarClose}
                        autoComplete="off"
                        popperPlacement="bottom-end"
                        selectedDate={dateValue}
                        onChange={(date) =>
                          date && onFactorDateChange(date, onChange, `inputArr.${index}.field1`, false)
                        }
                        onSelect={(date) => onFactorDateChange(date, onChange, `inputArr.${index}.field1`, true)}
                        onBlur={() => {
                          setTimeout(() => {
                            onFactorDateBlur(`inputArr.${index}.field1`);
                          }, 100);
                        }}
                        name={name}
                      />
                    );
                  }}
                />
              </Box>
            ) : (
              <Box w="5.5rem">
                {!customDial ? (
                  <CAInput
                    type="number"
                    key={`inputArr.${index}.field1`}
                    {...register(`inputArr.${index}.field1`, {
                      valueAsNumber: true,
                    })}
                    onChange={onInputChange}
                    onBlur={onInputBlur}
                    {...{
                      autoComplete: "off",
                    }}
                  />
                ) : (
                  <Text variant="tableLeft" textAlign="right">
                    {modalInputs[index].field1}
                  </Text>
                )}
              </Box>
            )}
            <Box w="7rem">
              <CAInput
                type="number"
                step="any"
                key={`inputArr.${index}.field2`}
                {...register(`inputArr.${index}.field2`, {
                  valueAsNumber: true,
                })}
                onChange={(e) => {
                  if (!e.target.value?.endsWith(".")) {
                    onInputChange(e);
                  }
                }}
                {...{
                  autoComplete: "off",
                }}
                onBlur={onInputBlur}
              />
            </Box>
          </HStack>
        );
      });
    };

    const onSubmit = handleSubmit((data) => {
      let newModelDial: CA_Mastr_Models_v1_0_Models_ModelDialData = {
        ...modelDial,
        extrapolate: data.extrapolate,
        interpolation: data.interpolation,
        anchor_type: data.anchorType,
        anchor: [],
        value: [],
      };

      let hasError = false;
      let errorMessage = "Invalid Format";
      const lastIndex = modalInputs.length - 1;

      if (
        lastIndex === 1 &&
        !modalInputs[0].field1 &&
        (modalInputs[0].field2 || modalInputs[0].field2 === 0) &&
        !modalInputs[lastIndex].field1 &&
        !modalInputs[lastIndex].field2
      ) {
        // value is constant
        newModelDial = {
          value: [+modalInputs[0].field2],
        };
      } else {
        // value is not constant
        modalInputs.map((item) => {
          if (
            typeof item.field1 === "number" &&
            (typeof item.field2 === "number" || typeof item.field2 === "string") &&
            !isNaN(+item.field2) &&
            !isNaN(item.field1)
          ) {
            newModelDial.anchor?.push(item.field1);
            newModelDial.value?.push(+item.field2);
          }
        });
      }

      // check vectors have at least two entries of anchor/value pair
      const anchorLength = newModelDial?.anchor?.length ?? 0;
      const valueLength = newModelDial?.value?.length ?? 0;
      const isConstantType = isConstant || (valueLength === 1 && anchorLength === 0);
      if (!isConstantType && valueLength < 2) {
        hasError = true;
        errorMessage = "Please enter at least two vector items.";
      }

      // check if all anchor values are unique
      const anchors = modalInputs.map((input) => input.field1);
      const areAnchorsUnique = !anchors.find((n, i) => anchors.indexOf(n) !== i);
      if (!areAnchorsUnique) {
        const anchorDisplayName = getAnchorDisplayValueByKey(watch_anchorType);
        errorMessage = `Please enter unique "${anchorDisplayName}" values.`;
        hasError = true;
      }

      if (hasError) {
        showErrorToast("Error", errorMessage);
      } else {
        setModelDial(newModelDial);
        setIsOpen(false);
      }
    });

    const onPopoverClose = () => {
      reset();
      const inputFields = {
        field1: undefined,
        field2: undefined,
      };
      setModalInputs([inputFields]);
      setIsOpen(false);
    };

    const onModelDialInputValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setModelDialInputValue(e.target.value);
    };

    const onVectorClick = () => {
      setValue(
        "anchorType",
        modelDial.anchor_type ??
          (customDial ? CA_Mastr_Models_v1_0_Models_AnchorType.CUSTOM : CA_Mastr_Models_v1_0_Models_AnchorType.MONTH)
      );
      setValue("extrapolate", modelDial.extrapolate ?? true);
      if (!customDial) {
        setValue("interpolation", modelDial.interpolation ?? CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR);
      }
      const inputFieldArr: { field1: number | undefined; field2: number | undefined }[] = [];
      const anchorArr = modelDial.anchor ?? [];
      const valueArr = modelDial.value ?? [];

      if (!customDial) {
        if (anchorArr.length) {
          for (let i = 0; i < anchorArr.length; i++) {
            inputFieldArr.push({
              field1: anchorArr[i] ?? undefined,
              field2: valueArr[i] ?? undefined,
            });
          }
        } else if (valueArr.length) {
          for (let i = 0; i < valueArr.length; i++) {
            inputFieldArr.push({
              field1: anchorArr[i] ?? undefined,
              field2: valueArr[i] ?? undefined,
            });
          }
        }
        inputFieldArr.push({
          field1: undefined,
          field2: undefined,
        });
      } else {
        for (let i = 0; i < customDial.anchor.length; i++) {
          const anchor = customDial.anchor[i];
          inputFieldArr.push({
            field1: anchor,
            field2: anchorArr.indexOf(anchor) > -1 ? valueArr[anchorArr.indexOf(anchor)] ?? undefined : undefined,
          });
        }
      }

      setModalInputs([...inputFieldArr]);
      setValue("inputArr", [...inputFieldArr]);
      setIsOpen(true);
    };

    const onModelDialInputBlur = () => {
      if (modelDialInputValue) {
        const parsedModelDial = getModelDialFromDisplayValue(modelDialInputValue);
        if (!parsedModelDial) {
          showErrorToast("Invalid Format", "Make sure entered dial format is valid.");
          setModelDialInputValue("");
          const inputFields = {
            field1: undefined,
            field2: undefined,
          };
          setModelDial({
            model_type: modelDial.model_type,
            operation: modelDial.operation,
            submodel_dial_name: modelDial.submodel_dial_name,
          });
          setModalInputs([inputFields]);
          setValue("inputArr", [inputFields]);
        } else if (
          customDial &&
          ((parsedModelDial?.anchor?.length ?? 0) === 0 ||
            parsedModelDial?.anchor_type !== CA_Mastr_Models_v1_0_Models_AnchorType.CUSTOM)
        ) {
          showErrorToast("Error", "Please choose a vector for custom dial.");
          setModelDialInputValue("");
        } else if (!parsedModelDial?.anchor) {
          setModelDial({
            ...{
              model_type: modelDial.model_type,
              operation: modelDial.operation,
              submodel_dial_name: modelDial.submodel_dial_name,
            },
            ...parsedModelDial,
          });
        } else {
          setModelDial({ ...modelDial, ...parsedModelDial });
        }
      } else {
        const emptyModelDial = {
          model_type: modelDial.model_type,
          operation: modelDial.operation,
          submodel_dial_name: modelDial.submodel_dial_name,
        };
        setModelDial(emptyModelDial);
      }
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const onDependencyChange = (e: any) => {
      const inputFields = {
        field1: undefined,
        field2: undefined,
      };
      const dependencyOptions = getDependencyOptions();
      const selectedOption = dependencyOptions.filter((item) => {
        return item.value === e.target.value;
      });
      setModalInputs([inputFields]);

      reset({
        anchorType: selectedOption[0].value,
      });
    };

    const getDependencyOptions = () => {
      return customDial
        ? [
            {
              id: 3,
              value: CA_Mastr_Models_v1_0_Models_AnchorType.CUSTOM,
              displayValue: "Custom",
            },
          ]
        : [
            {
              id: 0,
              value: CA_Mastr_Models_v1_0_Models_AnchorType.MONTH,
              displayValue: "Month",
            },
            {
              id: 1,
              value: CA_Mastr_Models_v1_0_Models_AnchorType.AGE,
              displayValue: "WALA",
            },
            {
              id: 2,
              value: CA_Mastr_Models_v1_0_Models_AnchorType.FACTOR_MONTH,
              displayValue: "Factor Month",
            },
          ];
    };

    return (
      <HStack role="group">
        <Stack direction="row" position="relative" alignItems="center" spacing={0.5} width="100%">
          <Tooltip
            label={isConstant ? "" : modelDialInputValue}
            aria-label={`Input value ${modelDialInputValue}`}
            placement="bottom"
            isDisabled={isConstant}
          >
            <CAInput
              hideLabel
              type="text"
              ref={ref}
              name={name}
              textAlign={isConstant ? "right" : "left"}
              value={modelDialInputValue}
              onBlur={onModelDialInputBlur}
              onChange={onModelDialInputValueChange}
            />
          </Tooltip>
          <Popover
            isLazy
            trigger="click"
            isOpen={isOpen}
            onClose={onPopoverClose}
            closeOnBlur={closePopoverOnBlur}
            placement="right"
          >
            <PopoverTrigger>
              <Button
                _groupHover={{
                  visibility: "visible",
                }}
                visibility="hidden"
                size="xs"
                h="26px"
                w="26px"
                mt="2px"
                variant="primary"
                onClick={onVectorClick}
                lineHeight={0}
              >
                V
              </Button>
            </PopoverTrigger>
            <Portal>
              <PopoverContent>
                <PopoverArrow />
                <PopoverCloseButton />
                <chakra.form onSubmit={onSubmit} px={5} py={4} width="fit-content">
                  <Flex flexDirection="column" px={5} py={3} justifyContent="flex-start">
                    <Text variant="tableHead" mb={6}>
                      VECTOR DIAL
                    </Text>

                    <ValueDisplay
                      name="Sub Model"
                      value={
                        SubModelLabels[
                          capitalizeFirstLetter(modelDial.submodel_dial_name?.split("_", 1).toString())
                        ]?.replace(new RegExp(/submodel[s]?/gi), "") ?? "-"
                      }
                    />
                    <ValueDisplay
                      name="Parameter"
                      value={getParameterFromSubmodel(modelDial.submodel_dial_name) ?? "-"}
                    />

                    <Box mt={5}>
                      <CASelectDropdown
                        width="7rem"
                        {...register("anchorType")}
                        label="Adjust By"
                        onChange={onDependencyChange}
                        options={
                          customDial
                            ? [
                                {
                                  id: 2,
                                  value: CA_Mastr_Models_v1_0_Models_AnchorType.CUSTOM,
                                  displayValue: "Custom",
                                },
                              ]
                            : [
                                {
                                  id: 0,
                                  value: CA_Mastr_Models_v1_0_Models_AnchorType.MONTH,
                                  displayValue: "Month",
                                },
                                {
                                  id: 1,
                                  value: CA_Mastr_Models_v1_0_Models_AnchorType.AGE,
                                  displayValue: "WALA",
                                },
                                {
                                  id: 3,
                                  value: CA_Mastr_Models_v1_0_Models_AnchorType.FACTOR_MONTH,
                                  displayValue: "Factor Month",
                                },
                              ]
                        }
                      />
                    </Box>
                    {!customDial && (
                      <Box>
                        <CASelectDropdown
                          width="7rem"
                          {...register("interpolation")}
                          label="Interpolation"
                          defaultValue={CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR}
                          options={[
                            {
                              id: 0,
                              value: CA_Mastr_Models_v1_0_Models_Interpolation.LINEAR,
                              displayValue: "Linear",
                            },
                            {
                              id: 1,
                              value: CA_Mastr_Models_v1_0_Models_Interpolation.STEP,
                              displayValue: "Step",
                            },
                          ]}
                        />
                      </Box>
                    )}
                    <HStack spacing="5" width="fit-content" mt="5">
                      <Text variant="tableHead" width="5.5rem">
                        {customDial?.anchorLabel ?? getAnchorDisplayValueByKey(watch_anchorType)}
                      </Text>
                      <Text variant="tableHead" width="6rem">
                        {capitalizeFirstLetter(modelDial.operation)}
                      </Text>
                    </HStack>
                    {getInputFields()}
                  </Flex>
                  <Box mt={4} px={5}>
                    <CACheckboxInput {...register("extrapolate")} variant="small" hideLabel label="Repeat last value" />
                  </Box>
                  <Flex direction="row" mt={4} mb={3} justifyContent="flex-end">
                    <HStack spacing={2}>
                      <Button variant="secondary" size="sm" onClick={onPopoverClose}>
                        Cancel
                      </Button>
                      <Button variant="primary" size="sm" type="submit">
                        OK
                      </Button>
                    </HStack>
                  </Flex>
                </chakra.form>
              </PopoverContent>
            </Portal>
          </Popover>
        </Stack>
      </HStack>
    );
  }
);

DialInput.displayName = "DialInput";

export default DialInput;
