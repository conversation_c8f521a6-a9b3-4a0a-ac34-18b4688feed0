import * as React from "react";
import { FormControl, FormLabel } from "@chakra-ui/form-control";
import { Input, InputGroup, InputRightAddon } from "@chakra-ui/input";
import { Box, Flex, Text } from "@chakra-ui/layout";
import { useForm } from "react-hook-form";
import { useColorModeValue } from "@chakra-ui/color-mode";
import CACard from "@/design-system/molecules/CACard";
import { cpr2smm, getFormattedNumberFixed, smm2cpr } from "@/utils/helpers";

type CPRToSMMConversionType = {
  cpr: number | undefined;
  smm: number | undefined;
};

const CPRToSMMConversionCard: React.FC = () => {
  const inputBgColor = useColorModeValue("misty.300", "celloBlue.400");

  const { register, setValue } = useForm<CPRToSMMConversionType>();

  const onCPRKeyUp: React.FormEventHandler<HTMLInputElement> = React.useCallback(
    (e: React.FormEvent<HTMLInputElement>) => {
      const cpr = (e.target as HTMLInputElement).valueAsNumber;
      if (!isNaN(cpr)) {
        setValue("smm", Number(getFormattedNumberFixed(2)(cpr2smm(cpr))));
      } else {
        setValue("smm", undefined);
      }
    },
    [setValue]
  );

  const onSMMKeyUp: React.FormEventHandler<HTMLInputElement> = React.useCallback(
    (e: React.FormEvent<HTMLInputElement>) => {
      const smm = (e.target as HTMLInputElement).valueAsNumber;
      if (!isNaN(smm)) {
        setValue("cpr", Number(getFormattedNumberFixed(2)(smm2cpr(smm))));
      } else {
        setValue("cpr", undefined);
      }
    },
    [setValue]
  );

  return (
    <CACard title="CPR to SMM Conversion">
      <Flex justifyContent="space-between">
        <Box w="43%">
          <FormControl>
            <FormLabel textAlign="center" mt="1">
              CPR
            </FormLabel>
            <InputGroup>
              <Input
                type="number"
                layerStyle="gray"
                backgroundColor={inputBgColor}
                _hover={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.300", "celloBlue.500"),
                }}
                _focus={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.400", "celloBlue.500"),
                }}
                textAlign="center"
                {...register("cpr")}
                onKeyUp={onCPRKeyUp}
              />
              <InputRightAddon w="6" p="0" justifyContent="center">
                %
              </InputRightAddon>
            </InputGroup>
          </FormControl>
        </Box>
        <Flex w="14%" alignItems="center" justifyContent="center">
          <Text fontSize="5xl" mt="6">
            =
          </Text>
        </Flex>
        <Box w="43%">
          <FormControl>
            <FormLabel textAlign="center" mt="1">
              SMM
            </FormLabel>
            <InputGroup>
              <Input
                type="number"
                layerStyle="gray"
                backgroundColor={inputBgColor}
                _hover={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.300", "celloBlue.500"),
                }}
                _focus={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.400", "celloBlue.500"),
                }}
                textAlign="center"
                {...register("smm")}
                onKeyUp={onSMMKeyUp}
              />
              <InputRightAddon w="6" p="0" justifyContent="center">
                %
              </InputRightAddon>
            </InputGroup>
          </FormControl>
        </Box>
      </Flex>
    </CACard>
  );
};

export default CPRToSMMConversionCard;
