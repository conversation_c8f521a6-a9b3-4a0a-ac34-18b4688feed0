import * as React from "react";
import { Box } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import ColumnWrapper from "@/components/helpers/ColumnWrapper";
import { PricerModuleProps } from "../..";
import PricerHeader from "../../PricerHeader";
import CPRToSMMConversionCard from "./CPRToSMMCoversion";
import PSAToCPRConversionCard from "./PSAToCPRConversion";

const PricerUtilitiesToolsView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  return (
    <Box>
      <PricerHeader title={pageTitle} withRun={false} />
      <MainInputContentTemplate>
        <Box mx={{ base: -1, sm: -2 }} my={{ base: 0, sm: -2 }}>
          <ColumnWrapper size="2xl" maxW="24rem">
            <CPRToSMMConversionCard />
            <PSAToCPRConversionCard />
          </ColumnWrapper>
        </Box>
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerUtilitiesToolsView;
