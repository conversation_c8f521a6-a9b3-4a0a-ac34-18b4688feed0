import * as React from "react";
import { Box, Flex, HStack, Text } from "@chakra-ui/layout";
import { FormControl, FormLabel } from "@chakra-ui/form-control";
import { Input, InputGroup, InputRightAddon } from "@chakra-ui/input";
import { useForm } from "react-hook-form";
import { useColorModeValue } from "@chakra-ui/color-mode";
import CAInput from "@/design-system/molecules/CAInput";
import CACard from "@/design-system/molecules/CACard";
import { cpr2psa, psa2cpr } from "@/utils/helpers";

type PSAToCPRConversionType = {
  cpr: number | undefined;
  psa: number | undefined;
  wala: number;
};

const PSAToCPRConversionCard: React.FC = () => {
  const inputBgColor = useColorModeValue("misty.300", "celloBlue.400");

  const { register, setValue, getValues, watch } = useForm<PSAToCPRConversionType>();

  const watchWala = watch("wala");

  const disableInput = !(watchWala > 0);

  const onKeyUpWala: React.FormEventHandler<HTMLInputElement> = React.useCallback(() => {
    const psa = getValues("psa");
    const cpr = getValues("cpr");
    /*
     * In some cases, we need to check for 'undefined' as well,
     * because setValue doesn't accept NaN, so it sets field value with undefined for some cases.
     */
    if (psa === undefined || !isNaN(psa)) setValue("psa", undefined);
    if (cpr === undefined || !isNaN(cpr)) setValue("cpr", undefined);
  }, [setValue, getValues]);

  const onPSAKeyUp: React.FormEventHandler<HTMLInputElement> = React.useCallback(
    (e: React.FormEvent<HTMLInputElement>) => {
      const psa = (e.target as HTMLInputElement).valueAsNumber;
      if (!isNaN(psa)) {
        setValue("cpr", psa2cpr(psa, getValues("wala")));
      } else {
        setValue("cpr", undefined);
      }
    },
    [setValue, getValues]
  );

  const onCPRKeyUp: React.FormEventHandler<HTMLInputElement> = React.useCallback(
    (e: React.FormEvent<HTMLInputElement>) => {
      const cpr = (e.target as HTMLInputElement).valueAsNumber;
      if (!isNaN(cpr)) {
        setValue("psa", cpr2psa(cpr, getValues("wala")));
      } else {
        setValue("psa", undefined);
      }
    },
    [setValue, getValues]
  );

  return (
    <CACard title="PSA To CPR Conversion">
      <HStack spacing={3}>
        <Text variant="primary">WALA (month)</Text>
        <Box w="30%">
          <CAInput
            hideLabel
            type="number"
            {...register("wala", {
              valueAsNumber: true,
            })}
            onKeyUp={onKeyUpWala}
          />
        </Box>
      </HStack>
      <Flex justifyContent="space-between">
        <Box w="43%">
          <FormControl>
            <FormLabel textAlign="center" mt="1">
              PSA
            </FormLabel>
            <InputGroup>
              <Input
                type="number"
                layerStyle="gray"
                backgroundColor={inputBgColor}
                _hover={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.300", "celloBlue.500"),
                }}
                _focus={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.400", "celloBlue.500"),
                }}
                textAlign="center"
                {...register("psa", { valueAsNumber: true })}
                onKeyUp={onPSAKeyUp}
                disabled={disableInput}
              />
              <InputRightAddon w="6" p="0" justifyContent="center">
                %
              </InputRightAddon>
            </InputGroup>
          </FormControl>
        </Box>
        <Flex w="14%" alignItems="center" justifyContent="center">
          <Text fontSize="5xl" mt="6">
            =
          </Text>
        </Flex>
        <Box w="43%">
          <FormControl>
            <FormLabel textAlign="center" mt="1">
              CPR
            </FormLabel>
            <InputGroup>
              <Input
                type="number"
                layerStyle="gray"
                backgroundColor={inputBgColor}
                _hover={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.300", "celloBlue.500"),
                }}
                _focus={{
                  borderBottomColor: "gemBlue.500",
                  backgroundColor: useColorModeValue("blackAlpha.400", "celloBlue.500"),
                }}
                textAlign="center"
                {...register("cpr", { valueAsNumber: true })}
                onKeyUp={onCPRKeyUp}
                disabled={disableInput}
              />
              <InputRightAddon w="6" p="0" justifyContent="center">
                %
              </InputRightAddon>
            </InputGroup>
          </FormControl>
        </Box>
      </Flex>
    </CACard>
  );
};

export default PSAToCPRConversionCard;
