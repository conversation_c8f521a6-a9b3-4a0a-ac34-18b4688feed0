import * as React from "react";
import {
  CustomRequestOptions,
  useGetBondCashFlowProjectionsSWR,
  useGetBondIndicativesSWRForReplinePage,
  useGetBondPrepayProjectionsSWR,
  useGetBondPrepayTrackingSWR,
  useGetBondPricingSWR,
  usePostBondCompare,
  useRunIdBatchRequestSWR,
} from "@/utils/swr-hooks";
import {
  BondCashFlowProjectionsRequest,
  BondIndicativesRequest,
  BondPrepayProjectionsRequest,
  BondPrepayTrackingRequest,
  BondPricingRequest,
} from "@/types/swr";
import {
  CA_Mastr_Api_v1_0_Models_Batch_BatchRequest,
  CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest,
  CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest,
} from "@/utils/openapi";
import { usePostRateProjection } from "@/utils/swr-hooks/RateProjections";
import { CAProgressIndicatorWrapper as ProgressIndicatorWrapper } from "@/design-system/molecules/CAProgressIndicator/CAProgressIndicatorWrapper";

type BondPricingProgressIndicatorProps = {
  requestOpts: CustomRequestOptions;
  bondPricingRequest: BondPricingRequest;
  onSuccess?: () => void;
  startTime?: string;
};

export const BondPricingProgressIndicator: React.FC<BondPricingProgressIndicatorProps> = ({
  bondPricingRequest,
  requestOpts,
  onSuccess,
}: BondPricingProgressIndicatorProps) => {
  const { data } = useGetBondPricingSWR(bondPricingRequest, requestOpts, onSuccess);

  return <ProgressIndicatorWrapper lastRun={requestOpts.lastRun} status={data?.status} />;
};

type BondCashFlowProjectionsProgressIndicatorProps = {
  bondCashFlowProjectionsRequest: BondCashFlowProjectionsRequest;
  opts: CustomRequestOptions;
};

export const BondCashFlowProjectionsProgressIndicatorWrapper: React.FC<
  BondCashFlowProjectionsProgressIndicatorProps
> = ({ bondCashFlowProjectionsRequest, opts }: BondCashFlowProjectionsProgressIndicatorProps) => {
  const { data } = useGetBondCashFlowProjectionsSWR(bondCashFlowProjectionsRequest, opts);
  return <ProgressIndicatorWrapper lastRun={opts?.lastRun} status={data?.status} />;
};

type BondIndicativesProgressIndicatorProps = {
  opts: CustomRequestOptions;
  bondIndicativesRequest: BondIndicativesRequest;
};

export const BondIndicativesProgressIndicatorWrapper: React.FC<BondIndicativesProgressIndicatorProps> = ({
  bondIndicativesRequest,
  opts,
}: BondIndicativesProgressIndicatorProps) => {
  const { data } = useGetBondIndicativesSWRForReplinePage(bondIndicativesRequest, opts);
  return <ProgressIndicatorWrapper lastRun={opts.lastRun} status={data?.status} />;
};

type BondPrepayTrackingProgressIndicatorProps = {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
};

export const BondPrepayTrackingProgressIndicatorWrapper: React.FC<BondPrepayTrackingProgressIndicatorProps> = ({
  bondPrepayTrackingRequest,
  requestOpts,
}: BondPrepayTrackingProgressIndicatorProps) => {
  const { data } = useGetBondPrepayTrackingSWR(bondPrepayTrackingRequest, requestOpts);
  return <ProgressIndicatorWrapper lastRun={requestOpts.lastRun} status={data?.status} />;
};

type BondPrepayProjectionsProgressIndocatorProps = {
  bondPrepayProjectionsRequest: BondPrepayProjectionsRequest;
  requestOpts: CustomRequestOptions;
};

export const BondPrepayProjectionsProgressIndicatorWrapper: React.FC<BondPrepayProjectionsProgressIndocatorProps> = ({
  bondPrepayProjectionsRequest,
  requestOpts,
}: BondPrepayProjectionsProgressIndocatorProps) => {
  const { data } = useGetBondPrepayProjectionsSWR(bondPrepayProjectionsRequest, requestOpts);

  return <ProgressIndicatorWrapper lastRun={requestOpts?.lastRun} status={data?.status} />;
};

type BondCompareProgressIndicatorProps = {
  requestOpts: CustomRequestOptions;
  bondCompareRequest: CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest;
};

export const BondCompareProgressIndicator: React.FC<BondCompareProgressIndicatorProps> = ({
  bondCompareRequest,
  requestOpts,
}: BondCompareProgressIndicatorProps) => {
  const { data } = usePostBondCompare(bondCompareRequest, requestOpts);
  return <ProgressIndicatorWrapper lastRun={requestOpts.lastRun} status={data?.status} />;
};

type RateProjectionProgressIndicatorProps = {
  requestOpts: CustomRequestOptions;
  rateProjectionRequest: CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest;
};

export const RateProjectionProgressIndicator: React.FC<RateProjectionProgressIndicatorProps> = ({
  rateProjectionRequest,
  requestOpts,
}: RateProjectionProgressIndicatorProps) => {
  const { data } = usePostRateProjection(rateProjectionRequest, requestOpts);
  return <ProgressIndicatorWrapper lastRun={requestOpts.lastRun} status={data?.status} />;
};

type BatchRequestProgressIndicatorProps = {
  requestOpts: CustomRequestOptions;
  request: CA_Mastr_Api_v1_0_Models_Batch_BatchRequest;
  onSuccess?: () => void;
};

export const BatchRequestProgressIndicator: React.FC<BatchRequestProgressIndicatorProps> = ({
  request,
  requestOpts,
  onSuccess,
}: BatchRequestProgressIndicatorProps) => {
  const { data } = useRunIdBatchRequestSWR(request, requestOpts, onSuccess);

  return <ProgressIndicatorWrapper lastRun={requestOpts.lastRun} status={data?.status} />;
};
