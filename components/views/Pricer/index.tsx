import * as React from "react";
import { Box, Flex, IconButton, Spinner, Text, useColorModeValue } from "@chakra-ui/react";
import { AiOutlineSearch } from "react-icons/ai";
import { IoSettings } from "react-icons/io5";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import MainLayout from "@/components/layouts/MainLayout";
import PricerSettingsDrawer from "@/components/views/Pricer/PricerSettingsDrawer";
import { BondSearchPopover } from "@/design-system/organisms/BondSearchPopover/BondSearchPopover";
import S from "@/constants/strings";
import CAIcon from "@/design-system/atoms/CAIcon";
import { CAInfo } from "@/design-system/molecules/CAInfo";
import CACard from "@/design-system/molecules/CACard";
import PricerHeader from "./PricerHeader";

export interface PricerModuleProps {
  pageTitle: string;
  needsBond?: boolean;
  headerMiddle?: React.ReactElement;
}

const PricerModule: React.FC<React.PropsWithChildren<PricerModuleProps>> = ({
  pageTitle,
  needsBond = true,
  children,
  headerMiddle,
}) => {
  const {
    state: { bond_name, pricerSettingsDrawerOpen, isLoadingBond, bondNotApplicableMessage },
    action: { togglePricerSettingsDrawer, redirectToBond },
  } = usePricerModule();

  const HeaderSettings = () => (
    <IconButton
      data-testid="toggle-settings-drawer"
      aria-label="Toggle Settings Drawer"
      icon={<CAIcon as={IoSettings} variant="secondary" />}
      variant="ghost"
      onClick={togglePricerSettingsDrawer}
    />
  );

  return bondNotApplicableMessage ? (
    <MainLayout
      title={pageTitle}
      headerMiddle={
        headerMiddle ?? (
          <BondSearchPopover
            bondName={bond_name ?? ""}
            withHotKey
            onBondSelected={redirectToBond}
            triggerEl={<BondSearchPlaceholder bondName={bond_name} />}
            triggerElWrapperProps={{
              w: "full",
            }}
          />
        )
      }
      headerSettings={<HeaderSettings />}
    >
      <Flex direction="column">
        <PricerHeader title={pageTitle} />
        <Box px="5" py="2" flexGrow={1}>
          <CACard
            h={{ base: "calc(100vh - 12rem)", sm: "calc(100vh - 11.6rem)" }}
            display="flex"
            justifyContent="center"
            alignItems="center"
          >
            <CAInfo status="warning" title="Bond is not applicable" description={bondNotApplicableMessage} />
          </CACard>
        </Box>
      </Flex>
      <PricerSettingsDrawer onClose={togglePricerSettingsDrawer} isOpen={pricerSettingsDrawerOpen} />
    </MainLayout>
  ) : (bond_name || !needsBond) && !isLoadingBond ? (
    <MainLayout
      title={needsBond && bond_name ? `${bond_name} - ${pageTitle}` : pageTitle}
      mb={{ base: "10", sm: "0" }}
      headerMiddle={
        headerMiddle ? (
          headerMiddle
        ) : needsBond ? (
          <BondSearchPopover
            bondName={bond_name ?? ""}
            withHotKey
            onBondSelected={redirectToBond}
            triggerEl={<BondSearchPlaceholder />}
            triggerElWrapperProps={{
              w: "full",
            }}
          />
        ) : null
      }
      headerSettings={<HeaderSettings />}
    >
      {children}
      <PricerSettingsDrawer onClose={togglePricerSettingsDrawer} isOpen={pricerSettingsDrawerOpen} />
    </MainLayout>
  ) : (
    <MainLayout
      title={pageTitle}
      headerMiddle={
        headerMiddle ?? (
          <BondSearchPopover
            bondName={bond_name ?? ""}
            withHotKey
            onBondSelected={redirectToBond}
            triggerEl={<BondSearchPlaceholder />}
            triggerElWrapperProps={{
              w: "full",
            }}
          />
        )
      }
      headerSettings={<HeaderSettings />}
    >
      <Flex direction="column">
        <PricerHeader title={pageTitle} />
        <Box px="5" py="2" flexGrow={1}>
          <CACard
            h={{ base: "calc(100vh - 12rem)", sm: "calc(100vh - 11.6rem)" }}
            display="flex"
            justifyContent="center"
            alignItems="center"
          >
            {isLoadingBond ? (
              <>
                <Spinner mr={4} emptyColor="celloBlue.200" />
                <Text fontSize="2xl" fontWeight="bold" textAlign="center">
                  Loading Bond...
                </Text>
              </>
            ) : (
              <CAInfo
                status="info"
                title="No bond selected"
                description={`Enter a bond and click ▷ to load ${pageTitle}.`}
              />
            )}
          </CACard>
        </Box>
      </Flex>
      <PricerSettingsDrawer onClose={togglePricerSettingsDrawer} isOpen={pricerSettingsDrawerOpen} />
    </MainLayout>
  );
};

export default React.memo(PricerModule);

interface Props {
  bondName?: string;
}

const BondSearchPlaceholder = ({ bondName }: Props) => {
  const bg = useColorModeValue("celloBlue.50", "celloBlue.1200");
  const color = useColorModeValue("charcoal.200", "whiteAlpha.600");

  return (
    <Flex
      py="0.5rem"
      pl="0.7rem"
      w={{ base: "full", md: "220px" }}
      borderRadius="3xl"
      alignItems="center"
      cursor="pointer"
      bg={bg}
    >
      <CAIcon color={color} size={5} as={AiOutlineSearch} mr={2} variant="disabled" />
      <Text color={color} fontWeight="normal" fontSize="md" lineHeight="5" data-testid="bond-placeholder">
        {bondName ?? S.MODULES.PRICER.ENTER_BOND}
      </Text>
    </Flex>
  );
};
