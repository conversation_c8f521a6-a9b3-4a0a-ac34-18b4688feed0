import { Text } from "@chakra-ui/layout";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON>onte<PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>tack,
  <PERSON>r,
  VStack,
  chakra,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import * as React from "react";
import { Controller, useForm } from "react-hook-form";
import { IoSyncCircleOutline } from "react-icons/io5";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { PricerUserSettingsType } from "@/contexts/ModuleContexts/PricerModuleContext/PricerModuleContextTypes";
import CAHeadingOriginal from "@/design-system/atoms/CAHeading";
import CADateInput from "@/design-system/molecules/CADateInput";
import CAInput from "@/design-system/molecules/CAInput";
import CASearch from "@/design-system/molecules/CASearch";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import {
  FormattedStringOptionsType,
  getDateTypeFromString,
  getFormattedIntegerOptions,
  getFormattedStringOptions,
  isSuperAdmin,
} from "@/utils/helpers";
import useResetFormData from "@/hooks/useResetFormData";
import { searchByKeywords } from "@/hooks/useKeywordsSearch";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { LastCloseDateHeader } from "@/design-system/organisms/LastCloseDateHeader/LastCloseDateHeader";
import PricerValueDisplayWrapper from "./shared/PricerValueDisplayWrapper";

export type PricerSettingsDrawerProps = {
  onClose: () => void;
  isOpen: boolean;
};

const SectionHeading: React.FC<React.PropsWithChildren & { allCaps?: boolean }> = ({ children, allCaps = true }) => (
  <Box pt={2}>
    <CAHeadingOriginal as={"h3"} variant={"subHeading"} textTransform={allCaps ? "uppercase" : "none"}>
      {children}
    </CAHeadingOriginal>
  </Box>
);

const PricerSettingsDrawer: React.FC<PricerSettingsDrawerProps> = ({ onClose, isOpen }: PricerSettingsDrawerProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userData },
  } = useAuthentication();
  const {
    state: { userSettings, activeSettingsDrawerKey },
    action: {
      updatePricerUserSettings,
      resetPricerUserSettings,
      getCelloCduDatesFromCurveDate,
      getIgnoreCduPayDateAfterFromCurveDate,
    },
  } = usePricerModule();
  const [searchKey, setSearchKey] = React.useState("");
  const isMobile = useBreakpointValue({ sm: true, md: false });

  const { register, handleSubmit, reset, control, setValue, watch } = useForm<PricerUserSettingsType>();
  const watchInterestRateType = watch("type");

  useResetFormData({ reset, formData: userSettings });

  const onSubmit = (data: PricerUserSettingsType) => {
    updatePricerUserSettings(data);
    closeDrawer(data);
  };

  const onReset = () => {
    resetPricerUserSettings();
    setSearchKey("");
  };

  const onSearchTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKey(e.target.value);
  };

  const setDisplayForField = (fieldName: string) => {
    return searchByKeywords(searchKey, fieldName) ? "block" : "none";
  };

  const closeDrawer = (data?: PricerUserSettingsType) => {
    setSearchKey("");
    reset(data ? data : userSettings);
    onClose();
  };

  const isActiveKey = (settingsKey: string) => {
    return settingsKey === activeSettingsDrawerKey && !isMobile;
  };

  const onCurveDateChange = React.useCallback(
    (curveDate: Date, onChange: (date: Date) => void) => {
      onChange(curveDate);
      getCelloCduDatesFromCurveDate(curveDate).then((dates) => {
        if (!dates) return;
        dates?.cdu_date_conventional &&
          setValue("cdu_date_conventional", getDateTypeFromString(dates?.cdu_date_conventional));
        dates?.cdu_date_gpl && setValue("cdu_date_gpl", getDateTypeFromString(dates?.cdu_date_gpl));
        dates?.cdu_date_gnm && setValue("cdu_date_gnm", getDateTypeFromString(dates?.cdu_date_gnm));
      });
      setValue("pricing_date", curveDate);
      setValue(
        "ignore_cdu_paydate_after",
        getIgnoreCduPayDateAfterFromCurveDate(
          curveDate,
          getDateTypeFromString(metadata?.pricer_settings?.curve_date?.value)
        )
      );
    },
    [getCelloCduDatesFromCurveDate, getIgnoreCduPayDateAfterFromCurveDate, metadata, setValue]
  );

  const onModelVersionChange = React.useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      if (metadata?.pricer_settings?.model_configs) {
        const modelConfig = metadata?.pricer_settings?.model_configs.find(
          (modelConfig) => modelConfig.model_name === e.target.value
        );
        if (modelConfig) {
          if (modelConfig.current_coupon_model_name) {
            setValue("current_coupon_model", modelConfig.current_coupon_model_name);
          }
          if (modelConfig.mortgage_model_type) {
            setValue("primary_secondary_spread", modelConfig.mortgage_model_type);
          }
        }
      }
    },
    [metadata?.pricer_settings?.model_configs, setValue]
  );

  const onTypeChange = React.useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      // UI-1250 Hard code paths to 256 for Numerix (LMM2F), otherwise default to 200
      if (e.target.value === "LMM2F") {
        setValue("interestRatePaths", 256);
      } else {
        setValue("interestRatePaths", 200);
      }
      if (metadata?.pricer_settings?.interest_rate_model_configs) {
        const modelConfig = metadata?.pricer_settings?.interest_rate_model_configs.find(
          (modelConfig) => modelConfig.model_name === e.target.value
        );
        if (modelConfig && modelConfig["calibrations"]) {
          setValue("calibration", modelConfig["calibrations"][0]);
        }
      }
    },
    [metadata, setValue]
  );

  const calibrationOptions = React.useMemo(() => {
    let options: FormattedStringOptionsType[] = [];
    if (metadata?.pricer_settings?.interest_rate_model_configs) {
      const modelConfig = metadata?.pricer_settings?.interest_rate_model_configs.find(
        (modelConfig) => modelConfig.model_name === watchInterestRateType
      );
      if (modelConfig && modelConfig["calibrations"]) {
        const formattedCalibrationList: FormattedStringOptionsType[] = getFormattedStringOptions(
          metadata?.pricer_settings?.calibration
        );
        options = formattedCalibrationList.filter(
          (calibrationOption) =>
            !!modelConfig["calibrations"]?.find((calibrationValue) => calibrationValue === calibrationOption.value)
        );
      }
    }
    return options;
  }, [metadata, watchInterestRateType]);

  return (
    <Drawer autoFocus={false} placement="right" onClose={() => closeDrawer()} isOpen={isOpen} size={"xs"}>
      <DrawerOverlay>
        <DrawerContent overflowY="scroll">
          <chakra.form p={4} onSubmit={handleSubmit(onSubmit)} position="relative">
            <Box pt={3} pb={2}>
              <CASearch
                placeholder="Filter"
                hideLabel={true}
                name={"searchSettings"}
                onChange={onSearchTextChange}
                bg={useColorModeValue("celloBlue.25", "celloBlue.1000")}
              />
            </Box>
            <VStack alignItems="stretch" pt={5}>
              <SectionHeading>Pricing</SectionHeading>
              <Box display={setDisplayForField("Market Data Source")}>
                <CASelectDropdown
                  label={"Market Data Source"}
                  {...register("market_data_source")}
                  {...{ autoFocus: isActiveKey("Market Data Source") }}
                  options={getFormattedStringOptions(userData?.user?.market_data_sources)}
                />
              </Box>
              <Box display={setDisplayForField("Curve Date")}>
                <Controller
                  name={"curve_date"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref, onBlur } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={(date: Date) => onCurveDateChange(date, onChange)}
                      name={name}
                      label={"Curve Date"}
                      openWithDelay={isActiveKey("Curve Date")}
                      {...{ autoFocus: isActiveKey("Curve Date") }}
                      headerEl={({ closeDatePicker }) => (
                        <LastCloseDateHeader
                          onChange={(value) => {
                            onCurveDateChange(value, onChange);
                            onBlur();
                          }}
                          closeDatePicker={closeDatePicker}
                        />
                      )}
                    />
                  )}
                />
              </Box>
              <Box display={setDisplayForField("Pricing Date")}>
                <Controller
                  name={"pricing_date"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label={"Pricing Date"}
                      openWithDelay={isActiveKey("Pricing Date")}
                      {...{ autoFocus: isActiveKey("Pricing Date") }}
                      headerEl={({ closeDatePicker }) => (
                        <LastCloseDateHeader
                          onChange={(value) => {
                            onChange(value);
                            closeDatePicker();
                          }}
                          closeDatePicker={closeDatePicker}
                        />
                      )}
                    />
                  )}
                />
              </Box>

              <SectionHeading>Mortgage Models</SectionHeading>
              <Box display={setDisplayForField("Prepay Model Version")}>
                <CASelectDropdown
                  label={"Prepay"}
                  {...register("model_version")}
                  {...{ autoFocus: isActiveKey("Prepay Model Version") }}
                  onChange={onModelVersionChange}
                  options={getFormattedStringOptions(metadata?.pricer_settings?.version)}
                />
              </Box>
              <Box display={setDisplayForField("Current Coupon Model")}>
                <CASelectDropdown
                  label={"Current Coupon"}
                  {...register("current_coupon_model")}
                  {...{ autoFocus: isActiveKey("Current Coupon Model") }}
                  options={getFormattedStringOptions(metadata?.pricer_settings?.current_coupon_model)}
                />
              </Box>
              <Box display={setDisplayForField("Primary-Secondary Spread Model")}>
                <CASelectDropdown
                  label={"Primary-Secondary Spread"}
                  {...register("primary_secondary_spread")}
                  {...{ autoFocus: isActiveKey("Primary-Secondary Spread Model") }}
                  options={getFormattedStringOptions(metadata?.pricer_settings?.primary_secondary_spread)}
                />
              </Box>

              <SectionHeading>Cello CDU</SectionHeading>
              <Box display={setDisplayForField("Repline Algo")}>
                <CASelectDropdown
                  label={"Repline Algo"}
                  {...register("repline_level")}
                  {...{ autoFocus: isActiveKey("Repline Algo") }}
                  options={getFormattedStringOptions(metadata?.pricer_settings?.repline_level)}
                />
              </Box>
              <Box display={setDisplayForField("Conventional")}>
                <Controller
                  name={"cdu_date_conventional"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      showMonthYearPicker
                      maxDate={new Date()}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label={"Conventional"}
                      openWithDelay={isActiveKey("Conventional")}
                      {...{ autoFocus: isActiveKey("Conventional") }}
                    />
                  )}
                />
              </Box>
              <Box display={setDisplayForField("Ginnie")}>
                <Controller
                  name={"cdu_date_gnm"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      showMonthYearPicker
                      maxDate={new Date()}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label={"Ginnie"}
                      openWithDelay={isActiveKey("Ginnie")}
                      {...{ autoFocus: isActiveKey("Ginnie") }}
                    />
                  )}
                />
              </Box>
              <Box display={setDisplayForField("GPL")}>
                <Controller
                  name={"cdu_date_gpl"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      showMonthYearPicker
                      maxDate={new Date()}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label={"GPL"}
                      openWithDelay={isActiveKey("GPL")}
                      {...{ autoFocus: isActiveKey("GPL") }}
                    />
                  )}
                />
              </Box>

              <SectionHeading>Intex CDU</SectionHeading>
              <Box display={setDisplayForField("Ignore PayDate After")}>
                <Controller
                  name={"ignore_cdu_paydate_after"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label={"Ignore PayDate After"}
                      openWithDelay={isActiveKey("Ignore PayDate After")}
                      {...{ autoFocus: isActiveKey("Ignore PayDate After") }}
                    />
                  )}
                />
              </Box>

              <SectionHeading>Interest Rate Model</SectionHeading>
              <Box display={setDisplayForField("Paths")}>
                <CAInput
                  type="number"
                  label={"Paths"}
                  // UI-1250 Hard code paths to 256 for Numerix (LMM2F)
                  disabled={watchInterestRateType === "LMM2F"}
                  {...register("interestRatePaths", {
                    valueAsNumber: true,
                  })}
                  {...{ autoFocus: isActiveKey("Interest Rate Paths") }}
                />
              </Box>
              <Box display={setDisplayForField("Type")}>
                <CASelectDropdown
                  label={"Type"}
                  {...register("type", {
                    onChange: onTypeChange,
                  })}
                  options={getFormattedStringOptions(metadata?.pricer_settings?.type)}
                  {...{ autoFocus: isActiveKey("Interest Rate Model") }}
                />
              </Box>
              <Box display={setDisplayForField("Calibration")}>
                <CASelectDropdown
                  label={"Calibration"}
                  {...register("calibration")}
                  {...{ autoFocus: isActiveKey("Calibration") }}
                  options={calibrationOptions}
                />
              </Box>
              <Box display={setDisplayForField("Yield Curve Model")}>
                <CASelectDropdown
                  label={"Yield Curve Model"}
                  {...register("yield_curve_model")}
                  {...{ autoFocus: isActiveKey("Yield Curve Model") }}
                  options={getFormattedStringOptions(metadata?.pricer_settings?.yield_curve_model)}
                />
              </Box>

              <SectionHeading>Interest Rate Duration</SectionHeading>
              <Box display={setDisplayForField("Curve shift (bps)")}>
                <CAInput
                  type="number"
                  label={"Curve shift (bps)"}
                  {...register("key_curve_shift", {
                    valueAsNumber: true,
                  })}
                />
              </Box>
              <Box display={setDisplayForField("Key Rates")}>
                <CASelectDropdown
                  label={"Key Rates"}
                  {...register("key_rate_points", {
                    valueAsNumber: true,
                  })}
                  options={getFormattedIntegerOptions(metadata?.pricer_settings?.key_rate_points)}
                />
              </Box>

              <SectionHeading>Overrides (Optional)</SectionHeading>
              <Box display={setDisplayForField("Current Coupon Calibration Date")}>
                <Controller
                  name="input_data_override.current_coupon_calibration_date_override"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => {
                    return (
                      <CADateInput
                        ref={ref}
                        popperPlacement="bottom-end"
                        selectedDate={value}
                        onChange={onChange}
                        name={name}
                        label="Current Coupon Calibration Date"
                        openWithDelay={isActiveKey("Current Coupon Calibration Date Override")}
                        {...{ autoFocus: isActiveKey("Current Coupon Calibration Date Override") }}
                      />
                    );
                  }}
                />
              </Box>
              <Box display={setDisplayForField("Curve Date Override")}>
                <Controller
                  name="input_data_override.curve_date_override"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label="Curve Date"
                      openWithDelay={isActiveKey("Curve Date Override")}
                      {...{ autoFocus: isActiveKey("Curve Date Override") }}
                    />
                  )}
                />
              </Box>
              <Box display={setDisplayForField("Interest Rate Calibration Date")}>
                <Controller
                  name="input_data_override.interest_rate_calibration_date_override"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label="Interest Rate Calibration Date"
                      openWithDelay={isActiveKey("Interest Rate Calibration Date Override")}
                      {...{ autoFocus: isActiveKey("Interest Rate Calibration Date Override") }}
                    />
                  )}
                />
              </Box>
              <Box display={setDisplayForField("Survey Rate Date")}>
                <Controller
                  name="input_data_override.survey_rate_date_override"
                  control={control}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={onChange}
                      name={name}
                      label="Survey Rate Date"
                      openWithDelay={isActiveKey("Survey Rate Date Override")}
                      {...{ autoFocus: isActiveKey("Survey Rate Date Override") }}
                    />
                  )}
                />
              </Box>

              <SectionHeading allCaps={false}>vCPUs</SectionHeading>
              <Box>
                <Box display={setDisplayForField("Organization Total")}>
                  <PricerValueDisplayWrapper name="Organization Total" value={"Variable"} />
                </Box>
                <Box display={setDisplayForField("User Total")}>
                  <PricerValueDisplayWrapper name="User Total" value={"Variable"} />
                </Box>
                <Box display={setDisplayForField("Dashboard Nominal")}>
                  <PricerValueDisplayWrapper name="Dashboard Nominal" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Dashboard OAS")}>
                  <Controller
                    name={"vcpuPerOas"}
                    control={control}
                    rules={{ required: true }}
                    render={({ field: { name, value, onChange } }) => (
                      <CAMultiSelectDropdown
                        label="Dashboard OAS"
                        minHeight={26}
                        isMultiSelect={false}
                        name={name}
                        value={value?.toString()}
                        onChange={onChange}
                        options={getFormattedIntegerOptions(metadata?.pricer_settings?.vcpu_per_oas).map((option) => ({
                          label: option.displayValue?.toString() ?? "",
                          value: option.value?.toString() ?? "",
                        }))}
                        menuPortalTarget={document.body}
                      />
                    )}
                  />
                </Box>
                <Box display={setDisplayForField("Matrix")}>
                  <PricerValueDisplayWrapper name="Matrix" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Scenario")}>
                  <PricerValueDisplayWrapper name="Scenario" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Cash Flows")}>
                  <PricerValueDisplayWrapper name="Cash Flows" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Comparison")}>
                  <PricerValueDisplayWrapper name="Comparison" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Repline")}>
                  <PricerValueDisplayWrapper name="Repline" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Tracking")}>
                  <PricerValueDisplayWrapper name="Tracking" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Projection Summary")}>
                  <PricerValueDisplayWrapper name="Projection Summary" value={"All"} />
                </Box>
                <Box display={setDisplayForField("Projection Detail")}>
                  <PricerValueDisplayWrapper name="Projection Detail" value={"All"} />
                </Box>
              </Box>

              <SectionHeading>Application</SectionHeading>
              <PricerValueDisplayWrapper
                name="Version"
                value={process.env.NEXT_PUBLIC_GIT_TAG || process.env.NEXT_PUBLIC_GIT_HASH || "-"}
                _key="application_version"
              />
              <Box>
                <HStack justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text _hover={{ cursor: "pointer" }} variant="primary">
                      Use Cache
                    </Text>
                  </Box>
                  <Box>
                    <Controller
                      name={"useCache"}
                      control={control}
                      render={({ field: { name, onChange, value } }) => {
                        return (
                          <CASwitchInput
                            mr="-2"
                            name={name}
                            hideLabel={true}
                            value={"useCache"}
                            defaultChecked={value}
                            isChecked={value}
                            onChange={onChange}
                          />
                        );
                      }}
                    />
                  </Box>
                </HStack>
              </Box>
              {isSuperAdmin(userData) && (
                <Box display={setDisplayForField("Timeout Minutes")}>
                  <CAInput
                    type="number"
                    inputType="positive-integer"
                    label={"Timeout (min)"}
                    {...register("timeoutMinutes", {
                      valueAsNumber: true,
                      required: true,
                    })}
                    {...{ autoFocus: isActiveKey("Timeout Minutes") }}
                  />
                </Box>
              )}
            </VStack>
            <Flex
              direction="row"
              mt={4}
              pt={3}
              pb={3}
              position="sticky"
              bottom={0}
              bg={useColorModeValue("white", "celloBlue.900")}
            >
              <Button
                leftIcon={<IoSyncCircleOutline />}
                onClick={onReset}
                size="3xl"
                fontSize="3xl"
                variant="secondary"
                pr="2"
                pl="1"
                title={`Defaults`}
                aria-label={`Defaults`}
              >
                <Text>Defaults</Text>
              </Button>
              <Spacer />
              <HStack spacing={2}>
                <Button variant="secondary" size="sm" onClick={() => closeDrawer()}>
                  Cancel
                </Button>
                <Button variant="primary" size="sm" type="submit">
                  Save
                </Button>
              </HStack>
            </Flex>
          </chakra.form>
        </DrawerContent>
      </DrawerOverlay>
    </Drawer>
  );
};

export default PricerSettingsDrawer;
