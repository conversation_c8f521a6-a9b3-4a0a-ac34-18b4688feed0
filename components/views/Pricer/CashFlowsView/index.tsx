import * as React from "react";
import { Box } from "@chakra-ui/react";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerCashFlowsPage } from "@/contexts/PageContexts/PricerCashFlowsPageContext";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { getRequests } from "@/utils/helpers/pricer/cash-flows";
import { BondCashFlowProjectionsStopWatchWrapper } from "../PricerStopWatchWrapper";
import { PricerModuleProps } from "../";
import PricerHeader from "../PricerHeader";
import { BondCashFlowProjectionsProgressIndicatorWrapper } from "../PricerProgressIndicatorWrapper";
import { BondCashFlowProjectionsStopRunningWrapper } from "../PricerStopRunningWrapper";
import CashFlowGrid from "./CashFlowGrid";
import CashFlowInput from "./CashFlowInput";
import { CashFlowInputHandler } from "./CashFlowInput";

const PricerCashFlowsView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { bond_name, userSettings, security_info },
    action: { updatePricerUserSettingsCopy, setIsTimerRunning },
  } = usePricerModule();
  const {
    state: { lastRunId, isOldRun, lastRun, api_start_time, api_end_time, ...restCashFlowsState },
    action: { updateCashFlowsPageSettings, run },
  } = usePricerCashFlowsPage();
  const { pushWithoutRendering } = useQueryParameters();

  const cashFlowInputRef = React.useRef<CashFlowInputHandler>(null);

  const { bondCashFlowProjectionsRequest, requestOpts } = getRequests({
    userSettings,
    security_info,
    lastRun,
    lastRunId,
    isOldRun,
    bond_name,
    ...restCashFlowsState,
  });

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    cashFlowInputRef.current?.handleSubmit((data) => {
      updateCashFlowsPageSettings(data);
      updatePricerUserSettingsCopy(userSettings);
      setIsTimerRunning(true);
      const run_id = run({ no_cache });
      pushWithoutRendering({ run_id });
    })();
  };

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        bond_name={bond_name}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        onRunClick={onSubmit}
        stopWatch={
          <BondCashFlowProjectionsStopWatchWrapper
            bondCashFlowProjectionsRequest={bondCashFlowProjectionsRequest}
            opts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endDate={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <BondCashFlowProjectionsProgressIndicatorWrapper
            bondCashFlowProjectionsRequest={bondCashFlowProjectionsRequest}
            opts={requestOpts}
          />
        }
        stopRunning={
          <BondCashFlowProjectionsStopRunningWrapper
            bondCashFlowProjectionsRequest={bondCashFlowProjectionsRequest}
            opts={requestOpts}
          />
        }
      />
      <MainInputContentTemplate inputs={<CashFlowInput ref={cashFlowInputRef} />}>
        <CashFlowGrid bondCashFlowProjectionsRequest={bondCashFlowProjectionsRequest} opts={requestOpts} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerCashFlowsView;
