import { Box, VStack } from "@chakra-ui/react";
import * as React from "react";
import { Controller, UseFormHandleSubmit, useForm } from "react-hook-form";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePricerCashFlowsPage } from "@/contexts/PageContexts/PricerCashFlowsPageContext";
import { cashFlowsPageSettingsType } from "@/contexts/PageContexts/PricerCashFlowsPageContext/PricerCashFlowsPageContextTypes";
import CAHeading from "@/design-system/atoms/CAHeading";
import CACard from "@/design-system/molecules/CACard";
import CADateInput from "@/design-system/molecules/CADateInput";
import CAInput from "@/design-system/molecules/CAInput";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import {
  getCDUDateKeyFromSubType,
  getDisplayValueByKey,
  getDrawerKeyFromSubType,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getFormattedYearMonth,
  getInfoMsg,
} from "@/utils/helpers";
import { CA_Mastr_Models_v1_0_Models_BondType, CA_Mastr_Models_v1_0_Models_PrepayMethod } from "@/utils/openapi";
import useResetFormData from "@/hooks/useResetFormData";
import useUpdateSettleDate from "@/hooks/useUpdateSettleDate";
import useOnPrepayModelChange from "@/hooks/useOnPrepayModelChange";
import { getReplineAlgoProps } from "@/utils/helpers/pricer";
import BondIndicativesValue from "../shared/BondIndicativesValue";
import ModelDialSelectorWrapper from "../shared/ModelDialSelectorWrapper";
import PrepayVectorSelector from "../shared/PrepayVectorSelector";
import PricerValueDisplayWrapper from "../shared/PricerValueDisplayWrapper";
import ReplineDialsSwitch from "../shared/ReplineDialsSwitch";

export interface CashFlowInputHandler {
  handleSubmit: UseFormHandleSubmit<cashFlowsPageSettingsType>;
}

const CashFlowInput: React.ForwardRefRenderFunction<CashFlowInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { bond_name, userSettings, userSettingsCopy, security_info },
  } = usePricerModule();
  const {
    state: { app, page, noCache, cashFlowsPageSettingsCopy, cashFlowsPageSettings, isOldRun, lastRun },
  } = usePricerCashFlowsPage();
  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);

  const {
    handleSubmit,
    register,
    watch,
    reset,
    control,
    setValue,
    formState: { touchedFields },
  } = useForm<cashFlowsPageSettingsType>();
  const [
    watch_prepay_model_type,
    watch_prepay_percentage,
    watch_settle_date,
    watch_cashflow_scenario,
    watch_modify_class,
  ] = watch(["prepay_model_type", "prepay_percentage", "settle_date", "cashflow_scenario", "modify_class"]);

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  useResetFormData({ reset, formData: cashFlowsPageSettings });

  useUpdateSettleDate<typeof cashFlowsPageSettings>({
    pageSettings: cashFlowsPageSettings,
    setSettleDate: (settleDate) => setValue("settle_date", settleDate),
    isOldRun,
    security_info,
    userSettings,
    userSettingsCopy,
    bond_name,
    app,
    page,
  });

  useOnPrepayModelChange({
    prepayModelType: watch_prepay_model_type,
    prepayPercentage: watch_prepay_percentage,
    setValue,
    isTouched: !!touchedFields.prepay_model_type,
    isOldRun,
  });

  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="cashflow-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
      overflow="visible"
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <PricerValueDisplayWrapper
            name="Curve Date"
            link
            value={getFormattedLocaleDate(userSettings.curve_date)}
            dateFormatter={getFormattedLocaleDate}
            _key="curve_date"
          />
          <PricerValueDisplayWrapper
            name="Pricing Date"
            link
            dateFormatter={getFormattedLocaleDate}
            value={getFormattedLocaleDate(userSettings.pricing_date)}
            _key="pricing_date"
          />
          <Controller
            name={"settle_date"}
            control={control}
            rules={{ required: true }}
            render={({ field: { name, value, onChange, ref } }) => (
              <CADateInput
                ref={ref}
                selectedDate={value}
                width={"6rem"}
                minDate={userSettings.curve_date}
                onChange={onChange}
                name={name}
                label={"Settle Date"}
                info={getInfoMsg(watch_settle_date, cashFlowsPageSettingsCopy.settle_date)}
                includeNonBusinessDays={true}
              />
            )}
          />
        </Box>
        <Box>
          <CAHeading as="h3" mb={4}>
            Intex CDU
          </CAHeading>
          <PricerValueDisplayWrapper
            name="Date"
            value={
              bond_name ? (
                <BondIndicativesValue
                  bond_name={bond_name}
                  location="bond_structure.tranche_cdu_date"
                  formatter={getFormattedYearMonth}
                  noCache={noCache}
                  requestMetadata={{ app, page }}
                />
              ) : (
                "-"
              )
            }
          />
          <PricerValueDisplayWrapper name="Deal Mode" value={security_info?.deal_mode ?? "-"} />
        </Box>
        <Box>
          <CASelectDropdown
            label={"Prepay Model"}
            {...register("prepay_model_type", {
              required: true,
            })}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_model_type, cashFlowsPageSettingsCopy.prepay_model_type)}
            options={getFormattedStringOptions(metadata?.pricer_settings?.prepay_models)}
          />
          <CAInput
            type="number"
            label={"Multiplier (%)"}
            {...register("prepay_percentage", {
              valueAsNumber: true,
              required: true,
            })}
            width={"6rem"}
            info={getInfoMsg(watch_prepay_percentage, cashFlowsPageSettingsCopy.prepay_percentage)}
          />
          <CASelectDropdown
            label={"Interest Rate Scenario"}
            {...register("cashflow_scenario")}
            width={"6rem"}
            info={getInfoMsg(
              getDisplayValueByKey(watch_cashflow_scenario),
              getDisplayValueByKey(cashFlowsPageSettingsCopy.cashflow_scenario)
            )}
            options={getFormattedStringOptions(metadata?.pricer_settings?.cash_flow_scenario)}
          />
          {watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR && (
            <PrepayVectorSelector lastRun={lastRun} />
          )}
          {(watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.CELLO ||
            watch_prepay_model_type === CA_Mastr_Models_v1_0_Models_PrepayMethod.VECTOR) && (
            <ModelDialSelectorWrapper lastRun={lastRun} />
          )}
          <ReplineDialsSwitch />
          <PricerValueDisplayWrapper
            name="Prepay Model Version"
            value={userSettings.model_version ?? ""}
            link
            _key="model_version"
          />
          <PricerValueDisplayWrapper name="Interest Rate Paths" value="1" _key="interestRatePaths" />
          <PricerValueDisplayWrapper
            name="Cello CDU Date"
            link
            value={getFormattedYearMonth(userSettings[cduDateKey])}
            _key={`cello_cdu_dates.${cduDateKey}`}
            dateFormatter={getFormattedYearMonth}
            drawerKey={getDrawerKeyFromSubType(security_info?.sub_type)}
          />
          <PricerValueDisplayWrapper {...getReplineAlgoProps(security_info?.sub_type, metadata, userSettings)} />
          {security_info?.bond_type !== CA_Mastr_Models_v1_0_Models_BondType.CMO && (
            <CASelectDropdown
              label="Cash Flow"
              width="6rem"
              info={getInfoMsg(watch_modify_class, cashFlowsPageSettingsCopy.modify_class)}
              {...register("modify_class", {
                required: true,
              })}
              options={getFormattedStringOptions(metadata?.pricer_settings?.bond_class)}
            />
          )}
        </Box>
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(CashFlowInput);
