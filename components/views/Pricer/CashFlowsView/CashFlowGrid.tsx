import * as React from "react";
import { Box } from "@chakra-ui/layout";
import { CustomRequestOptions, useGetBondCashFlowProjectionsSWR } from "@/utils/swr-hooks";
import { BondCashFlowProjectionsRequest } from "@/types/swr";
import cashFlowsColumnData from "@/utils/grid/CashFlowsColumnData";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { CA_Mastr_Api_v1_0_Models_BondCashFlowProjection_CashFlow } from "@/utils/openapi";
import CAGrid from "@/design-system/molecules/CAGrid";

const CashFlowGrid = ({
  bondCashFlowProjectionsRequest,
  opts: { lastRun, isOldRun },
}: {
  bondCashFlowProjectionsRequest: BondCashFlowProjectionsRequest;
  opts: CustomRequestOptions;
}) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const { data, error, isLoading } = useGetBondCashFlowProjectionsSWR(bondCashFlowProjectionsRequest, {
    lastRun,
    isOldRun,
  });

  const SCENARIO_VALUES = metadata?.pricer_settings?.cash_flow_scenario?.reduce(
    (acc: Record<string, string>, curr) => ((acc[curr.value] = curr.display_value), acc),
    {}
  );

  const gridData: CA_Mastr_Api_v1_0_Models_BondCashFlowProjection_CashFlow[] | undefined =
    data?.cash_flow_projection?.map((el) => ({ ...el, scenario: SCENARIO_VALUES?.[el.scenario ?? ""] }));

  return (
    <Box minH="calc(100vh - 11rem)" h="full">
      <CAGrid<CA_Mastr_Api_v1_0_Models_BondCashFlowProjection_CashFlow>
        gridDataType="futuristic"
        hasRun={!!lastRun}
        error={error}
        gridProps={{
          columnDefs: cashFlowsColumnData,
          rowData: gridData,
          pivotMode: false,
          suppressAggFuncInHeader: false,
          suppressColumnVirtualisation: false,
          grandTotalRow: "bottom",
          loading: isLoading,
        }}
        gridType="cashflow"
        showExpand
        hideSearch
        initialMessage={`Click on ▷ to load Cash Flows.`}
        cardProps={{
          title: " ",
          cardKey: "cashflow-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
        }}
        stackStyles={{
          h: "calc(100vh - 12rem)",
        }}
      />
    </Box>
  );
};

export default CashFlowGrid;
