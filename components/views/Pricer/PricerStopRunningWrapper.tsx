import * as React from "react";
import router from "next/router";
import {
  CustomRequestOptions,
  useGetBondCashFlowProjectionsSWR,
  useGetBondIndicativesSWRForReplinePage,
  useGetBondPrepayProjectionsSWR,
  useGetBondPrepayTrackingSWR,
  useGetBondPricingSWR,
} from "@/utils/swr-hooks";
import {
  BondCashFlowProjectionsRequest,
  BondIndicativesRequest,
  BondPrepayProjectionsRequest,
  BondPrepayTrackingRequest,
  BondPricingRequest,
} from "@/types/swr";
import {
  CA_Mastr_Api_v1_0_Models_Batch_BatchRequest,
  CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest,
  CancelService,
} from "@/utils/openapi";
import { usePricerBondComparePage } from "@/contexts/PageContexts/PricerBondComparePageContext";
import { usePricerDashboardPage } from "@/contexts/PageContexts/PricerDashboardPageContext";
import { usePricerCashFlowsPage } from "@/contexts/PageContexts/PricerCashFlowsPageContext";
import { usePricerReplinePage } from "@/contexts/PageContexts/PricerReplinePageContext";
import { usePricerTrackingPage } from "@/contexts/PageContexts/PricerTrackingPageContext";
import { usePricerProjectionDetailPage } from "@/contexts/PageContexts/PricerProjectionDetailPageContext";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { cancel } from "@/utils/cancel";
import { usePricerEmpiricalTrackingSimplifiedPage } from "@/contexts/PageContexts/PricerEmpiricalTrackingSimplifiedPageContext/PricerEmpiricalTrackingSimplified";
import { getAPIErrorMessage, getParam, getRunIdFromBatchRequestPayload } from "@/utils/helpers";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import { showErrorToast } from "@/design-system/theme/toast";
import { useEmpiricalTrackingData } from "./Collateral/EmpiricalTrackingSimplifiedView/EmpiricalTrackingSimplifiedView";

type BondPricingStopProps = {
  requestOpts: CustomRequestOptions;
  bondPricingRequest: BondPricingRequest;
};

const removeRunIdFromURL = () => {
  const query = router.query;
  delete query.run_id;
  router.push({
    query: {
      ...router.query,
    },
  });
};

export const BondPricingStopRunningWrapper: React.FC<BondPricingStopProps> = ({
  bondPricingRequest,
  requestOpts,
}: BondPricingStopProps) => {
  const { cancel } = useGetBondPricingSWR(bondPricingRequest, requestOpts);
  const {
    action: { resetLastRun },
  } = usePricerDashboardPage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (cancel) cancel();
    resetLastRun();
    removeRunIdFromURL();
  }, [cancel, resetLastRun, setIsTimerRunning]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type BondCashFlowProjectionsStopProps = {
  bondCashFlowProjectionsRequest: BondCashFlowProjectionsRequest;
  opts: CustomRequestOptions;
};

export const BondCashFlowProjectionsStopRunningWrapper: React.FC<BondCashFlowProjectionsStopProps> = ({
  bondCashFlowProjectionsRequest,
  opts,
}: BondCashFlowProjectionsStopProps) => {
  const { cancel } = useGetBondCashFlowProjectionsSWR(bondCashFlowProjectionsRequest, opts);
  const {
    action: { resetLastRun },
  } = usePricerCashFlowsPage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (cancel) cancel();
    resetLastRun();
    removeRunIdFromURL();
  }, [setIsTimerRunning, cancel, resetLastRun]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type BondIndicativesStopProps = {
  opts: CustomRequestOptions;
  bondIndicativesRequest: BondIndicativesRequest;
};

export const BondIndicativesStopRunningWrapper: React.FC<BondIndicativesStopProps> = ({
  bondIndicativesRequest,
  opts,
}: BondIndicativesStopProps) => {
  const { cancel } = useGetBondIndicativesSWRForReplinePage(bondIndicativesRequest, opts);
  const {
    action: { resetLastRun },
  } = usePricerReplinePage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (cancel) cancel();
    resetLastRun();
    removeRunIdFromURL();
  }, [setIsTimerRunning, cancel, resetLastRun]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type BondPrepayTrackingStopProps = {
  bondPrepayTrackingRequest: BondPrepayTrackingRequest;
  requestOpts: CustomRequestOptions;
};

export const BondPrepayTrackingStopRunningWrapper: React.FC<BondPrepayTrackingStopProps> = ({
  bondPrepayTrackingRequest,
  requestOpts,
}: BondPrepayTrackingStopProps) => {
  const { cancel } = useGetBondPrepayTrackingSWR(bondPrepayTrackingRequest, requestOpts);
  const {
    action: { resetLastRun },
  } = usePricerTrackingPage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (cancel) cancel();
    resetLastRun();
    removeRunIdFromURL();
  }, [setIsTimerRunning, cancel, resetLastRun]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type BondPrepayProjectionsStopProps = {
  bondPrepayProjectionsRequest: BondPrepayProjectionsRequest;
  requestOpts: CustomRequestOptions;
};

export const BondPrepayProjectionsStopRunningWrapper: React.FC<BondPrepayProjectionsStopProps> = ({
  bondPrepayProjectionsRequest,
  requestOpts,
}: BondPrepayProjectionsStopProps) => {
  const { cancel } = useGetBondPrepayProjectionsSWR(bondPrepayProjectionsRequest, requestOpts);
  const {
    action: { resetLastRun },
  } = usePricerProjectionDetailPage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (cancel) cancel();
    resetLastRun();
    removeRunIdFromURL();
  }, [setIsTimerRunning, resetLastRun, cancel]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type BondCompareStopProps = {
  requestOpts: CustomRequestOptions;
  bondCompareRequest: CA_Mastr_Api_v1_0_Models_BondCompare_BondCompareRequest;
};

export const BondCompareStopRunningWrapper: React.FC<BondCompareStopProps> = ({
  bondCompareRequest,
}: BondCompareStopProps) => {
  const {
    action: { resetLastRun },
  } = usePricerBondComparePage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (bondCompareRequest.run_id) cancel(undefined, bondCompareRequest.run_id);
    resetLastRun();
    removeRunIdFromURL();
  }, [setIsTimerRunning, bondCompareRequest.run_id, resetLastRun]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type BatchRequestStopProps = {
  requestOpts: CustomRequestOptions;
  request: CA_Mastr_Api_v1_0_Models_Batch_BatchRequest;
  resetLastRun: () => void;
};

export const BatchRequestStopRunningWrapper: React.FC<BatchRequestStopProps> = ({
  request,
  resetLastRun,
}: BatchRequestStopProps) => {
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    const RUN_ID = getRunIdFromBatchRequestPayload(request);
    if (RUN_ID) cancel(undefined, RUN_ID);
    setIsTimerRunning(false);
    resetLastRun();
  }, [setIsTimerRunning, request, resetLastRun]);

  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};

type EmpiricalTrackingSimplifiedRequestStopProps = {
  token: string | undefined;
};

export const EmpiricalTrackingSimplifiedRequestStopRunningWrapper: React.FC<
  EmpiricalTrackingSimplifiedRequestStopProps
> = ({ token }) => {
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();
  const {
    action: { resetRun },
  } = usePricerEmpiricalTrackingSimplifiedPage();
  const { replaceWithoutRendering } = useQueryParameters();
  const { setToken } = useEmpiricalTrackingData();

  const triggerCancel = React.useCallback(async () => {
    resetRun();
    setIsTimerRunning(false);
    const bondName = getParam("bond_name");
    replaceWithoutRendering({ bond_name: bondName ?? undefined }, true);
    setToken(undefined);
    if (token) {
      try {
        await CancelService.postCancel(token);
      } catch (error) {
        showErrorToast("Error", getAPIErrorMessage(error));
      }
    }
  }, [setIsTimerRunning, replaceWithoutRendering, token, resetRun, setToken]);

  if (!token) {
    return <CARun disabled title={S.MODULES.EMPIRICAL_TRACKING.RUN} />;
  }
  return <CARun onClick={triggerCancel} isRunning={true} disabled={!token} title={S.MODULES.PRICER.STOP} />;
};
