import * as React from "react";
import { Box, Flex, HStack, Text } from "@chakra-ui/layout";
import { usePostPrepayTracking } from "@/utils/swr-hooks";
import CAGrid from "@/design-system/molecules/CAGrid";
import { getFormattedLocaleDate, getGridKeySubType } from "@/utils/helpers";
import PrepaymentTrackingColumnData from "@/utils/grid/PrepaymentTrackingColumnData";
import {
  CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest,
  CA_Mastr_Api_v1_0_Models_PrepayData_PrepayTrackingData,
} from "@/utils/openapi";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { wavgFunction } from "@/design-system/molecules/CAGrid/helpers";

const aggFuncs = {
  wavg: wavgFunction,
};

const PrepaymentTrackingGrid: React.FC<{
  prepayTrackingRequest: CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest;
  lastRun: string | undefined;
}> = ({
  prepayTrackingRequest,
  lastRun,
}: {
  prepayTrackingRequest: CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest;
  lastRun: string | undefined;
}) => {
  const {
    state: { security_info },
  } = usePricerModule();

  const { data, isLoading } = usePostPrepayTracking(prepayTrackingRequest, lastRun);

  const HeaderElement = React.useMemo(() => {
    const curvedDate = data?.prepay_tracking?.[0].curve_date ?? null;
    const celloCDUDate = data?.prepay_tracking?.[0].cello_cdu_date ?? null;
    return (
      <Flex wrap="wrap">
        {curvedDate && (
          <HStack mr="3">
            <Text variant="tableLeft">Curve Date</Text>
            <Text>{getFormattedLocaleDate(new Date(curvedDate))}</Text>
          </HStack>
        )}
        {celloCDUDate && (
          <HStack mr="3">
            <Text variant="tableLeft">Cello CDU Date</Text>
            <Text>{getFormattedLocaleDate(new Date(celloCDUDate))}</Text>
          </HStack>
        )}
      </Flex>
    );
  }, [data]);

  const gridType = `PrepaymentTrackingList_${getGridKeySubType(security_info?.sub_type ?? "")}`;
  return (
    <Box h="600px" minH="calc(100vh - 12rem)">
      <CAGrid<CA_Mastr_Api_v1_0_Models_PrepayData_PrepayTrackingData>
        className="PrepaymentTracking"
        hasRun={!!lastRun}
        gridType={gridType}
        headingElement={HeaderElement}
        gridProps={{
          columnDefs: PrepaymentTrackingColumnData,
          rowData: data?.prepay_tracking,
          loading: isLoading,
          aggFuncs,
          groupDefaultExpanded: -1,
        }}
        initialMessage={`Click on ▷ to load Prepayment Tracking.`}
        showExpand
        cardProps={{
          title: " ",
          cardKey: "pre-payment-tracking",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
        }}
        stackStyles={{
          h: "calc(100vh - 15rem)",
        }}
      />
    </Box>
  );
};

export default PrepaymentTrackingGrid;
