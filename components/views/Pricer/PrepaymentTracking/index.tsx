import * as React from "react";
import { Box } from "@chakra-ui/react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest } from "@/utils/openapi";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { usePrepaymentTrackingPage } from "@/contexts/PageContexts/PricerPrepaymentTrackingPageContext";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import { usePostPrepayTracking } from "@/utils/swr-hooks";
import { PrepayTrackingStopWatchWrapper } from "../PricerStopWatchWrapper";
import PricerHeader from "../PricerHeader";
import { PricerModuleProps } from "..";
import PrepaymentTrackingInput, { PrepaymentTrackingInputHandler } from "./PrepaymentTrackingInput";
import PrepaymentTrackingGrid from "./PrepaymentTrackingGrid";

const PricerPrepaymentTrackingView: React.FC<PricerModuleProps> = ({ pageTitle }: PricerModuleProps) => {
  const {
    state: { lastRun, prepaymentTrackingPageSettings },
    action: { updatePaymentTrackingPageSettings, run },
  } = usePrepaymentTrackingPage();
  const {
    action: { setIsTimerRunning },
  } = usePricerModule();

  const prepaymentTrackingInput = React.useRef<PrepaymentTrackingInputHandler>(null);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    prepaymentTrackingInput.current?.handleSubmit((data) => {
      updatePaymentTrackingPageSettings(data);
      setIsTimerRunning(true);
      run({ no_cache });
    })();
  };

  const {
    prepayTrackingRequest,
  }: {
    prepayTrackingRequest: CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest;
  } = React.useMemo(() => {
    const prepayTrackingRequest: CA_Mastr_Api_v1_0_Models_PrepayData_PrepayDataRequest = {
      factor_date: parseDateToYYYYMMDD(prepaymentTrackingPageSettings.factor_date),
      model_version: prepaymentTrackingPageSettings.model_version as string,
      product_types: prepaymentTrackingPageSettings.product_types,
      stories: prepaymentTrackingPageSettings.stories,
      begin_origination_year: prepaymentTrackingPageSettings.begin_origination_year as number,
      end_origination_year: prepaymentTrackingPageSettings.end_origination_year as number,
      begin_current_balance: 1000000000 * (prepaymentTrackingPageSettings.begin_current_balance ?? 0),
      end_current_balance: 1000000000 * (prepaymentTrackingPageSettings.end_current_balance ?? 0),
    };

    return { prepayTrackingRequest };
  }, [prepaymentTrackingPageSettings]);

  const { data } = usePostPrepayTracking(prepayTrackingRequest, lastRun);

  return (
    <Box>
      <PricerHeader
        title={pageTitle}
        needsBond={false}
        onRunClick={onSubmit}
        stopWatch={
          <PrepayTrackingStopWatchWrapper
            prepayTrackingRequest={prepayTrackingRequest}
            lastRun={data?.status?.start_time ?? lastRun}
          />
        }
      />
      <MainInputContentTemplate inputs={<PrepaymentTrackingInput ref={prepaymentTrackingInput} />}>
        <PrepaymentTrackingGrid prepayTrackingRequest={prepayTrackingRequest} lastRun={lastRun} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default PricerPrepaymentTrackingView;
