import { Box, Flex, Spacer, Text, VStack } from "@chakra-ui/react";
import * as React from "react";
import { Controller, UseFormHandleSubmit, useForm } from "react-hook-form";
import { usePricerModule } from "@/contexts/ModuleContexts/PricerModuleContext";
import CACard from "@/design-system/molecules/CACard";
import CADateInput from "@/design-system/molecules/CADateInput";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getFormattedStringOptions, getInfoMsg } from "@/utils/helpers";
import { prepaymentTrackingPageSettingsType } from "@/contexts/PageContexts/PricerPrepaymentTrackingPageContext/PricerPrepaymentTrackingPageContextTypes";
import { usePrepaymentTrackingPage } from "@/contexts/PageContexts/PricerPrepaymentTrackingPageContext";
import CAInput from "@/design-system/molecules/CAInput";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { AGENCY_GPL } from "@/constants/enums";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";

export interface PrepaymentTrackingInputHandler {
  handleSubmit: UseFormHandleSubmit<prepaymentTrackingPageSettingsType>;
}

const PrepaymentTrackingInput: React.ForwardRefRenderFunction<PrepaymentTrackingInputHandler> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const {
    state: { latestFactorDate, userSettings },
  } = usePricerModule();
  const {
    state: { prepaymentTrackingPageSettings, prepaymentTrackingPageSettingsCopy },
    action: { updateFactorDate },
  } = usePrepaymentTrackingPage();

  React.useEffect(() => {
    if (!prepaymentTrackingPageSettings.factor_date) {
      const factorDate = latestFactorDate || new Date(new Date().setDate(1));
      updateFactorDate(factorDate);
    }
  }, [latestFactorDate, prepaymentTrackingPageSettings.factor_date, updateFactorDate]);

  const { handleSubmit, reset, register, watch, control, setValue } = useForm<prepaymentTrackingPageSettingsType>({
    defaultValues: {
      model_version: metadata?.pricer_settings?.version?.find((opt) => opt.is_default)?.value,
    },
  });
  const [
    watch_factor_date,
    watch_model_version,
    watch_product_types,
    watch_stories,
    watch_begin_origination_year,
    watch_end_origination_year,
    watch_begin_current_balance,
    watch_end_current_balance,
  ] = watch([
    "factor_date",
    "model_version",
    "product_types",
    "stories",
    "begin_origination_year",
    "end_origination_year",
    "begin_current_balance",
    "end_current_balance",
  ]);

  const storyOptions = React.useMemo(() => {
    return watch_product_types?.includes(AGENCY_GPL)
      ? metadata?.basic_search_settings?.spec_pool_gpl_story ?? []
      : (metadata?.basic_search_settings?.spec_pool_story ?? [])
          .filter((opt) => watch_product_types?.some((type) => opt.supported_agencies?.includes(type)))
          .map((opt) => ({
            ...opt,
            value: opt.value.replace(/[()]/g, ""),
          })); // spec_pool_story values contain () which needs to be stripped out
  }, [metadata, watch_product_types]);

  React.useEffect(() => {
    if (userSettings.model_version) {
      setValue("model_version", userSettings.model_version);
    }
  }, [setValue, userSettings.model_version]);

  const originationYears = React.useMemo(
    () =>
      getFormattedStringOptions(metadata?.basic_search_settings?.spec_pool_year ?? []).map((opt) => ({
        ...opt,
        value: opt.displayValue,
      })), // For spec_pool_year, we need to send the display value instead of the value on this request. i.e. 2020 instead of 20
    [metadata]
  );

  React.useEffect(() => {
    const tempDate = new Date(latestFactorDate ? latestFactorDate.getTime() : new Date().getTime());

    const defaultOriginationBeginYear = originationYears.length ? Number(originationYears[0].value) : undefined;
    const defaultOriginationEndYear = originationYears.length
      ? Number(originationYears[originationYears.length - 1].value)
      : undefined;

    reset({
      ...prepaymentTrackingPageSettings,
      factor_date: prepaymentTrackingPageSettings.factor_date ?? tempDate,
      begin_origination_year: prepaymentTrackingPageSettings.begin_origination_year ?? defaultOriginationBeginYear,
      end_origination_year: prepaymentTrackingPageSettings.end_origination_year ?? defaultOriginationEndYear,
    });
  }, [latestFactorDate, originationYears, prepaymentTrackingPageSettings, reset]);

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  return (
    <CACard
      title="Inputs"
      allowCollapse
      cardKey="prepayment-tracking-inputs"
      cardStyleOnOpen={{
        height: "100%",
      }}
    >
      <VStack alignItems="stretch" spacing={4}>
        <Box>
          <Controller
            name={"factor_date"}
            control={control}
            rules={{ required: true }}
            render={({ field: { name, value, onChange, ref } }) => {
              return (
                <CADateInput
                  ref={ref}
                  maxDate={latestFactorDate}
                  width={"7rem"}
                  selectedDate={value}
                  onChange={onChange}
                  name={name}
                  label={"Factor Month"}
                  showMonthYearPicker
                  info={getInfoMsg(watch_factor_date, prepaymentTrackingPageSettingsCopy.factor_date)}
                />
              );
            }}
          />
          <CASelectDropdown
            label={"Model"}
            width={"7rem"}
            info={getInfoMsg(watch_model_version, prepaymentTrackingPageSettingsCopy.model_version)}
            {...register("model_version", {
              required: true,
            })}
            options={getFormattedStringOptions(metadata?.pricer_settings?.version)}
          />
          <Controller
            control={control}
            name="product_types"
            rules={{ required: true }}
            render={({ field: { ref, name, value, onChange } }) => (
              <CAMultiSelectDropdown
                ref={ref}
                info={getInfoMsg(
                  watch_product_types?.length > 0 ? watch_product_types.join(", ") : undefined,
                  prepaymentTrackingPageSettings.product_types.length > 0
                    ? prepaymentTrackingPageSettings.product_types.join(", ")
                    : undefined
                )}
                label="Product Types"
                value={value ?? undefined}
                name={name}
                width="7rem"
                shouldOnlyReturnValue
                hasOptionDescription
                isMultiSelect
                options={getFormattedStringOptions(metadata?.basic_search_settings?.spec_pool_agency ?? [])}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                onChange={(selectedOption: any) => {
                  const lastSelectedOption = selectedOption[selectedOption.length - 1];
                  if (lastSelectedOption === AGENCY_GPL) {
                    setValue("stories", []);
                    onChange([AGENCY_GPL]);
                  } else {
                    onChange(selectedOption.filter((opt: string) => opt !== AGENCY_GPL));
                  }
                }}
              />
            )}
          />
          <Controller
            control={control}
            name="stories"
            rules={{ required: true }}
            render={({ field: { ref, name, value, onChange } }) => (
              <CAMultiSelectDropdown
                ref={ref}
                info={getInfoMsg(
                  watch_stories?.length > 0 ? watch_stories.join(", ") : undefined,
                  prepaymentTrackingPageSettings.stories.length > 0
                    ? prepaymentTrackingPageSettings.stories?.join(", ")
                    : undefined
                )}
                label="Stories"
                value={value ?? undefined}
                name={name}
                width="7rem"
                isMultiSelect
                shouldOnlyReturnValue
                hasOptionDescription
                options={getFormattedStringOptions(storyOptions)}
                onChange={(selectedOption) => {
                  onChange(selectedOption as string);
                }}
              />
            )}
          />
          <Flex alignItems="center">
            <Text variant="tableLeft">Orig Year</Text>
            <Spacer />
            <Box ml="1">
              <CASelectDropdown
                label={""}
                width={"4.3rem"}
                hideLabel={true}
                info={getInfoMsg(
                  watch_begin_origination_year,
                  prepaymentTrackingPageSettingsCopy.begin_origination_year
                )}
                {...register("begin_origination_year", {
                  required: true,
                })}
                options={originationYears}
              />
            </Box>
            <Box ml="1">
              <CASelectDropdown
                label={""}
                width={"4.3rem"}
                hideLabel={true}
                info={getInfoMsg(watch_end_origination_year, prepaymentTrackingPageSettingsCopy.end_origination_year)}
                {...register("end_origination_year", {
                  required: true,
                })}
                options={originationYears}
              />
            </Box>
          </Flex>
          <Flex alignItems="center">
            <Text variant="tableLeft">Balance ($B)</Text>
            <Spacer />
            <Box ml="1">
              <CAInput
                label={""}
                width={"4.3rem"}
                inputType="no-negative"
                type="number"
                hideLabel={true}
                info={getInfoMsg(watch_begin_current_balance, prepaymentTrackingPageSettingsCopy.begin_current_balance)}
                {...register("begin_current_balance", {
                  required: true,
                })}
              />
            </Box>
            <Box ml="1">
              <CAInput
                label={""}
                width={"4.3rem"}
                hideLabel={true}
                inputType="no-negative"
                type="number"
                info={getInfoMsg(watch_end_current_balance, prepaymentTrackingPageSettingsCopy.end_current_balance)}
                {...register("end_current_balance", {
                  required: true,
                })}
              />
            </Box>
          </Flex>
        </Box>
      </VStack>
    </CACard>
  );
};

export default React.forwardRef(PrepaymentTrackingInput);
