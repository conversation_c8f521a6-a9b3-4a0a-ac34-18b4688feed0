import { IServerSideGetRowsParams } from "ag-grid-community";
import { formatParamsForServerSide } from "@/design-system/molecules/CAGrid/helpers";
import { showErrorToast } from "@/design-system/theme/toast";
import { getAPIErrorMessage } from "@/utils/helpers";
import { AuditHistoryService, CA_Mastr_Api_v1_0_Models_AuditHistory_AuditHistory } from "@/utils/openapi";

export function createServerSideDatasource(setTotalRow: (totalRow: number) => void) {
  const cutoff_date = new Date().toISOString();
  return {
    getRows: async function (params: IServerSideGetRowsParams<CA_Mastr_Api_v1_0_Models_AuditHistory_AuditHistory>) {
      try {
        const _params = formatParamsForServerSide(params);
        const response = await AuditHistoryService.postSearch({
          ..._params,
          cutoff_date,
        });
        params.api.hideOverlay();

        setTotalRow(response.total_rows || 0);

        if (response.total_rows === 0) {
          params.api.showNoRowsOverlay();
        }

        const hasMoreRows = (response.total_rows || 0) > (params.request.endRow || 100);

        params.success({
          rowData: response.audit_histories ?? [],
          rowCount: !response.audit_histories?.length ? 0 : !hasMoreRows ? response.total_rows || 0 : undefined,
        });
      } catch (error) {
        params.fail();
        showErrorToast("Error", getAPIErrorMessage(error) || "");
      }
    },
  };
}
