import React from "react";
import { ColDef, GridReadyEvent } from "ag-grid-community";
import { Button, Flex, Icon, Text } from "@chakra-ui/react";
import { HiOutlineRefresh } from "react-icons/hi";
import { AgGridReact } from "ag-grid-react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { StatusBar } from "@/design-system/molecules/StatusBar/StatusBar";
import CAHeading from "@/design-system/atoms/CAHeading";
import { auditHistoryColumnDefs } from "@/utils/grid/AuditHistoryColumnData";
import { createServerSideDatasource } from "./serverSideDatasource";

const defaultColDef: ColDef = {
  resizable: true,
  floatingFilter: true,
  enableRowGroup: false,
  filter: true,
  filterParams: {
    maxNumConditions: 1,
  },
};

export const AuditHistoryView = () => {
  const gridRef = React.useRef<AgGridReact>(null);

  const [totalRow, setTotalRow] = React.useState<number | undefined>();

  const handleTotalRow = React.useCallback((totalRow: number) => {
    setTotalRow(totalRow);
  }, []);

  const handleRefreshGrid = () => {
    //Update cutOffDate to get the latest data
    const datasource = createServerSideDatasource(handleTotalRow);
    gridRef.current?.api?.setGridOption("serverSideDatasource", datasource);
  };

  const onGridReady = (params: GridReadyEvent) => {
    const datasource = createServerSideDatasource(handleTotalRow);
    // register the datasource with the grid
    params.api.setGridOption("serverSideDatasource", datasource);
  };

  return (
    <Flex
      flexDirection="column"
      px={{ base: "4", sm: "5", md: "7" }}
      justifyContent="space-between"
      minH="1.75rem"
      w="full"
      mt={2}
    >
      <CAHeading lineHeight="2.5rem" verticalAlign="center" as="h1" mb={2}>
        Audit History
      </CAHeading>
      <CAGrid
        ref={gridRef}
        hideSearch
        gridType="audit-history"
        contextMenuHandlers={{
          showCollapseAll: false,
          showExpandAll: false,
        }}
        headerActionButtons={
          <Button
            height={8}
            leftIcon={<Icon as={HiOutlineRefresh} fontSize="14" />}
            variant="iconButton"
            onClick={handleRefreshGrid}
          >
            <Text color="inherit">Refresh</Text>
          </Button>
        }
        gridProps={{
          columnDefs: auditHistoryColumnDefs,
          suppressAutoSize: true,
          defaultColDef,
          sideBar: {
            toolPanels: [
              {
                id: "columns",
                labelDefault: "Columns",
                labelKey: "columns",
                iconKey: "columns",
                toolPanel: "agColumnsToolPanel",
                toolPanelParams: {
                  suppressRowGroups: true,
                  suppressValues: true,
                  suppressPivotMode: true,
                },
              },
              "filters",
            ],
          },
          statusBar: undefined,
          //server side props
          cacheBlockSize: 100,
          rowModelType: "serverSide",
          onGridReady: onGridReady,
          blockLoadDebounceMillis: 500,
          grandTotalRow: "bottom",
        }}
        //styles
        stackStyles={{
          h: "calc(100vh - 15rem)",
        }}
        cardProps={{
          borderBottomRadius: 0,
        }}
      />
      <StatusBar totalRow={totalRow} />
    </Flex>
  );
};
