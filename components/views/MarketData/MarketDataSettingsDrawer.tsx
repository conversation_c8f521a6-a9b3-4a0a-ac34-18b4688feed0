import { Text } from "@chakra-ui/layout";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Drawer,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>lex,
  H<PERSON>tack,
  Spacer,
  VStack,
  chakra,
  useBreakpointValue,
  useColorModeValue,
} from "@chakra-ui/react";
import * as React from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { IoSyncCircleOutline } from "react-icons/io5";
import { useAuthentication } from "@/contexts/AppGlobalContexts/AuthenticationContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import CAHeadingOriginal from "@/design-system/atoms/CAHeading";
import CADateInput from "@/design-system/molecules/CADateInput";
import CAInput from "@/design-system/molecules/CAInput";
import CASearch from "@/design-system/molecules/CASearch";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CASwitchInput from "@/design-system/molecules/CASwitchInput";
import { getFormattedStringOptions, isSuperAdmin, parseDateToYYYYMMDD } from "@/utils/helpers";
import useResetFormData from "@/hooks/useResetFormData";
import { searchByKeywords } from "@/hooks/useKeywordsSearch";
import { MarketDataUserSettingsType } from "@/contexts/ModuleContexts/MarketDataModuleContext/MarketDataModuleContextTypes";
import { useMarketDataModelVersionsSWR } from "@/utils/swr-hooks/MarketData";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataModelVersionsResponse } from "@/utils/openapi";
import { LastCloseDateHeader } from "@/design-system/organisms/LastCloseDateHeader/LastCloseDateHeader";
import MarketDataValueDisplayWrapper from "./shared/MarketDataValueDisplayWrapper";

export type MarketDataSettingsDrawerProps = {
  onClose: () => void;
  isOpen: boolean;
};

const SectionHeading: React.FC<React.PropsWithChildren & { allCaps?: boolean }> = ({ children, allCaps = true }) => (
  <Box pt={2}>
    <CAHeadingOriginal as={"h3"} variant={"subHeading"} textTransform={allCaps ? "uppercase" : "none"}>
      {children}
    </CAHeadingOriginal>
  </Box>
);

const MarketDataSettingsDrawer: React.FC<MarketDataSettingsDrawerProps> = ({
  onClose,
  isOpen,
}: MarketDataSettingsDrawerProps) => {
  const {
    state: { userData },
  } = useAuthentication();
  const {
    state: { userSettings, activeSettingsDrawerKey },
    action: { updateMarketDataUserSettings, resetMarketDataUserSettings },
  } = useMarketDataModule();
  const [searchKey, setSearchKey] = React.useState("");
  const isMobile = useBreakpointValue({ sm: true, md: false });

  const { register, handleSubmit, reset, control, setValue, setError } = useForm<MarketDataUserSettingsType>({
    defaultValues: userSettings,
  });

  const [curveDate, modelVersion, marketDataSource] = useWatch({
    control,
    name: ["curve_date", "model_version", "market_data_source"],
  });

  useResetFormData({ reset, formData: userSettings });

  const { data: modelVersions } = useMarketDataModelVersionsSWR(parseDateToYYYYMMDD(curveDate), marketDataSource, {
    onError: () => {
      setValue("model_version", "");
      setError("model_version", { message: "Error fetching model versions" });
    },
    onSuccess: (data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataModelVersionsResponse) => {
      const defaultModelVersion = data?.market_data_models?.find((model) => model.is_default)?.value;

      const currentModelVersion = data?.market_data_models?.find(
        (model) => model.value === userSettings.model_version
      )?.value;

      setValue("model_version", defaultModelVersion || currentModelVersion || data?.market_data_models?.[0].value);
    },
  });

  const onSubmit = (data: MarketDataUserSettingsType) => {
    updateMarketDataUserSettings(data, false);
    closeDrawer(data);
  };

  const onReset = () => {
    resetMarketDataUserSettings();
    setSearchKey("");
  };

  const onSearchTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchKey(e.target.value);
  };

  const setDisplayForField = (fieldName: string) => {
    return searchByKeywords(searchKey, fieldName) ? "block" : "none";
  };

  const closeDrawer = (data?: MarketDataUserSettingsType) => {
    setSearchKey("");
    reset(data ? data : userSettings);
    onClose();
  };

  const isActiveKey = (settingsKey: string) => {
    return settingsKey === activeSettingsDrawerKey && !isMobile;
  };

  const onCurveDateChange = (curveDate: Date, onChange: (date: Date) => void) => {
    onChange(curveDate);
  };

  return (
    <Drawer autoFocus={false} placement="right" onClose={() => closeDrawer()} isOpen={isOpen} size={"xs"}>
      <DrawerOverlay>
        <DrawerContent overflowY="scroll">
          <chakra.form p={4} onSubmit={handleSubmit(onSubmit)} position="relative">
            <Box pt={3} pb={2}>
              <CASearch
                placeholder="Filter"
                hideLabel={true}
                name={"searchSettings"}
                onChange={onSearchTextChange}
                bg={useColorModeValue("celloBlue.25", "celloBlue.1000")}
              />
            </Box>
            <VStack alignItems="stretch" pt={5}>
              <SectionHeading>Market Data</SectionHeading>
              <Box display={setDisplayForField("Market Data Source")}>
                <CASelectDropdown
                  label={"Market Data Source"}
                  {...register("market_data_source")}
                  {...{ autoFocus: isActiveKey("Market Data Source") }}
                  options={getFormattedStringOptions(userData?.user?.market_data_sources)}
                />
              </Box>
              <Box display={setDisplayForField("Curve Date")}>
                <Controller
                  name={"curve_date"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref, onBlur } }) => (
                    <CADateInput
                      ref={ref}
                      popperPlacement="bottom-end"
                      selectedDate={value}
                      onChange={(date: Date) => onCurveDateChange(date, onChange)}
                      name={name}
                      label={"Curve Date"}
                      openWithDelay={isActiveKey("Curve Date")}
                      {...{ autoFocus: isActiveKey("Curve Date") }}
                      headerEl={({ closeDatePicker }) => (
                        <LastCloseDateHeader
                          onChange={(value) => {
                            onChange(value);
                            onBlur();
                          }}
                          closeDatePicker={closeDatePicker}
                        />
                      )}
                    />
                  )}
                />
              </Box>
              <Box display={setDisplayForField("Model Version")}>
                <Controller
                  name={"model_version"}
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { name, value, onChange, ref } }) => (
                    <CASelectDropdown
                      width="150px"
                      options={getFormattedStringOptions(modelVersions?.market_data_models)}
                      name={name}
                      label={"Model Version"}
                      value={value}
                      onChange={onChange}
                      ref={ref}
                      info={
                        !modelVersion && !modelVersions?.market_data_models
                          ? { show: true, msg1: `Model Version is required` }
                          : undefined
                      }
                      {...{ autoFocus: isActiveKey("Model Version") }}
                    />
                  )}
                />
              </Box>

              <SectionHeading>Application</SectionHeading>
              <MarketDataValueDisplayWrapper
                name="Version"
                value={process.env.NEXT_PUBLIC_GIT_TAG || process.env.NEXT_PUBLIC_GIT_HASH || "-"}
                _key="application_version"
              />
              <Box>
                <HStack justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text _hover={{ cursor: "pointer" }} variant="primary">
                      Use Cache
                    </Text>
                  </Box>
                  <Box>
                    <Controller
                      name={"useCache"}
                      control={control}
                      render={({ field: { name, onChange, value } }) => {
                        return (
                          <CASwitchInput
                            mr="-2"
                            name={name}
                            hideLabel={true}
                            value={"useCache"}
                            defaultChecked={value}
                            isChecked={value}
                            onChange={onChange}
                          />
                        );
                      }}
                    />
                  </Box>
                </HStack>
              </Box>
              {isSuperAdmin(userData) && (
                <Box display={setDisplayForField("Timeout Minutes")}>
                  <CAInput
                    type="number"
                    inputType="positive-integer"
                    label={"Timeout (min)"}
                    {...register("timeoutMinutes", {
                      valueAsNumber: true,
                      required: true,
                    })}
                    {...{ autoFocus: isActiveKey("Timeout Minutes") }}
                  />
                </Box>
              )}
            </VStack>
            <Flex
              direction="row"
              mt={4}
              pt={3}
              pb={3}
              position="sticky"
              bottom={0}
              bg={useColorModeValue("white", "celloBlue.900")}
            >
              <Button
                leftIcon={<IoSyncCircleOutline />}
                onClick={onReset}
                size="3xl"
                fontSize="3xl"
                variant="secondary"
                pr="2"
                pl="1"
                title={`Defaults`}
                aria-label={`Defaults`}
              >
                <Text>Defaults</Text>
              </Button>
              <Spacer />
              <HStack spacing={2}>
                <Button variant="secondary" size="sm" onClick={() => closeDrawer()}>
                  Cancel
                </Button>
                <Button variant="primary" size="sm" type="submit">
                  Save
                </Button>
              </HStack>
            </Flex>
          </chakra.form>
        </DrawerContent>
      </DrawerOverlay>
    </Drawer>
  );
};

export default MarketDataSettingsDrawer;
