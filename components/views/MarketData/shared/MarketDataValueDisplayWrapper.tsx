import * as React from "react";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { getDefaultsOfMetadata, getDisplayValueByKey } from "@/utils/helpers";
import MarketDataValueDisplay from "./MarketDataValueDisplay";

export type MarketDataValueDisplayWrapperProps = {
  name: string;
  link?: boolean;
  _key?: string;
  runid?: string;
  value?: string | React.JSX.Element;
  dateFormatter?: (dateString: Date | string | undefined | null) => void;
  drawerKey?: string;
  ignoreChanges?: boolean;
  showChanges?: boolean;
};

const MarketDataValueDisplayWrapper: React.FC<MarketDataValueDisplayWrapperProps> = ({
  name,
  link,
  _key,
  value,
  dateFormatter,
  drawerKey,
  showChanges = true,
  ...rest
}: MarketDataValueDisplayWrapperProps) => {
  const {
    state: { metadata },
  } = useGlobalContext();

  const {
    state: { userSettings, userSettingsCopy },
  } = useMarketDataModule();
  const key = _key?.split(".")[1] ? _key?.split(".")[1] : _key ?? "";
  const settings = userSettings as { [key: string]: string | undefined };
  const settingsCopy = userSettingsCopy as { [key: string]: string | undefined };

  //Message if value changes
  const hasChanged = showChanges
    ? settings[key]?.toString() !== settingsCopy[key]?.toString() && settingsCopy[key] !== undefined
    : false;
  const changedMsg = `Value changed from ${
    dateFormatter ? dateFormatter(settingsCopy[key]) : getDisplayValueByKey(settingsCopy[key], metadata)
  } to ${dateFormatter ? dateFormatter(settings[key]) : getDisplayValueByKey(settings[key], metadata)}`;

  //Get default Values from metadata based on `is_default` field
  const defaultValues = getDefaultsOfMetadata(_key, metadata?.pricer_settings);

  const defaultValue = dateFormatter
    ? new Date(defaultValues?.value || "").toString()
    : defaultValues?.value?.toString();

  //Show this message if settings values are different from default values from metadata
  const hasOutdated = settings[key]?.toString() !== defaultValue && !hasChanged && defaultValue !== undefined;

  const msg2 = hasOutdated
    ? `Default value is ${dateFormatter ? dateFormatter(defaultValue) : defaultValues?.display_value}`
    : "Click run to update your calculation";

  return (
    <MarketDataValueDisplay
      name={name}
      drawerKey={drawerKey}
      link={link}
      value={
        value ||
        ((dateFormatter ? dateFormatter(settings[key]) : getDisplayValueByKey(settings[key], metadata)) ?? "Unset")
      }
      info={{
        show: hasChanged || hasOutdated,
        msg1: hasChanged ? changedMsg : undefined,
        msg2: msg2,
        type: hasChanged ? "changed" : hasOutdated ? "outdated" : "unset",
      }}
      {...rest}
    />
  );
};

export default MarketDataValueDisplayWrapper;
