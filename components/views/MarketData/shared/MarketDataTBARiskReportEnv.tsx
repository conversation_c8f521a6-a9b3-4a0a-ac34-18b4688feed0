import { Flex, FormLabel, Text } from "@chakra-ui/react";
import React from "react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getFormattedStringOptions, parseDateToYYYYMMDD } from "@/utils/helpers";
import { useTBARiskModelVersionsSWR } from "@/utils/swr-hooks/MarketData";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";

export const MarketDataTBARiskReportEnv = ({
  value,
  onChange,
}: {
  value: string | undefined;
  onChange: (value: string | undefined) => void;
}) => {
  const {
    state: {
      userSettings: { curve_date: curveDate },
    },
  } = useMarketDataModule();

  const { data: tbaModelVersions } = useTBARiskModelVersionsSWR(parseDateToYYYYMMDD(curveDate), {
    onError: () => {
      onChange("");
    },
    onSuccess: (data) => {
      const modelVersionIsAlreadySelected = data?.tba_risk_models?.some((model) => model.value === value);

      if (modelVersionIsAlreadySelected) return;

      const defaultModelVersion = data?.tba_risk_models?.find((model) => model.is_default)?.value;
      const firstModelVersion = data?.tba_risk_models?.[0].value;

      onChange(defaultModelVersion || firstModelVersion);
    },
  });

  return (
    <Flex alignItems="center" gap={2}>
      <FormLabel data-testid="ca-form-label" m={0} fontWeight="base">
        <Text variant="tableLeft" whiteSpace="nowrap">
          Environment
        </Text>
      </FormLabel>

      <CASelectDropdown
        width="320px"
        options={getFormattedStringOptions(tbaModelVersions?.tba_risk_models)}
        name="riskModelVersion"
        value={value}
        onChange={(e) => {
          const val = e.target.value;
          onChange(val);
        }}
        info={
          !value
            ? {
                show: true,
                msg1: "Risk Model Version is required, please select a different Curve Date in the Settings Drawer.",
              }
            : undefined
        }
      />
    </Flex>
  );
};
