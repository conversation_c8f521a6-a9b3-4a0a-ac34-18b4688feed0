import React from "react";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { useMarketDataModelVersionsSWR } from "@/utils/swr-hooks/MarketData";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import MarketDataValueDisplay from "./MarketDataValueDisplay";

export const MarketDataModelVersionDisplay = () => {
  const {
    state: { userSettings, userSettingsCopy },
  } = useMarketDataModule();

  const { data: modelVersions } = useMarketDataModelVersionsSWR(
    parseDateToYYYYMMDD(userSettingsCopy.curve_date),
    userSettingsCopy.market_data_source
  );

  const modelVersionValue = userSettings.model_version;

  const isDefault = modelVersions
    ? modelVersions.market_data_models?.find((modelVersion) => modelVersion.is_default)?.value === modelVersionValue
    : true;

  const msg2 = !isDefault
    ? `Default value is ${modelVersions?.market_data_models?.find((modelVersion) => modelVersion.is_default)?.value}`
    : "Click run to update your calculation";

  return (
    <MarketDataValueDisplay
      name="Model Version"
      link
      drawerKey={"Model Version"}
      value={modelVersionValue ?? ""}
      info={{
        show: !isDefault || false,
        msg1: !isDefault ? "Non-default value is used." : "",
        msg2: msg2,
        type: !isDefault ? "outdated" : "unset",
      }}
    />
  );
};
