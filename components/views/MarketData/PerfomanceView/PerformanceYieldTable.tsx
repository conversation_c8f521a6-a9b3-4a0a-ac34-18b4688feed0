import React from "react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { useYieldCurveDetails } from "@/utils/swr-hooks";
import CAGrid from "@/design-system/molecules/CAGrid";
import { yieldCurveDetailsColumnData } from "@/utils/grid/PerformanceColumnData";
import CACard from "@/design-system/molecules/CACard";

interface Props {
  asOfDate: Date | undefined;
  lastRun?: string;
}

type TGridData = {
  num_years?: number | null | undefined;
  swap_prev_value?: number | null | undefined;
  swap_value?: number | null | undefined;
  swap_change?: number | null | undefined;
  tsy_prev_value?: number | null | undefined;
  tsy_value?: number | null | undefined;
  tsy_change?: number | null | undefined;
  spread_prev_value?: number | null | undefined;
  spread_value?: number | null | undefined;
  spread_change?: number | null | undefined;
};

type Acc = Record<string, Record<string, number | undefined | null>>;

export const PerformanceYieldTable = ({ asOfDate }: Props) => {
  const { data, isLoading } = useYieldCurveDetails(asOfDate ? parseDateToYYYYMMDD(new Date(asOfDate)) : undefined);

  const gridData: (TGridData | undefined)[] | undefined = data?.yield_curve_details
    ?.map((el) => {
      if (el.category === "Swap")
        return {
          num_years: el.num_years,
          swap_prev_value: el.prev_value,
          swap_value: el.value,
          swap_change: el.change,
        };
      if (el.category === "Treasury") {
        return {
          num_years: el.num_years,
          tsy_prev_value: el.prev_value,
          tsy_value: el.value,
          tsy_change: el.change,
        };
      }
      if (el.category === "Spread") {
        return {
          num_years: el.num_years,
          spread_prev_value: el.prev_value,
          spread_value: el.value,
          spread_change: el.change,
        };
      }
    })
    .filter((el) => el !== undefined);

  const mergedDataByTerm =
    gridData &&
    Object.values(
      gridData?.reduce((r: Acc, nextVal) => {
        if (nextVal?.num_years !== undefined && nextVal?.num_years !== null) {
          const { num_years, ...rest } = nextVal;
          r[num_years] = r[num_years] || { num_years };
          r[num_years] = { ...r[num_years], ...rest };
          return r;
        }
        return {};
      }, {})
    );

  return (
    <CACard
      title="Yield Curves"
      w="auto"
      cardBodyStyle={{ p: 0, pr: 5 }}
      allowCollapse
      cardKey="performance-yield-curves"
    >
      <CAGrid<Record<string, number | null | undefined>>
        className="MarketDataTable FullWidth"
        hideHeader
        hasRun={!!mergedDataByTerm}
        gridProps={{
          columnDefs: yieldCurveDetailsColumnData,
          rowData: mergedDataByTerm,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading: isLoading,
        }}
      />
    </CACard>
  );
};
