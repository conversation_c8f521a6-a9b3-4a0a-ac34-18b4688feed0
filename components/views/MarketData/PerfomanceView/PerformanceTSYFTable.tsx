import React from "react";
import { Box, Flex, Text } from "@chakra-ui/react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { useTreasuryFutureRiskDetails } from "@/utils/swr-hooks";
import CAGrid from "@/design-system/molecules/CAGrid";
import { treasuryRiskDetailsColumnData } from "@/utils/grid/PerformanceColumnData";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import CACard from "@/design-system/molecules/CACard";

interface Props {
  asOfDate: Date | undefined;
}

export const PerformanceTSYFTable = ({ asOfDate }: Props) => {
  const { data, isLoading } = useTreasuryFutureRiskDetails(
    asOfDate ? parseDateToYYYYMMDD(new Date(asOfDate)) : undefined
  );

  const [tsyfContract, setTsyfContract] = React.useState<string>(
    () => safeSessionStorage.getItem(SESSION_STORAGE_KEY.PERFORMANCE_TSYF_CONTRACT) || ""
  );

  const firstContract = data?.treasury_future_risk_details?.[0].contract?.toLowerCase();
  const gridData = data?.treasury_future_risk_details?.filter((el) => {
    return el.contract?.toLowerCase() === (tsyfContract || firstContract);
  });

  const contracts = data?.treasury_future_risk_details?.map((el) => el.contract);
  const uniqueContractsOptions = [...new Set(contracts)].map((el) => ({
    id: el || "",
    displayValue: el,
    value: el?.toLowerCase(),
  }));

  const onContractChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTsyfContract(e.currentTarget.value);
    safeSessionStorage.setItem(SESSION_STORAGE_KEY.PERFORMANCE_TSYF_CONTRACT, e.currentTarget.value);
  };

  return (
    <CACard title="TSYF" cardBodyStyle={{ p: 0, pr: 5 }} allowCollapse cardKey="performance-TSYF" w="auto">
      <Box>
        <CAGrid
          className="MarketDataTable FullWidth"
          hideSearch
          enableUserGridViews={false}
          headerActionButtons={
            <Flex gridGap="2" alignItems="center" justifyContent="flex-start">
              <Text variant="primary" whiteSpace="nowrap">
                Contract
              </Text>
              <CASelectDropdown
                width="6rem"
                defaultValue={uniqueContractsOptions[0]?.value}
                value={tsyfContract}
                options={uniqueContractsOptions}
                name="tsyf-contract"
                onChange={onContractChange}
              />
            </Flex>
          }
          headerStyles={{
            py: 0,
          }}
          hasRun={!isLoading}
          gridProps={{
            columnDefs: treasuryRiskDetailsColumnData,
            rowData: gridData,
            domLayout: "autoHeight",
            sideBar: false,
            statusBar: undefined,
            loading: isLoading,
          }}
        />
      </Box>
    </CACard>
  );
};
