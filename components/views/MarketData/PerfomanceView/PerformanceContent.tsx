import { Stack } from "@chakra-ui/react";
import React from "react";
import ColumnWrapper from "@/components/helpers/ColumnWrapper";
import { useMarketDataPerformancePage } from "@/contexts/PageContexts/MarketDataPerfomancePageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { PerformanceTBATable } from "./PerformanceTBATable";
import { PerformanceTSYFTable } from "./PerformanceTSYFTable";
import { PerformanceTSYTable } from "./PerformanceTSYTable";
import { PerformanceYieldTable } from "./PerformanceYieldTable";

export const PerformanceContent = () => {
  const {
    state: { userSettings },
  } = useMarketDataModule();
  const {
    state: { performancePageSettings },
  } = useMarketDataPerformancePage();
  return (
    <>
      <Stack direction={{ base: "column", lg: "row" }} spacing={4}>
        <ColumnWrapper size="performance-col" w="inherit" p={0}>
          <Stack direction={{ base: "column", "3xl": "row" }} spacing={4}>
            <ColumnWrapper size="performance-col" w="inherit" p={0}>
              <PerformanceYieldTable asOfDate={userSettings.curve_date} />
            </ColumnWrapper>
            <ColumnWrapper size="performance-col" w="inherit" p={0}>
              <PerformanceTBATable performancePageSettings={performancePageSettings} />
            </ColumnWrapper>
          </Stack>
        </ColumnWrapper>
        <ColumnWrapper size="performance-col" w="inherit" p={0}>
          <Stack direction={{ base: "column", "3xl": "row" }} spacing={4}>
            <ColumnWrapper size="performance-col" w="inherit" p={0}>
              <PerformanceTSYFTable asOfDate={userSettings.curve_date} />
            </ColumnWrapper>
            <ColumnWrapper size="performance-col" w="inherit" p={0}>
              <PerformanceTSYTable asOfDate={userSettings.curve_date} />
            </ColumnWrapper>
          </Stack>
        </ColumnWrapper>
      </Stack>
    </>
  );
};
