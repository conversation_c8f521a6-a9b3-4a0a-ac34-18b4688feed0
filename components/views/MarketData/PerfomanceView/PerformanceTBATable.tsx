import React from "react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { useGetTBAPerformanceDetails } from "@/utils/swr-hooks";
import CAGrid from "@/design-system/molecules/CAGrid";
import { PerformancePageSettingsType } from "@/contexts/PageContexts/MarketDataPerfomancePageContext/MarketDataPerformancePageContextTypes";
import { tbaPerformanceColumnData } from "@/utils/grid/PerformanceColumnData";
import CACard from "@/design-system/molecules/CACard";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";

interface Props {
  performancePageSettings: PerformancePageSettingsType;
}

export const PerformanceTBATable = ({ performancePageSettings }: Props) => {
  const {
    state: { userSettings },
  } = useMarketDataModule();

  const { data, isLoading } = useGetTBAPerformanceDetails(
    userSettings.curve_date ? parseDateToYYYYMMDD(new Date(userSettings.curve_date)) : undefined,
    performancePageSettings.riskModelVersion,
    performancePageSettings.agency,
    performancePageSettings.monthType
  );

  return (
    <CACard w="auto" title="TBA" cardBodyStyle={{ p: 0, pr: 5 }} allowCollapse cardKey="performance-TBA">
      <CAGrid
        className="MarketDataTable PerfomanceTBA FullWidth"
        hideHeader
        hasRun={!isLoading}
        gridProps={{
          columnDefs: tbaPerformanceColumnData,
          rowData: data?.tba_performance_details,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading: isLoading,
        }}
      />
    </CACard>
  );
};
