import * as React from "react";
import { Box, chakra } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import MarketDataHeader from "../MarketDataHeader";
import { MarketDataModuleProps } from "..";
import { PerformanceContent } from "./PerformanceContent";
import PerformanceInputs from "./PerformanceInputs";

const MarketDataTABView: React.FC<MarketDataModuleProps> = ({ pageTitle }: MarketDataModuleProps) => {
  return (
    <Box>
      <chakra.form>
        <MarketDataHeader title={pageTitle} withRun={false} />
        <PerformanceInputs />
      </chakra.form>

      <MainInputContentTemplate>
        <PerformanceContent />
      </MainInputContentTemplate>
    </Box>
  );
};

export default MarketDataTABView;
