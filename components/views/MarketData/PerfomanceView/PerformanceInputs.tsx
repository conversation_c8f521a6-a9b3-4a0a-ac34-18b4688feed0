import * as React from "react";
import { Box, Flex, HStack, Text } from "@chakra-ui/react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getFormattedLocaleDate, getFormattedStringOptions, parseDateToYYYYMMDD } from "@/utils/helpers";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import CACard from "@/design-system/molecules/CACard";
import {
  AgencyValue,
  agencyOptions,
  getTBAMonthAndSettleDate,
  monthTypeOptions,
} from "@/utils/helpers/market-data/helper";
import { useGetTBARiskDetailsSWR } from "@/utils/swr-hooks";
import { useMarketDataPerformancePage } from "@/contexts/PageContexts/MarketDataPerfomancePageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { TBAPageSettingsType } from "@/contexts/PageContexts/MarketDataTBAPageContext/MarketDataTBAPageContextTypes";
import MarketDataValueDisplayWrapper from "../shared/MarketDataValueDisplayWrapper";
import { MarketDataTBARiskReportEnv } from "../shared/MarketDataTBARiskReportEnv";

const PerformanceInputs = () => {
  const {
    state: { performancePageSettings },
    action: { updatePerformancePageSettings },
  } = useMarketDataPerformancePage();

  const {
    state: {
      userSettings: { curve_date: asOfDate },
    },
  } = useMarketDataModule();

  const { data } = useGetTBARiskDetailsSWR(
    asOfDate ? parseDateToYYYYMMDD(new Date(asOfDate)) : undefined,
    performancePageSettings.riskModelVersion,
    performancePageSettings.agency,
    performancePageSettings.monthType
  );

  const { tbaMonth, settleDate } = React.useMemo(
    () => getTBAMonthAndSettleDate(data),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data?.tba_risk_details]
  );

  return (
    <Flex px="5" pb="2" pt="2">
      <CACard allowCollapse title="Inputs" overflow={"unset"}>
        <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
          <Flex alignItems="center" gridGap="2">
            <MarketDataValueDisplayWrapper
              name="Curve Date"
              link
              _key="curve_date"
              dateFormatter={getFormattedLocaleDate}
              showChanges={false}
            />
          </Flex>
          <Flex maxW="lg" alignItems="center">
            <Box w="6rem" marginInline="0.5rem">
              <CAMultiSelectDropdown
                placeholder="Agency"
                name="agency"
                minHeight={26}
                hasOptionDescription
                tooltipText={performancePageSettings.agency?.displayValue ?? "Agency"}
                value={performancePageSettings.agency}
                shouldOnlyReturnValue={false}
                isMultiSelect={false}
                options={getFormattedStringOptions(agencyOptions ?? [])}
                onChange={(val) => {
                  updatePerformancePageSettings({ agency: val as AgencyValue });
                }}
                hideSelectedOptions={false}
              />
            </Box>
          </Flex>
          <Flex maxW="lg" alignItems="center" gridGap="2">
            <Text variant="primary" whiteSpace="nowrap">
              TBA Settle
            </Text>
            <CASelectDropdown
              width="6rem"
              name="monthType"
              value={performancePageSettings.monthType}
              options={monthTypeOptions}
              onChange={(e) => {
                const value = e.target.value as TBAPageSettingsType["monthType"];
                updatePerformancePageSettings({ monthType: value });
              }}
            />
          </Flex>
          {tbaMonth ? (
            <Text variant="cardHeaderDefault">
              {tbaMonth}&nbsp;&nbsp;{settleDate}
            </Text>
          ) : null}
          <MarketDataTBARiskReportEnv
            value={performancePageSettings.riskModelVersion}
            onChange={(value) => {
              updatePerformancePageSettings({ riskModelVersion: value });
            }}
          />
        </HStack>
      </CACard>
    </Flex>
  );
};

export default React.forwardRef(PerformanceInputs);
