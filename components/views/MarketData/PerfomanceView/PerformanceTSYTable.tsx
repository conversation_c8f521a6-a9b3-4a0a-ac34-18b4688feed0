import React from "react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { useTreasuryPrices } from "@/utils/swr-hooks";
import CAGrid from "@/design-system/molecules/CAGrid";
import { treasuryPricesColumnData } from "@/utils/grid/PerformanceColumnData";
import CACard from "@/design-system/molecules/CACard";

interface Props {
  asOfDate: Date | undefined;
  lastRun?: string;
}

export const PerformanceTSYTable = ({ asOfDate }: Props) => {
  const { data, isLoading } = useTreasuryPrices(asOfDate ? parseDateToYYYYMMDD(new Date(asOfDate)) : undefined);

  return (
    <CACard title="TSY" w="auto" cardBodyStyle={{ p: 0, pr: 5 }} allowCollapse cardKey="performance-TSY">
      <CAGrid
        className="MarketDataTable FullWidth"
        hideHeader
        hasRun={!isLoading}
        gridProps={{
          columnDefs: treasuryPricesColumnData,
          rowData: data?.treasury_prices,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading: isLoading,
        }}
      />
    </CACard>
  );
};
