import * as React from "react";
import { IconButton } from "@chakra-ui/react";
import { IoSettings } from "react-icons/io5";
import MainLayout from "@/components/layouts/MainLayout";
import MarketDataSettingsDrawer from "@/components/views/MarketData/MarketDataSettingsDrawer";
import CAIcon from "@/design-system/atoms/CAIcon";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";

export interface MarketDataModuleProps {
  pageTitle: string;
}

const MarketDataModule: React.FC<React.PropsWithChildren<MarketDataModuleProps>> = ({ pageTitle, children }) => {
  const {
    state: { marketDataSettingsDrawerOpen },
    action: { toggleMarketDataSettingsDrawer },
  } = useMarketDataModule();

  const HeaderSettings = () => (
    <IconButton
      data-testid="toggle-settings-drawer"
      aria-label="Toggle Settings Drawer"
      icon={<CAIcon as={IoSettings} variant="secondary" />}
      variant="ghost"
      onClick={toggleMarketDataSettingsDrawer}
    />
  );

  return (
    <MainLayout title={pageTitle} mb={{ base: "10", sm: "0" }} headerSettings={<HeaderSettings />}>
      {children}
      <MarketDataSettingsDrawer onClose={toggleMarketDataSettingsDrawer} isOpen={marketDataSettingsDrawerOpen} />
    </MainLayout>
  );
};

export default React.memo(MarketDataModule);
