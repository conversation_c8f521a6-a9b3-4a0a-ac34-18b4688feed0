import React from "react";
import { Box, Flex } from "@chakra-ui/react";
import { useMarketDataVolatilityPage } from "@/contexts/PageContexts/MarketDataVolatilityPageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType } from "@/utils/openapi";
import { VolatilityTables } from "./VolatilityTables";

export const VolatilityContent = () => {
  const {
    state: { volatilityPageSettings },
  } = useMarketDataVolatilityPage();

  const {
    state: {
      userSettings: { curve_date: asOfDate },
    },
  } = useMarketDataModule();

  return (
    <Box maxW="87.375rem">
      <Flex w="full" flexDirection="column" gridGap="4">
        {volatilityPageSettings.swaption === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.STRIKE ? (
          // Only display market data for swaption atm strikes
          <VolatilityTables
            type={volatilityPageSettings.firstType}
            volatilityPageSettings={{ ...volatilityPageSettings, asOfDate }}
          />
        ) : (
          <>
            <VolatilityTables
              type={volatilityPageSettings.firstType}
              volatilityPageSettings={{ ...volatilityPageSettings, asOfDate }}
            />
            <VolatilityTables
              type={volatilityPageSettings.secondType}
              volatilityPageSettings={{ ...volatilityPageSettings, asOfDate }}
            />
            <VolatilityTables type="DIFF" volatilityPageSettings={{ ...volatilityPageSettings, asOfDate }} />
          </>
        )}
      </Flex>
    </Box>
  );
};
