import React from "react";
import { Flex } from "@chakra-ui/react";
import { ColDef } from "ag-grid-community";
import { useGetCurveDateSWR } from "@/utils/swr-hooks";
import { VolatilityPageSettingsType } from "@/contexts/PageContexts/MarketDataVolatilityPageContext/MarketDataVolatilityPageContextTypes";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import {
  CA_Mastr_Models_v1_0_Models_InterestRateModelType,
  CA_Mastr_Models_v1_0_Models_MarketType,
  CA_Mastr_Models_v1_0_Models_VolCalibrationCategory,
  CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType,
} from "@/utils/openapi";
import CAGrid from "@/design-system/molecules/CAGrid";
import CACard from "@/design-system/molecules/CACard";
import { useGetVolCubeDetailsSWR } from "@/utils/swr-hooks/VolCalibration";
import { COLUMN_OPTIONS, useModelTypeVolatilityOptions } from "@/constants/market-data";
import { generateColumnDefs, generateRowData, getConfigArgs } from "./helper";

interface Props {
  volatilityPageSettings: VolatilityPageSettingsType & {
    asOfDate?: Date;
  };
  type: string;
}

const defaultColDef: ColDef = {
  floatingFilter: false,
  maxWidth: 57,
  suppressHeaderMenuButton: true,
  suppressMovable: true,
  lockPinned: true,
  sortable: false,
  suppressHeaderContextMenu: true,
  enablePivot: false,
  enableRowGroup: false,
  enableValue: true,
  suppressHeaderFilterButton: true,
  suppressFloatingFilterButton: true,
};

export const VolatilityTables = ({ volatilityPageSettings, type }: Props) => {
  const modelTypeOptions = useModelTypeVolatilityOptions({ withMarket: true });
  const { asOfDate, config } = volatilityPageSettings;

  //Get last business day
  const { data: lastBusinessDay } = useGetCurveDateSWR(
    asOfDate ? parseDateToYYYYMMDD(new Date(asOfDate)) : undefined,
    -1
  );

  const args = React.useMemo(
    () => ({
      interest_rate_models: [volatilityPageSettings.firstType, volatilityPageSettings.secondType].filter(
        (d) => !d.includes("Market")
      ) as CA_Mastr_Models_v1_0_Models_InterestRateModelType[],
      vol_calibration_series_types: [volatilityPageSettings.swaption],
      categories: [CA_Mastr_Models_v1_0_Models_VolCalibrationCategory.SWAPTION],
      market_type: [volatilityPageSettings.firstType, volatilityPageSettings.secondType].filter((d) =>
        d.includes("Market")
      ) as CA_Mastr_Models_v1_0_Models_MarketType[],
    }),
    [volatilityPageSettings.firstType, volatilityPageSettings.secondType, volatilityPageSettings.swaption]
  );
  //Get data for LAST BUSINESS Day
  const { data: dataForLastBusinessDay, isLoading: isLoadingLastBusinessDay } = useGetVolCubeDetailsSWR({
    as_of_date: lastBusinessDay?.curve_date ? parseDateToYYYYMMDD(new Date(lastBusinessDay?.curve_date)) : "",
    ...args,
    ...getConfigArgs(config),
  });

  const { data, isLoading } = useGetVolCubeDetailsSWR({
    as_of_date: parseDateToYYYYMMDD(asOfDate),
    ...args,
    ...getConfigArgs(config),
  });

  const gridData = React.useMemo(() => {
    const seperatedData1 = volatilityPageSettings.firstType.includes("Market")
      ? data?.vol_calibration_details?.filter(
          (d) => d.type_name === "Market" && d.market_type === volatilityPageSettings.firstType
        )
      : data?.vol_calibration_details?.filter(
          (d) => d.type_name === "Model" && d.interest_rate_model === volatilityPageSettings.firstType
        );
    const seperatedData2 = volatilityPageSettings.secondType.includes("Market")
      ? data?.vol_calibration_details?.filter(
          (d) => d.type_name === "Market" && d.market_type === volatilityPageSettings.secondType
        )
      : data?.vol_calibration_details?.filter(
          (d) => d.type_name === "Model" && d.interest_rate_model === volatilityPageSettings.secondType
        );

    const seperatedData1ForLastBusinessDay = volatilityPageSettings.firstType.includes("Market")
      ? dataForLastBusinessDay?.vol_calibration_details?.filter(
          (d) => d.type_name === "Market" && d.market_type === volatilityPageSettings.firstType
        )
      : dataForLastBusinessDay?.vol_calibration_details?.filter(
          (d) => d.type_name === "Model" && d.interest_rate_model === volatilityPageSettings.firstType
        );
    const seperatedData2ForLastBusinessDay = volatilityPageSettings.secondType.includes("Market")
      ? dataForLastBusinessDay?.vol_calibration_details?.filter(
          (d) => d.type_name === "Market" && d.market_type === volatilityPageSettings.secondType
        )
      : dataForLastBusinessDay?.vol_calibration_details?.filter(
          (d) => d.type_name === "Model" && d.interest_rate_model === volatilityPageSettings.secondType
        );

    const mergedData1 = seperatedData1?.map((d) => {
      const match = seperatedData1ForLastBusinessDay?.find(
        (el) => el.maturity === d.maturity && el.strike === d.strike && el.term === d.term
      );
      return {
        ...d,
        change: match ? (d.value ?? 0) - (match.value ?? 0) : null,
      };
    });
    const mergedData2 = seperatedData2?.map((d) => {
      const match = seperatedData2ForLastBusinessDay?.find(
        (el) => el.maturity === d.maturity && el.strike === d.strike && el.term === d.term
      );
      return {
        ...d,
        change: match ? (d.value ?? 0) - (match.value ?? 0) : null,
      };
    });

    const changeData = mergedData2?.map((d) => {
      const match = mergedData1?.find(
        (el) => el.maturity === d.maturity && el.strike === d.strike && el.term === d.term
      );
      return {
        ...d,
        value: match ? (d.value ?? 0) - (match.value ?? 0) : null,
        change: match ? (d.change ?? 0) - (match.change ?? 0) : null,
      };
    });

    return type === "DIFF" ? changeData : type === volatilityPageSettings.firstType ? mergedData1 : mergedData2;
  }, [
    data?.vol_calibration_details,
    dataForLastBusinessDay?.vol_calibration_details,
    type,
    volatilityPageSettings.firstType,
    volatilityPageSettings.secondType,
  ]);

  const getLabel = () => {
    const seriesType = volatilityPageSettings.swaption;
    if (seriesType === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.PRICE && type !== "DIFF") {
      return "Prices (Nominal $100)";
    }

    if (seriesType === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.BLACK_VOL && type !== "DIFF") {
      return "Volatility (%)";
    }

    if (seriesType === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.NORMALIZED_VOL && type !== "DIFF") {
      return "Volatility (bps)";
    }

    if (seriesType === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.STRIKE && type !== "DIFF") {
      return "ATM Strikes";
    }
    return "Value";
  };

  const context1 = React.useMemo(
    () => ({
      rowInput: volatilityPageSettings.row,
      colInput: volatilityPageSettings.column,
    }),
    [volatilityPageSettings.row, volatilityPageSettings.column]
  );
  const context2 = React.useMemo(
    () => ({
      rowInput: volatilityPageSettings.row,
      colInput: volatilityPageSettings.column,
      showChange: true,
    }),
    [volatilityPageSettings.row, volatilityPageSettings.column]
  );

  const columnDef = React.useMemo(
    () =>
      generateColumnDefs(
        volatilityPageSettings.column,
        volatilityPageSettings.row,
        gridData ?? [],
        volatilityPageSettings.swaption
      ),
    [gridData, volatilityPageSettings.column, volatilityPageSettings.row, volatilityPageSettings.swaption]
  );
  const rowData = React.useMemo(
    () => generateRowData(volatilityPageSettings.row, gridData ?? []),
    [gridData, volatilityPageSettings.row]
  );

  const isDIFF = type === "DIFF";

  return (
    <Flex w="full" gridGap={4} flexDirection={{ base: "column", "2xl": "row" }}>
      <CACard
        title={`${isDIFF ? "DIFF" : modelTypeOptions.find((opt) => opt.value === type)?.displayValue} ${getLabel()} ${
          isDIFF ? "(Second - First)" : ""
        }`}
        allowCollapse
        cardKey={`${type}-${volatilityPageSettings.swaption}`}
        cardBodyStyle={{ p: 0 }}
      >
        <CAGrid
          className="MarketDataTable"
          tableHeaderText={COLUMN_OPTIONS.find((opt) => opt.value === volatilityPageSettings.column)?.displayValue}
          cardProps={{ borderRadius: "none" }}
          hideHeader
          hasRun={true}
          gridProps={{
            columnDefs: columnDef,
            rowData: rowData,
            sideBar: false,
            statusBar: undefined,
            loading: isLoading,
            defaultColDef,
            context: context1,
            domLayout: "autoHeight",
          }}
        />
      </CACard>
      <CACard
        title={`${type === "DIFF" ? "DIFF" : modelTypeOptions.find((opt) => opt.value === type)?.displayValue} Change ${
          isDIFF ? "(Second - First)" : ""
        }`}
        allowCollapse
        cardKey={`${type}-${volatilityPageSettings.swaption}-change`}
        cardBodyStyle={{ p: 0 }}
      >
        <CAGrid
          className="MarketDataTable"
          tableHeaderText={COLUMN_OPTIONS.find((opt) => opt.value === volatilityPageSettings.column)?.displayValue}
          hideHeader
          hasRun={true}
          gridProps={{
            columnDefs: columnDef,
            rowData: rowData,
            sideBar: false,
            statusBar: undefined,
            loading: isLoading || isLoadingLastBusinessDay,
            defaultColDef,
            context: context2,
            domLayout: "autoHeight",
          }}
        />
      </CACard>
    </Flex>
  );
};
