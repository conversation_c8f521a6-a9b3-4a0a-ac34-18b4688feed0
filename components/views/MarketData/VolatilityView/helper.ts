/* eslint-disable @typescript-eslint/no-explicit-any */
import { ColDef, ValueGetterParams } from "ag-grid-community";
import { VOLATILITY_TABLE_CONFIG_OPTIONS } from "@/contexts/PageContexts/MarketDataVolatilityPageContext/MarketDataVolatilityPageContextTypes";
import {
  CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity,
  CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType,
  CA_Mastr_Models_v1_0_Models_VolCalibrationStrike,
  CA_Mastr_Models_v1_0_Models_VolCalibrationTerm,
} from "@/utils/openapi";
import { COLUMN_OPTIONS, MATURITY_OPTIONS, STRIKE_SHIFTS_OPTIONS, SWAP_TERM_OPTIONS } from "@/constants/market-data";

export function generateColumnDefs(
  inputForColumns: "maturity" | "strike" | "term",
  inputForRows: "maturity" | "strike" | "term",
  arr: any[],
  seriesType?: CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType
): ColDef[] {
  // Dynamically construct column definitions based on the selected input for columns
  const uniqueColumns = Array.from(new Set(arr.map((item) => item[inputForColumns])));

  if (inputForColumns === "strike") {
    uniqueColumns.sort((a, b) => Number(a) - Number(b));
  }

  const columnDefs: ColDef[] = [
    {
      field: "row",
      headerName: COLUMN_OPTIONS.find((opt) => opt.value === inputForRows)?.displayValue,
      type: "text",
      pinned: "left",
      sort: "desc",
      comparator: (a: string, b: string) => {
        return Number(b) - Number(a);
      },
    }, // First column is the row header
    ...uniqueColumns.map((col) => ({
      field: col,
      type:
        seriesType === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.BLACK_VOL ||
        seriesType === CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.NORMALIZED_VOL
          ? "number1"
          : "number2",
      headerName: col,
      valueGetter: (params: ValueGetterParams) => {
        // Retrieve the value based on the row and column combination
        const rowData = params.data.row;
        const colData = col;
        const match = arr.find(
          (item) => item[inputForColumns] === colData && item[params.context.rowInput] === rowData
        );

        return match ? (params.context.showChange ? match.change : match.value) : null;
      },
    })),
  ];

  return columnDefs;
}

export function generateRowData(inputForRows: "maturity" | "strike" | "term", arr: any[]) {
  // Dynamically construct row data based on the selected input for rows
  const uniqueRows = Array.from(new Set(arr.map((item) => item[inputForRows])));

  // Map the rows into data format for ag-grid
  const rowData = uniqueRows.map((row) => ({
    row, // The row header
  }));

  return rowData;
}

export const getConfigArgs = ({
  type,
  value,
}: {
  type: VOLATILITY_TABLE_CONFIG_OPTIONS;
  value: string;
}): {
  maturities: CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity[];
  strike_shifts: CA_Mastr_Models_v1_0_Models_VolCalibrationStrike[];
  terms: CA_Mastr_Models_v1_0_Models_VolCalibrationTerm[];
} => {
  if (type === "maturity") {
    return {
      maturities: [value as CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity],
      strike_shifts: STRIKE_SHIFTS_OPTIONS.filter((opt) => opt.value !== "ALL").map(
        (opt) => opt.value
      ) as CA_Mastr_Models_v1_0_Models_VolCalibrationStrike[],
      terms: SWAP_TERM_OPTIONS.filter((opt) => opt.value !== "ALL").map(
        (opt) => opt.value
      ) as CA_Mastr_Models_v1_0_Models_VolCalibrationTerm[],
    };
  }
  if (type === "strike") {
    return {
      strike_shifts: [value as CA_Mastr_Models_v1_0_Models_VolCalibrationStrike],
      maturities: MATURITY_OPTIONS.filter((opt) => opt.value !== "ALL").map(
        (opt) => opt.value
      ) as CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity[],
      terms: SWAP_TERM_OPTIONS.filter((opt) => opt.value !== "ALL").map(
        (opt) => opt.value
      ) as CA_Mastr_Models_v1_0_Models_VolCalibrationTerm[],
    };
  }
  return {
    terms: [value as CA_Mastr_Models_v1_0_Models_VolCalibrationTerm],
    maturities: MATURITY_OPTIONS.filter((opt) => opt.value !== "ALL").map(
      (opt) => opt.value
    ) as CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity[],
    strike_shifts: STRIKE_SHIFTS_OPTIONS.filter((opt) => opt.value !== "ALL").map(
      (opt) => opt.value
    ) as CA_Mastr_Models_v1_0_Models_VolCalibrationStrike[],
  };
};
