import * as React from "react";
import { Box } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import MarketDataHeader from "../MarketDataHeader";
import { MarketDataModuleProps } from "..";
import { VolatilityInputs } from "./VolatilityInputs";
import { VolatilityContent } from "./VolatilityContent";

const MarketDataVolatilityView: React.FC<MarketDataModuleProps> = ({ pageTitle }: MarketDataModuleProps) => {
  return (
    <Box>
      <MarketDataHeader title={pageTitle} withRun={false} />
      <VolatilityInputs />
      <MainInputContentTemplate>
        <VolatilityContent />
      </MainInputContentTemplate>
    </Box>
  );
};

export default MarketDataVolatilityView;
