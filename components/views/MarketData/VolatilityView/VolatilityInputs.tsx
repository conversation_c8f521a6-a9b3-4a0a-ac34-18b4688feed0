/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { Flex, HStack, Text, VStack } from "@chakra-ui/react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CACard from "@/design-system/molecules/CACard";
import { useMarketDataVolatilityPage } from "@/contexts/PageContexts/MarketDataVolatilityPageContext";
import { getFormattedLocaleDate } from "@/utils/helpers";
import {
  COLUMN_OPTIONS,
  MATURITY_OPTIONS,
  STRIKE_SHIFTS_OPTIONS,
  SWAP_TERM_OPTIONS,
  useModelTypeVolatilityOptions,
} from "@/constants/market-data";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { VOLATILITY_TABLE_CONFIG_OPTIONS } from "@/contexts/PageContexts/MarketDataVolatilityPageContext/MarketDataVolatilityPageContextTypes";
import {
  CA_Mastr_Models_v1_0_Models_InterestRateModelType,
  CA_Mastr_Models_v1_0_Models_MarketType,
  CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType,
} from "@/utils/openapi";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import MarketDataValueDisplayWrapper from "../shared/MarketDataValueDisplayWrapper";

export const getConfigValuesOptions = (config: VOLATILITY_TABLE_CONFIG_OPTIONS) => {
  switch (config) {
    case "maturity":
      return MATURITY_OPTIONS;
    case "term":
      return SWAP_TERM_OPTIONS;
    default:
      return STRIKE_SHIFTS_OPTIONS;
  }
};

export const VolatilityInputs = () => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { volatilityPageSettings },
    action: { updateVolatilityPageSettings },
  } = useMarketDataVolatilityPage();
  const modelTypeOptions = useModelTypeVolatilityOptions({ withMarket: true });

  React.useEffect(() => {
    const configLeft = COLUMN_OPTIONS.find(
      (option) => ![volatilityPageSettings.column, volatilityPageSettings.row].includes(option.value)
    );
    if (!configLeft) return;
    const values = getConfigValuesOptions(configLeft?.value ?? "strike") as any;
    updateVolatilityPageSettings({
      config: { type: configLeft?.value, value: values.find((el: any) => el?.isDefault)?.value ?? values[0].value },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [volatilityPageSettings.column, volatilityPageSettings.row]);

  const displayOptions = Object.values(metadata?.market_data_settings?.vol_calibration_series_types ?? {}).map(
    (type) => ({
      id: type.value,
      value: type.value,
      displayValue: type.display_value,
    })
  );

  return (
    <Flex px="5" pb="2" pt="2">
      <CACard allowCollapse title="Inputs" overflow={"unset"}>
        <VStack alignItems="flex-start">
          <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <MarketDataValueDisplayWrapper
                name="As of date"
                link
                _key="curve_date"
                dateFormatter={getFormattedLocaleDate}
                showChanges={false}
              />
            </Flex>

            <Flex w="200px" alignItems="center" gap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Column
              </Text>
              <CASelectDropdown
                name="column"
                value={volatilityPageSettings.column}
                options={COLUMN_OPTIONS as any}
                onChange={(e) => {
                  if (e.target.value === volatilityPageSettings.row) {
                    updateVolatilityPageSettings({
                      row: COLUMN_OPTIONS.find((el) => el.value !== e.target.value)?.value as any,
                    });
                  }
                  updateVolatilityPageSettings({ column: e.target.value as any });
                }}
              />
            </Flex>
            <Flex w="200px" alignItems="center" gap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Row
              </Text>
              <CASelectDropdown
                name="row"
                value={volatilityPageSettings.row}
                options={COLUMN_OPTIONS as any}
                onChange={(e) => {
                  if (e.target.value === volatilityPageSettings.column) {
                    updateVolatilityPageSettings({
                      column: COLUMN_OPTIONS.find((el) => el.value !== e.target.value)?.value as any,
                    });
                  }
                  updateVolatilityPageSettings({ row: e.target.value as any });
                }}
              />
            </Flex>
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <Text variant="primary" whiteSpace="nowrap">
                {COLUMN_OPTIONS.find((option) => option.value === volatilityPageSettings.config?.type)?.displayValue}
              </Text>
              <ToggleButtonGroup
                buttons={
                  (
                    getConfigValuesOptions((volatilityPageSettings.config?.type as any) ?? "strike_shifts") as any
                  ).filter((el: any) => el.value !== "ALL") as any
                }
                selectedButton={volatilityPageSettings.config.value}
                onChange={(value) =>
                  updateVolatilityPageSettings({ config: { type: volatilityPageSettings.config.type, value: value } })
                }
              />
            </Flex>
          </HStack>
          <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Display
              </Text>
              <CASelectDropdown
                name="swaption"
                value={volatilityPageSettings.swaption}
                options={displayOptions}
                onChange={(e) =>
                  updateVolatilityPageSettings({
                    swaption: e.target.value as CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType,
                  })
                }
              />
            </Flex>
            {/* Market will always be used for swaption atm strikes */}
            {volatilityPageSettings.swaption !== CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType.STRIKE && (
              <>
                <Flex maxW="lg" alignItems="center" gridGap="2">
                  <Text variant="primary" whiteSpace="nowrap">
                    First
                  </Text>
                  <CASelectDropdown
                    name="swaption"
                    value={volatilityPageSettings.firstType}
                    options={modelTypeOptions}
                    onChange={(e) =>
                      updateVolatilityPageSettings({
                        firstType: e.target.value as
                          | CA_Mastr_Models_v1_0_Models_MarketType
                          | CA_Mastr_Models_v1_0_Models_InterestRateModelType,
                      })
                    }
                  />
                </Flex>
                <Flex maxW="lg" alignItems="center" gridGap="2">
                  <Text variant="primary" whiteSpace="nowrap">
                    Second
                  </Text>
                  <CASelectDropdown
                    name="swaption"
                    value={volatilityPageSettings.secondType}
                    options={modelTypeOptions}
                    onChange={(e) =>
                      updateVolatilityPageSettings({
                        secondType: e.target.value as
                          | CA_Mastr_Models_v1_0_Models_MarketType
                          | CA_Mastr_Models_v1_0_Models_InterestRateModelType,
                      })
                    }
                  />
                </Flex>
              </>
            )}
          </HStack>
        </VStack>
      </CACard>
    </Flex>
  );
};
