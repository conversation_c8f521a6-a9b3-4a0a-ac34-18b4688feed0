import * as React from "react";
import { CustomRequestOptions } from "@/utils/swr-hooks";
import { CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest } from "@/utils/openapi";
import { usePostRateProjection } from "@/utils/swr-hooks/RateProjections";
import { CAProgressIndicatorWrapper as ProgressIndicatorWrapper } from "@/design-system/molecules/CAProgressIndicator/CAProgressIndicatorWrapper";

type RateProjectionProgressIndicatorProps = {
  requestOpts: CustomRequestOptions;
  rateProjectionRequest: CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest;
};

export const RateProjectionProgressIndicator: React.FC<RateProjectionProgressIndicatorProps> = ({
  rateProjectionRequest,
  requestOpts,
}: RateProjectionProgressIndicatorProps) => {
  const { data } = usePostRateProjection(rateProjectionRequest, requestOpts);
  return <ProgressIndicatorWrapper lastRun={requestOpts.lastRun} status={data?.status} />;
};
