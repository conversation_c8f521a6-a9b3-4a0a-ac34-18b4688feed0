import * as React from "react";
import { CustomRequestOptions } from "@/utils/swr-hooks";
import { CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest } from "@/utils/openapi";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { usePostRateProjection } from "@/utils/swr-hooks/RateProjections";
import { useMarketDataRateProjectionsPage } from "@/contexts/PageContexts/MarketDataRateProjectionsPageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";

type RateProjectionStopProps = {
  requestOpts: CustomRequestOptions;
  rateProjectionRequest: CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest;
};

export const RateProjectionStopRunningWrapper: React.FC<RateProjectionStopProps> = ({
  rateProjectionRequest,
  requestOpts,
}: RateProjectionStopProps) => {
  const { cancel } = usePostRateProjection(rateProjectionRequest, requestOpts);
  const {
    action: { resetLastRun },
  } = useMarketDataRateProjectionsPage();
  const {
    action: { setIsTimerRunning },
  } = useMarketDataModule();

  const triggerCancel = React.useCallback(() => {
    setIsTimerRunning(false);
    if (cancel) cancel();
    resetLastRun();
  }, [setIsTimerRunning, cancel, resetLastRun]);
  return <CARun onClick={triggerCancel} isRunning={true} title={S.MODULES.PRICER.STOP} />;
};
