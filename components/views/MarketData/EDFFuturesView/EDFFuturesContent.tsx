import React from "react";
import { Box, Flex } from "@chakra-ui/react";
import { useMarketDataEDFFuturesPage } from "@/contexts/PageContexts/MarketDataEDFAndFutures";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { EuroDollarFuturesTable } from "./EuroDollarFuturesTable";
import { EDFTreasuryFuturesTable } from "./EDFTreasuryFuturesTable";
import { EDFTreasuriesTable } from "./EDFTreasuries";
import { EDFSofrFuturesTable } from "./EDFSofrFutures";

export const EDFFuturesContent = () => {
  const {
    state: { edfFuturesPageSettings },
  } = useMarketDataEDFFuturesPage();

  const {
    state: {
      userSettings: { curve_date: asOfDate },
    },
  } = useMarketDataModule();

  const defaultCardWrapperStyle = { w: { base: "full", "2xl": "auto" } };

  return (
    <Flex
      direction="column"
      justifyContent="flex-start"
      alignContent="flex-start"
      wrap="wrap"
      rowGap={4}
      columnGap={2}
      maxH={{ base: "auto", "2xl": "128rem" }}
    >
      <Box order={1} {...defaultCardWrapperStyle}>
        <EDFTreasuriesTable {...edfFuturesPageSettings} asOfDate={asOfDate} />
      </Box>
      <Box order={{ base: 3, "3.5xl": 2 }} {...defaultCardWrapperStyle}>
        <EDFSofrFuturesTable {...edfFuturesPageSettings} asOfDate={asOfDate} />
      </Box>

      <Box order={{ base: 2, "3.5xl": 3 }} {...defaultCardWrapperStyle}>
        <EDFTreasuryFuturesTable {...edfFuturesPageSettings} asOfDate={asOfDate} />
        <EuroDollarFuturesTable />
      </Box>
    </Flex>
  );
};
