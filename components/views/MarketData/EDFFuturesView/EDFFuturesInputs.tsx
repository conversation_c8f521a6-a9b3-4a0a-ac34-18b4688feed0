import React from "react";
import { Flex, HStack, Text } from "@chakra-ui/react";
import { useMarketDataEDFFuturesPage } from "@/contexts/PageContexts/MarketDataEDFAndFutures";
import CACard from "@/design-system/molecules/CACard";
import { CA_Mastr_Models_v1_0_Models_EDFRiskDataSource } from "@/utils/openapi";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getFormattedLocaleDate } from "@/utils/helpers";
import MarketDataValueDisplayWrapper from "../shared/MarketDataValueDisplayWrapper";

export const EDFFuturesInputs = () => {
  const {
    state: { edfFuturesPageSettings },
    action: { updateEdfFuturesSettings },
  } = useMarketDataEDFFuturesPage();

  return (
    <Flex px="5" pb="2" pt="2">
      <CACard allowCollapse title="Inputs" overflow={"unset"}>
        <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
          <Flex maxW="lg" gap={4} alignItems="center">
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <MarketDataValueDisplayWrapper
                name="As of date"
                link
                _key="curve_date"
                dateFormatter={getFormattedLocaleDate}
                showChanges={false}
              />
            </Flex>
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Data Source
              </Text>
              <CASelectDropdown
                width="90px"
                name="dataSource"
                value={edfFuturesPageSettings.dataSource}
                options={[
                  {
                    id: 0,
                    value: CA_Mastr_Models_v1_0_Models_EDFRiskDataSource.CELLO,
                    displayValue: "Cello",
                  },
                  {
                    id: 1,
                    value: CA_Mastr_Models_v1_0_Models_EDFRiskDataSource.CME,
                    displayValue: "CME",
                  },
                ]}
                onChange={(e) => {
                  const val = e.target.value;
                  updateEdfFuturesSettings({ dataSource: val as CA_Mastr_Models_v1_0_Models_EDFRiskDataSource });
                }}
              />
            </Flex>
          </Flex>
        </HStack>
      </CACard>
    </Flex>
  );
};
