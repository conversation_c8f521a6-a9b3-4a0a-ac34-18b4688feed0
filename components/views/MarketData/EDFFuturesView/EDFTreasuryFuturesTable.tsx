import React from "react";
import { useTreasuryFutureRiskDetails } from "@/utils/swr-hooks";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import CAGrid from "@/design-system/molecules/CAGrid";
import { treasuryFuturesColumnData } from "@/utils/grid/edfFutures/EDFFuturesColumnData";
import CACard from "@/design-system/molecules/CACard";
import { CA_Mastr_Api_v1_0_Models_RiskData_TreasuryFutureRiskDetail } from "@/utils/openapi";

interface Props {
  asOfDate?: Date | undefined;
}

export const EDFTreasuryFuturesTable = ({ asOfDate }: Props) => {
  const { data, isLoading } = useTreasuryFutureRiskDetails(parseDateToYYYYMMDD(asOfDate));
  return (
    <CACard
      w="auto"
      title="Treasury Futures"
      cardBodyStyle={{ p: 0, pr: 5 }}
      allowCollapse
      cardKey="EDF-treasury-futures"
    >
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TreasuryFutureRiskDetail>
        hasRun={!isLoading}
        className="MarketDataTable FullWidth"
        hideHeader
        gridProps={{
          columnDefs: treasuryFuturesColumnData,
          rowData: data?.treasury_future_risk_details,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading: isLoading,
        }}
      />
    </CACard>
  );
};
