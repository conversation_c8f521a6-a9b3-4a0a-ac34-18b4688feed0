import React from "react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import CAGrid from "@/design-system/molecules/CAGrid";
import { sofrFuturesColumnData } from "@/utils/grid/edfFutures/EDFFuturesColumnData";
import CACard from "@/design-system/molecules/CACard";
import { useSofrFuturePrices } from "@/utils/swr-hooks/Rates";
import {
  CA_Mastr_Api_v1_0_Models_RiskData_SOFRRiskDetail,
  CA_Mastr_Models_v1_0_Models_SofrFutureType,
} from "@/utils/openapi";

interface Props {
  asOfDate?: Date | undefined;
}

export const EDFSofrFuturesTable = ({ asOfDate }: Props) => {
  const { data, isLoading } = useSofrFuturePrices(parseDateToYYYYMMDD(asOfDate));

  // Separate the data into two arrays for the two tables using ticket and separate based on if it starts with "SR1" or "SR3" using reduce
  const formattedData = data?.sofr_risk_details?.reduce(
    (acc, item) => {
      if (item.category === CA_Mastr_Models_v1_0_Models_SofrFutureType.SOFR_FUTURES_1M) {
        acc.sr1.push(item);
      } else {
        acc.sr3.push(item);
      }
      return acc;
    },
    { sr1: [], sr3: [] } as {
      sr1: CA_Mastr_Api_v1_0_Models_RiskData_SOFRRiskDetail[];
      sr3: CA_Mastr_Api_v1_0_Models_RiskData_SOFRRiskDetail[];
    }
  );

  return (
    <>
      <CACard
        w="auto"
        title="SOFR 1M Futures"
        cardBodyStyle={{ p: 0, pr: 5 }}
        allowCollapse
        cardKey="EDF-sofr-futures-table-1m"
      >
        <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_SOFRRiskDetail>
          hasRun={!isLoading}
          className="MarketDataTable FullWidth"
          hideHeader
          gridProps={{
            columnDefs: sofrFuturesColumnData,
            rowData: formattedData?.sr1,
            domLayout: "autoHeight",
            sideBar: false,
            statusBar: undefined,
            loading: isLoading,
          }}
        />
      </CACard>
      <CACard
        w="auto"
        title="SOFR 3M Futures"
        cardBodyStyle={{ p: 0, pr: 5 }}
        allowCollapse
        cardKey="EDF-sofr-futures-table-3m"
        mt={4}
      >
        <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_SOFRRiskDetail>
          hasRun={!isLoading}
          className="MarketDataTable FullWidth"
          hideHeader
          gridProps={{
            columnDefs: sofrFuturesColumnData,
            rowData: formattedData?.sr3,
            domLayout: "autoHeight",
            sideBar: false,
            statusBar: undefined,
            loading: isLoading,
          }}
        />
      </CACard>
    </>
  );
};
