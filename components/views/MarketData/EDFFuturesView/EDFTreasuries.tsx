import React from "react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import CAGrid from "@/design-system/molecules/CAGrid";
import { treasuriesColumnData } from "@/utils/grid/edfFutures/EDFFuturesColumnData";
import { useTreasuryRiskDetails } from "@/utils/swr-hooks/RiskData";
import CACard from "@/design-system/molecules/CACard";
import { CA_Mastr_Api_v1_0_Models_RiskData_TreasuryRiskDetail } from "@/utils/openapi";

interface Props {
  asOfDate?: Date | undefined;
}

export const EDFTreasuriesTable = ({ asOfDate }: Props) => {
  const { data, isLoading } = useTreasuryRiskDetails(parseDateToYYYYMMDD(asOfDate));
  return (
    <CACard w="auto" title="Treasuries" cardBodyStyle={{ p: 0, pr: 5 }} allowCollapse cardKey="EDF-treasuries">
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TreasuryRiskDetail>
        hasRun={!isLoading}
        className="MarketDataTable FullWidth"
        hideHeader
        gridProps={{
          columnDefs: treasuriesColumnData,
          rowData: data?.treasury_risk_details,
          domLayout: "autoHeight",
          sideBar: false,
          loading: isLoading,
        }}
      />
    </CACard>
  );
};
