import React from "react";
import { Box, Flex } from "@chakra-ui/react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { euroDollarFuturesColumnData } from "@/utils/grid/edfFutures/EDFFuturesColumnData";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { useMarketDataEDFFuturesPage } from "@/contexts/PageContexts/MarketDataEDFAndFutures";
import { useEdfRiskDetails } from "@/utils/swr-hooks/RiskData";
import CACard from "@/design-system/molecules/CACard";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";
import { CA_Mastr_Api_v1_0_Models_RiskData_EDFRiskDetail } from "@/utils/openapi";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";

export const EuroDollarFuturesTable = () => {
  const {
    state: { edfFuturesPageSettings, edfDescription },
    action: { updateEdfDescription },
  } = useMarketDataEDFFuturesPage();

  const {
    state: {
      userSettings: { curve_date: asOfDate },
    },
  } = useMarketDataModule();

  const { data, isLoading } = useEdfRiskDetails(parseDateToYYYYMMDD(asOfDate), edfFuturesPageSettings.dataSource);

  const descriptions = data?.edf_risk_details?.map((el) => el.description);
  const uniqueDescriptionsOptions = [...new Set(descriptions)].map((el) => ({
    id: el || "",
    displayValue: el,
    value: el?.toLowerCase(),
  }));

  const gridData = data?.edf_risk_details?.filter(
    (el) => el.description?.toLowerCase() === edfDescription.toLowerCase()
  );

  return (
    <CACard
      w="auto"
      title="Eurodollar Futures"
      cardBodyStyle={{ p: 0 }}
      allowCollapse
      cardKey="EDF-euro-dollar-futures"
      mt={4}
    >
      <Flex mr={2} mb={4} justifyContent="flex-end">
        <DescriptionToggle
          options={uniqueDescriptionsOptions}
          value={edfDescription ?? uniqueDescriptionsOptions[0]?.value}
          onChange={updateEdfDescription}
        />
      </Flex>
      <Box pr={5}>
        <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_EDFRiskDetail>
          hasRun={!isLoading}
          className="MarketDataTable FullWidth"
          hideHeader
          gridProps={{
            columnDefs: euroDollarFuturesColumnData,
            rowData: gridData,
            domLayout: "autoHeight",
            sideBar: false,
            statusBar: undefined,
            loading: isLoading,
          }}
        />
      </Box>
    </CACard>
  );
};

interface Props {
  options: {
    id: string;
    displayValue: string | null | undefined;
    value: string | undefined;
  }[];
  value: string;
  onChange: (value: string | undefined) => void;
}

const DescriptionToggle = ({ options, onChange, value }: Props) => {
  return (
    <ToggleButtonGroup
      buttons={options.map((option) => ({ label: option.displayValue ?? "", value: option.value }))}
      selectedButton={value}
      onChange={onChange}
    />
  );
};
