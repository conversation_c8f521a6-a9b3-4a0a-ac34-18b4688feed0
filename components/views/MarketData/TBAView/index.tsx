import * as React from "react";
import { Box, chakra } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import MarketDataHeader from "../MarketDataHeader";
import { MarketDataModuleProps } from "..";
import TBAInputs from "./inputs/TBAInputs";
import { TBAContent } from "./TBAContent";

const MarketDataTABView: React.FC<MarketDataModuleProps> = ({ pageTitle }: MarketDataModuleProps) => {
  return (
    <Box>
      <chakra.form>
        <MarketDataHeader withRun={false} title={pageTitle} />
        <TBAInputs />
      </chakra.form>

      <MainInputContentTemplate>
        <TBAContent />
      </MainInputContentTemplate>
    </Box>
  );
};

export default MarketDataTABView;
