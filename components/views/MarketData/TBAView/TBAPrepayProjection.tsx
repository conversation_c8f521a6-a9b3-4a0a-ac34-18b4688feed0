import React from "react";
import { Flex } from "@chakra-ui/react";
import { TBAProjectionLT } from "./grids/TBAProjectIonLT";
import { TBAGridProps } from "./TBAContent";
import { TBAProjection1YR } from "./grids/TBAProjection1YR";

export const TBAPrepayProjection = (props: TBAGridProps) => {
  return (
    <Flex maxW={{ base: "auto", lg: "full" }}>
      <Flex w="full" gridGap={4} flexDirection={{ base: "column", lg: "row" }}>
        <TBAProjection1YR {...props} />
        <TBAProjectionLT {...props} />
      </Flex>
    </Flex>
  );
};
