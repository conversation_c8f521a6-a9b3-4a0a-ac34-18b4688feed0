import React from "react";
import { TBAPageSettingsType } from "@/contexts/PageContexts/MarketDataTBAPageContext/MarketDataTBAPageContextTypes";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { useGetTBARiskDetailsSWR } from "@/utils/swr-hooks";
import { useMarketDataTBAPage } from "@/contexts/PageContexts/MarketDataTBAPageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { TBADuration } from "./TBADuration";
import { TBAPrepayProjection } from "./TBAPrepayProjection";
import { TBAPricingPriceImpliedDurationCollateral } from "./TBAPricingPriceImpliedDurationCollateral";

export interface TBAGridProps {
  data: CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail[] | null | undefined | null | undefined;
  loading: boolean;
}

interface GridPropGetterProps {
  tbaPageSettings?: TBAPageSettingsType;
  agency?: string;
  curveDate?: Date;
}

export const TBAContent = () => {
  const {
    state: { userSettings },
  } = useMarketDataModule();
  const {
    state: { tbaPageSettings },
  } = useMarketDataTBAPage();

  const { data, isLoading } = useGetTBARiskDetailsSWR(
    userSettings.curve_date ? parseDateToYYYYMMDD(new Date(userSettings.curve_date)) : undefined,
    tbaPageSettings.riskModelVersion,
    tbaPageSettings.agency,
    tbaPageSettings.monthType
  );

  const gridPropGetter = (props?: GridPropGetterProps) => {
    return {
      data: data?.tba_risk_details,
      loading: isLoading,
      ...props,
    };
  };

  return (
    <>
      <TBAPricingPriceImpliedDurationCollateral
        {...gridPropGetter({
          agency: tbaPageSettings.agency.displayValue,
          curveDate: userSettings.curve_date,
        })}
      />
      <TBADuration
        {...gridPropGetter({
          tbaPageSettings,
        })}
      />
      <TBAPrepayProjection {...gridPropGetter()} />
    </>
  );
};
