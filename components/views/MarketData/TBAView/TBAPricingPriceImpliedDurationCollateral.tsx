import React from "react";
import { Flex } from "@chakra-ui/react";
import { TBAPageSettingsType } from "@/contexts/PageContexts/MarketDataTBAPageContext/MarketDataTBAPageContextTypes";
import { TBAGridProps } from "./TBAContent";
import { TBAPricing } from "./grids/TBAPricing";
import { TBACollateral } from "./grids/TBACollateral";
import { TBAPriceImpliedDuration } from "./grids/TBAPriceImpliedDuration";

interface Props extends TBAGridProps {
  tbaPageSettings?: TBAPageSettingsType;
  curveDate?: Date;
}

export const TBAPricingPriceImpliedDurationCollateral = ({ ...props }: Props) => {
  return (
    <Flex maxW={{ base: "auto", "2xl": "full" }}>
      <Flex w="full" gridGap={4} flexDirection={{ base: "column", lg: "row" }}>
        <TBAPricing {...props} />
        <TBAPriceImpliedDuration {...props} />
        <TBACollateral {...props} />
      </Flex>
    </Flex>
  );
};
