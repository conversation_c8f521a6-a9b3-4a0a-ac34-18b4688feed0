import React from "react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { tbaCollateralColumnData } from "@/utils/grid/tba/TBACollateralColumnData";
import CACard from "@/design-system/molecules/CACard";

interface Props {
  data: CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail[] | null | undefined;
  loading?: boolean;
}

export const TBACollateral = ({ data, loading }: Props) => {
  return (
    <CACard title="Collateral" cardBodyStyle={{ p: 0 }} allowCollapse cardKey="TBA-collateral">
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hasRun={!!data}
        hideHeader
        gridProps={{
          domLayout: "autoHeight",
          columnDefs: tbaCollateralColumnData,
          rowData: data,
          sideBar: false,
          statusBar: undefined,
          loading,
        }}
      />
    </CACard>
  );
};
