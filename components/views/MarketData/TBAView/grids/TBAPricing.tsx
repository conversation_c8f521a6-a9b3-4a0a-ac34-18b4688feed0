import React from "react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { tbaPricingColumnData } from "@/utils/grid/tba/TBAPricingColumnData";
import CACard from "@/design-system/molecules/CACard";

interface Props {
  data: CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail[] | null | undefined;
  loading: boolean;
  curveDate?: Date;
}
export const TBAPricing = ({ data, loading, curveDate }: Props) => {
  return (
    <CACard
      title="Pricing"
      cardBodyStyle={{ p: 0 }}
      maxW={{ base: "auto", "2xl": "46.50rem" }}
      allowCollapse
      cardKey="TBA-pricing"
    >
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hasRun={!!data}
        hideHeader
        gridProps={{
          domLayout: "autoHeight",
          columnDefs: tbaPricingColumnData,
          rowData: data,
          sideBar: false,
          statusBar: undefined,
          loading,
          context: {
            curveDate: curveDate,
          },
        }}
      />
    </CACard>
  );
};
