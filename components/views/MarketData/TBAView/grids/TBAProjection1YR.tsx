import React from "react";
import CACard from "@/design-system/molecules/CACard";
import CAGrid from "@/design-system/molecules/CAGrid";
import { tbaPrepaymentProjectionCPR1YRColumnData } from "@/utils/grid/tba/TBAPrepaymentProjectionCPRColumnData";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { TBAGridProps } from "../TBAContent";

export const TBAProjection1YR = ({ data, loading }: TBAGridProps) => {
  return (
    <CACard
      title="1 YR PREPAYMENT PROJECTION (CPR)"
      cardBodyStyle={{ p: 0 }}
      allowCollapse
      cardKey="TBA-projection-1yr"
    >
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hideHeader
        hasRun={!!data}
        gridProps={{
          columnDefs: tbaPrepaymentProjectionCPR1YRColumnData,
          rowData: data,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading,
        }}
      />
    </CACard>
  );
};
