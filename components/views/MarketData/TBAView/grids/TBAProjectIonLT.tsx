import React from "react";
import CACard from "@/design-system/molecules/CACard";
import CAGrid from "@/design-system/molecules/CAGrid";
import { tbaPrepaymentProjectionCPRLTColumnData } from "@/utils/grid/tba/TBAPrepaymentProjectionCPRColumnData";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { TBAGridProps } from "../TBAContent";

export const TBAProjectionLT = ({ data, loading }: TBAGridProps) => {
  return (
    <CACard title="LT PREPAYMENT PROJECTION (CPR)" cardBodyStyle={{ p: 0 }} allowCollapse cardKey="TBA-projection-lt">
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hideHeader
        hasRun={!!data}
        gridProps={{
          columnDefs: tbaPrepaymentProjectionCPRLTColumnData,
          rowData: data ?? undefined,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading,
        }}
      />
    </CACard>
  );
};
