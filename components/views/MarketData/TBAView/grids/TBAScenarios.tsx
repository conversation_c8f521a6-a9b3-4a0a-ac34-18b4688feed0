import React from "react";
import CAGrid from "@/design-system/molecules/CAGrid";
import CACard from "@/design-system/molecules/CACard";
import { tbaDurationScenariosColumnData } from "@/utils/grid/tba/TBADurationScenariosColumnData";
import { tbaDV01ScenariosColumnData } from "@/utils/grid/tba/TBADV01ScenariosColumnData";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { TBAGridProps } from "../TBAContent";
import { DurationType } from "../inputs/TBADurationInput";

interface Props extends TBAGridProps {
  durationValue: DurationType;
  tbaDurationInput: React.JSX.Element;
}

export const TBAScenarios = ({ data, durationValue, tbaDurationInput, loading }: Props) => {
  return (
    <CACard title="Scenarios" cardBodyStyle={{ p: 0 }} allowCollapse cardKey="TBA-scenarios">
      {tbaDurationInput}
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hideHeader
        hasRun={!!data}
        gridProps={{
          columnDefs: durationValue === "Duration" ? tbaDurationScenariosColumnData : tbaDV01ScenariosColumnData,
          rowData: data,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading,
        }}
      />
    </CACard>
  );
};
