import React, { useState } from "react";
import { Box, IconButton, Menu, MenuButton, MenuItem, MenuList } from "@chakra-ui/react";
import { IoEllipsisVerticalOutline } from "react-icons/io5";
import CACard from "@/design-system/molecules/CACard";
import CAGrid from "@/design-system/molecules/CAGrid";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { tbaPriceImpliedDurationColumnData } from "@/utils/grid/tba/TBAPriceImpliedDuration";
import CAIcon from "@/design-system/atoms/CAIcon";
import { TBAGridProps } from "../TBAContent";

export type PriceImpliedDurationType = "avg" | "lower" | "upper" | "combined";

const TBAPriceImpliedFieldsByType = ["pid", "pid_ed_ratio", "pid_ed_diff"];

export const TBAPriceImpliedDuration = ({ data, loading }: TBAGridProps) => {
  const [selectedType, setSelectedType] = useState<PriceImpliedDurationType>("combined");

  const columnsBySelectedData = tbaPriceImpliedDurationColumnData?.map((column) => {
    if (TBAPriceImpliedFieldsByType.includes(column.field as string)) {
      return {
        ...column,
        field: `${column.field}_${selectedType}`,
      };
    }
    return column;
  });

  return (
    <CACard
      title={`Price-Implied Duration (${tabs.find((tab) => tab.value === selectedType)?.label})`}
      cardBodyStyle={{ p: 0 }}
      maxW={{ base: "auto", "2xl": "25.20rem" }}
      allowCollapse
      cardKey="TBA-price-implied-duration"
      headingRight={<CardHeadingRight onChange={setSelectedType} value={selectedType} />}
    >
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hideHeader
        hasRun={!!data}
        gridProps={{
          columnDefs: columnsBySelectedData,
          rowData: data ?? undefined,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading,
        }}
      />
    </CACard>
  );
};

interface Props {
  value: PriceImpliedDurationType;
  onChange: (duration: PriceImpliedDurationType) => void;
}

const tabs: {
  label: string;
  value: PriceImpliedDurationType;
}[] = [
  { label: "Combined", value: "combined" },
  { label: "Average", value: "avg" },
  { label: "Lower", value: "lower" },
  { label: "Upper", value: "upper" },
];

const CardHeadingRight = ({ onChange }: Props) => {
  return (
    <Box onClick={(e) => e.stopPropagation()}>
      <Menu placement="right-start" closeOnSelect>
        <MenuButton
          as={IconButton}
          height={"auto"}
          aria-label="market-options-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={5} variant="secondary" />}
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
        />
        <MenuList zIndex="popover" minW="8rem">
          {tabs.map((tab) => (
            <MenuItem key={tab.value} onClick={() => onChange(tab.value)}>
              {tab.label}
            </MenuItem>
          ))}
        </MenuList>
      </Menu>
    </Box>
  );
};
