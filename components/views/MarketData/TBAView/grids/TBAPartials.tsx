import React from "react";
import CACard from "@/design-system/molecules/CACard";
import CAGrid from "@/design-system/molecules/CAGrid";
import { tbaDurationPartialsColumnData } from "@/utils/grid/tba/TBADurationPartialsColumnData";
import { tbaDV01PartialsColumnData } from "@/utils/grid/tba/TBADV01PartialsColumnData";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { TBAGridProps } from "../TBAContent";
import { DurationType } from "../inputs/TBADurationInput";

interface Props extends TBAGridProps {
  durationValue: DurationType;
  tbaDurationInput: React.JSX.Element;
}

export const TBAPartials = ({ data, durationValue, tbaDurationInput, loading }: Props) => {
  return (
    <CACard allowCollapse cardKey="TBA-partials" title="Partials" cardBodyStyle={{ p: 0 }}>
      {tbaDurationInput}
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hideHeader
        hasRun={!!data}
        gridProps={{
          columnDefs: durationValue === "Duration" ? tbaDurationPartialsColumnData : tbaDV01PartialsColumnData,
          rowData: data,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading,
        }}
      />
    </CACard>
  );
};
