import React from "react";
import { Box, Flex } from "@chakra-ui/react";
import { TBAPageSettingsType } from "@/contexts/PageContexts/MarketDataTBAPageContext/MarketDataTBAPageContextTypes";
import { CA_Mastr_Models_v1_0_Models_TBATicker } from "@/utils/openapi";
import { TBAPartials } from "./grids/TBAPartials";
import { TBAScenarios } from "./grids/TBAScenarios";
import { DurationType, TBADurationInput } from "./inputs/TBADurationInput";
import { TBAGridProps } from "./TBAContent";

interface Props extends TBAGridProps {
  tbaPageSettings?: TBAPageSettingsType;
}

export const TBADuration = ({ data, tbaPageSettings, ...rest }: Props) => {
  const [durationValue, setDurationValue] = React.useState<DurationType>("Duration");

  const TbaDurationInput = () => (
    <Box m={4}>
      <TBADurationInput
        value={durationValue}
        onChange={(value) => setDurationValue(value)}
        impliedChange={
          tbaPageSettings?.agency.value === CA_Mastr_Models_v1_0_Models_TBATicker.FNCI
            ? data?.[0]?.oas_per_change_current_coupons
            : null
        }
      />
    </Box>
  );

  return (
    <Flex maxW={{ base: "auto", lg: "full" }}>
      <Flex w="full" gridGap={4} flexDirection={{ base: "column", lg: "row" }}>
        <TBAPartials {...rest} data={data} durationValue={durationValue} tbaDurationInput={<TbaDurationInput />} />
        <TBAScenarios {...rest} data={data} durationValue={durationValue} tbaDurationInput={<TbaDurationInput />} />
      </Flex>
    </Flex>
  );
};
