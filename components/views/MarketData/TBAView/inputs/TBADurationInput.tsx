import React from "react";
import { Flex, Text } from "@chakra-ui/react";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";

export type DurationType = "DV01" | "Duration";
interface Props {
  value: DurationType;
  impliedChange?: number | null | undefined;
  onChange: (duration: DurationType) => void;
}
export const TBADurationInput = ({ impliedChange, onChange, value }: Props) => {
  return (
    <Flex alignItems="center" gridGap="5" wrap="wrap">
      <ToggleButtonGroup
        buttons={[
          { label: "Duration", value: "Duration" },
          { label: "DV01", value: "DV01" },
        ]}
        selectedButton={value}
        onChange={onChange}
      />

      {impliedChange && (
        <Flex ml="auto" flexWrap="wrap" justifyContent="center" gridGap="2">
          <Text variant="primary" whiteSpace="nowrap">
            Implied Change
          </Text>
          <Text variant="cardHeaderDefault">{impliedChange.toFixed(3)}</Text>
          <Text variant="tableLeft" color="GrayText">
            (OAS per change CC)
          </Text>
        </Flex>
      )}
    </Flex>
  );
};
