import * as React from "react";
import { Box, Flex, HStack, Text } from "@chakra-ui/react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { getFormattedLocaleDate, getFormattedStringOptions, parseDateToYYYYMMDD } from "@/utils/helpers";
import CAMultiSelectDropdown from "@/design-system/molecules/CAMultiSelectDropdown";
import { useMarketDataTBAPage } from "@/contexts/PageContexts/MarketDataTBAPageContext";
import CACard from "@/design-system/molecules/CACard";
import {
  AgencyValue,
  agencyOptions,
  getTBAMonthAndSettleDate,
  monthTypeOptions,
} from "@/utils/helpers/market-data/helper";
import { useGetTBARiskDetailsSWR } from "@/utils/swr-hooks";
import { TBAPageSettingsType } from "@/contexts/PageContexts/MarketDataTBAPageContext/MarketDataTBAPageContextTypes";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import MarketDataValueDisplayWrapper from "../../shared/MarketDataValueDisplayWrapper";
import { MarketDataTBARiskReportEnv } from "../../shared/MarketDataTBARiskReportEnv";

const TBAInputs = () => {
  const {
    state: { tbaPageSettings },
    action: { updateTBAPageSettings },
  } = useMarketDataTBAPage();

  const {
    state: {
      userSettings: { curve_date: curveDate },
    },
  } = useMarketDataModule();

  const { data } = useGetTBARiskDetailsSWR(
    curveDate ? parseDateToYYYYMMDD(new Date(curveDate)) : undefined,
    tbaPageSettings.riskModelVersion,
    tbaPageSettings.agency,
    tbaPageSettings.monthType
  );

  const { tbaMonth, settleDate } = React.useMemo(
    () => getTBAMonthAndSettleDate(data),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [data?.tba_risk_details]
  );

  return (
    <Flex px="5" pb="2" pt="2">
      <CACard allowCollapse title="Inputs" overflow={"unset"}>
        <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
          <Flex alignItems="center" gridGap="2">
            <MarketDataValueDisplayWrapper
              name="Curve Date"
              link
              _key="curve_date"
              dateFormatter={getFormattedLocaleDate}
              showChanges={false}
            />
          </Flex>
          <Flex maxW="lg" alignItems="center">
            <Box w="6rem" marginInline="0.5rem">
              <CAMultiSelectDropdown
                placeholder="Agency"
                name="agency"
                minHeight={26}
                hasOptionDescription
                tooltipText={tbaPageSettings.agency?.displayValue ?? "Agency"}
                value={tbaPageSettings.agency}
                shouldOnlyReturnValue={false}
                isMultiSelect={false}
                options={getFormattedStringOptions(agencyOptions ?? [])}
                onChange={(val) => {
                  updateTBAPageSettings({ agency: val as AgencyValue });
                }}
                hideSelectedOptions={false}
              />
            </Box>
          </Flex>
          <Flex maxW="lg" alignItems="center" gridGap="2">
            <Text variant="primary" whiteSpace="nowrap">
              TBA Settle
            </Text>
            <CASelectDropdown
              width="6rem"
              name="monthType"
              value={tbaPageSettings.monthType}
              options={monthTypeOptions}
              onChange={(e) => {
                const value = e.target.value as TBAPageSettingsType["monthType"];
                updateTBAPageSettings({ monthType: value });
              }}
            />
          </Flex>
          {tbaMonth ? (
            <Text variant="cardHeaderDefault">
              {tbaMonth}&nbsp;&nbsp;{settleDate}
            </Text>
          ) : null}
          <MarketDataTBARiskReportEnv
            value={tbaPageSettings.riskModelVersion}
            onChange={(value) => {
              updateTBAPageSettings({ riskModelVersion: value });
            }}
          />
        </HStack>
      </CACard>
    </Flex>
  );
};

export default TBAInputs;
