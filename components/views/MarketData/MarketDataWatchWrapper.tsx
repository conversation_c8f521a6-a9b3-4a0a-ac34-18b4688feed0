import * as React from "react";
import { CustomRequestOptions } from "@/utils/swr-hooks";
import StopWatch from "@/design-system/molecules/StopWatch";
import { CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest } from "@/utils/openapi";
import { usePostRateProjection } from "@/utils/swr-hooks/RateProjections";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";

const StopWatchWrapper = ({ startDate, endDate }: { startDate: string | undefined; endDate: string | undefined }) => {
  const {
    state: { isTimerRunning },
  } = useMarketDataModule();
  return <StopWatch startDate={startDate} isTimerRunning={isTimerRunning} endDate={endDate} />;
};

type RateProjectionStopWatchWrapperProps = {
  requestOpts: CustomRequestOptions;
  rateProjectionRequest: CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest;
  endTime?: string;
};
export const RateProjectionStopWatchWrapper: React.FC<RateProjectionStopWatchWrapperProps> = ({
  rateProjectionRequest,
  requestOpts,
  endTime,
}: RateProjectionStopWatchWrapperProps) => {
  usePostRateProjection(rateProjectionRequest, requestOpts);
  return <StopWatchWrapper startDate={requestOpts?.lastRun} endDate={endTime} />;
};
