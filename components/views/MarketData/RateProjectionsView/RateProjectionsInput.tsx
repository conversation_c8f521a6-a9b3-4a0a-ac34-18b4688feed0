import { Box, Flex, HStack, Text, Tooltip } from "@chakra-ui/react";
import * as React from "react";
import { UseFormHandleSubmit, useForm } from "react-hook-form";
import { useMarketDataRateProjectionsPage } from "@/contexts/PageContexts/MarketDataRateProjectionsPageContext";
import { rateProjectionsPageSettingsType } from "@/contexts/PageContexts/MarketDataRateProjectionsPageContext/MarketDataRateProjectionsPageContextTypes";
import CACard from "@/design-system/molecules/CACard";
import CAInput from "@/design-system/molecules/CAInput";
import {
  FormattedStringOptionsType,
  getDefaultStringValue,
  getFormattedLocaleDate,
  getFormattedStringOptions,
  getInfoMsg,
} from "@/utils/helpers";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { CA_Mastr_Models_v1_0_Models_MortgageRateScenario } from "@/utils/openapi";
import { CURVE_SHIFT_OPTIONS } from "@/constants/enums";
import MarketDataValueDisplayWrapper from "../shared/MarketDataValueDisplayWrapper";

export interface RateProjectionsHander {
  handleSubmit: UseFormHandleSubmit<rateProjectionsPageSettingsType>;
}
const RateProjectionsInput: React.ForwardRefRenderFunction<RateProjectionsHander> = (_, ref) => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { rateProjectionsPageSettings, rateProjectionsPageSettingsCopy },
  } = useMarketDataRateProjectionsPage();

  const { handleSubmit, register, watch, setValue } = useForm<rateProjectionsPageSettingsType>({
    defaultValues: rateProjectionsPageSettings,
  });
  const [
    watch_curve_shift,
    watch_mortage_rate_scenario,
    watch_projection_paths,
    watch_path_start,
    watch_path_end,
    watch_interest_rate_model_name,
    watch_interest_rate_calibration_method,
  ] = watch([
    "curve_shift",
    "mortage_rate_scenario",
    "projection_paths",
    "path_start",
    "path_end",
    "interest_rate_model_name",
    "interest_rate_calibration_method",
  ]);

  const scenarioOptions = React.useMemo(
    () => getFormattedStringOptions(metadata?.pricer_settings?.mortage_rate_scenario),
    [metadata]
  );

  const interestRateModelOptions = React.useMemo(
    () => getFormattedStringOptions(metadata?.pricer_settings?.type),
    [metadata]
  );

  const calibrationOptions = React.useMemo(() => {
    let options: FormattedStringOptionsType[] = [];
    if (metadata?.pricer_settings?.interest_rate_model_configs) {
      const modelConfig = metadata?.pricer_settings?.interest_rate_model_configs.find(
        (modelConfig) => modelConfig.model_name === watch_interest_rate_model_name
      );
      if (modelConfig && modelConfig["calibrations"]) {
        const formattedCalibrationList: FormattedStringOptionsType[] = getFormattedStringOptions(
          metadata?.pricer_settings?.calibration
        );
        options = formattedCalibrationList.filter(
          (calibrationOption) =>
            !!modelConfig["calibrations"]?.find((calibrationValue) => calibrationValue === calibrationOption.value)
        );
      }
    }
    return options;
  }, [metadata, watch_interest_rate_model_name]);

  React.useEffect(() => {
    setValue("interest_rate_calibration_method", calibrationOptions[0]?.value);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calibrationOptions, metadata?.pricer_settings?.calibration]);

  React.useEffect(() => {
    let val = "";
    if (!rateProjectionsPageSettings.mortage_rate_scenario)
      val = getDefaultStringValue(metadata?.pricer_settings?.mortage_rate_scenario) ?? "";
    else
      val =
        metadata?.pricer_settings?.mortage_rate_scenario?.find(
          (c) => c.value === rateProjectionsPageSettings.mortage_rate_scenario
        )?.value ?? "";

    setValue("mortage_rate_scenario", val as CA_Mastr_Models_v1_0_Models_MortgageRateScenario);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [metadata?.pricer_settings?.mortage_rate_scenario, rateProjectionsPageSettings.mortage_rate_scenario]);

  React.useImperativeHandle(ref, () => ({
    handleSubmit,
  }));

  const isStochastic = watch_mortage_rate_scenario === "STOCHASTIC";

  const handleScenarioChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value as CA_Mastr_Models_v1_0_Models_MortgageRateScenario;
    if (
      value === CA_Mastr_Models_v1_0_Models_MortgageRateScenario.STOCHASTIC &&
      watch_interest_rate_model_name === "LMM2F"
    ) {
      setValue("path_start", 1);
      setValue("path_end", 256);
      setValue("projection_paths", 256);
      return;
    }

    setValue("path_start", watch_path_start ?? 1);
    setValue("path_end", watch_path_end ?? 200);
    setValue("projection_paths", watch_projection_paths ?? 200);
  };

  return (
    <Flex px="5" pb="2" pt="2" wrap="wrap">
      <CACard h="100%" allowCollapse title="Inputs" overflow={"unset"}>
        <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
          <Box>
            <MarketDataValueDisplayWrapper
              name="Curve Date"
              link
              _key="curve_date"
              dateFormatter={getFormattedLocaleDate}
            />
          </Box>
          <Tooltip
            label={`Available options for Numerix: ${CURVE_SHIFT_OPTIONS.join(", ")}`}
            aria-label={`Available options for Numerix: ${CURVE_SHIFT_OPTIONS.join(", ")}`}
            isDisabled={
              metadata?.pricer_settings?.type?.find((l) => l.value === watch_interest_rate_model_name)
                ?.display_value !== "Numerix"
            }
          >
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Curve shift (bps)
              </Text>
              <CAInput
                type="number"
                inputType="digit"
                width={"6rem"}
                inputWidth="85%"
                {...register("curve_shift", {
                  valueAsNumber: true,
                  required: true,
                })}
                info={
                  !isNaN(watch_curve_shift ?? 0)
                    ? getInfoMsg(watch_curve_shift, rateProjectionsPageSettingsCopy.curve_shift)
                    : undefined
                }
              />
            </Flex>
          </Tooltip>
          <Flex maxW="lg" alignItems="center" gridGap="2">
            <Text variant="primary" whiteSpace="nowrap">
              Scenario
            </Text>
            <CASelectDropdown
              info={getInfoMsg(watch_mortage_rate_scenario, rateProjectionsPageSettingsCopy.mortage_rate_scenario)}
              {...register("mortage_rate_scenario", {
                required: true,
                onChange: handleScenarioChange,
              })}
              options={scenarioOptions}
            />
          </Flex>
          {isStochastic && (
            <>
              <Flex maxW="lg" alignItems="center" gridGap="2">
                <Text variant="primary" whiteSpace="nowrap">
                  Projection Paths
                </Text>
                {watch_interest_rate_model_name === "LMM2F" ? (
                  <Tooltip label="Numerix supports only the 256 paths option.">
                    <CAInput
                      type="number"
                      inputType="digit"
                      width="6rem"
                      inputWidth="85%"
                      disabled
                      {...register("projection_paths", {
                        valueAsNumber: true,
                        shouldUnregister: true,
                        required: true,
                        onBlur(event) {
                          const value = Number(event.target.value);
                          if (watch_path_end && watch_path_end > value) {
                            setValue("path_end", value);
                          }
                        },
                      })}
                      info={
                        !isNaN(watch_projection_paths ?? 0)
                          ? getInfoMsg(watch_projection_paths, rateProjectionsPageSettingsCopy.projection_paths)
                          : undefined
                      }
                    />
                  </Tooltip>
                ) : (
                  <CAInput
                    type="number"
                    inputType="digit"
                    width="6rem"
                    inputWidth="85%"
                    disabled={watch_interest_rate_model_name === "LMM2F"}
                    {...register("projection_paths", {
                      valueAsNumber: true,
                      shouldUnregister: true,
                      required: true,
                      onBlur(event) {
                        const value = Number(event.target.value);
                        if (watch_path_end && watch_path_end > value) {
                          setValue("path_end", value);
                        }
                      },
                    })}
                    info={
                      !isNaN(watch_projection_paths ?? 0)
                        ? getInfoMsg(watch_projection_paths, rateProjectionsPageSettingsCopy.projection_paths)
                        : undefined
                    }
                  />
                )}
              </Flex>
              <Flex maxW="lg" alignItems="center" gridGap="2">
                <Text variant="primary" whiteSpace="nowrap">
                  Path Start
                </Text>
                <CAInput
                  type="number"
                  inputType="digit"
                  width="6rem"
                  inputWidth="85%"
                  {...register("path_start", {
                    valueAsNumber: true,
                    shouldUnregister: true,
                    required: true,
                  })}
                  info={
                    !isNaN(watch_path_start ?? 0)
                      ? getInfoMsg(watch_path_start, rateProjectionsPageSettingsCopy.path_start)
                      : undefined
                  }
                />
              </Flex>
              <Flex maxW="lg" alignItems="center" gridGap="2">
                <Text variant="primary" whiteSpace="nowrap">
                  Path End
                </Text>
                <CAInput
                  type="number"
                  inputType="digit"
                  width="6rem"
                  inputWidth="85%"
                  {...register("path_end", {
                    valueAsNumber: true,
                    shouldUnregister: true,
                    required: true,
                    onBlur(event) {
                      const value = Number(event.target.value);

                      if (watch_projection_paths && value > watch_projection_paths) {
                        setValue("projection_paths", value);
                      }
                    },
                  })}
                  info={
                    !isNaN(watch_path_end ?? 0)
                      ? getInfoMsg(watch_path_end, rateProjectionsPageSettingsCopy.path_end)
                      : undefined
                  }
                />
              </Flex>
            </>
          )}
          <Flex maxW="lg" alignItems="center" gridGap="2">
            <Text variant="primary" whiteSpace="nowrap">
              Interest Rate Model
            </Text>
            <CASelectDropdown
              info={getInfoMsg(
                watch_interest_rate_model_name,
                rateProjectionsPageSettingsCopy.interest_rate_model_name
              )}
              {...register("interest_rate_model_name", {
                required: true,
                onChange(event) {
                  const value = event.target.value;

                  if (value === "LMM2F") {
                    setValue("path_start", 1);
                    setValue("path_end", 256);
                    setValue("projection_paths", 256);
                    return;
                  }
                  const pathEnd = watch_path_end;
                  const projectionPaths = watch_projection_paths;

                  if (value !== "LMM2F" && pathEnd === 256 && projectionPaths === 256) {
                    setValue("path_start", 1);
                    setValue("path_end", 200);
                    setValue("projection_paths", 200);
                  }
                },
              })}
              options={interestRateModelOptions}
            />
          </Flex>
          <Flex maxW="lg" alignItems="center" gridGap="2">
            <Text variant="primary" whiteSpace="nowrap">
              Calibration
            </Text>
            <CASelectDropdown
              info={getInfoMsg(
                watch_interest_rate_calibration_method,
                rateProjectionsPageSettingsCopy.interest_rate_calibration_method
              )}
              {...register("interest_rate_calibration_method", {
                required: true,
              })}
              options={calibrationOptions}
            />
          </Flex>
        </HStack>
      </CACard>
    </Flex>
  );
};

export default React.forwardRef(RateProjectionsInput);
