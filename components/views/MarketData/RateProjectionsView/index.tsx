import * as React from "react";
import { Box } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { RatePrejectionRequest } from "@/types/swr";
import { getCDUDateKeyFromSubType, getUuid, parseDateToYYYYMMDD } from "@/utils/helpers";
import { useMarketDataRateProjectionsPage } from "@/contexts/PageContexts/MarketDataRateProjectionsPageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { CustomRequestOptions } from "@/utils/swr-hooks";
import { useQueryParameters } from "@/hooks/useQueryParameters";
import {
  CA_Mastr_Models_v1_0_Models_InterestRateCalibrationMethod,
  CA_Mastr_Models_v1_0_Models_MortgageRateScenario,
} from "@/utils/openapi";
import { MarketDataModuleProps } from "..";
import MarketDataHeader from "../MarketDataHeader";
import { RateProjectionStopWatchWrapper } from "../MarketDataWatchWrapper";
import { RateProjectionProgressIndicator } from "../MarketDataProgressIndicatorWrapper";
import { RateProjectionStopRunningWrapper } from "../MarketDataStopRunningWrapper";
import RateProjectionsViewBody from "./RateProjectionsViewBody";
import RateProjectionsInput, { RateProjectionsHander } from "./RateProjectionsInput";

const MarketDataUtilitiesRateProjectionsView: React.FC<MarketDataModuleProps> = ({
  pageTitle,
}: MarketDataModuleProps) => {
  const {
    state: { userSettings, security_info },
    action: { updateMarketDataUserSettingsCopy, setIsTimerRunning },
  } = useMarketDataModule();

  const {
    state: {
      rateProjectionsPageSettings,
      app,
      page,
      noCache,
      lastRun,
      isOldRun,
      lastRunId,
      api_start_time,
      isLoadingOldRun,
      api_end_time,
    },
    action: { run, updateRateProjectionsPageSettings },
  } = useMarketDataRateProjectionsPage();

  const { pushWithoutRendering } = useQueryParameters();

  const rateProjectionsInputRef = React.useRef<RateProjectionsHander>(null);
  const cduDateKey = getCDUDateKeyFromSubType(security_info?.sub_type);
  const { rateProjectionRequest, requestOpts } = React.useMemo(() => {
    const rateProjectionRequest: RatePrejectionRequest = {
      app,
      page,
      run_id: lastRunId,
      curve_shift: rateProjectionsPageSettings.curve_shift,
      curve_date: parseDateToYYYYMMDD(userSettings.curve_date),
      cello_cdu_date: parseDateToYYYYMMDD(userSettings[cduDateKey]),
      mortgage_rate_scenario: rateProjectionsPageSettings.mortage_rate_scenario,
      current_coupon_model_name: userSettings.current_coupon_model,
      yield_curve_model_name: userSettings.yield_curve_model,
      no_cache: noCache,
      market_data_source: userSettings.market_data_source,
      projection_paths: rateProjectionsPageSettings.projection_paths,
      path_start: rateProjectionsPageSettings.path_start,
      path_end: rateProjectionsPageSettings.path_end,
      interest_rate_model_name: rateProjectionsPageSettings.interest_rate_model_name,
      interest_rate_calibration_method:
        rateProjectionsPageSettings.interest_rate_calibration_method as CA_Mastr_Models_v1_0_Models_InterestRateCalibrationMethod,
    };

    // If mortgage_rate_scenario is not STOCHASTIC, remove projection_paths, path_start, and path_end
    if (rateProjectionRequest.mortgage_rate_scenario !== CA_Mastr_Models_v1_0_Models_MortgageRateScenario.STOCHASTIC) {
      delete rateProjectionRequest.projection_paths;
      delete rateProjectionRequest.path_start;
      delete rateProjectionRequest.path_end;
    }

    const requestOpts: CustomRequestOptions = { lastRun, isOldRun };

    return { rateProjectionRequest, requestOpts };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    userSettings.current_coupon_model,
    userSettings.yield_curve_model,
    cduDateKey,
    app,
    page,
    noCache,
    rateProjectionsPageSettings,
    lastRunId,
    lastRun,
    isOldRun,
  ]);

  const onSubmit = (opts: { no_cache: boolean }) => {
    const { no_cache } = opts;
    rateProjectionsInputRef.current?.handleSubmit((data) => {
      updateRateProjectionsPageSettings(data);
      updateMarketDataUserSettingsCopy(userSettings);
      setIsTimerRunning(true);

      const run_id = getUuid();
      run({ no_cache, runId: run_id });
      pushWithoutRendering({ run_id });
    })();
  };

  return (
    <Box>
      <MarketDataHeader
        title={pageTitle}
        onRunClick={onSubmit}
        runInfo={{
          id: lastRunId,
          date: isOldRun ? api_start_time : lastRun,
          isOldRun,
        }}
        stopWatch={
          <RateProjectionStopWatchWrapper
            rateProjectionRequest={rateProjectionRequest}
            requestOpts={{
              ...requestOpts,
              lastRun: isOldRun ? api_start_time : lastRun,
            }}
            endTime={isOldRun ? api_end_time : undefined}
          />
        }
        progressIndicator={
          <RateProjectionProgressIndicator rateProjectionRequest={rateProjectionRequest} requestOpts={requestOpts} />
        }
        stopRunning={
          <RateProjectionStopRunningWrapper rateProjectionRequest={rateProjectionRequest} requestOpts={requestOpts} />
        }
      />
      {!isLoadingOldRun && <RateProjectionsInput ref={rateProjectionsInputRef} />}
      <MainInputContentTemplate>
        <RateProjectionsViewBody rateProjectionRequest={rateProjectionRequest} requestOpts={requestOpts} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default MarketDataUtilitiesRateProjectionsView;
