import * as React from "react";
import { AgChartThemeOverrides } from "ag-charts-types";
import { rateProjectionDetailColumnData } from "@/utils/grid/RateProjectionsColumnData";
import {
  CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisResponse,
  CA_Mastr_Api_v1_0_Models_CurveAnalysis_InterestRateCurve,
} from "@/utils/openapi";
import { formatValue } from "@/design-system/molecules/CAGrid/helpers";
import { percentageGetter } from "@/utils/grid/formatters";
import CAGrid from "@/design-system/molecules/CAGrid";

type RateProjectionsDetailGridProps = {
  data?: CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisResponse | null | undefined;
  hasError?: boolean;
  loading: boolean;
  lastRun?: string;
};
const RateProjectionsDetailGrid: React.FC<RateProjectionsDetailGridProps> = ({
  data,
  hasError,
  loading,
  lastRun,
}: RateProjectionsDetailGridProps) => {
  const gridData = React.useMemo(() => {
    return hasError ? [] : data?.results;
  }, [data?.results, hasError]);

  return (
    <CAGrid<CA_Mastr_Api_v1_0_Models_CurveAnalysis_InterestRateCurve>
      gridDataType="futuristic"
      hasRun={!!lastRun}
      initialMessage="Click on ▷ to load Rate Projections."
      gridProps={{
        columnDefs: rateProjectionDetailColumnData,
        rowData: gridData,
        pivotMode: false,
        suppressAggFuncInHeader: false,
        suppressColumnVirtualisation: false,
        removePivotHeaderRowWhenSingleValueColumn: true,
        chartThemeOverrides: chartThemeOverrides,
        loading,
      }}
      cardProps={{
        title: "Projections",
      }}
      gridType="rate-projection-detail"
      showExpand={false}
      hideSearch
      stackStyles={{
        h: "calc(100vh - 22.4rem)",
      }}
    />
  );
};

const chartThemeOverrides: AgChartThemeOverrides = {
  line: {
    axes: {
      number: {
        label: {
          formatter: (params) => percentageGetter(2)(params.value),
        },
      },
    },
    series: {
      tooltip: {
        renderer: function (params) {
          return {
            title: formatValue(params.title),
            content: percentageGetter(2)(params.datum),
          };
        },
      },
      marker: {
        enabled: false,
      },
    },
    navigator: {
      enabled: true,
    },
  },
  common: {
    padding: {
      top: 16,
      right: 35,
      bottom: 16,
      left: 16,
    },
  },
};

export default React.memo(RateProjectionsDetailGrid);
