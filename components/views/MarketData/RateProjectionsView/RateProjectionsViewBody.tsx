import { VStack } from "@chakra-ui/layout";
import * as React from "react";
import { CustomRequestOptions } from "@/utils/swr-hooks";
import { CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest } from "@/utils/openapi";
import { usePostRateProjection } from "@/utils/swr-hooks/RateProjections";
import { useMarketDataRateProjectionsPage } from "@/contexts/PageContexts/MarketDataRateProjectionsPageContext";
import RateProjectionsDetailGrid from "./RateProjectionsDetailGrid";

type RateProjectionViewBodyProps = {
  requestOpts: CustomRequestOptions;
  rateProjectionRequest: CA_Mastr_Api_v1_0_Models_CurveAnalysis_CurveAnalysisRequest;
};

const RateProjectionsViewBody: React.FC<RateProjectionViewBodyProps> = ({
  requestOpts: { lastRun, isOldRun },
  rateProjectionRequest,
}: RateProjectionViewBodyProps) => {
  const {
    state: { isLoadingOldRun },
  } = useMarketDataRateProjectionsPage();
  const { data, error, isLoading } = usePostRateProjection(rateProjectionRequest, { lastRun, isOldRun });
  return (
    <VStack alignItems="stretch" spacing={4} h="full">
      <RateProjectionsDetailGrid
        lastRun={lastRun}
        data={data}
        hasError={!!error}
        loading={isLoading || isLoadingOldRun}
      />
    </VStack>
  );
};

export default React.memo(RateProjectionsViewBody);
