import * as React from "react";
import { Box } from "@chakra-ui/react";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import MarketDataHeader from "../MarketDataHeader";
import { MarketDataModuleProps } from "..";
import InterestRatesInputs from "./InterestRatesInput";
import { InterestRatesContent } from "./InterestRatesContent";

type CategorizedDataKeys =
  | "liborSwaps"
  | "liborSwapSpreads"
  | "sofrSwaps"
  | "sofrSwapSpreads"
  | "treasuries"
  | "CMM102"
  | "CMM102ToCurve"
  | "primaryRate"
  | "GPLSurvey"
  | "swaptionVol"
  | "capVol"
  | "liborRates"
  | "termSofrRates"
  | "LTPrimaryRates"
  | "LTPrimarySpreadFNCL"
  | "LTPrimarySpreadCMM102"
  | "spotPrimaryRates"
  | "spotPrimarySpreadFNCL"
  | "spotPrimarySpreadCMM102"
  | "CMM100"
  | "CMM100ToCurve"
  | "LTPrimarySpreadCMM100"
  | "spotPrimarySpreadCMM100";

export type CategorizedData = {
  [key in CategorizedDataKeys]: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[];
};

const MarketDataInterestRatesView: React.FC<MarketDataModuleProps> = ({ pageTitle }: MarketDataModuleProps) => {
  const {
    state: { userSettings },
  } = useMarketDataModule();

  return (
    <Box>
      <MarketDataHeader title={pageTitle} withRun={false} />
      <MainInputContentTemplate>
        <InterestRatesInputs />
        <InterestRatesContent userSettings={userSettings} marketDataSource={userSettings.market_data_source} />
      </MainInputContentTemplate>
    </Box>
  );
};

export default MarketDataInterestRatesView;
