import React from "react";
import {
  Box,
  Grid,
  GridItem,
  IconButton,
  Menu,
  MenuButton,
  MenuItem,
  Menu<PERSON>ist,
  <PERSON><PERSON>,
  useColorModeValue,
} from "@chakra-ui/react";
import { IoEllipsisVerticalOutline } from "react-icons/io5";
import ColumnWrapper from "@/components/helpers/ColumnWrapper";
import { useGetInterestRatesDetailsSWR } from "@/utils/swr-hooks";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import {
  CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail,
  CA_Mastr_Models_v1_0_Models_MarketDataSource,
} from "@/utils/openapi";
// Cards
import CACard from "@/design-system/molecules/CACard";
import CAIcon from "@/design-system/atoms/CAIcon";
import { MarketDataUserSettingsType } from "@/contexts/ModuleContexts/MarketDataModuleContext/MarketDataModuleContextTypes";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import RateAndChangeDisplayCard from "./cards/RateAndChangeDisplay";
import LevelAndChangeDisplayCard from "./cards/LevelAndChangeDisplay";
import SwaptionVOLCard from "./cards/Swaption_VOL";
import PrimaryRateCard from "./cards/PrimaryRate";
import CapVolCard from "./cards/CapVol";
import LiborRatesCard from "./cards/LiborRates";
import SofrRatesCard from "./cards/SofrRates";
import { CurrentCouponModel } from "./cards/CurrentCouponModel";
import TermSofrRates from "./cards/TermSofrRates";
import { CategorizedData } from ".";

interface Props {
  userSettings: MarketDataUserSettingsType;
  marketDataSource?: CA_Mastr_Models_v1_0_Models_MarketDataSource;
}

enum MarketDataMode {
  SUMMARY = "summary",
  DETAIL = "detail",
}

export const InterestRatesContent = ({ userSettings, marketDataSource }: Props) => {
  const bgColor = useColorModeValue("celloBlue.25", "celloBlue.1100");
  const {
    state: { metadata },
  } = useGlobalContext();
  const [dataMode, setDataMode] = React.useState<MarketDataMode>(MarketDataMode.SUMMARY);

  const { data: responseData, isLoading } = useGetInterestRatesDetailsSWR(
    parseDateToYYYYMMDD(userSettings.curve_date),
    userSettings.model_version,
    marketDataSource
  );

  const modelConfig = metadata?.pricer_settings?.model_configs?.find(
    (model) => model.model_name === userSettings.model_version
  );
  const useCmm100 = modelConfig?.use_cmm_102 === "N";

  const modelVersionGRE10_00 = React.useMemo(() => {
    try {
      const match = modelConfig?.model_name?.match(/Cello_(\d+\.\d+)/);
      return match ? parseFloat(match[1]) >= 10 : false;
    } catch (error) {
      return false;
    }
  }, [modelConfig?.model_name]);

  const cmm = useCmm100 ? "CMM100" : "CMM102";

  const { market_data_details } = responseData || {};
  const pageData: CategorizedData | undefined = React.useMemo(() => {
    if (!market_data_details && isLoading) return undefined;

    const categorizedData: CategorizedData = {
      liborSwaps: [],
      liborSwapSpreads: [],
      sofrSwaps: [],
      sofrSwapSpreads: [],
      treasuries: [],
      CMM102: [],
      CMM102ToCurve: [],
      CMM100ToCurve: [],
      swaptionVol: [],
      capVol: [],
      liborRates: [],
      termSofrRates: [],
      primaryRate: [],
      GPLSurvey: [],
      LTPrimaryRates: [],
      LTPrimarySpreadFNCL: [],
      LTPrimarySpreadCMM102: [],
      LTPrimarySpreadCMM100: [],
      spotPrimaryRates: [],
      spotPrimarySpreadFNCL: [],
      spotPrimarySpreadCMM102: [],
      spotPrimarySpreadCMM100: [],
      CMM100: [],
    };

    market_data_details?.sort(
      (
        a: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail,
        b: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail
      ) => {
        return (
          a?.category?.localeCompare(b?.category as string) || (a?.sort_order as number) - (b?.sort_order as number)
        );
      }
    );

    market_data_details?.forEach((data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail) => {
      if (data.category === "LIBOR Swaps") categorizedData.liborSwaps?.push(data);
      else if (data.category === "LIBOR Swap Spreads") categorizedData.liborSwapSpreads?.push(data);
      else if (data.category === "SOFR Swaps") categorizedData.sofrSwaps?.push(data);
      else if (data.category === "SOFR Swap Spreads") categorizedData.sofrSwapSpreads?.push(data);
      else if (data.category === "Treasuries") categorizedData.treasuries?.push(data);
      else if (data.category === "CMM 102") categorizedData.CMM102?.push(data);
      else if (data.category === "CMM102 Spread to Curve") categorizedData.CMM102ToCurve?.push(data);
      else if (data.category === "CMM100 Spread to Curve") categorizedData.CMM100ToCurve?.push(data);
      else if (data.category === "Primary Rate (FHLMC Survey)") categorizedData.primaryRate?.push(data);
      else if (data.category === "GPL Survey") categorizedData.GPLSurvey?.push(data);
      else if (data.category === "Swaption Vol") categorizedData.swaptionVol?.push(data);
      else if (data.category === "CAP Vol") categorizedData.capVol?.push(data);
      else if (data.category === "LIBOR Rates") categorizedData.liborRates?.push(data);
      else if (data.category === "SOFR Rates") categorizedData.termSofrRates?.push(data);
      else if (data.category === "Spot Rate") categorizedData.spotPrimaryRates?.push(data);
      else if (data.category === "Spot Primary Spread to CMM102") categorizedData.spotPrimarySpreadCMM102?.push(data);
      else if (data.category === "Spot Primary Spread to CMM100") categorizedData.spotPrimarySpreadCMM100?.push(data);
      else if (data.category === "Spot Primary Spread to FNCL") categorizedData.spotPrimarySpreadFNCL?.push(data);
      else if (data.category === "Long Term Rate") categorizedData.LTPrimaryRates?.push(data);
      else if (data.category === "LT Primary Spread to CMM102") categorizedData.LTPrimarySpreadCMM102?.push(data);
      else if (data.category === "LT Primary Spread to CMM100") categorizedData.LTPrimarySpreadCMM100?.push(data);
      else if (data.category === "LT Primary Spread to FNCL") categorizedData.LTPrimarySpreadFNCL?.push(data);
      else if (data.category === "CMM 100") categorizedData.CMM100?.push(data);
    });

    return categorizedData;
  }, [isLoading, market_data_details]);

  const CardHeadingRight = () => (
    <Box onClick={(e) => e.stopPropagation()}>
      <Menu placement="right-start" isLazy closeOnSelect>
        <MenuButton
          as={IconButton}
          height={"auto"}
          aria-label="market-options-menu"
          size="xs"
          icon={<CAIcon as={IoEllipsisVerticalOutline} boxSize={5} variant="secondary" />}
          variant="ghost"
          onClick={(e) => e.stopPropagation()}
        />
        <MenuList zIndex="popover" minW="8rem">
          {dataMode === MarketDataMode.SUMMARY ? (
            <MenuItem onClick={() => setDataMode(MarketDataMode.DETAIL)}>Detail</MenuItem>
          ) : (
            <MenuItem onClick={() => setDataMode(MarketDataMode.SUMMARY)}>Summary</MenuItem>
          )}
        </MenuList>
      </Menu>
    </Box>
  );

  return (
    <Stack direction={{ base: "column", lg: "row" }} spacing={4} maxW="85.625rem">
      <ColumnWrapper size="interest-rate-market" p={0}>
        <CACard
          title="Market"
          variant="secondary"
          bg={bgColor}
          allowCollapse
          cardKey="interest-rates-market"
          headingRight={<CardHeadingRight />}
        >
          <Stack direction={{ base: "column", md: "row" }} alignItems="flex-start" spacing={4}>
            <ColumnWrapper size="interest-rate-market-column" p={0}>
              <RateAndChangeDisplayCard
                title="LIBOR Swaps"
                valueLabel="rate"
                data={pageData?.liborSwaps}
                detailedView={dataMode === MarketDataMode.DETAIL}
              />
              <RateAndChangeDisplayCard
                title="SOFR Swaps"
                valueLabel="rate"
                data={pageData?.sofrSwaps}
                detailedView={dataMode === MarketDataMode.DETAIL}
              />
              <RateAndChangeDisplayCard
                title="Treasuries"
                valueLabel="rate"
                data={pageData?.treasuries}
                detailedView={dataMode === MarketDataMode.DETAIL}
              />
              <RateAndChangeDisplayCard
                title="LIBOR Swap Spread to Treasury"
                valueLabel="level"
                data={pageData?.liborSwapSpreads}
                detailedView={dataMode === MarketDataMode.DETAIL}
              />
              <RateAndChangeDisplayCard
                title="SOFR Swap Spread to Treasury"
                valueLabel="level"
                data={pageData?.sofrSwapSpreads}
                detailedView={dataMode === MarketDataMode.DETAIL}
              />
            </ColumnWrapper>
            <ColumnWrapper size="interest-rate-market-column" p={0}>
              <LevelAndChangeDisplayCard title="CMM100" data={pageData?.CMM100} spread_to="CMM100" level_in="%" />
              <LevelAndChangeDisplayCard title="CMM102" data={pageData?.CMM102} spread_to="CMM102" level_in="%" />
              <PrimaryRateCard title="Primary Rate (FHLMC Survey)" data={pageData?.primaryRate} />
              <LevelAndChangeDisplayCard
                title="GPL Survey"
                data={pageData?.GPLSurvey}
                spread_to="GPLSurvey"
                level_in="%"
              />
            </ColumnWrapper>
            <ColumnWrapper size="interest-rate-market-column" p={0}>
              <LiborRatesCard title="Libor Rates" data={pageData?.liborRates} />
              <SofrRatesCard asOfDate={userSettings.curve_date} />
              <TermSofrRates title="Term SOFR Rates" data={pageData?.termSofrRates} />
              <SwaptionVOLCard title="Swaption VOL" data={pageData?.swaptionVol} />
              <CapVolCard title="Cap VOL" data={pageData?.capVol} />
            </ColumnWrapper>
          </Stack>
        </CACard>
      </ColumnWrapper>
      <ColumnWrapper size="interest-rate-model" p={0}>
        <CACard title="Model" variant="secondary" bg={bgColor} allowCollapse cardKey="interest-rates-model">
          <Grid
            templateColumns={{
              base: "repeat(1, 1fr)",
              md: "repeat(2, 1fr)",
              lg: "repeat(1, 1fr)",
              xl: "repeat(1, 1fr)",
              "2xl": "repeat(2, 1fr)",
            }}
            gap={4}
          >
            <GridItem>
              <LevelAndChangeDisplayCard
                title="Spot Primary Rates"
                data={pageData?.spotPrimaryRates}
                spread_to="Rates"
                level_in="%"
                modelVersionGRE10_00={modelVersionGRE10_00}
              />
            </GridItem>
            <GridItem>
              <LevelAndChangeDisplayCard
                title="LT Primary Rates"
                data={pageData?.LTPrimaryRates}
                spread_to="Rates"
                level_in="%"
                modelVersionGRE10_00={modelVersionGRE10_00}
              />
            </GridItem>
            <GridItem>
              <LevelAndChangeDisplayCard
                title={`Spot Primary Spread to ${cmm}`}
                data={useCmm100 ? pageData?.spotPrimarySpreadCMM100 : pageData?.spotPrimarySpreadCMM102}
                spread_to={cmm}
                modelVersionGRE10_00={modelVersionGRE10_00}
              />
            </GridItem>
            <GridItem>
              <LevelAndChangeDisplayCard
                title={`LT Primary Spread to ${cmm}`}
                data={useCmm100 ? pageData?.LTPrimarySpreadCMM100 : pageData?.LTPrimarySpreadCMM102}
                spread_to={cmm}
                modelVersionGRE10_00={modelVersionGRE10_00}
              />
            </GridItem>
            <GridItem>
              <LevelAndChangeDisplayCard
                title="Spot Primary Spread to FNCL"
                data={pageData?.spotPrimarySpreadFNCL}
                spread_to="FNCL"
                modelVersionGRE10_00={modelVersionGRE10_00}
              />
            </GridItem>
            <GridItem>
              <LevelAndChangeDisplayCard
                title="LT Primary Spread to FNCL"
                data={pageData?.LTPrimarySpreadFNCL}
                spread_to="FNCL"
                modelVersionGRE10_00={modelVersionGRE10_00}
              />
            </GridItem>
            <GridItem>
              <LevelAndChangeDisplayCard
                title={`${cmm} Spread to Curve`}
                data={useCmm100 ? pageData?.CMM100ToCurve : pageData?.CMM102ToCurve}
                spread_to="Curve"
              />
            </GridItem>
            <GridItem>
              <CurrentCouponModel />
            </GridItem>
          </Grid>
        </CACard>
      </ColumnWrapper>
    </Stack>
  );
};
