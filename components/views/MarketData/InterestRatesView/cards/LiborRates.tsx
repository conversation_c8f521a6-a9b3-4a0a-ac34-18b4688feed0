import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedNumberFixed } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";

// eslint-disable-next-line @typescript-eslint/ban-types
type LiborRatesCardProps = {
  title: string;
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
};

const LiborRatesCard: React.FC<LiborRatesCardProps> = ({ title, data = undefined }: LiborRatesCardProps) => {
  const headers: string[] = ["Month", "Rate (%)", "Change (bp)"];

  const tableData = React.useMemo(() => {
    if (!data) return undefined;
    if (!data.length) return [];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const categorizedData: any = {};
    data?.forEach((d) => {
      const ticker_name: string = d.ticker_name?.trim() ?? "";
      categorizedData[ticker_name] = {
        rate: getFormattedNumberFixed(3)(d.value),
        change: getFormattedNumberFixed(1)(d.change),
      };
    });

    return [
      {
        name: "1",
        values: [categorizedData["1"]?.rate ?? "-", categorizedData["1"]?.change ?? "-"],
      },
      {
        name: "3",
        values: [categorizedData["3"]?.rate ?? "-", categorizedData["3"]?.change ?? "-"],
      },
      {
        name: "6",
        values: [categorizedData["6"]?.rate ?? "-", categorizedData["6"]?.change ?? "-"],
      },
      {
        name: "12",
        values: [categorizedData["12"]?.rate ?? "-", categorizedData["12"]?.change ?? "-"],
      },
    ];
  }, [data]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title={title} allowCollapse cardKey="market-data-libor-vol">
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(LiborRatesCard);
