import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { useSofrRates } from "@/utils/swr-hooks/Rates";
import { formattedChangeValue, formattedRateValue } from "./RateAndChangeDisplay";

type SofrRatesCardProps = {
  asOfDate?: Date | undefined;
};

const headers: string[] = ["Days", "Rate (%)", "Change (bps)"];

const SofrRatesCard = ({ asOfDate }: SofrRatesCardProps) => {
  const { data, error } = useSofrRates(parseDateToYYYYMMDD(asOfDate));

  const transformedData = React.useMemo(() => {
    const formattedData = data?.sofr_rates?.map((rate) => ({
      name: rate.ticker_name ?? "-",
      values: [formattedRateValue(rate.value, 3) ?? "-", formattedChangeValue(rate.change)],
    }));

    if (formattedData && !formattedData.find((rate) => rate.name === "1")) {
      return [{ name: "1", values: ["-", "-"] }, ...formattedData];
    }

    return formattedData;
  }, [data]);

  const tableData = error ? [] : transformedData;

  return (
    <CACard title="Historical Average SOFR Rates" allowCollapse cardKey="market-data-Sofr-vol">
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default SofrRatesCard;
