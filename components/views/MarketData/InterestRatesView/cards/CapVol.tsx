import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedNumberFixed } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";

// eslint-disable-next-line @typescript-eslint/ban-types
type CapVolCardProps = {
  title: string;
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
};

const CapVolCard: React.FC<CapVolCardProps> = ({ title, data = undefined }: CapVolCardProps) => {
  const headers: string[] = ["Maturity", "Vol (%)", "Change (%)"];

  const tableData = React.useMemo(() => {
    if (!data) return undefined;
    if (!data.length) return [];

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const categorizedData: any = {};
    data?.forEach((d) => {
      const ticker_name: string = d.ticker_name?.trim() ?? "";
      categorizedData[ticker_name] = {
        vol: getFormattedNumberFixed(1)(d.value),
        change: getFormattedNumberFixed(1)(d.change),
      };
    });

    return [
      {
        name: "1",
        values: [categorizedData["1"]?.vol ?? "-", categorizedData["1"]?.change ?? "-"],
      },
      {
        name: "3",
        values: [categorizedData["3"]?.vol ?? "-", categorizedData["3"]?.change ?? "-"],
      },
      {
        name: "5",
        values: [categorizedData["5"]?.vol ?? "-", categorizedData["5"]?.change ?? "-"],
      },
      {
        name: "7",
        values: [categorizedData["7"]?.vol ?? "-", categorizedData["7"]?.change ?? "-"],
      },
      {
        name: "10",
        values: [categorizedData["10"]?.vol ?? "-", categorizedData["10"]?.change ?? "-"],
      },
    ];
  }, [data]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title={title} allowCollapse cardKey="market-data-cap-vol">
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(CapVolCard);
