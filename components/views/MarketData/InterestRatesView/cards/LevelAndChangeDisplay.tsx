import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedNumberFixed } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";

type LevelAndChangeDisplayCardProps = {
  title: string;
  types?: string[];
  level_in?: "bp" | "%";
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
  spread_to: "Rates" | "FNCL" | "CMM102" | "Curve" | "GPLSurvey" | "CMM100";
  primaryColumnLabel?: "type" | "rate";
  modelVersionGRE10_00?: boolean;
};

const getFields = (
  spread_to: "Rates" | "FNCL" | "CMM102" | "Curve" | "GPLSurvey" | "CMM100",
  modelVersionGRE10_00?: boolean
) => {
  // Replace GNM with breakout fields for Cello version >= Cello 10.00
  const GNM_FIELDS = !modelVersionGRE10_00
    ? ["GNM"]
    : ["FHA Purch", "FHA Refi", "VA Purch", "VA Refi", "RHS Purch", "RHS Refi"];

  switch (spread_to) {
    case "Rates":
      return ["FNCL", "FNCI", ...GNM_FIELDS, "Jumbo", "GPL"];
    case "FNCL":
      return ["FNCI", ...GNM_FIELDS, "Jumbo", "GPL"];
    case "CMM102":
      return ["FNCL", "FNCI", ...GNM_FIELDS];
    case "CMM100":
      return ["FNCL", "FNCI", ...GNM_FIELDS];
    case "Curve":
      return ["FNCL", "FNCI"];
    case "GPLSurvey":
      return ["35 B10 Spread", "CMM 102 Rate", "Primary Rate"];
    default:
      return [];
  }
};

const LevelAndChangeDisplayCard: React.FC<LevelAndChangeDisplayCardProps> = ({
  title,
  level_in = "bp",
  data = undefined,
  spread_to,
  primaryColumnLabel = "type",
  modelVersionGRE10_00,
}: LevelAndChangeDisplayCardProps) => {
  const headers: string[] = [
    `${primaryColumnLabel === "type" ? "Type" : "Rate"}`,
    `Level (${level_in})`,
    "Change (bp)",
  ];
  const decimal_points: number = level_in === "bp" ? 1 : 3;

  const tableData = React.useMemo(() => {
    if (!data) return undefined;
    if (!data.length) return [];

    return getFields(spread_to, modelVersionGRE10_00).map((f) => {
      const entry = data?.find((d) => d?.ticker_name === f);

      return {
        name: entry?.ticker_name ?? f,
        values: [getFormattedNumberFixed(decimal_points)(entry?.value), getFormattedNumberFixed(1)(entry?.change)],
      };
    });
  }, [data, spread_to, decimal_points, modelVersionGRE10_00]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard
      title={title}
      allowCollapse
      cardKey={`market-data-${title.toLowerCase().replace(/ /g, "-")}`}
      cardBodyStyle={{ h: "full" }}
    >
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(LevelAndChangeDisplayCard);
