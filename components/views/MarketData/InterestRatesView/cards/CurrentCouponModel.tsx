import React from "react";
import { Skeleton } from "@chakra-ui/react";
import { useCurrentCoupon, useCurrentCouponToken } from "@/utils/swr-hooks/CurrentCoupon";
import { getFormattedNumberFixed, parseDateToYYYYMMDD } from "@/utils/helpers";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_CurrentCouponModel_CurrentCoupon } from "@/utils/openapi";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { getPreviousBusinessDate } from "@/utils/helpers/pricer";

const title = "Current Coupon Model Weights";

const headers = ["Type", "Year", "Weight (%)", "Change (%)"];

const types: { name: string; key: "current_coupon_30_year" | "current_coupon_15_year" }[] = [
  { name: "FNCL", key: "current_coupon_30_year" },
  { name: "FNCI", key: "current_coupon_15_year" },
];

const years: { name: string; key: keyof CA_Mastr_Api_v1_0_Models_CurrentCouponModel_CurrentCoupon }[] = [
  { name: "2", key: "swap_2yr_weight" },
  { name: "5", key: "swap_5yr_weight" },
  { name: "10", key: "swap_10yr_weight" },
  { name: "30", key: "swap_30yr_weight" },
];

export const CurrentCouponModel = () => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings },
  } = useMarketDataModule();

  const prev_business_date = getPreviousBusinessDate(userSettings?.curve_date);

  const { data: tokens } = useCurrentCouponToken({
    curve_date: parseDateToYYYYMMDD(userSettings?.curve_date),
    current_coupon_model_version: metadata?.pricer_settings?.model_configs?.find(
      (modelConfig) => modelConfig.model_name === userSettings.model_version
    )?.current_coupon_model_name,
    prev_business_date: prev_business_date ? parseDateToYYYYMMDD(new Date(prev_business_date)) : undefined,
    no_cache: !userSettings.useCache,
  });
  const { data, error, isLoading } = useCurrentCoupon(tokens);

  const { current_coupon_30_year, current_coupon_15_year } = data ?? {};

  const tableData = types
    .flatMap((type) => [
      ...years.map((year, i) => {
        const row = { current_coupon_30_year, current_coupon_15_year };
        const value = row?.[type.key]?.[year.key];
        //Remove this logic once API returns change values UI-2404
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const chngVal = (row?.[type.key] as any)?.["change"]?.[year.key];
        return {
          name: i === 0 ? type.name : "",
          values: [
            year.name,
            value === null || value === undefined ? "-" : getFormattedNumberFixed(1)(value * 100),
            chngVal === null || chngVal === undefined ? "-" : getFormattedNumberFixed(1)(chngVal * 100),
          ],
        };
      }),
      {
        name: "",
        values: [],
      },
    ])
    .slice(0, -1);

  return (
    <CACard
      title={title}
      allowCollapse
      cardKey={`market-data-${title.toLowerCase().replace(/ /g, "-")}`}
      cardStyleOnOpen={{ height: "100%" }}
      cardBodyStyle={{ h: "full" }}
    >
      {!data && !error && isLoading ? (
        <Skeleton height="25px" />
      ) : (
        <CATable headers={headers} data={error || !data ? [] : tableData} />
      )}
    </CACard>
  );
};
