import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedNumberFixed } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";

// eslint-disable-next-line @typescript-eslint/ban-types
type RateAndChangeDisplayCardProps = {
  title: string;
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
  valueLabel: "rate" | "level";
  detailedView?: boolean;
};

export const formattedRateValue = (value: number | undefined, decimalPoints: number) => {
  return getFormattedNumberFixed(decimalPoints)(value);
};

export const formattedChangeValue = (value: number | undefined | null) => {
  return getFormattedNumberFixed(1)(value);
};

const summaryViewItems = ["2", "5", "10", "30"];

const RateAndChangeDisplayCard: React.FC<RateAndChangeDisplayCardProps> = ({
  title,
  data = undefined,
  valueLabel,
  detailedView,
}: RateAndChangeDisplayCardProps) => {
  const isRate: boolean = valueLabel === "rate";
  const decimalPointsForValue = isRate ? 3 : 1;
  const headers: string[] = ["Year", isRate ? "Rate (%)" : "Level (bp)", "Change (bp)"];

  const tableData = React.useMemo(
    () =>
      data
        ?.map((el) => {
          return {
            name: el.ticker_name ?? "-",
            values: [
              formattedRateValue(el.value ?? undefined, decimalPointsForValue) ?? "-",
              formattedChangeValue(el.change),
            ],
          };
        })
        .filter((el) => (detailedView ? true : summaryViewItems.includes(el.name))),
    [data, decimalPointsForValue, detailedView]
  );

  return (
    <CACard title={title} allowCollapse cardKey={`market-data-${title.toLowerCase().replace(/ /g, "-")}`}>
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(RateAndChangeDisplayCard);
