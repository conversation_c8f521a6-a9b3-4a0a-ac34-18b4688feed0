import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";
import { formattedChangeValue, formattedRateValue } from "./RateAndChangeDisplay";

interface TermSofrRatesProps {
  title: string;
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
}

const TermSofrRates: React.FC<TermSofrRatesProps> = ({ title, data = undefined }: TermSofrRatesProps) => {
  const headers: string[] = ["Term", "Rate (%)", "Change (bps)"];

  const tableData = React.useMemo(() => {
    if (!data) return undefined;
    if (!data.length) return [];

    return data?.map((rate) => {
      return {
        name: rate.term,
        values: [formattedRateValue(rate.value ?? undefined, 3), formattedChangeValue(rate.change)],
      };
    });
  }, [data]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title={title} allowCollapse cardKey="market-data-term-sofr-rates">
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(TermSofrRates);
