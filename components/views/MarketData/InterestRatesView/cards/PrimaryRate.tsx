import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedLocaleDate, getFormattedNumberFixed } from "@/utils/helpers";
import CATable from "@/design-system/molecules/CATable";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";

// eslint-disable-next-line @typescript-eslint/ban-types
type PrimaryRateCardProps = {
  title: string;
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
};

const PrimaryRateCard: React.FC<PrimaryRateCardProps> = ({ title, data = undefined }: PrimaryRateCardProps) => {
  const headers: string[] = ["", "Current", "Previous"];

  const tableData = React.useMemo(() => {
    if (!data) return undefined;

    const currentTermIndex = 0;
    const previousTermIndex = 1;
    const current_term = data?.[currentTermIndex]?.term;
    const previous_term = data?.[previousTermIndex]?.term;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const categorizedData: any = {};
    data?.forEach((d) => {
      const ticker_name: string = d.ticker_name?.trim() ?? "";
      if (!categorizedData[ticker_name]) {
        categorizedData[ticker_name] = {};
      }
      const forField = d.term === current_term ? "current" : "previous";
      categorizedData[ticker_name][forField] = getFormattedNumberFixed(2)(d.value);
    });

    return [
      {
        name: "Date",
        values: [getFormattedLocaleDate(current_term), getFormattedLocaleDate(previous_term)],
      },
      {
        name: "Raw Rate",
        values: [categorizedData["Raw Rate"]?.current ?? "-", categorizedData["Raw Rate"]?.previous ?? "-"],
      },
      {
        name: "Points",
        values: [categorizedData["Points"]?.current ?? "-", categorizedData["Points"]?.previous ?? "-"],
      },
      {
        name: "1Pt Adj Rate",
        values: [categorizedData["1 Pt Adj Rate"]?.current ?? "-", categorizedData["1 Pt Adj Rate"]?.previous ?? "-"],
      },
      {
        name: "Avg CMM100",
        values: [categorizedData["Avg CMM100"]?.current ?? "-", categorizedData["Avg CMM100"]?.previous ?? "-"],
      },
      {
        name: "Primary CMM100",
        values: [categorizedData["Primary-CMM100"]?.current ?? "-", categorizedData["Primary-CMM100"]?.previous ?? "-"],
      },
      {
        name: "Avg CMM102",
        values: [categorizedData["Avg CMM102"]?.current ?? "-", categorizedData["Avg CMM102"]?.previous ?? "-"],
      },
      {
        name: "Primary CMM102",
        values: [categorizedData["Primary-CMM102"]?.current ?? "-", categorizedData["Primary-CMM102"]?.previous ?? "-"],
      },
    ];
  }, [data]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title={title} allowCollapse cardKey="market-data-primary-rate">
      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(PrimaryRateCard);
