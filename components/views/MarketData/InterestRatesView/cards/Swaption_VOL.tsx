import * as React from "react";
import { Skeleton } from "@chakra-ui/skeleton";
import { HStack } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedNumberFixed } from "@/utils/helpers";
import { CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail } from "@/utils/openapi";
import CATable from "@/design-system/molecules/CATable";
import ToggleButtonGroup from "@/design-system/molecules/ToggleButtonGroup";

// eslint-disable-next-line @typescript-eslint/ban-types
type SwaptionVOLCardProps = {
  title: string;
  data: CA_Mastr_Api_v1_0_Models_MarketRates_MarketDataDetail[] | undefined;
};

enum SwaptionVOLDataTypes {
  PERCENT_VOL = "PERCENT_VOL",
  BPS_VOL = "BPS_VOL",
}

type categorizedTableDataType = {
  percentVolValue: number | undefined;
  percentVolChange: number | null | undefined;
  bpsVolValue: number | undefined;
  bpsVolChange: number | null | undefined;
};

const SwaptionVOLCard: React.FC<SwaptionVOLCardProps> = ({ title, data }: SwaptionVOLCardProps) => {
  const [selectedTerm, setSelectedTerm] = React.useState<SwaptionVOLDataTypes>(SwaptionVOLDataTypes.BPS_VOL);

  const headers: string[] = ["Term (Year)", "Swaption", "Change"];

  const tableData = React.useMemo(() => {
    if (!data) return undefined;
    if (!data.length) return [];
    const categorizedData: { [key: string]: Partial<categorizedTableDataType> } = {};

    const displayFieldName = selectedTerm === SwaptionVOLDataTypes.PERCENT_VOL ? "percentVol" : "bpsVol";
    data?.forEach((d) => {
      const field: string = d.ticker_name ?? "";
      if (!categorizedData[field]) {
        categorizedData[field] = {
          percentVolValue: d.value ?? undefined,
          percentVolChange: d.change,
        };
      } else {
        categorizedData[field]["bpsVolValue"] = d.value ?? undefined;
        categorizedData[field]["bpsVolChange"] = d.change;
      }
    });

    const rowData: {
      name: string;
      values: (string | number)[];
    }[] = [
      {
        name: "1x10",
        values: [
          getFormattedNumberFixed(1)(categorizedData["1x10"]?.[`${displayFieldName}Value`]) ?? "-",
          getFormattedNumberFixed(1)(categorizedData["1x10"]?.[`${displayFieldName}Change`]) ?? "-",
        ],
      },
      {
        name: "3x10",
        values: [
          getFormattedNumberFixed(1)(categorizedData["3x10"]?.[`${displayFieldName}Value`]) ?? "-",
          getFormattedNumberFixed(1)(categorizedData["3x10"]?.[`${displayFieldName}Change`]) ?? "-",
        ],
      },
      {
        name: "5x10",
        values: [
          getFormattedNumberFixed(1)(categorizedData["5x10"]?.[`${displayFieldName}Value`]) ?? "-",
          getFormattedNumberFixed(1)(categorizedData["5x10"]?.[`${displayFieldName}Change`]) ?? "-",
        ],
      },
      {
        name: "7x10",
        values: [
          getFormattedNumberFixed(1)(categorizedData["7x10"]?.[`${displayFieldName}Value`]) ?? "-",
          getFormattedNumberFixed(1)(categorizedData["7x10"]?.[`${displayFieldName}Change`]) ?? "-",
        ],
      },
      {
        name: "10x10",
        values: [
          getFormattedNumberFixed(1)(categorizedData["10x10"]?.[`${displayFieldName}Value`]) ?? "-",
          getFormattedNumberFixed(1)(categorizedData["10x10"]?.[`${displayFieldName}Change`]) ?? "-",
        ],
      },
    ];

    return rowData;
  }, [data, selectedTerm]) as {
    name: string;
    values: (string | number)[];
  }[];

  return (
    <CACard title={title} allowCollapse cardKey="market-data-swaption-vol" cardBodyStyle={{ overflow: "auto" }}>
      {data && (
        <HStack justifyContent="flex-end" my={2}>
          <ToggleButtonGroup
            buttons={[
              { label: "% Vol", value: SwaptionVOLDataTypes.PERCENT_VOL },
              { label: "Bps Vol", value: SwaptionVOLDataTypes.BPS_VOL },
            ]}
            selectedButton={selectedTerm}
            onChange={setSelectedTerm}
          />
        </HStack>
      )}

      {!tableData ? <Skeleton height="25px" /> : <CATable headers={headers} data={tableData} />}
    </CACard>
  );
};

export default React.memo(SwaptionVOLCard);
