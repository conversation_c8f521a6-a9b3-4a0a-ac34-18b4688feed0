import * as React from "react";
import { Flex, Text } from "@chakra-ui/react";
import CACard from "@/design-system/molecules/CACard";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { getFormattedLocaleDate } from "@/utils/helpers";
import MarketDataValueDisplayWrapper from "../shared/MarketDataValueDisplayWrapper";
import { MarketDataModelVersionDisplay } from "../shared/MarketDataModelVersionDisplay";

const InterestRatesInput = () => {
  const {
    state: { metadata },
  } = useGlobalContext();
  const {
    state: { userSettings },
  } = useMarketDataModule();

  const modelVersionValue = userSettings.model_version;

  const currentCouponModel = metadata?.pricer_settings?.model_configs?.find(
    (modelConfig) => modelConfig.model_name === userSettings.model_version
  )?.current_coupon_model_name;

  return (
    <CACard allowCollapse title="Inputs" overflow={"unset"}>
      <Flex flexWrap="wrap" alignItems="center" p="1" gridGap="2">
        <Flex alignItems="center" gridGap="2">
          <MarketDataValueDisplayWrapper
            name="Curve Date"
            link
            _key="curve_date"
            dateFormatter={getFormattedLocaleDate}
            showChanges={false}
          />
        </Flex>
        <Flex alignItems="center" gridGap="2">
          <MarketDataModelVersionDisplay />
        </Flex>
        {modelVersionValue && (
          <Flex gridGap="2" flexWrap="wrap">
            <Flex alignItems="center" gridGap="1">
              <Text variant="primary" whiteSpace="nowrap">
                Current Coupon
              </Text>
              <Text variant="secondary" whiteSpace="nowrap">
                {
                  metadata?.pricer_settings?.current_coupon_model?.find((coupon) => coupon.value === currentCouponModel)
                    ?.display_value
                }
              </Text>
            </Flex>
          </Flex>
        )}
      </Flex>
    </CACard>
  );
};

export default InterestRatesInput;
