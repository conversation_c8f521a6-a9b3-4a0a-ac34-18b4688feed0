/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { Flex, HStack, Text, VStack } from "@chakra-ui/react";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import CACard from "@/design-system/molecules/CACard";
import { getFormattedLocaleDate } from "@/utils/helpers";
import { useVolatilityCubePage } from "@/contexts/PageContexts/MarketDataVolatilityCubePageContext";
import CAMultiSelectDropdown, { MultiSelectOptions } from "@/design-system/molecules/CAMultiSelectDropdown";
import {
  MATURITY_OPTIONS,
  STRIKE_SHIFTS_OPTIONS,
  SWAP_TERM_OPTIONS,
  useModelTypeVolatilityOptions,
} from "@/constants/market-data";
import { showWarningToast } from "@/design-system/theme/toast";
import {
  CA_Mastr_Models_v1_0_Models_InterestRateModelType,
  CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType,
} from "@/utils/openapi";
import MarketDataValueDisplayWrapper from "../shared/MarketDataValueDisplayWrapper";

const displaySwaptionOptions = Object.values(CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType).map((val) => ({
  id: val,
  value: val,
  displayValue: val.replace(/_/g, " "),
}));

export const VolatilityCubeInputs = () => {
  const {
    state: { volatilityCubePageSettings },
    action: { updatevolatilityCubePageSettings },
  } = useVolatilityCubePage();
  const modelTypeOptions = useModelTypeVolatilityOptions({ withMarket: false });

  const handleSelectChange = (
    value: string | string[] | MultiSelectOptions | MultiSelectOptions[],
    index: "maturities" | "swapTerms" | "strikes",
    options: any[]
  ) => {
    const val = Array.isArray(value) ? value : [value];

    const allOptions = options.filter((el) => el.value !== "ALL").map((el) => el.value);

    updatevolatilityCubePageSettings({
      [index]: val.includes("ALL") ? allOptions : val,
    });
  };

  const modelTypes =
    volatilityCubePageSettings.modelTypes.length === 1
      ? [volatilityCubePageSettings.modelTypes[0], "Market"]
      : volatilityCubePageSettings.modelTypes;

  const displayOptions = modelTypes.flatMap((model, i) => {
    const modelLabel = modelTypeOptions.find((option) => option.value === model)?.displayValue ?? "Market";
    return [
      { displayValue: `Value (${modelLabel})`, value: `value_${i}` },
      { displayValue: `Daily Change (${modelLabel})`, value: `daily_change_${i}` },
    ];
  });

  return (
    <Flex px="5" pb="2" pt="2">
      <CACard allowCollapse title="Inputs" overflow={"unset"}>
        <VStack alignItems="flex-start">
          <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
            <Flex maxW="lg" alignItems="center" gridGap="2">
              <MarketDataValueDisplayWrapper
                name="As of date"
                link
                _key="curve_date"
                dateFormatter={getFormattedLocaleDate}
                showChanges={false}
              />
            </Flex>
            <Flex alignItems="center" gap="2">
              <Text variant="primary">Models</Text>
              <CAMultiSelectDropdown
                name="model_type_options"
                width="190px"
                value={volatilityCubePageSettings.modelTypes}
                options={modelTypeOptions}
                isMultiSelect
                onChange={(val) => {
                  if ((val as any).length > 2) {
                    showWarningToast("Model Types", "You can only select up to 2 models");
                    return;
                  }
                  updatevolatilityCubePageSettings({
                    modelTypes: val as unknown as CA_Mastr_Models_v1_0_Models_InterestRateModelType[],
                  });
                }}
                persistSelectionOrder
              />
            </Flex>
            <Flex alignItems="center" gap="2">
              <Text variant="primary">Maturities</Text>
              <CAMultiSelectDropdown
                name="maturities"
                width="190px"
                value={volatilityCubePageSettings.maturities}
                options={MATURITY_OPTIONS as any}
                isMultiSelect
                onChange={(e) => handleSelectChange(e, "maturities", MATURITY_OPTIONS as any)}
              />
            </Flex>
            <Flex alignItems="center" gap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Swap Terms
              </Text>
              <CAMultiSelectDropdown
                name="swapTerms"
                width="190px"
                value={volatilityCubePageSettings.swapTerms}
                options={SWAP_TERM_OPTIONS as any}
                isMultiSelect
                onChange={(e) => handleSelectChange(e, "swapTerms", SWAP_TERM_OPTIONS as any)}
              />
            </Flex>

            <Flex alignItems="center" gap="2">
              <Text variant="primary">Strikes</Text>
              <CAMultiSelectDropdown
                name="strikes"
                width="190px"
                value={volatilityCubePageSettings.strikes}
                options={STRIKE_SHIFTS_OPTIONS as any}
                isMultiSelect
                onChange={(e) => handleSelectChange(e, "strikes", STRIKE_SHIFTS_OPTIONS as any)}
              />
            </Flex>
          </HStack>
          <HStack spacing={4} flexWrap="wrap" alignItems="center" p="1">
            <Flex alignItems="center" gridGap="2">
              <CASelectDropdown
                width="190px"
                name="swaption"
                value={volatilityCubePageSettings.swaption}
                options={displaySwaptionOptions}
                onChange={(e) =>
                  updatevolatilityCubePageSettings({
                    swaption: e.target.value as CA_Mastr_Models_v1_0_Models_VolCalibrationSeriesType,
                  })
                }
              />
            </Flex>
            <Flex alignItems="center" gap="2">
              <Text variant="primary" whiteSpace="nowrap">
                Display
              </Text>
              <CASelectDropdown
                name="display"
                width="350px"
                value={volatilityCubePageSettings.display}
                options={
                  [
                    ...displayOptions,
                    {
                      displayValue: `Difference (${
                        modelTypeOptions.find((option) => option.value === volatilityCubePageSettings.modelTypes[0])
                          ?.displayValue
                      } - ${
                        modelTypeOptions.find((option) => option.value === volatilityCubePageSettings.modelTypes[1])
                          ?.displayValue ?? "Market"
                      })`,
                      value: "difference",
                    },
                  ] as any
                }
                onChange={(e) => {
                  updatevolatilityCubePageSettings({ display: e.target.value as any });
                }}
              />
            </Flex>
          </HStack>
        </VStack>
      </CACard>
    </Flex>
  );
};
