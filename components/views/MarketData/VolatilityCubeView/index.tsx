import * as React from "react";
import { Box } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import MarketDataHeader from "../MarketDataHeader";
import { MarketDataModuleProps } from "..";
import { VolatilityCubeInputs } from "./VolatilityCubeInputs";
import { VolatilityCubeContent } from "./VolatilityCubeContent";

const VolatilityCubeView: React.FC<MarketDataModuleProps> = ({ pageTitle }: MarketDataModuleProps) => {
  return (
    <Box>
      <MarketDataHeader title={pageTitle} withRun={false} />
      <VolatilityCubeInputs />
      <MainInputContentTemplate>
        <VolatilityCubeContent />
      </MainInputContentTemplate>
    </Box>
  );
};

export default VolatilityCubeView;
