/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Box, ButtonGroup, Flex, HStack, IconButton, Text, Tooltip } from "@chakra-ui/react";
import { ColDef } from "ag-grid-community";
import { IoBarChartOutline, IoListOutline } from "react-icons/io5";
import CACard from "@/design-system/molecules/CACard";
import { GridView, GridViewValuesType } from "@/components/helpers/GridChartToggle";
import CAGrid from "@/design-system/molecules/CAGrid";
import { useGetVolCubeDetailsSWR } from "@/utils/swr-hooks/VolCalibration";
import { useVolatilityCubePage } from "@/contexts/PageContexts/MarketDataVolatilityCubePageContext";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { X_AXIS_OPTIONS, useModelTypeVolatilityOptions } from "@/constants/market-data";
import {
  CA_Mastr_Models_v1_0_Models_InterestRateModelType,
  CA_Mastr_Models_v1_0_Models_VolCalibrationCategory,
  CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity,
  CA_Mastr_Models_v1_0_Models_VolCalibrationStrike,
  CA_Mastr_Models_v1_0_Models_VolCalibrationTerm,
} from "@/utils/openapi";
import { volatilityCubeColumnData } from "@/utils/grid/VolatilityCubeColumnData";
import { useGetCurveDateSWR } from "@/utils/swr-hooks";
import CASelectDropdown from "@/design-system/molecules/CASelectDropdown";
import { autoGroupColumnDef as defaultAutoGroupColumnDef } from "@/design-system/molecules/CAGrid/helpers";

const defaultColDef: ColDef = {
  floatingFilter: false,
  suppressHeaderMenuButton: true,
  suppressHeaderContextMenu: true,
  suppressMovable: true,
  lockPinned: true,
  sortable: false,
  enablePivot: false,
  enableRowGroup: false,
  enableValue: true,
  suppressHeaderFilterButton: true,
  suppressFloatingFilterButton: true,
  maxWidth: 130,
  width: 88,
};

const autoGroupColumnDef: ColDef = {
  ...defaultAutoGroupColumnDef,
  width: 130,
};

export const VolatilityCubeContent = () => {
  const {
    state: { volatilityCubePageSettings },
    action: { updatevolatilityCubePageSettings },
  } = useVolatilityCubePage();
  const {
    state: { userSettings },
  } = useMarketDataModule();
  const modelTypeOptions = useModelTypeVolatilityOptions({ withMarket: false });

  //Get last business day
  const { data: lastBusinessDay } = useGetCurveDateSWR(
    userSettings.curve_date ? parseDateToYYYYMMDD(new Date(userSettings.curve_date)) : undefined,
    -1
  );

  //Get data for LAST BUSINESS Day
  const { data: dataForLastBusinessDay, isLoading: isLoadingLastBusinessDay } = useGetVolCubeDetailsSWR({
    as_of_date: lastBusinessDay?.curve_date ? parseDateToYYYYMMDD(new Date(lastBusinessDay?.curve_date)) : "",
    interest_rate_models: volatilityCubePageSettings.modelTypes as CA_Mastr_Models_v1_0_Models_InterestRateModelType[],
    maturities: volatilityCubePageSettings.maturities as CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity[],
    strike_shifts: volatilityCubePageSettings.strikes as CA_Mastr_Models_v1_0_Models_VolCalibrationStrike[],
    terms: volatilityCubePageSettings.swapTerms as CA_Mastr_Models_v1_0_Models_VolCalibrationTerm[],
    vol_calibration_series_types: [volatilityCubePageSettings.swaption],
    categories: volatilityCubePageSettings.swaption?.startsWith("cap_")
      ? [CA_Mastr_Models_v1_0_Models_VolCalibrationCategory.CAP]
      : [CA_Mastr_Models_v1_0_Models_VolCalibrationCategory.SWAPTION],
  });

  const { data, isLoading } = useGetVolCubeDetailsSWR({
    as_of_date: parseDateToYYYYMMDD(userSettings.curve_date),
    interest_rate_models: volatilityCubePageSettings.modelTypes as CA_Mastr_Models_v1_0_Models_InterestRateModelType[],
    maturities: volatilityCubePageSettings.maturities as CA_Mastr_Models_v1_0_Models_VolCalibrationMaturity[],
    strike_shifts: volatilityCubePageSettings.strikes as CA_Mastr_Models_v1_0_Models_VolCalibrationStrike[],
    terms: volatilityCubePageSettings.swapTerms as CA_Mastr_Models_v1_0_Models_VolCalibrationTerm[],
    vol_calibration_series_types: [volatilityCubePageSettings.swaption],
    categories: [CA_Mastr_Models_v1_0_Models_VolCalibrationCategory.SWAPTION],
  });

  const [gridChartToggle, setGridChartToggle] = React.useState<GridViewValuesType>(GridView.GRID);

  const modelTypes = React.useMemo(() => {
    return volatilityCubePageSettings.modelTypes.length === 1
      ? [volatilityCubePageSettings.modelTypes[0], "Market"]
      : volatilityCubePageSettings.modelTypes;
  }, [volatilityCubePageSettings.modelTypes]);

  const unPivotedData = React.useMemo(() => {
    if (isLoading || isLoadingLastBusinessDay) {
      return undefined;
    }

    // Get all unique maturity, term, strike combinations from data?.vol_calibration_details
    const uniqueCombinationsOfMaturityTermStrike =
      data?.vol_calibration_details?.reduce((acc, { maturity, term, strike }) => {
        const key = `${maturity}-${term}-${strike}`;
        if (!acc[key] && maturity && term && strike) {
          acc[key] = { maturity, term, strike };
        }
        return acc;
      }, {} as { [key: string]: { maturity: string; term: string; strike: string } }) ?? {};

    const unPivotedData = Object.values(uniqueCombinationsOfMaturityTermStrike).map(({ maturity, term, strike }) => {
      const matchingItemForModel1 = data?.vol_calibration_details?.find(
        (el) =>
          el.type_name === "Model" &&
          el.interest_rate_model === volatilityCubePageSettings.modelTypes[0] &&
          el.maturity === maturity &&
          el.term === term &&
          el.strike === strike
      );
      const matchingItemForModel1FromLastBusinessDay = dataForLastBusinessDay?.vol_calibration_details?.find(
        (el) =>
          el.type_name === "Model" &&
          el.interest_rate_model === volatilityCubePageSettings.modelTypes[0] &&
          el.maturity === maturity &&
          el.term === term &&
          el.strike === strike
      );

      const matchingItemForModel2OrMarket = volatilityCubePageSettings.modelTypes[1]
        ? data?.vol_calibration_details?.find(
            (el) =>
              el.type_name === "Model" &&
              el.interest_rate_model === volatilityCubePageSettings.modelTypes[1] &&
              el.maturity === maturity &&
              el.term === term &&
              el.strike === strike
          )
        : data?.vol_calibration_details?.find(
            (el) => el.type_name === "Market" && el.maturity === maturity && el.term === term && el.strike === strike
          );
      const matchingItemForModel2OrMarketFromLastBusinessDay = volatilityCubePageSettings.modelTypes[1]
        ? dataForLastBusinessDay?.vol_calibration_details?.find(
            (el) =>
              el.type_name === "Model" &&
              el.interest_rate_model === volatilityCubePageSettings.modelTypes[1] &&
              el.maturity === maturity &&
              el.term === term &&
              el.strike === strike
          )
        : dataForLastBusinessDay?.vol_calibration_details?.find(
            (el) => el.type_name === "Market" && el.maturity === maturity && el.term === term && el.strike === strike
          );

      return {
        maturity,
        term,
        strike,
        [`value_0`]: matchingItemForModel1?.value,
        [`value_0_last`]: matchingItemForModel1FromLastBusinessDay?.value,
        [`value_1`]: matchingItemForModel2OrMarket?.value,
        [`value_1_last`]: matchingItemForModel2OrMarketFromLastBusinessDay?.value,
      };
    });

    return unPivotedData;
  }, [
    data?.vol_calibration_details,
    dataForLastBusinessDay?.vol_calibration_details,
    isLoading,
    isLoadingLastBusinessDay,
    volatilityCubePageSettings.modelTypes,
  ]);

  const columnDefs = React.useMemo(() => {
    const xAxis = volatilityCubePageSettings.xAxis;
    const display = volatilityCubePageSettings.display;

    const columnDefs: ColDef[] = [
      ...volatilityCubeColumnData.map((col) => ({
        ...col,
        rowGroup: xAxis !== col.field,
        enableRowGroup: true,
        hide: xAxis === col.field,
        enablePivot: xAxis === col.field,
        pivot: xAxis === col.field,
      })),
      ...modelTypes.flatMap((model, i) => {
        const modelName =
          model === "Market" ? "Market" : modelTypeOptions.find((option) => option.value === model)?.label;
        return [
          {
            field: `value_${i}`,
            type: "number2",
            headerName: `Value ${modelName}`,
            valueGetter: (params) => {
              return params.data?.[`value_${i}`];
            },
            aggFunc: display === `value_${i}` ? "sum" : null,
          },
          {
            field: `daily_change_${i}`,
            type: "number2",
            headerName: `Daily Change ${modelName}`,
            valueGetter: (params) => {
              return params.data?.[`value_${i}`] - params.data?.[`value_${i}_last`];
            },
            aggFunc: display === `daily_change_${i}` ? "sum" : null,
          },
        ] as ColDef[];
      }),
      {
        field: "difference",
        type: "number2",
        headerName: "Difference",
        valueGetter: (params) => {
          return params.data?.[`value_${0}`] - params.data?.[`value_${1}`];
        },
        aggFunc: display === `difference` ? "sum" : null,
      },
    ];

    return columnDefs;
  }, [modelTypeOptions, modelTypes, volatilityCubePageSettings.display, volatilityCubePageSettings.xAxis]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const groupTotalRow = React.useMemo(() => (<></>) as any, []);
  return (
    <CACard p="0" cardBodyStyle={{ p: 0 }}>
      <Flex w="full" justifyContent="space-between" my="4" pr="4">
        <HStack ml="4">
          <Flex w="200px" alignItems="center" gap="2">
            <Text variant="primary" whiteSpace="nowrap">
              Column
            </Text>
            <CASelectDropdown
              name="x-axis"
              value={volatilityCubePageSettings.xAxis}
              options={X_AXIS_OPTIONS as any}
              onChange={(e) => {
                updatevolatilityCubePageSettings({ xAxis: e.target.value as any });
              }}
            />
          </Flex>

          <Flex w="200px" alignItems="center" gap="2">
            <Text variant="primary" whiteSpace="nowrap">
              Row
            </Text>
            <Text whiteSpace="nowrap">
              {((xAxis: typeof volatilityCubePageSettings.xAxis) => {
                switch (xAxis) {
                  case "maturity":
                    return "Swap Term x Strike Shift";
                  case "term":
                    return "Maturity x Strike Shift";
                  case "strike":
                    return "Maturity x Swap Term";
                  default:
                    return "N/A";
                }
              })(volatilityCubePageSettings.xAxis)}
            </Text>
          </Flex>
        </HStack>

        <ButtonGroup size="xs" isAttached variant="outline">
          <IconButton
            icon={<IoListOutline />}
            onClick={() => setGridChartToggle?.(GridView.GRID)}
            size="sm"
            fontSize="xl"
            mr="-1px"
            variant={gridChartToggle === "grid" ? "primary" : "secondary"}
            title="Grid"
            aria-label="Grid"
          />
          <Tooltip label="Coming soon..." placement="bottom">
            <IconButton
              icon={<IoBarChartOutline />}
              size="sm"
              fontSize="xl"
              variant={gridChartToggle === "chart" ? "primary" : "secondary"}
              title="Coming soon..."
              aria-label="Chart"
              disabled
              opacity={0.5}
            />
          </Tooltip>
        </ButtonGroup>
      </Flex>
      <Box h="calc(100vh - 415px)">
        <CAGrid
          gridProps={{
            enableCharts: false,
            defaultColDef,
            rowData: unPivotedData,
            columnDefs: columnDefs,
            loading: isLoading || isLoadingLastBusinessDay,
            groupDefaultExpanded: 1,
            groupTotalRow: groupTotalRow,
            pivotMode: true,
            pivotDefaultExpanded: 1,
            removePivotHeaderRowWhenSingleValueColumn: true,
            rowGroupPanelShow: "never",
            sideBar: false,
            autoGroupColumnDef,
            statusBar: {
              statusPanels: [],
            },
          }}
          enableUserGridViews={false}
          hideSearch
        />
      </Box>
    </CACard>
  );
};
