import * as React from "react";
import { useHotkeys } from "react-hotkeys-hook";
import { Box, Flex, HStack, useBreakpointValue, useColorModeValue } from "@chakra-ui/react";
import CAHeading from "@/design-system/atoms/CAHeading";
import CARun from "@/design-system/molecules/CARun";
import S from "@/constants/strings";
import { blurActiveElement } from "@/utils/helpers";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { RunInfo, RunTag, RunTagButtonGroup } from "@/design-system/molecules/RunTag/RunTag";
import { runToastManager } from "@/utils/run-toast-manager";
import RunAlertButton from "@/components/helpers/RunAlertButton";

type MarketDataHeader = {
  title: string;
  onRunClick?: (opts: { no_cache: boolean }) => void;
  stopWatch?: React.JSX.Element;
  progressIndicator?: React.JSX.Element;
  stopRunning?: React.JSX.Element;
  runInfo?: RunInfo;
  withRun?: boolean;
  runActionComponent?: React.JSX.Element | null;
  hasColorSupport?: boolean;
  rightSideContent?: React.JSX.Element;
};

const MarketDataHeader: React.FC<MarketDataHeader> = ({
  title,
  onRunClick,
  stopWatch,
  progressIndicator,
  stopRunning,
  runInfo,
  withRun = true,
  runActionComponent,
  rightSideContent,
}: MarketDataHeader) => {
  const {
    state: { isTimerRunning, userSettings },
  } = useMarketDataModule();
  const bg = useColorModeValue("celloBlue.75", "celloBlue.1200");
  const isMobile = useBreakpointValue({ sm: true, md: false });

  const onRun = React.useCallback(() => {
    runToastManager.removeToasts();
    blurActiveElement();
    onRunClick?.({ no_cache: !userSettings.useCache });
  }, [onRunClick, userSettings.useCache]);

  useHotkeys("Ctrl+Enter, Command+Enter", onRun, {
    enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
  });

  useHotkeys(
    "Ctrl+Shift+Enter, Command+Shift+Enter",
    () => {
      blurActiveElement();
      runToastManager.removeToasts();
      onRunClick?.({ no_cache: true });
    },
    {
      enableOnTags: ["INPUT", "SELECT", "TEXTAREA"],
    }
  );

  const isProgressIndicatorVisible = isTimerRunning && progressIndicator;

  return (
    <Flex
      px={{ base: "4", sm: "5", md: "7" }}
      py={2}
      mb={-2}
      alignItems={{ base: "flex-start", md: "center" }}
      justifyContent="space-between"
      minH="1.75rem"
      bg={bg}
      position="sticky"
      top="0"
      zIndex="2"
      flexWrap="wrap"
      gap={2}
    >
      <Box>
        <CAHeading as="h1" lineHeight="2rem">
          {title}
        </CAHeading>
      </Box>

      <Flex
        my="auto"
        alignItems={{ base: "flex-end", md: "center" }}
        justifyContent="flex-end"
        flexDirection={{ base: "column-reverse", md: "row" }}
        gap={runInfo?.id ? 2 : { base: 0, md: 4 }}
      >
        {!isMobile && (
          <HStack display={isProgressIndicatorVisible ? "inline" : "none"} w="full">
            {progressIndicator}
          </HStack>
        )}
        {rightSideContent}
        <Box display={isProgressIndicatorVisible ? "none" : "block"}>
          <RunTagButtonGroup>
            <RunAlertButton />
            {(runInfo?.id || runActionComponent) && <RunTag runInfo={runInfo} />}
            {runActionComponent}
          </RunTagButtonGroup>
        </Box>
        {withRun && (
          <Flex alignItems="center" gap={4}>
            {stopWatch}
            {isTimerRunning && stopRunning ? (
              stopRunning
            ) : (
              <CARun onClick={onRun} disabled={isTimerRunning} title={S.MODULES.MARKET_DATA.RUN} />
            )}
          </Flex>
        )}
      </Flex>
      {isMobile && (
        <HStack display={isProgressIndicatorVisible ? "inline" : "none"} w="full">
          {progressIndicator}
        </HStack>
      )}
    </Flex>
  );
};

export default MarketDataHeader;
