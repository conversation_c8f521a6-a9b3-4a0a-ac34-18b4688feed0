import * as React from "react";
import { Box, chakra } from "@chakra-ui/react";
import MainInputContentTemplate from "@/design-system/templates/MainInputContentTemplate";
import MarketDataHeader from "../MarketDataHeader";
import { MarketDataModuleProps } from "..";
import HedgeRatioInputs from "./HedgeRatioInputs";
import { HedgeRatioContent } from "./HedgeRatioContent";

const MarketDataHedgeRatioView: React.FC<MarketDataModuleProps> = ({ pageTitle }: MarketDataModuleProps) => {
  return (
    <Box>
      <chakra.form>
        <MarketDataHeader title={pageTitle} withRun={false} />
        <HedgeRatioInputs />
      </chakra.form>

      <MainInputContentTemplate>
        <HedgeRatioContent />
      </MainInputContentTemplate>
    </Box>
  );
};

export default MarketDataHedgeRatioView;
