import React from "react";
import { parseDateToYYYYMMDD } from "@/utils/helpers";
import { useGetTBARiskDetailsSWR } from "@/utils/swr-hooks";
import CAGrid from "@/design-system/molecules/CAGrid";
import { hedgeRatioPricingColumnData } from "@/utils/grid/HedgeRatioColumnData";
import CACard from "@/design-system/molecules/CACard";
import { CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail } from "@/utils/openapi";
import { useMarketDataModule } from "@/contexts/ModuleContexts/MarketDataModuleContext";
import { useMarketDataHedgeRatioPage } from "@/contexts/PageContexts/MarketDataHedgeRatio";

export const HedgeRatioContent = () => {
  const {
    state: { userSettings },
  } = useMarketDataModule();
  const {
    state: { hedgeRatioPageSettings },
  } = useMarketDataHedgeRatioPage();

  const { data, isLoading } = useGetTBARiskDetailsSWR(
    userSettings.curve_date ? parseDateToYYYYMMDD(new Date(userSettings.curve_date)) : undefined,
    hedgeRatioPageSettings.riskModelVersion,
    hedgeRatioPageSettings.agency,
    hedgeRatioPageSettings.monthType
  );

  return (
    <CACard allowCollapse cardKey="hedge-ratio-table" title="Hedge Ratio" cardBodyStyle={{ p: 0 }}>
      <CAGrid<CA_Mastr_Api_v1_0_Models_RiskData_TBARiskDetail>
        className="MarketDataTable"
        hideHeader
        hasRun={!isLoading}
        gridProps={{
          columnDefs: hedgeRatioPricingColumnData,
          rowData: data?.tba_risk_details,
          domLayout: "autoHeight",
          sideBar: false,
          statusBar: undefined,
          loading: isLoading,
        }}
      />
    </CACard>
  );
};
