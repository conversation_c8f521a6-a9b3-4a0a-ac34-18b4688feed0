import React from "react";
import { FirstDataRenderedEvent, GridReadyEvent } from "ag-grid-community";
import { Flex } from "@chakra-ui/react";
import { AgGridReact } from "ag-grid-react";
import CAGrid from "@/design-system/molecules/CAGrid";
import { runActivityStreamColumnData } from "@/utils/grid/ActivityStreamColumnData";
import { StatusBar } from "@/design-system/molecules/StatusBar/StatusBar";
import CAHeading from "@/design-system/atoms/CAHeading";
import { getFilterDataFromRouteQuery } from "@/utils/helpers/activity-stream";
import { sideBar } from "../config";
import {
  TApiActivityStreamData,
  createServerSideDatasource,
  getDetailCellRendererParams,
  onCellClickedHandler,
} from "./helper";
import { ActivityStreamHeader } from "./ActivityStreamHeader";

export const ActivityStreamView = () => {
  const gridRef = React.useRef<AgGridReact>(null);

  const [totalRow, setTotalRow] = React.useState<number | undefined>();
  const [gridHasCollapsed, setGridCollapsed] = React.useState(false);

  const handleTotalRow = React.useCallback((totalRow: number) => {
    setTotalRow(totalRow);
  }, []);

  const handleRefreshGrid = () => {
    //Update cutOffDate to get the latest data
    const datasource = createServerSideDatasource(handleTotalRow, "postSearch1");
    gridRef.current?.api?.setGridOption("serverSideDatasource", datasource);
  };

  const onGridReady = (params: GridReadyEvent) => {
    const datasource = createServerSideDatasource(handleTotalRow, "postSearch1");
    // register the datasource with the grid
    params.api.setGridOption("serverSideDatasource", datasource);
  };

  const handleFirstDataRendered = React.useCallback((event: FirstDataRenderedEvent<TApiActivityStreamData>) => {
    const defaultFilters = getFilterDataFromRouteQuery();
    if (defaultFilters) {
      event.api.setFilterModel(defaultFilters);
      event.api.onFilterChanged();
    }
  }, []);

  return (
    <Flex
      flexDirection="column"
      px={{ base: "4", sm: "5", md: "7" }}
      justifyContent="space-between"
      minH="1.75rem"
      w="full"
      mt={2}
    >
      <CAHeading lineHeight="2.5rem" verticalAlign="center" as="h1" mb={2}>
        Activity Stream
      </CAHeading>
      <CAGrid
        className="ActivityStream"
        ref={gridRef}
        hideSearch
        gridType="run-activity-stream"
        headerActionButtons={<ActivityStreamHeader onRefreshClick={handleRefreshGrid} />}
        contextMenuHandlers={{
          showCollapseAll: false,
          showExpandAll: false,
        }}
        gridProps={{
          columnDefs: runActivityStreamColumnData,
          sideBar,
          onCellClicked: onCellClickedHandler,
          onFirstDataRendered: handleFirstDataRendered,
          onGridReady: onGridReady,
          detailCellRendererParams: getDetailCellRendererParams,
          cacheBlockSize: 100,
          rowModelType: "serverSide",
          blockLoadDebounceMillis: 500,
          groupTotalRow: undefined,
          masterDetail: true,
          suppressGroupRowsSticky: true,
          detailRowAutoHeight: true,
          suppressAutoSize: true,
          statusBar: undefined,
          enableCharts: false,
        }}
        stackStyles={{
          h: "calc(100vh - 15rem)",
        }}
        cardProps={{
          onCollapseExpandCard: (value) => {
            setGridCollapsed(!value);
          },
          title: " ",
          cardKey: "run-activity-stream-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          allowCollapse: true,
          borderBottomRadius: !gridHasCollapsed ? 0 : undefined,
        }}
      />
      {totalRow !== undefined && !gridHasCollapsed && <StatusBar totalRow={totalRow} />}
    </Flex>
  );
};
