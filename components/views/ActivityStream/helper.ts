import { CellClickedEvent, GetDetailRowDataParams, IServerSideGetRowsParams } from "ag-grid-community";
import Router from "next/router";
import { APP_ROUTES_V2, VALID_APPLICATIONS, pagesWithoutSecurity } from "@/constants/enums";
import { formatParamsForServerSide } from "@/design-system/molecules/CAGrid/helpers";
import { showErrorToast, showWarningToast } from "@/design-system/theme/toast";
import { getAPIErrorMessage } from "@/utils/helpers";
import { defaultColDef } from "@/design-system/molecules/CAGrid/constants";
import {
  ActivityService,
  CA_Mastr_Api_v1_0_Models_Activity_Activity,
  CA_Mastr_Models_v1_0_Models_Application,
  CA_Mastr_Models_v1_0_Models_Page,
} from "@/utils/openapi";
import { safeSessionStorage } from "@/utils/local-storage";
import { SESSION_STORAGE_KEY } from "@/constants/storage-keys";
import {
  PAGES_NOT_SUPPORTING_RUN_ID,
  apiActivityStreamColumnDataNoFilters,
} from "@/utils/grid/ActivityStreamColumnData";

export type TApiActivityStreamData = CA_Mastr_Api_v1_0_Models_Activity_Activity & { user: string };

export function createServerSideDatasource(setTotalRow: (totalRow: number) => void, key: keyof typeof ActivityService) {
  const cutoffDate = new Date().toISOString();
  return {
    getRows: async function (params: IServerSideGetRowsParams<TApiActivityStreamData>) {
      const _params = {
        ...formatParamsForServerSide(params),
        cutoff_date: cutoffDate,
      };
      const activityKey = key === "postSearch1" ? "run_activities" : "api_activities";

      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const response = await (ActivityService as any)[key](_params);
        params.api.hideOverlay();

        const gridData: TApiActivityStreamData[] = response[activityKey]?.map(
          (activity: CA_Mastr_Api_v1_0_Models_Activity_Activity) => {
            return {
              ...activity,
              user: `${activity.user?.firstName} ${activity.user?.lastName}`,
            };
          }
        );
        setTotalRow(response.total_rows || 0);

        if (response.total_rows === 0) {
          params.api.showNoRowsOverlay();
        }

        const hasMoreRows = (response.total_rows || 0) > (params.request.endRow || 100);

        params.success({
          rowData: gridData ?? [],
          rowCount: !gridData?.length ? 0 : !hasMoreRows ? response.total_rows || 0 : undefined,
        });

        if (key === "postSearch1" && Router.query.run_id && gridData.length === 1) {
          params?.api.getDisplayedRowAtIndex(0)?.setExpanded(true);
        }
      } catch (error) {
        params.fail();
        showErrorToast("Error", getAPIErrorMessage(error) || "");
      }
    },
  };
}

export const onCellClickedHandler = (event: CellClickedEvent<TApiActivityStreamData>) => {
  const columnName = event.column.getColId();

  if (columnName === "run_id" && !event.data?.[columnName]) {
    console.error("No run_id found!");
    return;
  }

  if (columnName === "input_security" && !event.rowPinned) {
    const bondName = event.data?.[columnName] || ""; // Set default value to empty string if undefined
    if (!bondName) {
      return;
    }
    safeSessionStorage.removeItem(SESSION_STORAGE_KEY.DASHBOARD_LAST_RUN_ID);
    window.open(`/pricer/pricing/dashboard?bond_name=${encodeURIComponent(bondName)}`, "_blank");
  }

  if (!event.data?.page || PAGES_NOT_SUPPORTING_RUN_ID.includes(event.data?.page)) {
    return;
  }

  if (columnName === "run_id") {
    if (!event.data) return;
    const { page, security, input_security, run_id, app } = event.data;
    const runIdParam = run_id && `run_id=${encodeURIComponent(run_id)}`;

    // if (page && page === CA_Mastr_Models_v1_0_Models_Page.SLICER) {
    //   const userSlicerId =
    //     event.data?.user_slicer_id && `user_slicer_id=${encodeURIComponent(event.data?.user_slicer_id)}`;
    //   const url = `${APP_ROUTES?.[page as CA_Mastr_Models_v1_0_Models_Page]}?${userSlicerId}&${runIdParam}`;

    //   window.open(url, "_blank");
    //   return;
    // }

    const route = APP_ROUTES_V2?.[app as unknown as VALID_APPLICATIONS]?.[page as CA_Mastr_Models_v1_0_Models_Page];

    if (
      page === CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING &&
      app === CA_Mastr_Models_v1_0_Models_Application.PRICER
    ) {
      const url = `${route}?bond_name=${encodeURIComponent(input_security ?? security ?? "")}&${runIdParam}`;
      window.open(url, "_blank");

      return;
    }

    if (
      (page && pagesWithoutSecurity.includes(page as CA_Mastr_Models_v1_0_Models_Page)) ||
      (app === CA_Mastr_Models_v1_0_Models_Application.EMPIRICAL_TRACKING &&
        page === CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING)
    ) {
      const url = `${route}?${runIdParam}`;
      window.open(url, "_blank");
      return;
    }

    if (!security && !input_security) {
      showWarningToast("Missing Security", "Can't perform action due to missing security parameter.");
      return;
    }

    const url = `${route}?bond_name=${encodeURIComponent(input_security ?? security ?? "")}&${runIdParam}`;
    window.open(url, "_blank");
  }
};

export const getDetailCellRendererParams = () => {
  return {
    detailGridOptions: {
      columnDefs: apiActivityStreamColumnDataNoFilters,
      defaultColDef: defaultColDef,
      className: "ag-master-detail",
      onCellClicked: onCellClickedHandler,
    },
    getDetailRowData: async function (params: GetDetailRowDataParams<TApiActivityStreamData>) {
      if (!params.data?.run_id) return;
      try {
        const response = await ActivityService.getApiActivity(params.data.run_id);
        params.successCallback(response?.api_activities ?? []);
      } catch (error) {
        params.successCallback([]);
        showErrorToast("Error", getAPIErrorMessage(error));
      }
    },
  };
};
