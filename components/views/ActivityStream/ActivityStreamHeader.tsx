import { Button, ButtonGroup, Flex, Icon, Text } from "@chakra-ui/react";
import { useRouter } from "next/router";
import React from "react";
import { HiOutlineRefresh } from "react-icons/hi";

interface Props {
  onRefreshClick: () => void;
}
export const ActivityStreamHeader = ({ onRefreshClick }: Props) => {
  const router = useRouter();
  const page = router.pathname.split("/")[2];
  return (
    <Flex alignItems="center" gridGap="2">
      <ButtonGroup size="sm" isAttached variant="outline" ml={{ base: 0, sm: "auto" }}>
        <Flex flexWrap={{ base: "wrap", lg: "nowrap" }} alignItems="center">
          <Button
            variant={page === "run" ? "primary" : "secondary"}
            mr="-1px"
            borderRightRadius="0"
            onClick={() => router.push("/activity-stream/run" + window.location.search)}
          >
            RUN
          </Button>
          <Button
            variant={page === "api" ? "primary" : "secondary"}
            mr="-1px"
            borderLeftRadius="0"
            onClick={() => router.push("/activity-stream/api" + window.location.search)}
          >
            API
          </Button>
        </Flex>
      </ButtonGroup>
      <Button
        height={8}
        leftIcon={<Icon as={HiOutlineRefresh} fontSize="14" />}
        variant="iconButton"
        onClick={onRefreshClick}
      >
        <Text color="inherit">Refresh</Text>
      </Button>
    </Flex>
  );
};
