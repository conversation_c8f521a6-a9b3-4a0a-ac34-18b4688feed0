import React from "react";
import { IconButton } from "@chakra-ui/react";
import { GroupCellRendererParams } from "ag-grid-community";
import { AiOutlineEdit } from "react-icons/ai";
import CAIcon from "@/design-system/atoms/CAIcon";
import { canDelete, canEdit } from "@/utils/helpers";

export const EditCellRenderer = (props: GroupCellRendererParams) => {
  if ((!canEdit(props.data.role) && !canDelete(props.data.role)) || !props.data.security) return null;
  return (
    <IconButton
      variant="ghost"
      w="full"
      aria-label="edit"
      borderRadius="none"
      size="xs"
      h="31px"
      cursor="pointer"
      icon={<CAIcon as={AiOutlineEdit} variant="secondary" />}
    />
  );
};
