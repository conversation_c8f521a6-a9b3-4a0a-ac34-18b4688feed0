import React from "react";
import { HiOutlineRefresh } from "react-icons/hi";
import type { CellClickedEvent, ColDef, FirstDataRenderedEvent, GridReadyEvent } from "ag-grid-community";
import { Button, Flex, Icon, Text } from "@chakra-ui/react";
import { AgGridReact } from "ag-grid-react";
import { runColorColumnData } from "@/utils/grid/color/RunColorColumnDefs";
import CAGrid from "@/design-system/molecules/CAGrid";
import CAHeading from "@/design-system/atoms/CAHeading";
import EditColorModal from "@/components/helpers/EditColor/EditColorModal";
import { getFilterDataFromRouteQuery } from "@/utils/helpers/activity-stream";
import { StatusBar } from "@/design-system/molecules/StatusBar/StatusBar";
import { defaultColDef } from "@/design-system/molecules/CAGrid/constants";
import { useGetUserModelDialsSWR, useGetUserVectorsSWR } from "@/utils/swr-hooks";
import { canDelete, canEdit } from "@/utils/helpers";
import { sideBar } from "../config";
import { TColorGridData, createServerSideDatasource, onCellClickedHandler } from "./helper";
import { EditCellRenderer } from "./components/EditCellRenderer";
import { TagsCellRenderer } from "./components/TagsCellRenderer";

interface EditColorInfo {
  color_id?: number;
  bond_name?: string;
  fullPrice?: number;
  factor?: number;
}

const colDef: ColDef = {
  ...defaultColDef,
  filterParams: {
    // supress additional filter as it's not supported by color search API and not need for SSR
    maxNumConditions: 1,
  },
  rowGroup: false,
  enableRowGroup: false,
  sortable: false,
};

const components = {
  tagsCellRenderer: TagsCellRenderer,
  editCellRenderer: EditCellRenderer,
};

export const ColorView = () => {
  const { data: userModelDials } = useGetUserModelDialsSWR();
  const { data: userVectors } = useGetUserVectorsSWR();
  const [gridHasCollapsed, setGridCollapsed] = React.useState(false);

  const [editColorInfo, setEditColorInfo] = React.useState<EditColorInfo>();

  const gridRef = React.useRef<AgGridReact>(null);
  const [totalRow, setTotalRow] = React.useState<number | undefined>();

  const handleTotalRow = React.useCallback((totalRow: number) => {
    setTotalRow(totalRow);
  }, []);

  const onRefreshClick = () => {
    //Update cutoff date to receive the latest data
    const datasource = createServerSideDatasource(handleTotalRow);
    gridRef.current?.api?.setGridOption("serverSideDatasource", datasource);
  };

  const onGridReady = React.useCallback(
    (event: GridReadyEvent<TColorGridData>) => {
      const datasource = createServerSideDatasource(handleTotalRow);
      // register the datasource with the grid
      event.api.setGridOption("serverSideDatasource", datasource);
    },
    [handleTotalRow]
  );

  const toggleEditModal = (event: CellClickedEvent<TColorGridData>) => {
    if (!event.data) return;
    const { color_id, security, role, run_id } = event.data;

    if (!canEdit(role) || !canDelete(role) || !color_id || !security) return;

    if (!run_id) {
      setEditColorInfo({
        color_id,
        bond_name: security,
      });
      return;
    }

    const indicativesData = event.data.outputs?.bond_indicatives_response;
    const bondPricingData = event.data?.outputs?.bond_pricing_response;

    const factor = indicativesData?.bond_structure?.tranche_factor ?? undefined;
    let fullPrice: number | undefined = undefined;
    if (
      typeof bondPricingData?.results?.[0]?.price === "number" &&
      typeof bondPricingData?.results?.[0]?.accrued_interest === "number"
    ) {
      fullPrice = bondPricingData.results[0].price + bondPricingData.results[0].accrued_interest;
    }

    setEditColorInfo({
      color_id,
      bond_name: security,
      fullPrice,
      factor,
    });
  };

  const handleModalClose = (isDirty?: boolean) => {
    if (isDirty) {
      gridRef.current?.api?.refreshServerSide();
    }
    setEditColorInfo(undefined);
  };

  const context = React.useMemo(
    () => ({
      modelDials:
        userModelDials?.user_model_dials?.map((dial) => ({
          id: dial.user_model_dial_id,
          name: dial.user_model_dial_name,
        })) ?? [],
      vectors: userVectors?.user_vectors?.map((vector) => ({
        id: vector.user_vector_id,
        name: vector.user_vector_name,
      })),
    }),
    [userModelDials?.user_model_dials, userVectors?.user_vectors]
  );

  const handleCellClick = React.useCallback(
    (event: CellClickedEvent<TColorGridData>) => onCellClickedHandler(event, toggleEditModal),
    []
  );

  const handleFirstDataRendered = React.useCallback((event: FirstDataRenderedEvent<TColorGridData>) => {
    const defaultFilters = getFilterDataFromRouteQuery("/color");
    if (defaultFilters) {
      event.api.setFilterModel(defaultFilters);
      event.api.onFilterChanged();
    }
  }, []);

  return (
    <Flex
      flexDirection="column"
      px={{ base: "4", sm: "5", md: "7" }}
      justifyContent="space-between"
      minH="1.75rem"
      w="full"
      mt={2}
    >
      <CAHeading lineHeight="2.5rem" verticalAlign="center" as="h1" mb={2}>
        Color
      </CAHeading>
      <CAGrid<TColorGridData>
        ref={gridRef}
        hideSearch
        gridType="color"
        className="Color"
        contextMenuHandlers={{
          showCollapseAll: false,
          showExpandAll: false,
        }}
        headerActionButtons={
          <Button
            height={8}
            leftIcon={<Icon as={HiOutlineRefresh} fontSize="14" />}
            variant="iconButton"
            onClick={onRefreshClick}
          >
            <Text color="inherit">Refresh</Text>
          </Button>
        }
        gridProps={{
          columnDefs: runColorColumnData,
          context: context,
          sideBar,
          defaultColDef: colDef,
          components,
          onCellClicked: handleCellClick,
          onGridReady: onGridReady,
          onFirstDataRendered: handleFirstDataRendered,
          cacheBlockSize: 30,
          rowModelType: "serverSide",
          blockLoadDebounceMillis: 500,
          groupTotalRow: undefined,
          suppressColumnVirtualisation: false,
          suppressAutoSize: true,
          animateRows: false,
          statusBar: undefined,
          enableCharts: false,
        }}
        stackStyles={{
          h: "calc(100vh - 15rem)",
        }}
        cardProps={{
          onCollapseExpandCard: (value) => {
            setGridCollapsed(!value);
          },
          title: " ",
          cardKey: "color-grid",
          cardStyleOnOpen: {
            height: "full",
          },
          borderBottomRadius: !gridHasCollapsed ? 0 : undefined,
          allowCollapse: true,
          className: "Color",
        }}
      />
      {totalRow !== undefined && !gridHasCollapsed && <StatusBar totalRow={totalRow} />}

      {Boolean(editColorInfo?.color_id) && (
        <EditColorModal
          bondName={editColorInfo?.bond_name}
          colorId={editColorInfo?.color_id}
          showEditColorModal={Boolean(editColorInfo?.color_id)}
          onModalClose={handleModalClose}
          fullPrice={editColorInfo?.fullPrice}
          factor={editColorInfo?.factor}
        />
      )}
    </Flex>
  );
};
