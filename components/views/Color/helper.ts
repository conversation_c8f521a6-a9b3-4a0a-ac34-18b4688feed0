import { CellClickedEvent, ColDef, IServerSideGetRowsParams } from "ag-grid-community";
import { APP_ROUTES_V2, VALID_APPLICATIONS, pagesWithoutSecurity } from "@/constants/enums";
import {
  CA_Mastr_Api_v1_0_Models_Color_Color,
  CA_Mastr_Models_v1_0_Models_Application,
  CA_Mastr_Models_v1_0_Models_Page,
  ColorService,
} from "@/utils/openapi";
import { showErrorToast, showWarningToast } from "@/design-system/theme/toast";
import { getAPIErrorMessage } from "@/utils/helpers";
import { capitalize, formatParamsForServerSide } from "@/design-system/molecules/CAGrid/helpers";
import { ColumnTypes, getColumnDataProps } from "@/utils/grid/column";

export type TColorGridData = CA_Mastr_Api_v1_0_Models_Color_Color & { user: string };

export function createServerSideDatasource(setTotalRow: (totalRow: number) => void) {
  const cutoffDate = new Date().toISOString();

  return {
    getRows: async function (params: IServerSideGetRowsParams<TColorGridData>) {
      const _params = formatParamsForServerSide(params);

      try {
        const response = await ColorService.postSearch({
          ..._params,
          cutoff_date: cutoffDate,
        });

        params.api.hideOverlay();

        const gridData: TColorGridData[] | undefined = response.run_colors?.map((runColor) => {
          return {
            ...runColor,
            user: runColor.user?.full_name ?? "-",
          };
        });

        setTotalRow(response.total_rows || 0);

        if (response.total_rows === 0) {
          params.api.showNoRowsOverlay();
        }

        const hasMoreRows = (response.total_rows || 0) > (params.request.endRow || 100);

        params.success({
          rowData: gridData ?? [],
          rowCount: !gridData?.length ? 0 : !hasMoreRows ? response.total_rows || 0 : undefined,
        });
      } catch (error) {
        if (!error) return;
        params.fail();
        showErrorToast("Error", getAPIErrorMessage(error) || "");
      }
    },
  };
}

export const constructUrlWithRunIdAndBondName = ({
  page,
  security,
  run_id,
  app,
}: {
  page: CA_Mastr_Models_v1_0_Models_Page;
  security?: string;
  run_id: string;
  app: CA_Mastr_Models_v1_0_Models_Application;
}) => {
  const runIdParam = `run_id=${encodeURIComponent(run_id)}`;

  const modifiedApp = app === "mastr_xl" ? "pricer" : app;
  const route =
    APP_ROUTES_V2?.[modifiedApp as unknown as VALID_APPLICATIONS]?.[page as CA_Mastr_Models_v1_0_Models_Page];

  if (
    (page && pagesWithoutSecurity.includes(page as CA_Mastr_Models_v1_0_Models_Page)) ||
    (app === CA_Mastr_Models_v1_0_Models_Application.EMPIRICAL_TRACKING &&
      page === CA_Mastr_Models_v1_0_Models_Page.EMPIRICAL_TRACKING)
  ) {
    const url = `${route}?${runIdParam}`;
    return url;
  }

  if (!security) {
    showWarningToast("Missing Security", "Can't perform action due to missing security parameter.");
    return;
  }

  const url = `${route}?bond_name=${encodeURIComponent(security ?? "")}&${runIdParam}`;
  return url;
};

export const onCellClickedHandler = (event: CellClickedEvent, cb: (event: CellClickedEvent) => void) => {
  const columnName = event.column.getColId();

  if (columnName === "edit") {
    cb(event);
  }

  if (columnName === "run_id" && !event.data[columnName]) {
    console.error("No run_id found!");
    return;
  }

  if (columnName === "run_id") {
    const {
      page,
      security,
      run_id,
      app,
    }: {
      page: CA_Mastr_Models_v1_0_Models_Page;
      security?: string;
      run_id: string;
      api_start_time?: string;
      app: CA_Mastr_Models_v1_0_Models_Application;
    } = event.data;
    const url = constructUrlWithRunIdAndBondName({ page, security, run_id, app });
    window.open(url, "_blank");
  }
};

export const getColumnDataColorProps = (
  type: ColumnTypes,
  fieldKey: string,
  props: ColDef & {
    hasHeaderTooltip?: boolean | undefined;
    headerTooltipName?: string | undefined;
  }
): ColDef => {
  const filterOptions = type.startsWith("number")
    ? ["equals", "greaterThan", "greaterThanOrEqual", "lessThan", "lessThanOrEqual", "inRange"]
    : type.startsWith("date")
    ? ["equals", "greaterThan", "lessThan", "inRange"]
    : ["contains", "blank", "notBlank"];

  const transformedHeaderName =
    props.field && props.field?.length > 1
      ? props.field?.split("_")?.map(capitalize)?.join(" ")
      : capitalize(props.headerName?.[0] || "");

  const headerName = props.headerName ? props.headerName : transformedHeaderName;

  return getColumnDataProps(type, {
    headerName,
    filterParams: {
      filterOptions,
    },
    floatingFilter: true,
    maxWidth: 100,
    ...props,
    field: `${fieldKey}.${props.field}`,
  });
};
