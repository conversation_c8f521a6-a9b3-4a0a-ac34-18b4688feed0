import * as React from "react";
import Head from "next/head";
import { WithLayoutProps } from "@/types";
import BlankTemplate from "@/design-system/templates/BlankTemplate";

const BlankLayout: React.FC<React.PropsWithChildren<WithLayoutProps>> = ({ children, title }) => (
  <>
    <Head>
      <title>{title}</title>
    </Head>
    <BlankTemplate>{children}</BlankTemplate>
  </>
);

export default BlankLayout;
