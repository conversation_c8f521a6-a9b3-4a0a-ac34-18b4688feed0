import * as React from "react";
import Head from "next/head";
import { HStack } from "@chakra-ui/layout";
import { SpaceProps } from "@chakra-ui/styled-system";
import { useRouter } from "next/router";
import { WithLayoutProps } from "@/types";
import LeftNavigationContent from "@/components/overlay/LeftNavigation";
import MainTemplate from "@/design-system/templates/MainTemplate";
import { useGlobalContext } from "@/contexts/AppGlobalContexts/GlobalContext";
import { getAppDisplayNameFromRoute, getAppNameFromRoute } from "@/utils/helpers";
import { appsWithoutLeftNavigation } from "@/constants/enums";
import UserMenu from "../overlay/UserMenu";
import AppSelectionMenu from "../overlay/AppSelectionMenu";
import NotificationsWrapper from "../views/NotificationWrapper";
import { KnowledgeBase } from "../overlay/KnowledgeBase/KnowledgeBase";

interface MainLayoutProps extends WithLayoutProps, SpaceProps {}

const MainLayoutGlobalComponents: React.FC = () => <></>;

const MainLayout: React.FC<React.PropsWithChildren<MainLayoutProps>> = ({
  children,
  title,
  headerMiddle,
  headerSettings,
  ...props
}) => {
  const {
    action: { toggleNavigationMenu },
  } = useGlobalContext();
  const router = useRouter();
  const appName = getAppNameFromRoute(router.pathname);
  const hasLeftNavigation = appName ? !appsWithoutLeftNavigation.includes(appName) : true;

  return (
    <>
      <Head>
        <title>{title}</title>
      </Head>
      <MainTemplate
        navigationDrawer={hasLeftNavigation ? <LeftNavigationContent /> : null}
        overlayComponents={<MainLayoutGlobalComponents />}
        headerMiddle={headerMiddle}
        headerRight={
          <HStack spacing={0} h="full">
            {headerSettings}
            <AppSelectionMenu />
            <KnowledgeBase />
            <NotificationsWrapper />
            <UserMenu />
          </HStack>
        }
        toggleNavigationMenu={toggleNavigationMenu}
        currentSelectedApp={getAppDisplayNameFromRoute(router.pathname)}
        {...props}
      >
        {children}
      </MainTemplate>
    </>
  );
};

export default React.memo(MainLayout);
