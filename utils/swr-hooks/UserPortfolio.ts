import useS<PERSON>, { Key, SWRConfiguration, mutate } from "swr";
import useSWRMutation from "swr/mutation";
import Router from "next/router";
import { useParams } from "next/navigation";
import { usePricingInputsStore } from "@/components/views/Portfolio/Portfolio/PricingInputs/PricingInputsContext";
import { UserPortfolioService } from "../openapi/services/UserPortfolioService";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio } from "../openapi/models/CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio } from "../openapi/models/CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio";
import {
  CA_Mastr_Api_v1_0_Models_UserPortfolio_DeleteUserPortfolioResponse,
  CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse,
  CA_Mastr_Models_v1_0_Models_Stage,
} from "../openapi";
import { queueAPIErrorMessage } from "../helpers";
import { TIMEOUT_INTERVAL } from "./constants";

// GET all user portfolios
export function useUserPortfolios(options = {} as SWRConfiguration) {
  return useSWR(["getUserPortfolios"], UserPortfolioService.getUserPortfolios, {
    ...options,
    revalidateOnFocus: true,
    revalidateIfStale: true,
    revalidateOnMount: true,
    refreshInterval: (data) => {
      // Check if any portfolio has processing status
      const hasProcessingPortfolios = data?.user_portfolios?.some(
        (portfolio) => portfolio.status === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING
      );

      return hasProcessingPortfolios ? TIMEOUT_INTERVAL : 0;
    },
  });
}

// GET a single user portfolio by id
export function useUserPortfolio(userPortfolioId?: number) {
  return useSWR(
    userPortfolioId ? ["getUserPortfolio", userPortfolioId] : null,
    () => UserPortfolioService.getUserPortfolio(userPortfolioId),
    {
      onError: (error) => {
        if (error.status === 404) {
          queueAPIErrorMessage(error?.body ?? error?.message);
          Router.push("/portfolio");
          return;
        }
      },
    }
  );
}

const addUserPortfolio = async (
  url: string,
  { arg }: Readonly<{ arg: CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio }>
) => {
  return UserPortfolioService.postUserPortfolio(arg);
};

// POST create user portfolio
export function useCreateUserPortfolio() {
  return useSWRMutation<
    CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse,
    Error,
    Key,
    CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio
  >(["createUserPortfolio"], addUserPortfolio, {
    onSuccess: () => mutate(["getUserPortfolios"]),
  });
}

const updateUserPortfolio = async (
  url: string,
  { arg }: Readonly<{ arg: CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio }>
) => {
  return UserPortfolioService.putUserPortfolio(arg);
};

// PUT update user portfolio
export function useUpdateUserPortfolio() {
  return useSWRMutation<
    CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse,
    Error,
    Key,
    CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio
  >(["updateUserPortfolio"], updateUserPortfolio, {
    onSuccess: (res) => {
      mutate(["getUserPortfolios"]);
      mutate(["getUserPortfolio", res.user_portfolio?.user_portfolio_id]);
    },
  });
}

export function usePortfolioActions() {
  const { id } = useParams();
  const { data } = useUserPortfolio(Number(id));

  // mutations
  const { trigger: removePortfolio, isMutating: isDeleting } = useDeleteUserPortfolio();
  const { trigger: updatePortfolio, isMutating: isUpdating } = useUpdateUserPortfolio();
  // store
  const { rowData, setHasChanges } = usePricingInputsStore((s) => ({
    rowData: s.rowData,
    setHasChanges: s.setHasChanges,
  }));

  const userPortfolio = data?.user_portfolio;

  const savePortfolio = async (changeAdHoc = true) => {
    const adHoc = changeAdHoc ? { is_ad_hoc: false } : {};
    await updatePortfolio({
      ...userPortfolio,
      last_updated: userPortfolio?.updated ?? "",
      user_portfolio_input: {
        ...userPortfolio?.user_portfolio_input,
        security_records: rowData.filter((sec) => !!sec.name),
      },
      ...adHoc,
    });
    setHasChanges(false);
  };

  const renamePortfolio = async (newName: string) => {
    await updatePortfolio({
      ...userPortfolio,
      user_portfolio_name: newName,
      last_updated: userPortfolio?.updated ?? "",
      is_ad_hoc: false,
    });
  };

  const deletePortfolio = async (id: number | string) => {
    await removePortfolio({ id: Number(id) });
  };

  const isMutating = isDeleting || isUpdating;

  return { isMutating, savePortfolio, renamePortfolio, deletePortfolio };
}

const deleteUserPortfolio = async (url: string, { arg }: Readonly<{ arg: { id: number | undefined } }>) => {
  if (arg.id === undefined) throw new Error("id is required");
  return UserPortfolioService.deleteUserPortfolio(arg.id);
};

// DELETE user portfolio
export function useDeleteUserPortfolio() {
  return useSWRMutation<
    CA_Mastr_Api_v1_0_Models_UserPortfolio_DeleteUserPortfolioResponse,
    Error,
    Key,
    { id: number | undefined }
  >(["deleteUserPortfolio"], deleteUserPortfolio, {
    onSuccess: () => {
      mutate(["getUserPortfolios"]);
    },
  });
}
