import useSWR, { Key, mutate } from "swr";
import useSWRMutation from "swr/mutation";

import {
  CA_Mastr_Api_v1_0_Models_Portfolio_UserPortfolioRunRequest,
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResponse,
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultParameters,
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse,
  CA_Mastr_Models_v1_0_Models_Stage,
  UserPortfolioRunService,
} from "../openapi";
import { TIMEOUT_INTERVAL } from "./constants";

const runUserPortfolio = async (
  url: string,
  { arg }: Readonly<{ arg: CA_Mastr_Api_v1_0_Models_Portfolio_UserPortfolioRunRequest }>
) => {
  return UserPortfolioRunService.postUserPortfolioRunAsync(arg);
};

export function useRunUserPortfolio() {
  return useSWRMutation<
    CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResponse,
    Error,
    Key,
    CA_Mastr_Api_v1_0_Models_Portfolio_UserPortfolioRunRequest
  >(["createUserPortfolio"], runUserPortfolio, {
    onSuccess: () => mutate(["getUserPortfolios"]),
  });
}

const runUserPortfolioResults = async ({
  arg,
}: Readonly<{ arg: CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultParameters }>) => {
  if (!arg.user_portfolio_id) return;
  return UserPortfolioRunService.postUserPortfolioRunResultsAsync(arg);
};

export function useRunUserPortfolioResults(
  args: CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultParameters,
  userPortfolioRunId: string | null | undefined
) {
  return useSWR<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse | undefined>(
    args.user_portfolio_id ? `user-portfolio-results-${args.user_portfolio_id}-${userPortfolioRunId}` : undefined,
    () =>
      runUserPortfolioResults({
        arg: args,
      }),
    {
      refreshInterval: (data) => {
        if (data?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING) {
          return TIMEOUT_INTERVAL;
        } else {
          return 0;
        }
      },
      onSuccess: (data) => {
        if (data?.status?.stage === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING) {
          mutate(["getUserPortfolios"]);
        }
        // Override to not show warnings and errors as toast
      },
    }
  );
}
