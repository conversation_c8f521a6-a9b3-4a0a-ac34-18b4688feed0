import Router from "next/router";
import { CellClickedEvent, ColDef } from "ag-grid-enterprise";
import { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio } from "@/utils/openapi";
import { PortfolioActionsCellRenderer } from "../../../components/views/Portfolio/Portfolios/PortfolioActionsCellRenderer";
import { StatusCellRenderer } from "./StatusCellRenderer";

const handleCellClick = (row: CellClickedEvent<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio>) => {
  if (!row.data?.user_portfolio_id) return;
  Router.push(`/portfolio/${row.data?.user_portfolio_id}`);
};

export const PortfoliosColumnDefs: ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio>[] = [
  {
    headerName: "",
    cellRenderer: PortfolioActionsCellRenderer,
    filter: false,
    sortable: false,
    width: 60,
    pinned: "left",
    suppressHeaderMenuButton: true,
    resizable: false,
  },
  {
    field: "status",
    headerName: "",
    maxWidth: 60,
    cellRenderer: StatusCellRenderer,
    suppressAutoSize: true,
    suppressHeaderMenuButton: true,
  },
  {
    field: "user_portfolio_name",
    headerName: "Name",
    type: "text",
    cellClass: "ag-grid-link-cell",
    width: 200,
    onCellClicked: handleCellClick,
  },
  {
    field: "user",
    headerName: "User",
    type: "text",
    width: 200,
    valueGetter: (params) => `${params.data?.user?.firstName} ${params.data?.user?.lastName}`,
  },
  {
    field: "tags",
    headerName: "Tags",
    type: "text",
    minWidth: 200,
  },
  {
    field: "inserted",
    headerName: "Date Created",
    type: "dateWithTime",
    width: 150,
  },
  {
    field: "updated",
    headerName: "Date Updated",
    type: "dateWithTime",
    width: 150,
  },
  {
    field: "last_run.start_time",
    headerName: "Run Start Time",
    type: "dateWithTimeInMS",
    width: 180,
  },
  {
    field: "last_run.end_time",
    headerName: "Run End Time",
    type: "dateWithTimeInMS",
    width: 180,
  },
  {
    field: "last_run.duration",
    headerName: "Run Duration (ms)",
    type: "number0",
    width: 115,
  },
  {
    field: "last_run.total_requests",
    headerName: "# Req",
    type: "number",
    width: 115,
  },
  {
    field: "last_run.succeeded_requests",
    headerName: "# Success",
    type: "number",
    width: 115,
  },
  {
    field: "last_run.failed_requests",
    headerName: "# Errors",
    type: "number",
    width: 115,
  },
];
