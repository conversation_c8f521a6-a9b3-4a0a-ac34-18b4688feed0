import { ColDef, ColGroupDef, ValueFormatterFunc } from "ag-grid-enterprise";
import {
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult,
  CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions,
  CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  CA_Mastr_Api_v1_0_Models_Util_StringOption,
  CA_Mastr_Models_v1_0_Models_PricingType,
} from "@/utils/openapi";
import { capitalize } from "@/design-system/molecules/CAGrid/helpers";
import { StatusCellRenderer } from "./StatusCellRenderer";

const getDisplayValueFromArrayMetadata = (
  metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  currentValue: string,
  metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions
) => {
  const options = metadata?.portfolio_settings?.calculation_assumptions?.[
    metadataKey
  ] as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>;

  const matchedOption = options?.find((option) => option.value === currentValue);

  if (matchedOption) {
    return matchedOption.display_value ?? matchedOption.value ?? "";
  }
  if (currentValue) {
    return currentValue.toString();
  }
  return "-";
};

const formatValueFromMetadata =
  (
    metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
    metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions
  ): ValueFormatterFunc<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult> =>
  (params) => {
    return getDisplayValueFromArrayMetadata(metadata, params.value, metadataKey);
  };

const formatPricingType = (value: string) => {
  if (!value) return "-";
  if (value === CA_Mastr_Models_v1_0_Models_PricingType.OAS) {
    return "OAS";
  }
  return capitalize(value);
};

// General Information
const generalColumnDefs: ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>[] = [
  {
    field: "status.stage",
    headerName: "",
    maxWidth: 60,
    cellRenderer: StatusCellRenderer,
    suppressAutoSize: true,
    suppressMovable: true,
    suppressHeaderMenuButton: true,
    pinned: "left",
    lockPosition: "left",
  },
  {
    field: "name",
    headerName: "Security",
    type: "text",
    tooltipField: "name",
    pinned: "left",
    suppressMovable: true,
    lockPosition: "left",
  },
  {
    field: "face_amount",
    headerName: "Face Amount",
    type: "number0",
    pinned: "left",
    suppressMovable: true,
    lockPosition: "left",
  },
  {
    field: "pricing_type",
    headerName: "Type",
    type: "text",
    valueFormatter: (params) => formatPricingType(params.value),
  },
  {
    field: "pricing_level",
    headerName: "Value",
    type: "number",
  },
  {
    field: "prepay_method",
    headerName: "Prepay Model",
    type: "text",
  },
  {
    field: "prepay_percentage",
    headerName: "Multiplier (%)",
    type: "number",
  },
  {
    field: "rate_shift",
    headerName: "Rate Shift",
    type: "number",
  },
  {
    field: "note",
    headerName: "Note",
    type: "text",
    tooltipField: "note",
  },
];

// Calculations
const calculationsColumnDefs: ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>[] = [
  {
    field: "calculations.oas",
    headerName: "OAS",
    type: "boolean",
  },
  {
    field: "calculations.effective_duration",
    headerName: "Effective Duration",
    type: "boolean",
  },
  {
    field: "calculations.key_rate_duration",
    headerName: "Key Rate Duration",
    type: "boolean",
  },
  {
    field: "calculations.prepay_duration",
    headerName: "Prepay Duration",
    type: "boolean",
  },
  {
    field: "calculations.current_coupon_duration",
    headerName: "Current Coupon Duration",
    type: "boolean",
  },
  {
    field: "calculations.volatility_duration",
    headerName: "Volatility Duration",
    type: "boolean",
  },
  {
    field: "calculations.prepay_tracking",
    headerName: "Prepay Tracking",
    type: "boolean",
  },
  {
    field: "calculations.prepay_projections",
    headerName: "Prepay Projections",
    type: "boolean",
  },
  {
    field: "calculations.indicative",
    headerName: "Indicative",
    type: "boolean",
  },
  {
    field: "calculations.cashflow",
    headerName: "Cashflow",
    type: "boolean",
  },
  {
    field: "calculations.pricing",
    headerName: "Pricing",
    type: "boolean",
  },
];

// Calculation Assumptions
const getCalculationAssumptionsColumnDefs = (
  metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse
): ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>[] => [
  {
    field: "calculation_assumptions_override.market_data_source",
    headerName: "Market Data Source",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "market_data_sources"),
  },
  {
    field: "calculation_assumptions_override.curve_date",
    headerName: "Curve Date",
    type: "date",
  },
  {
    field: "calculation_assumptions_override.pricing_date",
    headerName: "Pricing Date",
    type: "date",
  },
  {
    field: "calculation_assumptions_override.settle_date",
    headerName: "Settle Date",
    type: "date",
  },
  {
    field: "calculation_assumptions_override.model_version",
    headerName: "Prepay Model Version",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "version"),
  },
  {
    field: "calculation_assumptions_override.current_coupon_model_name",
    headerName: "Current Coupon",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "current_coupon_model"),
  },
  {
    field: "calculation_assumptions_override.mortgage_model_type",
    headerName: "Primary-Secondary Spread",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "primary_secondary_spread"),
  },
  {
    field: "calculation_assumptions_override.repline_algorithm",
    headerName: "Repline Algo",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "repline_level"),
  },
  {
    field: "calculation_assumptions_override.cello_cdu_date",
    headerName: "Cello CDU Date",
    type: "date",
  },
  {
    field: "calculation_assumptions_override.ignore_cdu_paydate_after",
    headerName: "Ignore Paydate After",
    type: "date",
  },
  {
    field: "calculation_assumptions_override.projection_paths",
    headerName: "Paths",
    type: "number",
  },
  {
    field: "calculation_assumptions_override.interest_rate_model_name",
    headerName: "Type",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "type"),
  },
  {
    field: "calculation_assumptions_override.interest_rate_calibration_method",
    headerName: "Calibration",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "calibration"),
  },
  {
    field: "calculation_assumptions_override.yield_curve_model_name",
    headerName: "Yield Curve Model",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "yield_curve_model"),
  },
  {
    field: "calculation_assumptions_override.key_rate_points",
    headerName: "Key Rates",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "key_rate_points"),
  },
  {
    field: "calculation_assumptions_override.key_rate_shift",
    headerName: "Curve shift (bps)",
    type: "number",
  },
  {
    field: "calculation_assumptions_override.num_dist_workers",
    headerName: "Dashboard OAS",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "vcpu_per_oas"),
  },
];

// Security Info
const securityInfoColumnDefs: ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>[] = [
  {
    field: "security_info.deal_mode",
    headerName: "Deal Mode",
    type: "text",
  },
  {
    field: "security_info.bloomberg_name",
    headerName: "Bloomberg",
    type: "text",
    maxWidth: 150,
    tooltipField: "security_info.bloomberg_name",
  },
  {
    field: "security_info.intex_name",
    headerName: "Intex",
    type: "text",
    maxWidth: 150,
    tooltipField: "security_info.intex_name",
  },
  {
    field: "security_info.yieldbook_name",
    headerName: "Yieldbook",
    type: "text",
    maxWidth: 150,
    tooltipField: "security_info.yieldbook_name",
  },
];

// Security Structure (Bond Indicatives)
const securityStructureColumnDefs: ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>[] = [
  {
    field: "bond_indicatives_response.bond_structure.deal_dealer",
    headerName: "Dealer",
    type: "text",
    maxWidth: 200,
    tooltipField: "bond_indicatives_response.bond_structure.deal_dealer",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_ground_groups",
    headerName: "Group",
    type: "text",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_orig_balance",
    headerName: "Original Balance ($)",
    type: "number0",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_current_balance",
    headerName: "Current Balance ($)",
    type: "number0",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_factor",
    headerName: "Factor",
    type: "number8",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_coupon",
    headerName: "Coupon (%)",
    type: "number2",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_type",
    headerName: "Structure",
    type: "text",
    tooltipField: "bond_indicatives_response.bond_structure.tranche_type",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_pac_range",
    headerName: "PAC Range",
    type: "number3",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_lev",
    headerName: "Leverage",
    type: "number2",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_index",
    headerName: "Index",
    type: "text",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_margin",
    headerName: "Margin (%)",
    type: "number2",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_cap",
    headerName: "Cap (%)",
    type: "number2",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_floor",
    headerName: "Floor (%)",
    type: "number2",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_next_pay_date",
    headerName: "Next Pay",
    type: "date",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_delay",
    headerName: "Delay",
    type: "number0",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_accrual_start_date",
    headerName: "Accrual Start",
    type: "date",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_maturity_date",
    headerName: "Maturity",
    type: "date",
  },
  {
    field: "bond_indicatives_response.bond_structure.deal_settle_date",
    headerName: "Deal Settle",
    type: "date",
  },
  {
    field: "bond_indicatives_response.bond_structure.has_structured_collat",
    headerName: "Reremic",
    type: "bool",
  },
  {
    field: "bond_indicatives_response.bond_structure.tranche_cdu_date",
    headerName: "Intex CDU",
    type: "date",
  },
];

// Pricing Results
const pricingColumnDefs: (ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult> | ColGroupDef)[] = [
  // Basic Pricing
  {
    field: "bond_pricing_response.price",
    headerName: "Price",
    type: "number6",
  },
  {
    field: "bond_pricing_response.tick_price",
    headerName: "Tick Price",
    type: "text",
  },
  {
    field: "bond_pricing_response.accrued_interest",
    headerName: "Accrued Interest",
    type: "number6",
  },
  {
    field: "bond_pricing_response.tick_accrued_interest",
    headerName: "Tick Accrued Interest",
    type: "text",
  },
  {
    field: "bond_pricing_response.full_price",
    headerName: "Full Price",
    type: "number6",
  },
  {
    field: "bond_pricing_response.tick_full_price",
    headerName: "Tick Full Price",
    type: "text",
  },

  // Nominal
  {
    field: "bond_pricing_response.yield",
    headerName: "Yield (%)",
    type: "number3",
  },
  {
    field: "bond_pricing_response.forward_yield",
    headerName: "Forward Yield (%)",
    type: "number3",
  },
  {
    field: "bond_pricing_response.weighted_average_life",
    headerName: "Average Life (years)",
    type: "number2",
  },
  {
    field: "bond_pricing_response.forward_weighted_average_life",
    headerName: "Forward Average Life (years)",
    type: "number2",
  },
  {
    field: "bond_pricing_response.modified_duration",
    headerName: "Modified Duration",
    type: "number3",
  },
  {
    field: "bond_pricing_response.forward_modified_duration",
    headerName: "Forward Modified Duration",
    type: "number3",
  },
  {
    field: "bond_pricing_response.macaulay_duration",
    headerName: "Macaulay Duration",
    type: "number3",
  },
  {
    field: "bond_pricing_response.effective_duration",
    headerName: "Effective Duration",
    type: "number1",
  },
  {
    field: "bond_pricing_response.effective_convexity",
    headerName: "Convexity Duration",
    type: "number1",
  },

  // Payment Dates
  {
    field: "bond_pricing_response.accrual_start_date",
    headerName: "Accrual Start",
    type: "date",
  },
  {
    field: "bond_pricing_response.forward_accrual_start_date",
    headerName: "Forward Accrual Start",
    type: "date",
  },
  {
    field: "bond_pricing_response.next_payment_date",
    headerName: "Next Pay",
    type: "date",
  },
  {
    field: "bond_pricing_response.forward_next_payment_date",
    headerName: "Forward Next Pay",
    type: "date",
  },
  {
    field: "bond_pricing_response.payment_window_begin",
    headerName: "1st Payment",
    type: "date",
  },
  {
    field: "bond_pricing_response.forward_payment_window_begin",
    headerName: "Forward 1st Payment",
    type: "date",
  },
  {
    field: "bond_pricing_response.payment_window_end",
    headerName: "Last Payment",
    type: "date",
  },
  {
    field: "bond_pricing_response.forward_payment_window_end",
    headerName: "Forward Last Payment",
    type: "date",
  },
  {
    field: "bond_pricing_response.principal_window_begin",
    headerName: "1st Principal",
    type: "date",
  },
  {
    field: "bond_pricing_response.forward_principal_window_begin",
    headerName: "Forward 1st Principal",
    type: "date",
  },
  {
    field: "bond_pricing_response.principal_window_end",
    headerName: "Last Principal",
    type: "date",
  },
  {
    field: "bond_pricing_response.forward_principal_window_end",
    headerName: "Forward Last Principal",
    type: "date",
  },

  // GPL
  {
    field: "bond_pricing_response.price_coupon",
    headerName: "Price Coupon",
    type: "number6",
  },
  {
    field: "bond_pricing_response.price_penalty",
    headerName: "Price Penalty",
    type: "number6",
  },

  // OAS
  {
    field: "bond_pricing_response.oas",
    headerName: "OAS (bps)",
    type: "number0",
  },
  {
    field: "bond_pricing_response.zvoas",
    headerName: "ZVOAS (bps)",
    type: "number0",
  },
  {
    field: "bond_pricing_response.opt_cost",
    headerName: "Opt Cost (bps)",
    type: "number0",
  },
  {
    field: "bond_pricing_response.doas/dprepay",
    headerName: "dOAS/dPrepay",
    type: "number3",
  },

  // Additional Fields
  {
    field: "bond_pricing_response.security",
    headerName: "Pricing Security",
    type: "text",
  },
  {
    field: "bond_pricing_response.input_security",
    headerName: "Input Security",
    type: "text",
  },
  {
    field: "bond_pricing_response.dv01",
    headerName: "DV01",
    type: "number4",
  },
  {
    field: "bond_pricing_response.market_value",
    headerName: "Market Value",
    type: "number0",
  },
  {
    field: "bond_pricing_response.settle_date_factor",
    headerName: "Settle Date Factor",
    type: "number8",
  },
  {
    field: "bond_pricing_response.horizon_return",
    headerName: "Horizon Return",
    type: "number3",
  },
  {
    field: "bond_pricing_response.horizon_price",
    headerName: "Horizon Price",
    type: "number6",
  },
  {
    field: "bond_pricing_response.three_month_cpr",
    headerName: "3 Month CPR (%)",
    type: "number1",
  },
  {
    field: "bond_pricing_response.six_month_cpr",
    headerName: "6 Month CPR (%)",
    type: "number1",
  },

  // Duration Group
  {
    headerName: "Duration",
    children: [
      {
        field: "bond_pricing_response.prepay_duration",
        headerName: "Prepay Duration",
        type: "number3",
      },
      {
        field: "bond_pricing_response.current_coupon_duration",
        headerName: "Current Coupon Duration",
        type: "number3",
      },
      {
        field: "bond_pricing_response.spread_duration",
        headerName: "Spread Duration",
        type: "number3",
      },
      {
        field: "bond_pricing_response.volatility_duration",
        headerName: "Volatility Duration",
        type: "number3",
      },
      {
        field: "bond_pricing_response.key_rate1_yr_duration",
        headerName: "Duration 1yr",
        type: "number3",
      },
      {
        field: "bond_pricing_response.key_rate2_yr_duration",
        headerName: "Duration 2yr",
        type: "number3",
      },
      {
        field: "bond_pricing_response.key_rate5_yr_duration",
        headerName: "Duration 5yr",
        type: "number3",
      },
      {
        field: "bond_pricing_response.key_rate10_yr_duration",
        headerName: "Duration 10yr",
        type: "number3",
      },
      {
        field: "bond_pricing_response.key_rate30_yr_duration",
        headerName: "Duration 30yr",
        type: "number3",
      },
    ],
  },

  // DV01 Group
  {
    headerName: "DV01",
    children: [
      {
        field: "bond_pricing_response.effective_dv01",
        headerName: "Effective DV01",
        type: "number4",
      },
      {
        field: "bond_pricing_response.effective_convexity_dv01",
        headerName: "Convexity DV01",
        type: "number4",
      },
      {
        field: "bond_pricing_response.prepay_dv01",
        headerName: "Prepay DV01",
        type: "number4",
      },
      {
        field: "bond_pricing_response.current_coupon_dv01",
        headerName: "Currrent Coupon DV01",
        type: "number4",
      },
      {
        field: "bond_pricing_response.spread_dv01",
        headerName: "Spread DV01",
        type: "number4",
      },
      {
        field: "bond_pricing_response.volatility_dv01",
        headerName: "Volatility DV01",
        type: "number4",
      },
      {
        field: "bond_pricing_response.key_rate1_yr_dv01",
        headerName: "DV01 1yr",
        type: "number4",
      },
      {
        field: "bond_pricing_response.key_rate2_yr_dv01",
        headerName: "DV01 2yr",
        type: "number4",
      },
      {
        field: "bond_pricing_response.key_rate5_yr_dv01",
        headerName: "DV01 5yr",
        type: "number4",
      },
      {
        field: "bond_pricing_response.key_rate10_yr_dv01",
        headerName: "DV01 10yr",
        type: "number4",
      },
      {
        field: "bond_pricing_response.key_rate30_yr_dv01",
        headerName: "DV01 30yr",
        type: "number4",
      },
    ],
  },

  // Static Prepay Projections CPR
  {
    headerName: "Static Prepay Projections CPR",
    children: [
      {
        field: "bond_pricing_response.one_month_cpr",
        headerName: "Static CPR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.one_year_cpr",
        headerName: "Static CPR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_cpr",
        headerName: "Static CPR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_cpr",
        headerName: "Static CPR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.long_term_cpr",
        headerName: "Static CPR LT (%)",
        type: "number1",
      },
    ],
  },

  // Static Prepay Projections CRR
  {
    headerName: "Static Prepay Projections CRR",
    children: [
      {
        field: "bond_pricing_response.one_month_crr",
        headerName: "Static CRR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.one_year_crr",
        headerName: "Static CRR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_crr",
        headerName: "Static CRR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_crr",
        headerName: "Static CRR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.long_term_crr",
        headerName: "Static CRR LT (%)",
        type: "number1",
      },
    ],
  },

  // Static Prepay Projections CDR
  {
    headerName: "Static Prepay Projections CDR",
    children: [
      {
        field: "bond_pricing_response.one_month_cdr",
        headerName: "Static CDR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.one_year_cdr",
        headerName: "Static CDR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_cdr",
        headerName: "Static CDR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_cdr",
        headerName: "Static CDR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.long_term_cdr",
        headerName: "Static CDR LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Prepay Projections CPR
  {
    headerName: "Forward Prepay Projections CPR",
    children: [
      {
        field: "bond_pricing_response.one_month_forward_cpr",
        headerName: "Forward CPR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.one_year_forward_cpr",
        headerName: "Forward CPR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_cpr",
        headerName: "Forward CPR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_cpr",
        headerName: "Forward CPR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.forward_long_term_cpr",
        headerName: "Forward CPR LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Prepay Projections CRR
  {
    headerName: "Forward Prepay Projections CRR",
    children: [
      {
        field: "bond_pricing_response.one_month_forward_crr",
        headerName: "Forward CRR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.one_year_forward_crr",
        headerName: "Forward CRR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_crr",
        headerName: "Forward CRR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_crr",
        headerName: "Forward CRR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.forward_long_term_crr",
        headerName: "Forward CRR LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Prepay Projections CDR
  {
    headerName: "Forward Prepay Projection CDR",
    children: [
      {
        field: "bond_pricing_response.one_month_forward_cdr",
        headerName: "Forward CDR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.one_year_forward_cdr",
        headerName: "Forward CDR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_cdr",
        headerName: "Forward CDR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_cdr",
        headerName: "Forward CDR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.forward_long_term_cdr",
        headerName: "Forward CDR LT (%)",
        type: "number1",
      },
    ],
  },

  // Cumulative CRR
  {
    headerName: "Cumulative CRR",
    children: [
      {
        field: "bond_pricing_response.one_year_cum_prepay_pct",
        headerName: "Cumulative CRR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_cum_prepay_pct",
        headerName: "Cumulative CRR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_cum_prepay_pct",
        headerName: "Cumulative CRR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_cum_prepay_pct",
        headerName: "Cumulative CRR LT (%)",
        type: "number1",
      },
    ],
  },

  // Cumulative CDR
  {
    headerName: "Cumulative CDR",
    children: [
      {
        field: "bond_pricing_response.one_year_cum_default_pct",
        headerName: "Cumulative CDR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_cum_default_pct",
        headerName: "Cumulative CDR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_cum_default_pct",
        headerName: "Cumulative CDR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_cum_default_pct",
        headerName: "Cumulative CDR LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Cumulative CRR
  {
    headerName: "Forward Cumulative CRR",
    children: [
      {
        field: "bond_pricing_response.one_year_forward_cum_prepay_pct",
        headerName: "Forward Cumulative CRR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_cum_prepay_pct",
        headerName: "Forward Cumulative CRR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_cum_prepay_pct",
        headerName: "Forward Cumulative CRR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_forward_cum_prepay_pct",
        headerName: "Forward Cumulative CRR LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Cumulative CDR
  {
    headerName: "Forward Cumulative CDR",
    children: [
      {
        field: "bond_pricing_response.one_year_forward_cum_default_pct",
        headerName: "Forward Cumulative CDR 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_cum_default_pct",
        headerName: "Forward Cumulative CDR 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_cum_default_pct",
        headerName: "Forward Cumulative CDR 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_forward_cum_default_pct",
        headerName: "Forward Cumulative CDR LT (%)",
        type: "number1",
      },
    ],
  },

  // Amortization
  {
    headerName: "Amortization",
    children: [
      {
        field: "bond_pricing_response.one_year_cum_amort_pct",
        headerName: "Cumulative Amort. 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_cum_amort_pct",
        headerName: "Cumulative Amort. 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_cum_amort_pct",
        headerName: "Cumulative Amort. 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_cum_amort_pct",
        headerName: "Cumulative Amort. LT (%)",
        type: "number1",
      },
    ],
  },

  // Remaining Balance
  {
    headerName: "Remaining Balance",
    children: [
      {
        field: "bond_pricing_response.one_year_rem_bal_pct",
        headerName: "Remaining Balance 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_rem_bal_pct",
        headerName: "Remaining Balance 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_rem_bal_pct",
        headerName: "Remaining Balance 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_rem_bal_pct",
        headerName: "Remaining Balance LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Amortization
  {
    headerName: "Forward Amortization",
    children: [
      {
        field: "bond_pricing_response.one_year_forward_cum_amort_pct",
        headerName: "Forward Cumulative Amort. 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_cum_amort_pct",
        headerName: "Forward Cumulative Amort. 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_cum_amort_pct",
        headerName: "Forward Cumulative Amort. 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_forward_cum_amort_pct",
        headerName: "Forward Cumulative Amort. LT (%)",
        type: "number1",
      },
    ],
  },

  // Forward Remaining Balance
  {
    headerName: "Forward Remaining Balance",
    children: [
      {
        field: "bond_pricing_response.one_year_forward_rem_bal_pct",
        headerName: "Forward Remaining Balance 1yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.three_year_forward_rem_bal_pct",
        headerName: "Forward Remaining Balance 3yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.five_year_forward_rem_bal_pct",
        headerName: "Forward Remaining Balance 5yr (%)",
        type: "number1",
      },
      {
        field: "bond_pricing_response.life_forward_rem_bal_pct",
        headerName: "Forward Remaining Balance LT (%)",
        type: "number1",
      },
    ],
  },
];
// Prepay Tracking
const prepayTrackingColumnDefs: (
  | ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>
  | ColGroupDef
)[] = [
  {
    field: "bond_prepay_tracking_response.security",
    headerName: "Tracking Security",
    type: "text",
  },
  {
    field: "bond_prepay_tracking_response.cello_cdu_date",
    headerName: "Cello CDU Date",
    type: "date",
  },
  {
    field: "bond_prepay_tracking_response.prepay_month",
    headerName: "Prepay Month",
    type: "date",
  },
  {
    field: "bond_prepay_tracking_response.factor_month",
    headerName: "Tracking Factor Month",
    type: "date",
  },
  {
    field: "bond_prepay_tracking_response.balance",
    headerName: "Tracking Balance",
    type: "number0",
  },

  // CPR Tracking
  {
    headerName: "CPR",
    children: [
      {
        field: "bond_prepay_tracking_response.act_cpr_1m",
        headerName: "Actual CPR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cpr_1m",
        headerName: "Model CPR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cpr_1m",
        headerName: "Act/Proj CPR 1m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cpr_1m",
        headerName: "Act-Proj CPR 1m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cpr_3m",
        headerName: "Actual CPR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cpr_3m",
        headerName: "Model CPR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cpr_3m",
        headerName: "Act/Proj CPR 3m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cpr_3m",
        headerName: "Act-Proj CPR 3m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cpr_6m",
        headerName: "Actual CPR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cpr_6m",
        headerName: "Model CPR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cpr_6m",
        headerName: "Act/Proj CPR 6m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cpr_6m",
        headerName: "Act-Proj CPR 6m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cpr_12m",
        headerName: "Actual CPR 12m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cpr_12m",
        headerName: "Model CPR 12m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cpr_12m",
        headerName: "Act/Proj CPR 12m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cpr_12m",
        headerName: "Act-Proj CPR 12m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cpr_24m",
        headerName: "Actual CPR 24m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cpr_24m",
        headerName: "Model CPR 24m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cpr_24m",
        headerName: "Act/Proj CPR 24m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cpr_24m",
        headerName: "Act-Proj CPR 24m",
        type: "number2",
      },
    ],
  },

  // CRR Tracking
  {
    headerName: "CRR",
    children: [
      {
        field: "bond_prepay_tracking_response.act_crr_1m",
        headerName: "Actual CRR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_crr_1m",
        headerName: "Model CRR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_crr_1m",
        headerName: "Act/Proj CRR 1m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_crr_1m",
        headerName: "Act-Proj CRR 1m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_crr_3m",
        headerName: "Actual CRR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_crr_3m",
        headerName: "Model CRR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_crr_3m",
        headerName: "Act/Proj CRR 3m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_crr_3m",
        headerName: "Act-Proj CRR 3m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_crr_6m",
        headerName: "Actual CRR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_crr_6m",
        headerName: "Model CRR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_crr_6m",
        headerName: "Act/Proj CRR 6m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_crr_6m",
        headerName: "Act-Proj CRR 6m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_crr_12m",
        headerName: "Actual CRR 12m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_crr_12m",
        headerName: "Model CRR 12m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_crr_12m",
        headerName: "Act/Proj CRR 12m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_crr_12m",
        headerName: "Act-Proj CRR 12m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_crr_24m",
        headerName: "Actual CRR 24m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_crr_24m",
        headerName: "Model CRR 24m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_crr_24m",
        headerName: "Act/Proj CRR 24m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_crr_24m",
        headerName: "Act-Proj CRR 24m",
        type: "number2",
      },
    ],
  },

  // CDR Tracking
  {
    headerName: "CDR",
    children: [
      {
        field: "bond_prepay_tracking_response.act_cdr_1m",
        headerName: "Actual CDR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cdr_1m",
        headerName: "Model CDR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cdr_1m",
        headerName: "Act/Proj CDR 1m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cdr_1m",
        headerName: "Act-Proj CDR 1m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cdr_3m",
        headerName: "Actual CDR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cdr_3m",
        headerName: "Model CDR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cdr_3m",
        headerName: "Act/Proj CDR 3m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cdr_3m",
        headerName: "Act-Proj CDR 3m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cdr_6m",
        headerName: "Actual CDR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cdr_6m",
        headerName: "Model CDR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cdr_6m",
        headerName: "Act/Proj CDR 6m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cdr_6m",
        headerName: "Act-Proj CDR 6m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cdr_12m",
        headerName: "Actual CDR 12m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cdr_12m",
        headerName: "Model CDR 12m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cdr_12m",
        headerName: "Act/Proj CDR 12m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cdr_12m",
        headerName: "Act-Proj CDR 12m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act_cdr_24m",
        headerName: "Actual CDR 24m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.proj_cdr_24m",
        headerName: "Model CDR 24m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_tracking_response.act/proj_cdr_24m",
        headerName: "Act/Proj CDR 24m",
        type: "number2",
      },
      {
        field: "bond_prepay_tracking_response.act-proj_cdr_24m",
        headerName: "Act-Proj CDR 24m",
        type: "number2",
      },
    ],
  },
];

// Prepay Projections
const prepayProjectionsColumnDefs: (
  | ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>
  | ColGroupDef
)[] = [
  {
    field: "bond_prepay_projections_response.security",
    headerName: "Projections Security",
    type: "text",
  },
  {
    headerName: "Prepay Projection Speeds",
    children: [
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.period",
        headerName: "Projection Period",
        type: "number0",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.prepay_month",
        headerName: "Projection Prepay Month",
        type: "date",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.factor_month",
        headerName: "Projection Factor Month",
        type: "date",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.date",
        headerName: "Projection Date",
        type: "date",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_1m",
        headerName: "Proj Speed CPR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_3m",
        headerName: "Proj Speed CPR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_6m",
        headerName: "Proj Speed CPR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_1y",
        headerName: "Proj Speed CPR 1y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_3y",
        headerName: "Proj Speed CPR 3y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_5y",
        headerName: "Proj Speed CPR 5y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cpr_lt",
        headerName: "Proj Speed CPR LT (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_1m",
        headerName: "Proj Speed CRR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_3m",
        headerName: "Proj Speed CRR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_6m",
        headerName: "Proj Speed CRR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_1y",
        headerName: "Proj Speed CRR 1y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_3y",
        headerName: "Proj Speed CRR 3y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_5y",
        headerName: "Proj Speed CRR 5y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.crr_lt",
        headerName: "Proj Speed CRR LT (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_1m",
        headerName: "Proj Speed CDR 1m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_3m",
        headerName: "Proj Speed CDR 3m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_6m",
        headerName: "Proj Speed CDR 6m (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_1y",
        headerName: "Proj Speed CDR 1y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_3y",
        headerName: "Proj Speed CDR 3y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_5y",
        headerName: "Proj Speed CDR 5y (%)",
        type: "number1",
      },
      {
        field: "bond_prepay_projections_response.prepay_projection_speeds.cdr_lt",
        headerName: "Proj Speed CDR LT (%)",
        type: "number1",
      },
    ],
  },
];

// Cashflow
const cashflowColumnDefs: ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>[] = [
  {
    field: "bond_cashflow_response.path",
    headerName: "Path",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.cdu_balance",
    headerName: "CDU Balance",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.period",
    headerName: "Period",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.accrual_start_date",
    headerName: "CF Accrual Start",
    type: "date",
  },
  {
    field: "bond_cashflow_response.accrual_end_date",
    headerName: "CF Accrual End",
    type: "date",
  },
  {
    field: "bond_cashflow_response.payment_date",
    headerName: "CF Payment Date",
    type: "date",
  },
  {
    field: "bond_cashflow_response.start_balance",
    headerName: "CF Start Balance",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.end_balance",
    headerName: "CF End Balance",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.total_cash_flow",
    headerName: "Total Cash Flow",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.principal",
    headerName: "CF Principal",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.interest",
    headerName: "CF Interest",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.penalty_payment",
    headerName: "Penalty Payment",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.coupon_interest",
    headerName: "Coupon Interest",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.scheduled_principal",
    headerName: "Scheduled Principal",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.unschedule_principal",
    headerName: "Unscheduled Principal",
    type: "number0",
  },
  {
    field: "bond_cashflow_response.smm",
    headerName: "SMM",
    type: "number6",
  },
  {
    field: "bond_cashflow_response.cpr",
    headerName: "CF CPR",
    type: "number1",
  },
  {
    field: "bond_cashflow_response.cdr",
    headerName: "CF CDR",
    type: "number1",
  },
  {
    field: "bond_cashflow_response.crr",
    headerName: "CF CRR",
    type: "number1",
  },
  {
    field: "bond_cashflow_response.mortgage_rate",
    headerName: "Mortgage Rate",
    type: "number3",
  },
  {
    field: "bond_cashflow_response.factor_month",
    headerName: "Factor Month",
    type: "date",
  },
  {
    field: "bond_cashflow_response.coupon",
    headerName: "CF Coupon",
    type: "number2",
  },
  {
    field: "bond_cashflow_response.effective_coupon",
    headerName: "Effective Coupon",
    type: "number2",
  },
  {
    field: "bond_cashflow_response.scenario",
    headerName: "Scenario",
    type: "text",
  },
  {
    field: "bond_cashflow_response.discount_rate",
    headerName: "Discount Rate",
    type: "number3",
  },
  {
    field: "bond_cashflow_response.coupon_index_name",
    headerName: "Coupon Index Name",
    type: "text",
  },
  {
    field: "bond_cashflow_response.coupon_index",
    headerName: "Coupon Index",
    type: "number3",
  },
];

// Main export function that accepts metadata for proper display formatting
export const getPortfolioResultsColumnDefs = (
  metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse
): (ColDef<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult> | ColGroupDef)[] => [
  {
    headerName: "General",
    children: generalColumnDefs,
  },
  {
    headerName: "Calculations",
    children: calculationsColumnDefs,
  },
  {
    headerName: "Calculation Assumptions",
    children: getCalculationAssumptionsColumnDefs(metadata),
  },
  {
    headerName: "Security Info",
    children: securityInfoColumnDefs,
  },
  {
    headerName: "Security Structure",
    children: securityStructureColumnDefs,
  },
  {
    headerName: "Pricing",
    children: pricingColumnDefs,
  },
  {
    headerName: "Prepay Tracking",
    children: prepayTrackingColumnDefs,
  },
  {
    headerName: "Prepay Projections",
    children: prepayProjectionsColumnDefs,
  },
  {
    headerName: "Cashflow",
    children: cashflowColumnDefs,
  },
  {
    field: "security_record_id",
    headerName: "Security Record ID",
    type: "text",
    hide: true,
  },
];
