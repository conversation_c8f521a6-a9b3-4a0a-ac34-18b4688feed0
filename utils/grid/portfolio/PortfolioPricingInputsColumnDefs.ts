import { CellClassParams, ColDef, ValueFormatterFunc, ValueGetterFunc } from "ag-grid-enterprise";
import {
  CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions,
  CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  CA_Mastr_Api_v1_0_Models_Util_StringOption,
  CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions,
  CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord,
  CA_Mastr_Models_v1_0_Models_PrepayMethod,
  CA_Mastr_Models_v1_0_Models_PricingType,
} from "@/utils/openapi";
import { capitalize } from "@/design-system/molecules/CAGrid/helpers";

const getDisplayValueFromArrayMetadata = (
  metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  currentValue: string,
  metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions
) => {
  const options = metadata?.portfolio_settings?.calculation_assumptions?.[
    metadataKey
  ] as Array<CA_Mastr_Api_v1_0_Models_Util_StringOption>;

  const matchedOption = options?.find((option) => option.value === currentValue);

  if (matchedOption) {
    return matchedOption.display_value ?? matchedOption.value ?? "";
  }
  if (currentValue) {
    return currentValue.toString();
  }
  return "-";
};

const formatSelectorValue =
  (
    metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
    metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions
  ) =>
  (value: string) => {
    return getDisplayValueFromArrayMetadata(metadata, value, metadataKey);
  };

const formatValueFromMetadata =
  (
    metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
    metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions
  ): ValueFormatterFunc<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord> =>
  (params) => {
    return getDisplayValueFromArrayMetadata(metadata, params.value, metadataKey);
  };

const getOptionsFromMetadata = (
  metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  metadataKey: keyof CA_Mastr_Api_v1_0_Models_Util_CalculationAssumptions
) => {
  if (!metadataKey) return undefined;
  const options = metadata?.portfolio_settings?.calculation_assumptions?.[metadataKey];
  if (Array.isArray(options)) {
    return options.map((opt) => ("value" in opt ? opt.value : undefined)).filter(Boolean);
  }
  return options;
};

const valueGetter =
  (
    defaultCalculationAssumptions: CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions
  ): ValueGetterFunc<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord> =>
  (params) => {
    const fieldPath = params.colDef.field;
    const securityRecordId = params.data?.security_record_id;
    if (!fieldPath || !securityRecordId) return;

    // Handle nested fields like "calculation_assumptions_override.field_name"
    if (fieldPath.includes(".")) {
      const parts = fieldPath.split(".");
      if (parts.length === 2 && parts[0] === "calculation_assumptions_override") {
        const fieldName = parts[1] as keyof CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions;
        return params.data?.calculation_assumptions_override?.[fieldName] ?? defaultCalculationAssumptions[fieldName];
      }
    }

    // Handle direct fields
    const fieldName = fieldPath as keyof CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord;
    return params.data?.[fieldName];
  };

const shouldHighlightCell = (params: CellClassParams<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>) => {
  const fieldPath = params.colDef.field;
  if (!fieldPath || !params.data) return false;

  // Only highlight cells for calculation_assumptions_override fields
  if (fieldPath.includes("calculation_assumptions_override.")) {
    const parts = fieldPath.split(".");
    if (parts.length === 2 && parts[0] === "calculation_assumptions_override") {
      const fieldName = parts[1] as keyof CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions;
      // Highlight if the field has a value in calculation_assumptions_override
      return (
        params.data.calculation_assumptions_override?.[fieldName] !== undefined &&
        params.data.calculation_assumptions_override?.[fieldName] !== null
      );
    }
  }

  return false;
};

const formatPricingType = (value: string) => {
  if (!value) return "-";
  if (value === CA_Mastr_Models_v1_0_Models_PricingType.OAS) {
    return "OAS";
  }
  return capitalize(value);
};
export const getPortfolioPricingInputsColumnDefs = (
  metadata: CA_Mastr_Api_v1_0_Models_Util_MetadataResponse,
  defaultCalculationAssumptions: CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions
): ColDef<CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord>[] => [
  {
    field: "name",
    headerName: "Security",
    type: "text",
    editable: true,
    cellEditor: "agTextCellEditor",
    rowDrag: true,
    width: 150,
  },
  {
    field: "face_amount",
    headerName: "Face Amount",
    type: "number",
    editable: true,
    cellEditor: "agNumberCellEditor",
  },
  {
    field: "pricing_type",
    headerName: "Type",
    type: "text",
    editable: true,
    cellEditor: "agRichSelectCellEditor",
    cellEditorParams: {
      values: [
        CA_Mastr_Models_v1_0_Models_PricingType.PRICE,
        CA_Mastr_Models_v1_0_Models_PricingType.OAS,
        CA_Mastr_Models_v1_0_Models_PricingType.YIELD,
      ],
      formatValue: formatPricingType,
    },
    valueFormatter: (params) => formatPricingType(params.value),
  },
  {
    field: "pricing_level",
    headerName: "Value",
    type: "number",
    editable: true,
    cellEditor: "agNumberCellEditor",
  },
  {
    field: "prepay_method",
    headerName: "Prepay Model",
    type: "text",
    editable: true,
    cellEditor: "agRichSelectCellEditor",
    cellEditorParams: {
      values: Object.values(CA_Mastr_Models_v1_0_Models_PrepayMethod),
    },
  },
  {
    field: "prepay_percentage",
    headerName: "Multiplier (%)",
    type: "number",
    editable: true,
    cellEditor: "agNumberCellEditor",
  },
  {
    field: "rate_shift",
    headerName: "Rate Shift",
    type: "number",
    editable: true,
    cellEditor: "agNumberCellEditor",
  },
  {
    field: "calculation_assumptions_override.market_data_source",
    headerName: "Market Data Source",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "market_data_sources"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "market_data_sources"),
      formatValue: formatSelectorValue(metadata, "market_data_sources"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.curve_date",
    headerName: "Curve Date",
    type: "date",
    cellEditor: "agDateStringCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.pricing_date",
    headerName: "Pricing Date",
    type: "date",
    cellEditor: "agDateStringCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.settle_date",
    headerName: "Settle Date",
    type: "date",
    cellEditor: "agDateStringCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.model_version",
    headerName: "Prepay Model Version",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "version"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "version"),
      formatValue: formatSelectorValue(metadata, "version"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.current_coupon_model_name",
    headerName: "Current Coupon",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "current_coupon_model"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "current_coupon_model"),
      formatValue: formatSelectorValue(metadata, "current_coupon_model"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.mortgage_model_type",
    headerName: "Primary-Secondary Spread",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "primary_secondary_spread"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "primary_secondary_spread"),
      formatValue: formatSelectorValue(metadata, "primary_secondary_spread"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.repline_algorithm",
    headerName: "Repline Algo",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "repline_level"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "repline_level"),
      formatValue: formatSelectorValue(metadata, "repline_level"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.cello_cdu_date",
    headerName: "Cello CDU Date",
    type: "date",
    cellEditor: "agDateStringCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.ignore_cdu_paydate_after",
    headerName: "Ignore Paydate After",
    type: "date",
    cellEditor: "agDateStringCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.projection_paths",
    headerName: "Paths",
    type: "number",
    cellEditor: "agNumberCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.interest_rate_model_name",
    headerName: "Type",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "type"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "type"),
      formatValue: formatSelectorValue(metadata, "type"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.interest_rate_calibration_method",
    headerName: "Calibration",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "calibration"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "calibration"),
      formatValue: formatSelectorValue(metadata, "calibration"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.yield_curve_model_name",
    headerName: "Yield Curve Model",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "yield_curve_model"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "yield_curve_model"),
      formatValue: formatSelectorValue(metadata, "yield_curve_model"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.key_rate_points",
    headerName: "Key Rates",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "key_rate_points"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "key_rate_points"),
      formatValue: formatSelectorValue(metadata, "key_rate_points"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.key_rate_shift",
    headerName: "Curve shift (bps)",
    type: "number",
    cellEditor: "agNumberCellEditor",
    editable: true,
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "calculation_assumptions_override.num_dist_workers",
    headerName: "Dashboard OAS",
    type: "text",
    valueFormatter: formatValueFromMetadata(metadata, "vcpu_per_oas"),
    cellEditor: "agRichSelectCellEditor",
    editable: true,
    cellEditorParams: {
      values: getOptionsFromMetadata(metadata, "vcpu_per_oas"),
      formatValue: formatSelectorValue(metadata, "vcpu_per_oas"),
    },
    valueGetter: valueGetter(defaultCalculationAssumptions),
    cellClassRules: {
      "ag-cell-data-changed": shouldHighlightCell,
    },
  },
  {
    field: "note",
    headerName: "Note",
    type: "text",
    editable: true,
    cellEditor: "agLargeTextCellEditor",
    cellEditorPopup: true,
    cellEditorParams: {
      maxLength: 100,
    },
  },
];
