import React from "react";

import type { CustomCellRendererProps } from "ag-grid-react";
import { FaCheckCircle } from "react-icons/fa";
import { IoMdCloseCircle } from "react-icons/io";
import { Box, Flex, Spinner, Text, Tooltip, useColorModeValue } from "@chakra-ui/react";
import {
  CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult,
  CA_Mastr_Models_v1_0_Models_Stage,
} from "@/utils/openapi";
import CAIcon from "@/design-system/atoms/CAIcon";

export const StatusCellRenderer = (
  params: CustomCellRendererProps<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult>
) => {
  const textColor = useColorModeValue("white", "celloBlue.900");
  if (params.value === CA_Mastr_Models_v1_0_Models_Stage.COMPLETED) {
    return (
      <Flex h="full" justifyContent="center" alignItems="center">
        <CAIcon as={FaCheckCircle} color="green" boxSize={5} />
      </Flex>
    );
  }
  if (params.value === CA_Mastr_Models_v1_0_Models_Stage.PROCESSING) {
    return (
      <Flex h="full" justifyContent="center" alignItems="center">
        <Spinner size="sm" />
      </Flex>
    );
  }
  if (params.value === CA_Mastr_Models_v1_0_Models_Stage.FAILED) {
    return (
      <Tooltip
        label={
          <Box>
            {params.data?.status?.error_messages?.filter(Boolean)?.map((err, i) => (
              <Text key={`Error-${i}`} color={textColor}>
                {err?.error_message}
              </Text>
            ))}
          </Box>
        }
        hidden={!params.data?.status?.error_messages}
        placement="bottom-end"
      >
        <Flex h="full" justifyContent="center" alignItems="center" cursor="pointer">
          <CAIcon as={IoMdCloseCircle} color="red" boxSize={6} />
        </Flex>
      </Tooltip>
    );
  }
  if (params.value === CA_Mastr_Models_v1_0_Models_Stage.COMPLETED_WITH_ERRORS) {
    return (
      <Tooltip
        label={params.value?.message ? <Text color={textColor}>{params.value?.message}</Text> : ""}
        placement="bottom-end"
      >
        <Flex h="full" justifyContent="center" alignItems="center" cursor="pointer">
          <CAIcon as={FaCheckCircle} color="orange" boxSize={4} />
        </Flex>
      </Tooltip>
    );
  }
  return params.value;
};
