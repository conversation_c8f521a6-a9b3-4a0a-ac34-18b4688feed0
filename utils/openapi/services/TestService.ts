/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CA_Mastr_Api_v1_0_Controllers_TestRequest } from "../models/CA_Mastr_Api_v1_0_Controllers_TestRequest";

import type { CancelablePromise } from "../core/CancelablePromise";
import { OpenAPI } from "../core/OpenAPI";
import { request as __request } from "../core/request";

export class TestService {
  /**
   * @param requestBody
   * @returns any OK
   * @throws ApiError
   */
  public static postSendMskMessage(requestBody?: CA_Mastr_Api_v1_0_Controllers_TestRequest): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/1.0/SendMSKMessage",
      body: requestBody,
      mediaType: "application/json",
    });
  }
}
