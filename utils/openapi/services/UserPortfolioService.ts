/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_DeleteUserPortfolioResponse } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolio_DeleteUserPortfolioResponse";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfoliosResponse } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfoliosResponse";

import type { CancelablePromise } from "../core/CancelablePromise";
import { OpenAPI } from "../core/OpenAPI";
import { request as __request } from "../core/request";

export class UserPortfolioService {
  /**
   * Inserts a new user portfolio
   * @param requestBody Insert user portfolio request
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse OK
   * @throws ApiError
   */
  public static postUserPortfolio(
    requestBody?: CA_Mastr_Api_v1_0_Models_UserPortfolio_InsertUserPortfolio
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/1.0/UserPortfolio",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }

  /**
   * Deletes a user portfolio
   * @param userPortfolioId User portfolio id
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolio_DeleteUserPortfolioResponse OK
   * @throws ApiError
   */
  public static deleteUserPortfolio(
    userPortfolioId?: number
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolio_DeleteUserPortfolioResponse> {
    return __request(OpenAPI, {
      method: "DELETE",
      url: "/1.0/UserPortfolio",
      query: {
        user_portfolio_id: userPortfolioId,
      },
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }

  /**
   * Gets a single user portfolio by ID
   * @param userPortfolioId User portfolio ID
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse OK
   * @throws ApiError
   */
  public static getUserPortfolio(
    userPortfolioId?: number
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/1.0/UserPortfolio",
      query: {
        user_portfolio_id: userPortfolioId,
      },
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }

  /**
   * Updates a user portfolio
   * @param requestBody Update user portfolio
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse OK
   * @throws ApiError
   */
  public static putUserPortfolio(
    requestBody?: CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/1.0/UserPortfolio",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }

  /**
   * Gets all available user portfolios
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfoliosResponse OK
   * @throws ApiError
   */
  public static getUserPortfolios(): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfoliosResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/1.0/UserPortfolios",
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }
}
