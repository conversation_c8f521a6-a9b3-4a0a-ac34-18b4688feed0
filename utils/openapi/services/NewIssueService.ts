/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueFileReponse } from "../models/CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueFileReponse";
import type { CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueLoadRequest } from "../models/CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueLoadRequest";
import type { CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse } from "../models/CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse";
import type { CA_Mastr_Api_v1_0_Models_NewIssue_NewIssuesResponse } from "../models/CA_Mastr_Api_v1_0_Models_NewIssue_NewIssuesResponse";

import type { CancelablePromise } from "../core/CancelablePromise";
import { OpenAPI } from "../core/OpenAPI";
import { request as __request } from "../core/request";

export class NewIssueService {
  /**
   * Uploads multiple files for a New Issue.
   * **Important:**
   * The actual production endpoint for single file upload is **`POST /1.0/File`**, which uses `IFormFile`.
   *
   * This `Upload` endpoint exists **only for Swagger UI compatibility**, because Swagger has limitations
   * when handling `IFormFile` in our case.
   *
   * Use this endpoint only for testing and a more user-friendly Swagger experience.
   * @param formData
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueFileReponse OK
   * @throws ApiError
   */
  public static postUpload(formData?: {
    /**
     * The files to upload.
     */
    files?: Array<Blob>;
  }): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueFileReponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/1.0/Upload",
      formData: formData,
      mediaType: "multipart/form-data",
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }

  /**
   * Retrieves new issues by deal name and user ID
   * @param requestBody
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse OK
   * @throws ApiError
   */
  public static postLoad(
    requestBody?: CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueLoadRequest
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/1.0/Load",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        400: `Bad Request`,
        404: `Not Found`,
      },
    });
  }

  /**
   * Gets a New Issue by its unique identifier.
   * @param newIssueId The New Issue GUID
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse OK
   * @throws ApiError
   */
  public static getNewIssue(
    newIssueId?: string
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/1.0/NewIssue",
      query: {
        newIssueId: newIssueId,
      },
      errors: {
        404: `Not Found`,
      },
    });
  }

  /**
   * Gets all New Issues for the current client.
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssuesResponse OK
   * @throws ApiError
   */
  public static getNewIssues(): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssuesResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/1.0/NewIssues",
      errors: {
        404: `Not Found`,
      },
    });
  }
}
