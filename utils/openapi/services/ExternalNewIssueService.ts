/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CA_Mastr_Api_v1_0_Models_NewIssue_ExternalNewIssueUpdateRequest } from "../models/CA_Mastr_Api_v1_0_Models_NewIssue_ExternalNewIssueUpdateRequest";
import type { CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse } from "../models/CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse";

import type { CancelablePromise } from "../core/CancelablePromise";
import { OpenAPI } from "../core/OpenAPI";
import { request as __request } from "../core/request";

export class ExternalNewIssueService {
  /**
   * @param parametersNewIssueId
   * @param appId
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse OK
   * @throws ApiError
   */
  public static getExternalNewIssue(
    parametersNewIssueId?: string,
    appId?: string
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse> {
    return __request(OpenAPI, {
      method: "GET",
      url: "/1.0/ExternalNewIssue",
      query: {
        "parameters.new_issue_id": parametersNewIssueId,
        app_id: appId,
      },
      errors: {
        404: `Not Found`,
      },
    });
  }

  /**
   * @param requestBody
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse OK
   * @throws ApiError
   */
  public static putUpdateNewIssueStage(
    requestBody?: CA_Mastr_Api_v1_0_Models_NewIssue_ExternalNewIssueUpdateRequest
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/1.0/UpdateNewIssueStage",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        404: `Not Found`,
      },
    });
  }

  /**
   * @param requestBody
   * @returns CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse OK
   * @throws ApiError
   */
  public static putUpdateNewIssueStatus(
    requestBody?: CA_Mastr_Api_v1_0_Models_NewIssue_ExternalNewIssueUpdateRequest
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_NewIssue_NewIssueResponse> {
    return __request(OpenAPI, {
      method: "PUT",
      url: "/1.0/UpdateNewIssueStatus",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        404: `Not Found`,
      },
    });
  }
}
