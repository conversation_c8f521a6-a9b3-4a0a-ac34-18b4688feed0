/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CA_Mastr_Api_v1_0_Models_Portfolio_UserPortfolioRunRequest } from "../models/CA_Mastr_Api_v1_0_Models_Portfolio_UserPortfolioRunRequest";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResponse } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResponse";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultParameters } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultParameters";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse } from "../models/CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse";

import type { CancelablePromise } from "../core/CancelablePromise";
import { OpenAPI } from "../core/OpenAPI";
import { request as __request } from "../core/request";

export class UserPortfolioRunService {
  /**
   * Run a User Portfolio
   * @param requestBody
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResponse OK
   * @throws ApiError
   */
  public static postUserPortfolioRunAsync(
    requestBody?: CA_Mastr_Api_v1_0_Models_Portfolio_UserPortfolioRunRequest
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/1.0/UserPortfolioRunAsync",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        400: `Bad Request`,
      },
    });
  }

  /**
   * Get the results of a User Portfolio Run
   * @param requestBody
   * @returns CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse OK
   * @throws ApiError
   */
  public static postUserPortfolioRunResultsAsync(
    requestBody?: CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultParameters
  ): CancelablePromise<CA_Mastr_Api_v1_0_Models_UserPortfolioRun_UserPortfolioRunResultResponse> {
    return __request(OpenAPI, {
      method: "POST",
      url: "/1.0/UserPortfolioRunResultsAsync",
      body: requestBody,
      mediaType: "application/json",
      errors: {
        400: `Bad Request`,
      },
    });
  }
}
