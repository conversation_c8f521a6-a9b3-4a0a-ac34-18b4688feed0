/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_Activity_User } from "./CA_Mastr_Api_v1_0_Models_Activity_User";
import type { CA_Mastr_Api_v1_0_Models_NewIssue_Stage } from "./CA_Mastr_Api_v1_0_Models_NewIssue_Stage";

export type CA_Mastr_Api_v1_0_Models_NewIssue_NewIssue = {
  new_issue_id?: string;
  deal_name?: string | null;
  file_name?: string | null;
  new_deal_name?: string | null;
  prepay_model_version?: string | null;
  progress?: number;
  note?: string | null;
  user_id?: number;
  client_id?: number;
  is_deleted?: boolean;
  s3_path?: string | null;
  security_suffix?: string | null;
  security_names?: Array<string> | null;
  inserted?: string;
  updated?: string | null;
  user?: CA_Mastr_Api_v1_0_Models_Activity_User;
  stages?: Array<CA_Mastr_Api_v1_0_Models_NewIssue_Stage> | null;
  error_message?: string | null;
  duration?: number;
  status?: string | null;
  start_date?: string | null;
  end_date?: string | null;
};
