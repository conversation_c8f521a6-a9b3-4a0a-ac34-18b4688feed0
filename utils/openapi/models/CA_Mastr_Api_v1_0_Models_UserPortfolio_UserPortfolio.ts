/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_User } from "./CA_Mastr_Api_v1_0_Models_UserPortfolio_User";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioRun } from "./CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioRun";
import type { CA_Mastr_Models_v1_0_Models_Portfolio_UserPortfolioInput } from "./CA_Mastr_Models_v1_0_Models_Portfolio_UserPortfolioInput";
import type { CA_Mastr_Models_v1_0_Models_Stage } from "./CA_Mastr_Models_v1_0_Models_Stage";

export type CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolio = {
  user_portfolio_id?: number;
  user_portfolio_name?: string | null;
  as_of_date?: string | null;
  tags?: Array<string> | null;
  user_portfolio_input?: CA_Mastr_Models_v1_0_Models_Portfolio_UserPortfolioInput;
  is_ad_hoc?: boolean;
  inserted?: string;
  updated?: string | null;
  run_id?: string | null;
  user_id?: number;
  client_id?: number;
  user?: CA_Mastr_Api_v1_0_Models_UserPortfolio_User;
  last_run?: CA_Mastr_Api_v1_0_Models_UserPortfolio_UserPortfolioRun;
  status?: CA_Mastr_Models_v1_0_Models_Stage;
};
