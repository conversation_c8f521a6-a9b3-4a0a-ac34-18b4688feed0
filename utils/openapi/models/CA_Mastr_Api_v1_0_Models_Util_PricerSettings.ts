/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_Util_BoolOption } from "./CA_Mastr_Api_v1_0_Models_Util_BoolOption";
import type { CA_Mastr_Api_v1_0_Models_Util_CduDateInfo } from "./CA_Mastr_Api_v1_0_Models_Util_CduDateInfo";
import type { CA_Mastr_Api_v1_0_Models_Util_DateOption } from "./CA_Mastr_Api_v1_0_Models_Util_DateOption";
import type { CA_Mastr_Api_v1_0_Models_Util_IntegerOption } from "./CA_Mastr_Api_v1_0_Models_Util_IntegerOption";
import type { CA_Mastr_Api_v1_0_Models_Util_InterestRateModelConfig } from "./CA_Mastr_Api_v1_0_Models_Util_InterestRateModelConfig";
import type { CA_Mastr_Api_v1_0_Models_Util_ModelConfig } from "./CA_Mastr_Api_v1_0_Models_Util_ModelConfig";
import type { CA_Mastr_Api_v1_0_Models_Util_StringOption } from "./CA_Mastr_Api_v1_0_Models_Util_StringOption";

export type CA_Mastr_Api_v1_0_Models_Util_PricerSettings = {
  curve_date?: CA_Mastr_Api_v1_0_Models_Util_DateOption;
  settle_date?: CA_Mastr_Api_v1_0_Models_Util_DateOption;
  freeze_settlement_date?: CA_Mastr_Api_v1_0_Models_Util_BoolOption;
  cello_cdu_dates?: CA_Mastr_Api_v1_0_Models_Util_CduDateInfo;
  readonly repline_level?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  ignore_paydate_after?: CA_Mastr_Api_v1_0_Models_Util_DateOption;
  readonly deal_mode?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly market_data_source?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly prepay_models?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  rate_shift_bps?: CA_Mastr_Api_v1_0_Models_Util_IntegerOption;
  readonly key_rate_points?: Array<CA_Mastr_Api_v1_0_Models_Util_IntegerOption> | null;
  key_rate_bp_shift?: CA_Mastr_Api_v1_0_Models_Util_IntegerOption;
  readonly vcpu_per_oas?: Array<CA_Mastr_Api_v1_0_Models_Util_IntegerOption> | null;
  readonly version?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly current_coupon_model?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly primary_secondary_spread?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  interest_rate_paths?: CA_Mastr_Api_v1_0_Models_Util_IntegerOption;
  readonly type?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly calibration?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly yield_curve_model?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly prepay_model_scenario?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly cash_flow_scenario?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly bond_class?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly mortage_rate_scenario?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  previous_business_date?: CA_Mastr_Api_v1_0_Models_Util_DateOption;
  model_configs?: Array<CA_Mastr_Api_v1_0_Models_Util_ModelConfig> | null;
  readonly interest_rate_model_configs?: Array<CA_Mastr_Api_v1_0_Models_Util_InterestRateModelConfig> | null;
};
