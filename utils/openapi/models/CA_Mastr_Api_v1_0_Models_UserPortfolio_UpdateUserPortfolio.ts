/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_EntityRequestBase } from "./CA_Mastr_Api_v1_0_Models_EntityRequestBase";
import type { CA_Mastr_Models_v1_0_Models_Portfolio_UserPortfolioInput } from "./CA_Mastr_Models_v1_0_Models_Portfolio_UserPortfolioInput";

export type CA_Mastr_Api_v1_0_Models_UserPortfolio_UpdateUserPortfolio = CA_Mastr_Api_v1_0_Models_EntityRequestBase & {
  user_portfolio_id?: number;
  user_portfolio_name?: string | null;
  tags?: Array<string> | null;
  is_ad_hoc?: boolean;
  as_of_date?: string | null;
  user_portfolio_input?: CA_Mastr_Models_v1_0_Models_Portfolio_UserPortfolioInput;
};
