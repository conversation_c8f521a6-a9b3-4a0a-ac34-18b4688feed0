/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_Util_StringOption } from "./CA_Mastr_Api_v1_0_Models_Util_StringOption";
import type { CA_Mastr_Api_v1_0_Models_Util_StringOptionExt } from "./CA_Mastr_Api_v1_0_Models_Util_StringOptionExt";

export type CA_Mastr_Api_v1_0_Models_Util_BasicSearchSettings = {
  readonly spec_pool_agency?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOptionExt> | null;
  readonly tba_agency?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOptionExt> | null;
  readonly spec_pool_coupon?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly tba_coupon?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly tba_month?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly spec_pool_year?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly spec_pool_story?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOptionExt> | null;
  readonly spec_pool_gpl_story?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly api_functions?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly holidays?: Array<string> | null;
};
