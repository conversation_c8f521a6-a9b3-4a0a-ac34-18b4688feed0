/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_Util_DateOption } from "./CA_Mastr_Api_v1_0_Models_Util_DateOption";
import type { CA_Mastr_Api_v1_0_Models_Util_StringOption } from "./CA_Mastr_Api_v1_0_Models_Util_StringOption";

export type CA_Mastr_Api_v1_0_Models_Util_MarketDataSettings = {
  curve_date?: CA_Mastr_Api_v1_0_Models_Util_DateOption;
  readonly interest_rate_model?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly market_type?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
  readonly vol_calibration_series_types?: Array<CA_Mastr_Api_v1_0_Models_Util_StringOption> | null;
};
