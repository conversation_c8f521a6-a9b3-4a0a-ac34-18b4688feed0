/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Models_v1_0_Models_InterestRateCalibrationMethod } from "./CA_Mastr_Models_v1_0_Models_InterestRateCalibrationMethod";
import type { CA_Mastr_Models_v1_0_Models_ReplineAlgorithmType } from "./CA_Mastr_Models_v1_0_Models_ReplineAlgorithmType";

export type CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions = {
  market_data_source?: string | null;
  curve_date?: string | null;
  pricing_date?: string | null;
  settle_date?: string | null;
  model_version?: string | null;
  current_coupon_model_name?: string | null;
  mortgage_model_type?: string | null;
  repline_algorithm?: CA_Mastr_Models_v1_0_Models_ReplineAlgorithmType;
  cello_cdu_date?: string | null;
  ignore_cdu_paydate_after?: string | null;
  projection_paths?: number | null;
  interest_rate_model_name?: string | null;
  interest_rate_calibration_method?: CA_Mastr_Models_v1_0_Models_InterestRateCalibrationMethod;
  yield_curve_model_name?: string | null;
  key_rate_points?: number | null;
  key_rate_shift?: number | null;
  num_dist_workers?: number | null;
  run_distributed?: boolean | null;
};
