/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse } from "./CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse";
import type { CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse } from "./CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse";
import type { CA_Mastr_Api_v1_0_Models_BondPrepayTracking_BondPrepayTrackingResponse } from "./CA_Mastr_Api_v1_0_Models_BondPrepayTracking_BondPrepayTrackingResponse";
import type { CA_Mastr_Api_v1_0_Models_ResponseBase } from "./CA_Mastr_Api_v1_0_Models_ResponseBase";
import type { CA_Mastr_Api_v1_0_Models_SecurityInfo } from "./CA_Mastr_Api_v1_0_Models_SecurityInfo";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_PortfolioBondCashflowResponse } from "./CA_Mastr_Api_v1_0_Models_UserPortfolio_PortfolioBondCashflowResponse";
import type { CA_Mastr_Api_v1_0_Models_UserPortfolio_PortfolioBondPricingResponse } from "./CA_Mastr_Api_v1_0_Models_UserPortfolio_PortfolioBondPricingResponse";
import type { CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions } from "./CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions";
import type { CA_Mastr_Models_v1_0_Models_Portfolio_Calculations } from "./CA_Mastr_Models_v1_0_Models_Portfolio_Calculations";
import type { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "./CA_Mastr_Models_v1_0_Models_PrepayMethod";
import type { CA_Mastr_Models_v1_0_Models_PricingType } from "./CA_Mastr_Models_v1_0_Models_PricingType";

export type CA_Mastr_Api_v1_0_Models_UserPortfolioRun_SecurityRecordResult = CA_Mastr_Api_v1_0_Models_ResponseBase & {
  security_record_id?: string | null;
  input_partial_portfolio_run_id?: string | null;
  face_amount?: number | null;
  name?: string | null;
  note?: string | null;
  pricing_level?: number | null;
  pricing_type?: CA_Mastr_Models_v1_0_Models_PricingType;
  rate_shift?: number | null;
  prepay_percentage?: number | null;
  prepay_method?: CA_Mastr_Models_v1_0_Models_PrepayMethod;
  calculation_assumptions_override?: CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions;
  calculations?: CA_Mastr_Models_v1_0_Models_Portfolio_Calculations;
  security_info?: CA_Mastr_Api_v1_0_Models_SecurityInfo;
  bond_pricing_response?: CA_Mastr_Api_v1_0_Models_UserPortfolio_PortfolioBondPricingResponse;
  bond_prepay_tracking_response?: CA_Mastr_Api_v1_0_Models_BondPrepayTracking_BondPrepayTrackingResponse;
  bond_prepay_projections_response?: CA_Mastr_Api_v1_0_Models_BondPrepayProjections_BondPrepayProjectionsResponse;
  bond_indicatives_response?: CA_Mastr_Api_v1_0_Models_BondIndicatives_BondIndicativesResponse;
  bond_cashflow_response?: CA_Mastr_Api_v1_0_Models_UserPortfolio_PortfolioBondCashflowResponse;
};
