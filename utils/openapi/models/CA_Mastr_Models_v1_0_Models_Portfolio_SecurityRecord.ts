/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

import type { CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions } from "./CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions";
import type { CA_Mastr_Models_v1_0_Models_Portfolio_Calculations } from "./CA_Mastr_Models_v1_0_Models_Portfolio_Calculations";
import type { CA_Mastr_Models_v1_0_Models_PrepayMethod } from "./CA_Mastr_Models_v1_0_Models_PrepayMethod";
import type { CA_Mastr_Models_v1_0_Models_PricingType } from "./CA_Mastr_Models_v1_0_Models_PricingType";

export type CA_Mastr_Models_v1_0_Models_Portfolio_SecurityRecord = {
  security_record_id?: string | null;
  name?: string | null;
  face_amount?: number;
  pricing_level?: number | null;
  pricing_type?: CA_Mastr_Models_v1_0_Models_PricingType;
  rate_shift?: number | null;
  calculation_assumptions_override?: CA_Mastr_Models_v1_0_Models_Portfolio_CalculationAssumptions;
  calculations?: CA_Mastr_Models_v1_0_Models_Portfolio_Calculations;
  prepay_percentage?: number | null;
  prepay_method?: CA_Mastr_Models_v1_0_Models_PrepayMethod;
  note?: string | null;
};
